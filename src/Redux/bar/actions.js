const barActions = {
	BAR_ID_ACTION: 'BAR_ID_ACTION',
	REMOVE_BAR_ID: 'REMOVE_BAR_ID',
	SET_CURRENT_PRODUCT: 'SET_CURRENT_PRODUCT',

	setBarId: (data) => {
		return {
			type: barActions.BAR_ID_ACTION,
			bar_id: data
		};
	},
	removeBarId: () => {
		return {
			type: barActions.REMOVE_BAR_ID,
		};
	},
	setCurrentProduct: (data) => {
		return {
			type: barActions.SET_CURRENT_PRODUCT,
			currentProduct: data.currentProduct
		};
	}
};

export default barActions;
