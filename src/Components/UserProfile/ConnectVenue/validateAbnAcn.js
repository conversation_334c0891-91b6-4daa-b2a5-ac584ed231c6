/* eslint-disable react-hooks/rules-of-hooks */
import { useEffect } from 'react';
import { useState, useDeferredValue } from 'react';

function validateAbnAcn(event) {
	const abn = event.target.value;
	if (abn.length !== 11 || isNaN(parseInt(abn))) return false;

	const [weighted, setWeighted] = useState(0);
	const deferredAbn = useDeferredValue(abn);

	useEffect(() => {
		let weighting = [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19];
		let firstDigitProcessed = parseInt(deferredAbn.charAt(0)) - 1;
		let weightedSum = firstDigitProcessed * weighting[0];

		for (let i = 1; i < deferredAbn.length; i++) {
			weightedSum += parseInt(deferredAbn.charAt(i)) * weighting[i];
		}

		setWeighted(weightedSum);
	}, [deferredAbn]);

	return weighted % 89 === 0;
}

export default validateAbnAcn;
