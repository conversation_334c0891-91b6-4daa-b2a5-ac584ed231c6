import { useState } from 'react';
import OtpInput from 'react-otp-input';
import { useFormik } from 'formik';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import * as yup from 'yup';

import * as validation from '../../../Helper/YupValidation';
import CustomModal from '../../Common/Modal/CustomModal';
import ModalWrapper from './verifyOtp.style';
import passwordImage from '../../../Assets/images/password-icon.png';
import Api from '../../../Helper/Api';
import { VenueApiRoutes } from '../../../Utils/routes';
import CustomButton from '../../Common/CustomButton';
import authActions from '../../../Redux/auth/actions';
import { useDispatch } from 'react-redux';
import NewCustomModal from '../../Common/Modal/NewCustomModal';
import NewVerifyModalWrapper from '../../Authentication/newVerifyOtp.style';
import NewFormInput from '../../Form/NewFormInput';

const NewVerifyOtpModal = ({
	isOpen,
	handleModal,
	navigateTo,
	modalData,
	refetch,
	routeState
}) => {
	const [otp, setOtp] = useState('');
	const [loading, setLoading] = useState(false);
	const allThemeData = useSelector((state) => ({ ...state.themeChanger }));
	const authData = useSelector((state) => ({ ...state.auth }));
	const dispatch = useDispatch();

	const handleOTPChange = (otp) => {
		setOtp(otp);
		verifyOtpFormik.setFieldValue('otp', otp);
	};

	const validationSchema = yup.object().shape({
		otp: validation.YUP_VALIDATION.OTP
	});

	const handelOnsubmit = async () => {
		const payload = {
			email: modalData?.email,
			otp: verifyOtpFormik?.values?.otp
		};
		try {
			setLoading(true);
			const res = await Api(
				'POST',
				VenueApiRoutes.connectVenueOtpVerification,
				payload
			);
			setLoading(false);
			if (res?.data?.status) {
				verifyOtpFormik.resetForm();
				handleModal();
				toast.success(res?.data?.message);
				dispatch(authActions.venue_login(routeState?.authData));
				dispatch(authActions.venue_change_login(true));
				navigateTo();
				if (refetch) refetch();
			} else {
				toast.error(res?.data?.message);
			}
		} catch (err) {
			setLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	const verifyOtpFormik = useFormik({
		initialValues: {
			otp: ''
		},
		validationSchema,
		validateOnChange: false,
		validateOnBlur: false,
		onSubmit: handelOnsubmit
	});
	const handleSetupAuthenticator = async () => {
		try {
			const res = await Api('POST', VenueApiRoutes.connectVenue, {
				email: modalData?.email,
				password: modalData?.password
			});
			if (res?.data?.status) {
				toast.success(res?.data?.message);
			} else {
				toast.error(res?.data?.message);
			}
		} catch (err) {
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	return (
		<NewCustomModal
			isOpen={isOpen}
			size="md"
			handleModal={handleModal}
			modalClassName="verifyOtp"
		>
			<NewVerifyModalWrapper>
				<p className="verifyOtpHeading">
					Your security is our priority
				</p>
				<p className="verifyOtpSubText">
					Please enter the 6 digit verification code on your{' '}
					<span className="verifyOtpSubText-auth">
						venue account’s email
					</span>
				</p>
				<form onSubmit={verifyOtpFormik.handleSubmit}>
					<NewFormInput
						name="otp"
						placeholder="Verification code"
						type="text"
						maxlength="6"
						onBlur={verifyOtpFormik.handleBlur}
						value={verifyOtpFormik?.values?.otp}
						onChange={verifyOtpFormik?.handleChange}
						error={verifyOtpFormik?.errors?.otp}
						errorMsg={
							verifyOtpFormik?.touched?.otp &&
							verifyOtpFormik?.errors?.otp
						}
					/>

					<div className="pa-t-20">
						<CustomButton
							type="submit"
							className="newThemeButtonFullWidth"
							loading={loading}
						>
							Verify
						</CustomButton>
					</div>
					<p className="helperText mt-2 mb-4">
						Didn’t receive an email? Check your junk/spam or click{' '}
						<span
							className="spanLink"
							onClick={handleSetupAuthenticator}
						>
							here
						</span>{' '}
						to re-send.
					</p>
				</form>
			</NewVerifyModalWrapper>
		</NewCustomModal>
	);
};

export default NewVerifyOtpModal;
