import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { Button } from 'reactstrap';

import Api from '../../../Helper/Api';
import { ComputerIcon } from '../../Icons';
import { somethingWentWrongError } from '../../../Helper/somethingWentWrongError';

const DeviceList = () => {
	const profileData = useSelector((state) => ({ ...state.auth }));
	const [deviceList, setDeviceList] = useState('');
	const [updateDeviceList, setUpdateDeviceList] = useState(true);

	const handleDeviceLogout = async (id) => {
		// Logout device
		let api_type = profileData.login_type === 'venue' ? '/venue' : '/cms';
		let payload = { id: id };
		try {
			const res = await Api(
				'POST',
				api_type + '/security/logout-device',
				payload
			);
			if (res?.data?.status) {
				toast.success(res?.data?.message);
				setUpdateDeviceList(true);
			} else {
				toast.error(res?.data?.message);
			}
		} catch (err) {
			somethingWentWrongError();
		}
	};

	useEffect(() => {
		let api_type = profileData.login_type === 'venue' ? '/venue' : '/cms';
		(async () => {
			try {
				const res = await Api(
					'GET',
					api_type + '/security/device-list'
				);
				if (res?.data?.status) {
					setDeviceList(res?.data?.data);
				} else {
					setDeviceList({});
				}
			} catch (err) {
				console.log(err.response.data.message);
			}
			setUpdateDeviceList(false);
		})();
	}, [updateDeviceList]);

	return (
		<div className="d-flex flex-column flex-md-row w-100 gap-3">
			<div className="fs-20 medium-text flex-3">
				Devices
				<p className="fs-16 regular-text">
					You are currently logged in to your MyTab CMS account on
					these devices. If you don’t recognize a device, log out to
					keep your account secure.
				</p>
			</div>
			<div className="boxCard w-100 p-4 flex-7 d-flex flex-row justify-content-between pb-0">
				<div className="w-100">
					<p className="fs-20 semi-bold-text">Logged in</p>
					<hr />
					{deviceList?.currentDevice?.length > 0 &&
						deviceList?.currentDevice?.map((device) => (
							<>
								<div className="d-flex justify-content-between flex-column flex-md-row gap-2 gap-md-1">
									<div>
										<div className="d-flex align-items-center">
											<ComputerIcon
												height={48}
												width={48}
											/>
											<p className="fs-16 semi-bold-text pl-10 text-dark">
												{device?.device_name ?? ''}
												<span className="tagThisDevice fs-16 medium-text">
													This device
												</span>
											</p>
										</div>
										<div className="fs-16 regular-text text-dark">
											{device?.createdAt ?? ''}
										</div>
										<div className="fs-16 regular-text text-dark">
											{device?.device_location ?? ''}
										</div>
									</div>
									<div>
										<Button className="borderButton fs-18 semi-bold-text">
											Logout
										</Button>
									</div>
								</div>
								<hr />
							</>
						))}
					{deviceList?.otherDevice?.length > 0 &&
						deviceList?.otherDevice?.map((device, index) => (
							<div key={index}>
								<div className="d-flex justify-content-between flex-column flex-md-row gap-2 gap-md-1">
									<div>
										<div className="d-flex align-items-center">
											<ComputerIcon
												height={48}
												width={48}
											/>
											<p className="fs-16 semi-bold-text pl-10 text-dark">
												{device?.device_name ?? ''}
											</p>
										</div>
										<div className="fs-16 regular-text text-dark">
											{device?.createdAt ?? ''}
										</div>
										<div className="fs-16 regular-text text-dark">
											{device?.device_location ?? ''}
										</div>
									</div>
									<div>
										<Button
											className="borderButton fs-18 semi-bold-text"
											onClick={() =>
												handleDeviceLogout(device?.id)
											}
										>
											Logout
										</Button>
									</div>
								</div>
								<hr />
							</div>
						))}
				</div>
			</div>
		</div>
	);
};

export default DeviceList;
