import { Button } from 'reactstrap';
import CustomModal from '../../Common/Modal/CustomModal';

const DeleteConfirmModal = ({ handleModal, isOpen, handleDelete }) => {
	return (
		<CustomModal
			isOpen={isOpen}
			handleModal={handleModal}
			title="Delete Account"
			size="md"
		>
			<div className="w-100">
				<p className="text-center fs-12 medium-text pa-b-18">
					Are you sure you want to delete your account?
				</p>
				<div className="d-flex" style={{ gap: '12px' }}>
					<div className="flex-1">
						<Button
							className="borderButtonFullWidth"
							onClick={handleModal}
						>
							No
						</Button>
					</div>
					<div className="flex-1">
						<Button
							type="button"
							className="themeButtonFullWidth"
							onClick={handleModal}
						>
							Yes
						</Button>
					</div>
				</div>
			</div>
		</CustomModal>
	);
};

export default DeleteConfirmModal;
