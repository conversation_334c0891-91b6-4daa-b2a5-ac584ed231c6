import { useState, useCallback, useEffect } from 'react';
import {
	DndContext,
	closestCenter,
	MouseSensor,
	TouchSensor,
	DragOverlay,
	useSensor,
	useSensors,
	PointerSensor,
	KeyboardSensor
} from '@dnd-kit/core';
import { restrictToFirstScrollableAncestor } from '@dnd-kit/modifiers';
import {
	arrayMove,
	SortableContext,
	rectSortingStrategy,
	verticalListSortingStrategy,
	sortableKeyboardCoordinates
} from '@dnd-kit/sortable';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';

import CustomModal from '../Common/Modal/CustomModal';
import SubHeadingDraggableCard from './SubHeadingDraggableCard';
import Api from '../../Helper/Api';
import { VenueApiRoutes } from '../../Utils/routes';
import CustomButton from '../Common/CustomButton';

const RearrangeMenuModal = ({
	isOpen,
	handleModal,
	tabMenuData,
	setMenuItems,
	setTabId,
	activeTabMenu,
	isPopularExist,
	refetch
}) => {
	const sensors = useSensors(
		useSensor(PointerSensor),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates
		})
	);
	const authData = useSelector((state) => ({ ...state.auth }));
	const [updateLoading, setUpdateLoading] = useState(false);
	const [menuData, setMenuData] = useState([]);
	const [items, setItems] = useState([]);
	const [activeId, setActiveId] = useState(null);
	const handleDragStart = useCallback((event) => {
		setActiveId(event?.active?.id);
	}, []);
	const handleDragEnd = useCallback(
		(event) => {
			const { active, over } = event;
			if (active?.id !== over?.id) {
				const oldIndex = items?.findIndex(
					(item) => item?.id == active?.id
				);
				const newIndex = items?.findIndex(
					(item) => item?.id == over?.id
				);
				const updatedItems = arrayMove(items, oldIndex, newIndex);
				setItems([...updatedItems]);
			}
			setActiveId(null);
		},
		[items]
	);
	const handleDragCancel = useCallback(() => {
		setActiveId(null);
	}, []);
	const handleUpdate = async () => {
		let payload = {};
		let subCategoryIDs = [];
		if (isPopularExist) {
			if (items && items?.length !== 0) {
				subCategoryIDs = [
					menuData[0].categoryID,
					...items?.map((item) => item?.data?.categoryID)
				];
				[
					menuData[0].categoryName,
					...items?.map((item) => item?.data?.categoryName)
				].forEach((item, index) => {
					if (activeTabMenu === item) {
						setTabId(index);
					}
				});
			}
		} else {
			if (items && items?.length !== 0) {
				subCategoryIDs = [
					...items?.map((item) => item?.data?.categoryID)
				];
				items.forEach((item, index) => {
					if (activeTabMenu === item?.data?.categoryName) {
						setTabId(index);
					}
				});
			}
		}
		payload = {
			bar_id: authData?.selectedVenue?.id,
			sub_category_ids: subCategoryIDs
		};
		setUpdateLoading(true);
		try {
			const res = await Api(
				'POST',
				VenueApiRoutes.updateSubcategorySequence,
				payload
			);
			if (res?.data?.status) {
				if (refetch) refetch();
				handleModal();
			} else {
				toast.error(res?.data?.message);
			}
			setUpdateLoading(false);
		} catch (err) {
			setUpdateLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	const modalFooter = (
		<div className="pa-t-12 w-100">
			<CustomButton
				type="button"
				className="fs-18 medium-text themeButtonFullWidth ptb-10 plr-60"
				onClick={handleUpdate}
				loading={updateLoading}
			>
				Update
			</CustomButton>
		</div>
	);
	useEffect(() => {
		setMenuData(tabMenuData);
		if (isPopularExist) {
			setItems(
				tabMenuData?.slice(1)?.map((item, index) => {
					return { id: index + 1, data: item };
				})
			);
		} else {
			setItems(
				tabMenuData?.map((item, index) => {
					return { id: index + 1, data: item };
				})
			);
		}
	}, [tabMenuData, isPopularExist]);
	const onModalClosed = () => {
		if (isPopularExist) {
			setItems(
				tabMenuData?.slice(1)?.map((item, index) => {
					return { id: index + 1, data: item };
				})
			);
		} else {
			setItems(
				tabMenuData?.map((item, index) => {
					return { id: index + 1, data: item };
				})
			);
		}
	};
	return (
		<CustomModal
			title={'Re-arrange Menu'}
			size="md"
			isOpen={isOpen}
			handleModal={handleModal}
			modalFooter={modalFooter}
			onClosed={onModalClosed}
			autoHeightMin={0}
		>
			<DndContext
				sensors={sensors}
				collisionDetection={closestCenter}
				onDragStart={handleDragStart}
				onDragEnd={handleDragEnd}
				onDragCancel={handleDragCancel}
			>
				<SortableContext
					items={items}
					strategy={verticalListSortingStrategy}
					handle
				>
					{items?.length > 0 &&
						items?.map((item, index) => (
							<SubHeadingDraggableCard
								key={index}
								id={item?.id}
								text={item?.data?.categoryName}
							/>
						))}
				</SortableContext>
			</DndContext>
		</CustomModal>
	);
};

export default RearrangeMenuModal;
