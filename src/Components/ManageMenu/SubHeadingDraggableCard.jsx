import { HamburgerIcon } from '../Icons';

const SubHeadingDraggableCard = (props) => {
	return (
		<div
			className="d-flex draggableMenuCard justify-content-between p-2 gap-2 mt-3 bg-white"
			{...props}
		>
			<div className="d-flex align-items-center">{props.text}</div>
			<div className="dragHandler d-flex align-items-center">
				<HamburgerIcon height={32} width={32} />
			</div>
		</div>
	);
};

export default SubHeadingDraggableCard;
