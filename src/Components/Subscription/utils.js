export const MODAL_TYPE = {
	SALES_PERFORMANCE_ANALYTICS: 'SalesPerformanceAnalytics',
	BEHAVIOURAL_ANALYTICS: 'BehaviouralAnalytics'
};

export const salesPerformanceAnalyticsData = [
	{
		title: 'Sales Analytics',
		data: [
			'Comparative sales data over time',
			'Revenue & Orders by service type',
			'Revenue & Orders by location',
			'Top Menu Subheadings by units sold',
			'Top Food Items by units sold',
			'Top Drink Items by units sold',
			'Total refunded orders',
			'Promo codes performance',
			'Taxes performance'
		]
	},
	{
		title: 'Performance Analytics',
		data: [
			'Comparative performance data to Venues within 15km of your location',
			'Wait time performance averages by service type',
			'Wait time performance averages by location',
			'Comparative customer wait time accuracy'
		]
	}
];

export const behaviouralAnalyticsData = [
	{
		title: '',
		data: [
			'Customer average spend by age demographic',
			'Popular menu items by age demographic',
			'Abandoned carts',
			'Average menu browsing duration',
			'Total customer email issues',
			'Non-purchase views'
		]
	},
	{
		title: '',
		data: [
			'Customer orders by postcode',
			'Venue exposure ratio',
			'Customer same day re-ordering',
			'Customer device',
			'Taxes performance',
			'Most removed menu items from cart'
		]
	}
];
