import { Button } from 'reactstrap';
import CustomModal from '../../Common/Modal/CustomModal';
import CustomButton from '../../Common/CustomButton';

const NotPickedUpModal = ({
	handleModal,
	isOpen,
	handleNotPickedUpOrder,
	notPickedUpModalData,
	notPickedUpOrderLoading
}) => {
	return (
		<CustomModal
			isOpen={isOpen}
			handleModal={handleModal}
			title="Order not collected?"
			size="md"
		>
			<div className="w-100">
				<p className="text-left fs-12 medium-text pa-b-18">
					This order will be removed from dashboard and the customer
					will not be refunded.
				</p>
				<div className="d-flex ma-b-2" style={{ gap: '12px' }}>
					<div className="flex-1">
						<Button
							className="borderButtonFullWidth"
							onClick={handleModal}
						>
							No
						</Button>
					</div>
					<div className="flex-1">
						<CustomButton
							type="button"
							className="themeButtonFullWidth"
							onClick={() =>
								handleNotPickedUpOrder(notPickedUpModalData)
							}
							loading={notPickedUpOrderLoading}
						>
							Yes
						</CustomButton>
					</div>
				</div>
			</div>
		</CustomModal>
	);
};

export default NotPickedUpModal;
