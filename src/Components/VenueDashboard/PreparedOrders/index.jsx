import PrepareCard from '../PrepareCard';

const PreparedOrders = ({ data, getVenueOrderList, viewOnly }) => {
	return (
		<div className="d-flex flex-column gap-4">
			{data?.length > 0 &&
				data?.map((item, index) => (
					<PrepareCard
						key={index}
						data={item}
						getVenueOrderList={getVenueOrderList}
						viewOnly={viewOnly}
						orders={data}
						index={index}
					/>
				))}
		</div>
	);
};

export default PreparedOrders;
