import styled from 'styled-components';

const ModalWrapper = styled.div`
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 0px 20px 20px 20px;
	@media only screen and (max-width: 768px) {
		padding: 0px;
		margin-bottom: 25px;
	}
	@media only screen and (max-width: 575px) {
		margin-bottom: 10px;
		padding: 5px;
	}
	.modalTitle {
		padding-top: 40px;
		padding-bottom: 16px;
		@media only screen and (max-width: 768px) {
			padding-top: 20px;
		}
	}
	.modalSubTitle {
		padding-bottom: 20px;
	}
	.otpForm {
		width: 100%;
		input {
			width: 5.2rem !important;
			height: 3rem !important;
			margin: 0 1rem;
			font-size: 2rem;
			border-radius: 0px !important;
			border: 0px;
			border-bottom: 1px solid #a2a2a2 !important;
			margin-bottom: 32px;
			outline: none;
			color: ${(props) => props.layoutTheme.textColor};
			@media only screen and (max-width: 1200px) {
				width: 3rem !important;
				height: 3rem !important;
				margin: 0 0.5rem;
				font-size: 2rem;
				margin-bottom: 20px;
			}
			@media only screen and (max-width: 575px) {
				width: 2.5rem !important;
				height: 2.5rem !important;
				margin: 0 0.3rem;
				font-size: 1.5rem;
				margin-bottom: 10px;
			}
			@media only screen and (max-width: 320px) {
				width: 2rem !important;
				height: 2rem !important;
				margin: 0 0.1rem;
				font-size: 1rem;
				margin-bottom: 10px;
			}
		}
	}
`;

export default ModalWrapper;
