import styled from 'styled-components';

const InputCheckBoxWrraper = styled.div`
	label {
		cursor: pointer; /* Make the label clickable */
	}

	.checkmark {
		/* Style the custom checkbox element */
		content: '';
		display: block;
		width: 16px;
		height: 16px;
		border: 2px solid red; /* Set red border here */
		border-radius: 4px; /* Optional rounding */
		margin-right: 5px;
	}

	input[type='checkbox']:checked + .checkmark {
		/* Style the custom checkbox when checked */
		background-color: #f00; /* Red background */
	}
`;

export default InputCheckBoxWrraper;
