import { FormGroup, Label } from 'reactstrap';
import Select from 'react-select';
import { NewFormSelectWrapper } from './formSelect.style';

const NewFormSelect = ({
	id,
	name,
	label,
	value,
	onChange,
	error,
	errorMsg,
	className,
	options,
	placeholder,
	maxMenuHeight,
	isLoading = false,
	isSearchable = false,
	disabled = false,
	...rest
}) => {
	return (
		<FormGroup className="mb-0">
			{label && (
				<Label
					for={id}
					className="fs-12 medium-text themeText label-color ma-b-6"
				>
					{label}
				</Label>
			)}
			<NewFormSelectWrapper>
				<Select
					name={name}
					value={value}
					options={options}
					onChange={onChange}
					placeholder={placeholder ?? 'Please Select'}
					isLoading={isLoading}
					className={`p-0 custom-dd ${error ? 'is-invalid' : ''} ${
						className ? className : ''
					}`}
					components={{
						IndicatorSeparator: () => null
					}}
					maxMenuHeight={maxMenuHeight ?? 150}
					styles={{
						control: (baseStyles, state) => ({
							...baseStyles,
							border: 'none !important',
							boxShadow: '0px 0px 0px  !important',
							fontFamily: `poppins-regular !important`,
							fontSize: `24px !important`,
							// backgroundColor: '#F9F9F9 !important',
							color: '#313132eb !important'
						}),
						valueContainer: (baseStyles, state) => ({
							...baseStyles,
							fontWeight: '500',
							fontFamily: `poppins-regular !important`,
							fontSize: `24px !important`
						}),
						option: (baseStyles, state) => {
							return {
								...baseStyles,
								'&:hover': {
									background: state.isSelected
										? '#FF5F5F !important'
										: 'rgba(0,0,0,0.1) !important',
									color: state.isSelected
										? '#FFFFFF !important'
										: '#000000 !important'
								},
								background: state.isSelected
									? '#FF5F5F !important'
									: '#FFFFFF !important',
								color: state.isSelected
									? '#FFFFFF !important'
									: '#242424 !important',
								fontFamily: 'montserrat-medium',
								fontWeight: '500',
								fontSize: '12px',
								margin: '0px'
							};
						}
					}}
					menuShouldScrollIntoView={false}
					isSearchable={isSearchable}
					isDisabled={disabled}
				/>
			</NewFormSelectWrapper>

			{error && <p className="error">{errorMsg}</p>}
		</FormGroup>
	);
};

export default NewFormSelect;
