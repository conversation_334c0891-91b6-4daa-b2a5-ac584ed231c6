import styled from 'styled-components';

const ImageUploadWrapper = styled.div`
	.imageContainer {
		width: 100%;
		height: 235px;
		border-radius: 6px;
		border: 2px dashed rgba(49, 49, 50, 0.35);
		padding: 1em;
	}

	.innerImageContainer {
		width: 100%;
		height: 100%;
		position: relative;
		display: flex;
		flex-direction: column;
		gap: 6px;
		align-items: center;
		justify-content: center;
	}

	.custom-modal {
		max-width: 700px !important;
		border-radius: 10px;
		background-color: #f0f0f0;
		border: none;
	}

	.modal-body {
		padding: 20px;
		background-color: #ffffff;
		border-radius: 10px;
	}

	.imageSelectionLabel {
		position: absolute;
		z-index: 1;
		text-align: center;
		width: 100%;
		height: 100%;
		display: flex;
	}

	.image {
		width: 100%;
		height: 100%;
		object-fit: fill;
		border: 0px !important;
		outline: 0px !important;
		position: absolute;
	}

	.image:hover {
		cursor: pointer;
	}

	img {
		border: 0px !important;
		outline: 0px !important;
	}

	.suggestionParagraph {
		font-size: 14px;
		font-weight: 600;
		max-width: 20rem;
		color: #31313259;
	}

	.sugesstion0 {
		font-size: 20px;
		font-weight: 600;
		color: #313132eb;
	}

	.sugesstion1 {
		margin-top: 8px;
		font-size: 14px;
		font-weight: 600;
		color: #31313259;
	}

	.imageTextOne {
		font-size: 10px;
		font-family: nunitosans-regular;
		color: #202224;
		cursor: pointer;
	}

	.imageTextTwo {
		font-size: 10px;
		font-family: nunitosans-regular;
		color: #979797;
	}

	.textOfUploadImage {
		width: 100%;
		justify-content: center;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.cameraUploadImage {
		display: flex;
		justify-content: flex-end;
		align-items: end;
	}

	.helperLink {
		user-select: none;
		padding-top: 10px;
		text-align: center;
		margin-bottom: 0px;
		font-size: 20px;
		font-weight: 600;
		color: #313132eb;
		font-family: 'poppins-regular';
	}

	.helperLinkSpan {
		color: #f95c69;
		cursor: pointer;
	}

	.icon {
		position: absolute;
		width: 27px;
		height: 26px;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 100%;
		bottom: -10px;
		right: -10px;
		cursor: ${(props) => (props.disabled ? '' : 'pointer')};
	}
`;

export default ImageUploadWrapper;
