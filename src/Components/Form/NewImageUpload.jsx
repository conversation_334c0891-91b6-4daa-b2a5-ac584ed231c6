import React, { useEffect, useRef, useState } from 'react';
import ImageUploadWrapper from './newImageUpload.style';
import { UploadImage } from '../Icons/UploadImage/UploadImage';
import { Modal, ModalBody } from 'reactstrap';
import Cropper from 'react-cropper';
import 'cropperjs/dist/cropper.css';
import { CancelIcon } from '../Icons';
import { CameraForUploadImage } from '../Icons/Camera/CameraForUploadImage';

const NewImageUpload = ({
    name,
    className,
    containerClass,
    value,
    error,
    errorMsg,
    setFieldValue,
    icon,
    handleReset,
    defaultImage,
    disabled,
    venueProfile = false,
    ...props
}) => {
    const fileInput = useRef();
    const [file, setFile] = useState();
    const [image, setImage] = useState(null);
    const [cropData, setCropData] = useState(null); // Cropped image
    const [showCropper, setShowCropper] = useState(false);
    const cropperRef = useRef(null);

    const handleChange = (e) => {
        const selectedFile = e.target.files[0];

        if (!selectedFile) {
            return;
        }

        const reader = new FileReader();
        reader.onload = () => {
            setImage(reader.result);
            setShowCropper(true);
        };
        reader.readAsDataURL(selectedFile);
    };

    const handleUpload = () => {
        fileInput.current.click();
    };

    const handleCrop = () => {
        if (cropperRef.current?.cropper) {
            const croppedCanvas = cropperRef.current.cropper.getCroppedCanvas();
            croppedCanvas.toBlob((blob) => {
                const imageFile = new File(
                    [blob],
                    `converted_image${Math.floor(Math.random() * 1000)}.png`,
                    { type: 'image/png' }
                );
                const imageUrl = URL.createObjectURL(imageFile);
                setCropData(imageUrl);
                setShowCropper(false);
                setFieldValue(name, imageFile);
            }, 'image/png');
        }
    };

    const handleCancel = () => {
        setShowCropper(false);
        setImage(null);
        handleReset();
    };

    useEffect(() => {
        if (value === null) {
            setFile(undefined);
        }

        if (value) {
            if (value instanceof File) {
                setFile(URL.createObjectURL(value));
            } else {
                setFile(value);
            }
        }
    }, [value]);

    return (
        <ImageUploadWrapper className={`form-group mb-0 ${className ? className : ''}`} disabled={disabled}>
            <input
                type="file"
                name={name}
                ref={fileInput}
                onChange={handleChange}
                accept="image/jpg,image/png,image/jpeg"
                disabled={disabled}
                hidden
            />
            <div className={`imageContainer ${containerClass || ''}`}>
                <div className="innerImageContainer">
                    {!cropData && !file && (
                        venueProfile ?
                            <div className="imageSelectionLabel">
                                <div className='textOfUploadImage'>
                                <p className="imageTextOne" onClick={handleUpload}>Click to select an image</p>
                                <p className="imageTextTwo">PNG, JPG up to 2MB</p>
                                </div>
                                <div className='cameraUploadImage'>
                                    <div></div>
                                    <CameraForUploadImage style={{cursor: 'pointer'}} onClick={handleUpload} height={40} weight={40} />
                                </div>
                            </div>
                            :
                            <div className="imageSelectionLabel">
                                <UploadImage height={40} width={40} />
                                <h5 className="helperLink">
                                    Click{' '}
                                    <span className="helperLinkSpan" onClick={handleUpload}>
                                        here
                                    </span>{' '}
                                    to upload your image.
                                </h5>
                                <p className="sugesstion1">Supports: PNG, JPG up to 5MB</p>
                                <p className="suggestionParagraph pt-3">600 Width x 400 Height</p>
                            </div>
                    )}
                    {cropData && (
                        <img src={cropData} className="image" onClick={handleUpload} alt="Cropped" />
                    )}
                    {file && !cropData && (
                        <img src={file ?? defaultImage} onClick={handleUpload} className="image" alt="Uploaded" />
                    )}
                    {cropData && (
                        <span
                            onClick={(e) => {
                                e.stopPropagation();
                                handleReset();
                                setCropData(null);
                            }}
                            className="icon"
                        >
                            {icon}
                        </span>
                    )}
                </div>
            </div>
            {error && (
                <p className="fs-10 semi-bold-text headingTextColor pa-t-6">{errorMsg}</p>
            )}

            {/* Modal for cropping */}
            <div className='d-flex align-items-center justify-content-center'>
                <Modal
                    isOpen={showCropper}
                    toggle={() => { }} // Disable closing on outside click
                    backdrop="static" // Prevent closing when clicking outside
                    size="lg"
                    centered
                    className="custom-modal"
                    style={{ width: '634px', height: 'auto' }}
                >
                    <ModalBody className="modal-body">
                        <div className="d-flex justify-content-between align-items-center">
                            <div></div>
                            <div className="cancelIconWrapper" onClick={handleCancel}>
                                <CancelIcon className="cursor-pointer" height={24} width={24} roundBorder />
                            </div>
                        </div>
                        <Cropper
                            ref={cropperRef}
                            src={image}
                            style={{ height: '100%', width: 600 }}
                            aspectRatio={16 / 9} // Set aspect ratio as per requirements
                            guides={true}
                            viewMode={3}
                            minCropBoxHeight={10}
                            minCropBoxWidth={10}
                            background={false}
                            responsive={true}
                            autoCropArea={1}
                            checkOrientation={false}
                        />
                    </ModalBody>
                    <div className='p-3'>
                        <button onClick={handleCrop} className="btn btn-primary crop-btn" style={{
                            background: 'linear-gradient(90deg, #ff6895aa, #ff7885aa, #ff9568ac, #ff9964ac)',
                            width: '100%',
                            height: '65px',
                            fontFamily: "'montserrat-bold', sans-serif",
                            fontSize: '16px',
                            border: 'none',
                            borderRadius: '0',
                        }}>
                            Crop Image
                        </button>
                    </div>
                </Modal>
            </div>
        </ImageUploadWrapper>
    );
};

export default NewImageUpload;
