import { useEffect, useRef, useState } from 'react';
import Select, { components } from 'react-select';
import { Input } from 'reactstrap';
import { CheckboxStyleWrapper, StyleWrapper } from './formMultiSelect2.style';

const FormMultiSelect2 = ({
	id,
	name,
	label,
	value,
	onChange,
	error,
	errorMsg,
	className,
	options,
	placeholder,
	isLoading = false,
	isSearchable = false,
	disabled = false,
	maxMenuHeight,
	isShowSelectedOptions = true,
	...rest
}) => {
	const [menuIsOpen, setMenuIsOpen] = useState(false);
	const [selectedOptions, setSelectedOptions] = useState([]);
	const formMultiSelect2Ref = useRef(null);

	const handleMenuOpen = () => setMenuIsOpen(true);
	const handleMenuClose = () => setMenuIsOpen(false);
	const handleChange = (value) => {
		if (onChange) {
			onChange(value);
		} else {
			setSelectedOptions(value);
		}
	};
	const CheckboxOption = (props) => {
		return (
			<components.Option {...props}>
				<CheckboxStyleWrapper>
					<Input
						className="newFormCheckbox"
						type="checkbox"
						checked={props?.isSelected}
						readOnly={true}
					/>
				</CheckboxStyleWrapper>
				{props.label}
			</components.Option>
		);
	};
	const CustomValueContainer = ({ children, ...props }) => {
		const getPlaceholder = () => {
			let selectedOptionLength = selectedOptions?.length;
			if (!isSearchable) {
				return (
					<div
						className={`customPlaceholder ${
							selectedOptionLength > 0 ? 'active' : ''
						}`}
					>
						{placeholder}
					</div>
				);
			}
			if (isSearchable && !props?.selectProps?.menuIsOpen) {
				return (
					<div
						className={`customPlaceholder ${
							selectedOptionLength > 0 ? 'active' : ''
						}`}
					>
						{placeholder}
					</div>
				);
			}
		};
		return (
			<components.ValueContainer {...props}>
				{getPlaceholder()}
				{children}
			</components.ValueContainer>
		);
	};
	useEffect(() => {
		const handleClickOutside = (event) => {
			if (!formMultiSelect2Ref?.current?.contains(event?.target)) {
				setMenuIsOpen(false);
			}
		};
		document.addEventListener('click', handleClickOutside, true);
		return () => {
			document.removeEventListener('click', handleClickOutside, true);
		};
	}, [setMenuIsOpen]);
	useEffect(() => {
		if (value?.length > 0) {
			setSelectedOptions(value);
		} else {
			setSelectedOptions([]);
		}
	}, [value]);
	return (
		<StyleWrapper>
			{label && (
				<div className="newLabelContainer">
					<span className="newLabel">{label}</span>
				</div>
			)}
			<div style={{ paddingLeft: '1px' }} ref={formMultiSelect2Ref}>
				<Select
					isMulti
					name={name}
					options={options}
					value={selectedOptions}
					onChange={handleChange}
					closeMenuOnSelect={false}
					hideSelectedOptions={false}
					isLoading={isLoading}
					isDisabled={disabled}
					isSearchable={isSearchable}
					isClearable={false}
					backspaceRemovesValue={false}
					components={{
						IndicatorSeparator: () => null,
						Option: CheckboxOption,
						ValueContainer: CustomValueContainer,
						MultiValue: () => null,
						Placeholder: () => null
					}}
					maxMenuHeight={maxMenuHeight}
					classNames={{
						container: () => 'customContainer',
						control: () => 'customControl',
						valueContainer: () => 'customValue',
						input: () => 'customInput',
						indicatorsContainer: () => 'customIndicatorsContainer',
						dropdownIndicator: () => 'customDropdownIndicator',
						menu: () => 'customMenu',
						menuList: () => 'customMenuList',
						option: () => 'customOption',
						noOptionsMessage: () => 'customNoOptionsMessage'
					}}
					theme={(theme) => ({
						...theme,
						borderRadius: '6px',
						colors: {
							...theme.colors,
							primary25: 'rgba(255,255,255,1)',
							primary: 'rgba(255,255,255,1)',
							primary50: 'rgba(255,255,255,1)'
						}
					})}
					menuIsOpen={menuIsOpen}
					onMenuOpen={handleMenuOpen}
					onMenuClose={handleMenuClose}
				/>
			</div>
			{isShowSelectedOptions && selectedOptions?.length > 0 && (
				<div className="selectedOptionsContainer">
					{selectedOptions?.map((item, i) => {
						return (
							<div key={i} className="selectedOptionsItem">
								{item?.label}
							</div>
						);
					})}
				</div>
			)}
			{error && <div className="errorMessage">{errorMsg}</div>}
		</StyleWrapper>
	);
};

export default FormMultiSelect2;
