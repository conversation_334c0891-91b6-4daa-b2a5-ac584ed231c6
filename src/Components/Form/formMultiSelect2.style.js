import styled from 'styled-components';

export const StyleWrapper = styled.div`
	.newLabelContainer {
		display: flex;
		align-items: center;
		gap: 6px;
		padding-bottom: 6px;
		.newLabel {
			color: rgba(32, 34, 36, 1) !important;
			font-family: 'nunitosans-bold' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
	}
	.customContainer {
		height: 40px !important;
	}
	.customControl {
		border: none !important;
		border-radius: 6px !important;
		box-shadow: none !important;
		height: 100% !important;
		padding-inline: 12px !important;
		min-height: 0 !important;
		cursor: pointer !important;
		background-color: #ffffff !important;
	}
	.customValue {
		height: 100% !important;
		padding: 0 !important;
		display: flex !important;
	}
	.customPlaceholder {
		height: 100% !important;
		color: #979797 !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 16px !important;
		line-height: 1 !important;
		overflow: hidden !important;
		text-overflow: ellipsis !important;
		white-space: nowrap !important;
		margin: 0 !important;
		display: flex !important;
		align-items: center !important;
	}
	.customPlaceholder.active {
		color: rgba(32, 34, 36, 1) !important;
	}
	.customInput {
		height: 100% !important;
		margin: 0 !important;
		padding: 0 !important;
		input {
			color: rgba(32, 34, 36, 1) !important;
			font-family: 'nunitosans-regular' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
	}
	.customIndicatorsContainer {
		height: 100% !important;
		display: flex !important;
		justify-content: center !important;
		align-items: center !important;
	}
	.customDropdownIndicator {
		padding: 0 !important;
		width: 22px !important;
		height: 22px !important;
	}
	.customMenu {
		margin-top: 3px !important;
		margin-bottom: 3px !important;
		border: 1px solid rgba(0, 0, 0, 0.17) !important;
		box-shadow: none !important;
	}
	.customMenuList {
		&::-webkit-scrollbar {
			width: 4px !important;
		}
		&::-webkit-scrollbar-thumb {
			border-radius: 4px !important;
			background: rgba(0, 0, 0, 0.2) !important;
		}
	}
	.customMenuList > div:last-child {
		border-bottom: none !important;
	}
	.customOption {
		display: flex;
		align-items: center;
		gap: 14px;
		padding-inline: 12px;
		padding-block: 8px;
		color: rgba(32, 34, 36, 1) !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 16px !important;
		line-height: 1 !important;
		border-bottom: 1px solid rgba(0, 0, 0, 0.17) !important;
		cursor: pointer;
	}
	.customNoOptionsMessage {
		padding: 18px;
		display: flex;
		justify-content: center;
		align-items: center;
		color: rgba(32, 34, 36, 1) !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 12px !important;
	}
	.selectedOptionsContainer {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 8px;
		padding-top: 11px;
		.selectedOptionsItem {
			background: rgba(130, 128, 255, 0.2);
			border-radius: 5px;
			display: flex;
			justify-content: center;
			align-items: center;
			min-width: 92px;
			max-width: 225px;
			height: 28px;
			padding-inline: 8px;
			color: #8280ff;
			font-family: 'nunitosans-bold' !important;
			line-height: 1;
			font-size: 12px;
		}
	}
	.errorMessage {
		color: rgb(255, 95, 95) !important;
		font-family: 'nunitosans-semi-bold' !important;
		font-size: 12px !important;
		line-height: 1 !important;
		padding-top: 6px !important;
	}
	@media (max-width: 600px) {
		.newLabelContainer {
			.newLabel {
				font-size: 12px !important;
			}
		}
		.customPlaceholder {
			font-size: 12px !important;
		}
		.customInput {
			input {
				font-size: 12px !important;
			}
		}
		.customOption {
			font-size: 12px !important;
		}
		.customNoOptionsMessage {
			font-size: 10px !important;
		}
		.selectedOptionsContainer {
			.selectedOptionsItem {
				font-size: 10px;
			}
		}
		.errorMessage {
			font-size: 10px !important;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.newLabelContainer {
			.newLabel {
				font-size: 12px !important;
			}
		}
		.customPlaceholder {
			font-size: 12px !important;
		}
		.customInput {
			input {
				font-size: 12px !important;
			}
		}
		.customOption {
			font-size: 12px !important;
		}
		.customNoOptionsMessage {
			font-size: 10px !important;
		}
		.selectedOptionsContainer {
			.selectedOptionsItem {
				font-size: 10px;
			}
		}
		.errorMessage {
			font-size: 10px !important;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.newLabelContainer {
			.newLabel {
				font-size: 12px !important;
			}
		}
		.customPlaceholder {
			font-size: 12px !important;
		}
		.customInput {
			input {
				font-size: 12px !important;
			}
		}
		.customOption {
			font-size: 12px !important;
		}
		.customNoOptionsMessage {
			font-size: 10px !important;
		}
		.selectedOptionsContainer {
			.selectedOptionsItem {
				font-size: 10px;
			}
		}
		.errorMessage {
			font-size: 10px !important;
		}
	}
`;

export const CheckboxStyleWrapper = styled.div`
	width: 21px !important;
	height: 21px !important;
	display: flex;
	justify-content: center;
	align-items: center;
	.newFormCheckbox {
		border-radius: 2px !important;
		border: 2px solid rgba(0, 0, 0, 0.25) !important;
		float: unset !important;
		margin-left: unset !important;
		margin-top: unset !important;
		vertical-align: unset !important;
		width: 100% !important;
		height: 100% !important;
		cursor: pointer !important;
	}
	.newFormCheckbox:focus {
		box-shadow: none !important;
		outline: none !important;
		border-color: #00000040 !important;
	}
	.newFormCheckbox:checked {
		background-color: #f95c69 !important;
		border: 0px !important;
		box-shadow: none !important;
		&:active,
		&:focus {
			border: 0px !important;
		}
	}
	.newFormCheckbox:active,
	.newFormCheckbox:focus {
		box-shadow: none !important;
		outline: none !important;
		border-color: #00000040 !important;
	}
	@media (max-width: 600px) {
		width: 14px !important;
		height: 14px !important;
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		width: 17px !important;
		height: 17px !important;
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		width: 17px !important;
		height: 17px !important;
	}
`;
