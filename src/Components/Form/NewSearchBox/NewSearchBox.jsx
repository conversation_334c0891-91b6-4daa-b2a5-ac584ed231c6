import {
	InputGroup,
	InputGroupText,
	Input,
	FormGroup,
	Label
} from 'reactstrap';

const NewSearchBox = ({
	id,
	error,
	errorMsg,
	className,
	icon,
	iconPlacement,
	label,
	helperText,
	formGroupClassName,
	labelClassName,
	inputGroupTextClassName,
	inputClassName,
	iconBackgroundClass,
	...rest
}) => {
	return (
		<FormGroup
			className={formGroupClassName}
		>
			{label && (
				<Label
					for={id}
					className={labelClassName}
				>
					{label}
				</Label>
			)}
			<InputGroup>
				{iconPlacement === 'start' && (
					<InputGroupText className={inputGroupTextClassName}>
						{icon}
					</InputGroupText>
				)}
				<Input
					{...rest}
					className={inputClassName}
					style={{ zIndex: '0' }}
				/>
				{iconPlacement === 'end' && (
					<InputGroupText
						className={iconBackgroundClass}
					>
						{icon}
					</InputGroupText>
				)}
			</InputGroup>
			{helperText && (
				<p className="fs-10 medium-text helperText pt-1">
					{helperText}
				</p>
			)}
			{error && (
				<p className="fs-10 semi-bold-text headingTextColor pa-t-6">
					{errorMsg}
				</p>
			)}
		</FormGroup>
	);
};

export default NewSearchBox;
