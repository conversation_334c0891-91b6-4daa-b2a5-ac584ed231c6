import styled from 'styled-components';

export const StyleWrapper = styled.div`
	.newLabelContainer {
		display: flex;
		align-items: center;
		gap: 6px;
		padding-bottom: 6px;
		.newLabel {
			color: rgba(32, 34, 36, 1) !important;
			font-family: 'nunitosans-bold' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
	}
	.customContainer {
		height: 40px !important;
	}
	.customControl {
		border: none !important;
		border-radius: 6px !important;
		box-shadow: none !important;
		height: 100% !important;
		padding-inline: 12px !important;
		min-height: 0 !important;
		cursor: pointer !important;
		background-color: #ffffff !important;
	}
	.customValue {
		height: 100% !important;
		padding: 0 !important;
	}
	.customPlaceholder,
	.customSingleValue {
		height: 100% !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 16px !important;
		line-height: 1 !important;
		overflow: hidden !important;
		text-overflow: ellipsis !important;
		white-space: nowrap !important;
		margin: 0 !important;
		display: flex !important;
		align-items: center !important;
	}
	.customPlaceholder {
		color: #979797 !important;
	}
	.customSingleValue {
		color: rgba(32, 34, 36, 1) !important;
	}
	.customInput {
		height: 100% !important;
		margin: 0 !important;
		padding: 0 !important;
		input {
			color: rgba(32, 34, 36, 1) !important;
			font-family: 'nunitosans-regular' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
	}
	.customIndicatorsContainer {
		height: 100% !important;
		display: flex !important;
		justify-content: center !important;
		align-items: center !important;
	}
	.customClearIndicator {
		padding: 0 !important;
		width: 29px !important;
		height: 29px !important;
	}
	.customDropdownIndicator {
		padding: 0 !important;
		width: 22px !important;
		height: 22px !important;
	}
	.customMenu {
		margin-top: 3px !important;
		margin-bottom: 3px !important;
		border: 1px solid rgba(0, 0, 0, 0.17) !important;
		box-shadow: none !important;
	}
	.customMenuList {
		&::-webkit-scrollbar {
			width: 4px !important;
		}
		&::-webkit-scrollbar-thumb {
			border-radius: 4px !important;
			background: rgba(0, 0, 0, 0.2) !important;
		}
	}
	.customOption {
		color: rgba(32, 34, 36, 1) !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 16px !important;
		line-height: 1 !important;
		cursor:pointer !important;
	}
	.customOption.isSelected {
		color: #ffffff !important;
	}
	.customNoOptionsMessage {
		color: rgba(32, 34, 36, 1) !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 12px !important;
	}
	.errorMessage {
		color: rgb(255, 95, 95) !important;
		font-family: 'nunitosans-semi-bold' !important;
		font-size: 12px !important;
		line-height: 1 !important;
		padding-top: 6px !important;
	}
	@media (max-width: 600px) {
		.newLabelContainer {
			.newLabel {
				font-size: 12px !important;
			}
		}
		.customPlaceholder,
		.customSingleValue {
			font-size: 12px !important;
		}
		.customInput {
			input {
				font-size: 12px !important;
			}
		}
		.customClearIndicator {
			width: 24px !important;
			height: 24px !important;
		}
		.customOption {
			font-size: 12px !important;
		}
		.customNoOptionsMessage {
			font-size: 10px !important;
		}
		.errorMessage {
			font-size: 10px !important;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.newLabelContainer {
			.newLabel {
				font-size: 12px !important;
			}
		}
		.customPlaceholder,
		.customSingleValue {
			font-size: 12px !important;
		}
		.customInput {
			input {
				font-size: 12px !important;
			}
		}
		.customClearIndicator {
			width: 24px !important;
			height: 24px !important;
		}
		.customOption {
			font-size: 12px !important;
		}
		.customNoOptionsMessage {
			font-size: 10px !important;
		}
		.errorMessage {
			font-size: 10px !important;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.newLabelContainer {
			.newLabel {
				font-size: 12px !important;
			}
		}
		.customPlaceholder,
		.customSingleValue {
			font-size: 12px !important;
		}
		.customInput {
			input {
				font-size: 12px !important;
			}
		}
		.customClearIndicator {
			width: 24px !important;
			height: 24px !important;
		}
		.customOption {
			font-size: 12px !important;
		}
		.customNoOptionsMessage {
			font-size: 10px !important;
		}
		.errorMessage {
			font-size: 10px !important;
		}
	}
`;
