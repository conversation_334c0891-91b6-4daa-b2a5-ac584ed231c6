import React, { useState } from 'react';
import formRequiredImg from '../../Assets/images/formRequired.png';
import { Tooltip } from 'reactstrap';
import NewDesignCustomLableStyle from './newDesignCustomLabel.style';

const NewDesignCustomLabel = ({
	id,
	label,
	showRequired,
	style,
	className,
	tootlTipMessage = 'Required'
}) => {
	const [tooltipOpen, setTooltipOpen] = useState(false);
	const toggle = () => setTooltipOpen(!tooltipOpen);
	return (
		<NewDesignCustomLableStyle>
			<span className={`label ${className || ''}`} style={{ ...style }}>
				{label}&nbsp;
				{showRequired && (
					<>
						<img
							src={formRequiredImg}
							alt="form-required"
							className="requiredImg"
							id={id}
						/>
						<Tooltip
							className="tooltip-style"
							placement="right"
							isOpen={tooltipOpen}
							autohide={false}
							target={id}
							toggle={toggle}
						>
							{tootlTipMessage}
						</Tooltip>
					</>
				)}
			</span>
		</NewDesignCustomLableStyle>
	);
};

export default NewDesignCustomLabel;