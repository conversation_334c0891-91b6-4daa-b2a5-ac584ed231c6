/*
	This Component is same as FormSelect but font-family and font-size is different. 
*/
import Select, { components } from 'react-select';
import { StyleWrapper } from './formSelect2.style';
import { CancelIcon } from '../Icons';

const ClearIndicator = (props) => {
	return (
		<components.ClearIndicator {...props}>
			<CancelIcon
				width="100%"
				height="100%"
				stroke="rgba(32, 34, 36, 1)"
			/>
		</components.ClearIndicator>
	);
};

const FormSelect2 = ({
	id,
	name,
	label,
	value,
	onChange,
	error,
	errorMsg,
	className,
	options,
	placeholder,
	isLoading = false,
	isSearchable = false,
	disabled = false,
	isClearable = false,
	maxMenuHeight,
	...rest
}) => {
	return (
		<StyleWrapper>
			{label && (
				<div className="newLabelContainer">
					<span className="newLabel">{label}</span>
				</div>
			)}
			<div style={{ paddingInline: '1px' }}>
				<Select
					name={name}
					value={value}
					options={options}
					onChange={onChange}
					placeholder={placeholder}
					isLoading={isLoading}
					isDisabled={disabled}
					isSearchable={isSearchable}
					isClearable={isClearable}
					components={{
						IndicatorSeparator: () => null,
						ClearIndicator
					}}
					maxMenuHeight={maxMenuHeight ? maxMenuHeight : 150}
					classNames={{
						container: () => 'customContainer',
						control: () => 'customControl',
						valueContainer: () => 'customValue',
						placeholder: () => 'customPlaceholder',
						singleValue: () => 'customSingleValue',
						input: () => 'customInput',
						indicatorsContainer: () => 'customIndicatorsContainer',
						clearIndicator: () => 'customClearIndicator',
						dropdownIndicator: () => 'customDropdownIndicator',
						menu: () => 'customMenu',
						menuList: () => 'customMenuList',
						option: (state) =>
							state?.isSelected
								? 'customOption isSelected'
								: 'customOption',
						noOptionsMessage: () => 'customNoOptionsMessage'
					}}
					theme={(theme) => ({
						...theme,
						borderRadius: '6px',
						colors: {
							...theme.colors,
							primary25: 'rgba(1,1,1,0.1)',
							primary: '#ff5f5f',
							primary50: 'rgba(1,1,1,0.1)'
						}
					})}
				/>
			</div>
			{error && <div className="errorMessage">{errorMsg}</div>}
		</StyleWrapper>
	);
};

export default FormSelect2;
