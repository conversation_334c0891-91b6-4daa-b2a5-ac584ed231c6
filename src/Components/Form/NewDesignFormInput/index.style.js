import styled from 'styled-components';

const NewDesignStyleWrapper = styled.div`
	color: #313132 !important;
	.label {
		display: flex;
		align-items: center;
		gap: 2px;
		font-family: 'nunitosans-bold';
		font-size: 18px;
		text-transform: uppercase;
		padding-bottom: 6px;
		.requiredImg {
			width: 22px;
			height: 19px;
		}
	}

	.input,
	.searchInput {
		width: 100%;
		height: 65px;
		padding-inline: 16px;
		color: #313132eb !important;
		font-family: 'nunitosans-regular';
		font-size: 16px;
		border: 0.6px solid #31313259 !important;
		border-radius: 2px !important;
		outline: none;
		border-radius: 0 !important;
	}
	.searchInput {
		border-left: none !important;
	}

	.inputWrapper {
		display: flex;
		align-items: center;
		height: 65px;
		.input,
		.searchInput {
			width: 100%;
			height: 100%;
			padding-left: 16px;
			color: #313132eb !important;
			font-family: 'nunitosans-regular';
			font-size: 16px;
			border: 0.6px solid #31313259;
			border-radius: 2px !important;
			outline: none;
			border-radius: none;
			${(props) => props}
		}
		.iconWrapper {
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			border: 0.6px solid #31313259;
			border-left: none;
			padding-inline: 8px;
			cursor: pointer;
		}
		.iconWrapper-suggestor-search {
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			border: 0.6px solid #31313259;
			padding-inline: 8px;
			cursor: pointer;
			border-right: 0;
		}
		.iconWrapper-suggestor {
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			border: 0.6px solid #31313259;
			padding-inline: 8px;
			cursor: pointer;
			border-left: 0;
			border-radius: 2px;
		}
	}
	.error {
		padding-top: 6px;
		font-family: 'nunitosans-semi-bold';
		font-size: 14px;
		color: #ff5f5f;
	}
	@media (max-width: 600px) {
		.label {
			font-size: 12px;

			.requiredImg {
				object-fit: contain;
				width: 15px;
				height: 12px;
			}
		}
		.inputWrapper {
			height: 47px;
			.input {
				font-size: 10px;
			}
		}
		.input,
		.searchInput {
			height: 47px;
			font-size: 10px;
		}
		.error {
			font-size: 8px;
		}
	}

	@media only screen and (min-width: 600px) and (max-width: 960px) {
		.label {
			font-size: 13px;
		}
		.inputWrapper {
			height: 48.75px;
			.input {
				font-size: 10px;
			}
		}
		.input,
		.searchInput {
			height: 48.75px;
			font-size: 11px;
		}
		.error {
			font-size: 9px;
		}
	}

	@media only screen and (max-width: 1224px) {
	}

	@media only screen and (min-width: 961px) and (max-width: 1824px) and (max-height: 933px) {
		.label {
			font-size: 13.5px;

			.requiredImg {
				width: 12.3px;
				height: 10.6px;
			}
		}
		.inputWrapper {
			height: 48.75px;
			.input {
				font-size: 12px;
			}
		}
		.input,
		.searchInput {
			height: 48.75px;
			font-size: 12px;
		}
		.error {
			font-size: 10px;
		}
	}
`;

export default NewDesignStyleWrapper;
