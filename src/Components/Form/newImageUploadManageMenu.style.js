import styled from 'styled-components';

const NewImageUploadWrapper = styled.div`
	.imageContainer {
		width: 210px;
		height: 210px;
		position: relative;
		.image {
			width: 100%;
			height: 100%;
			border-radius: 4%;
		}
		.icon {
			position: absolute;
			width: 27px;
			height: 26px;
			background-color: #ff5f5f;
			display: flex;
			justify-content: center;
			align-items: center;
			border-radius: 100%;
			bottom: -4px;
			right: -7px;
			cursor: ${(props) => (props.disabled ? '' : 'pointer')};
		}

		@media (max-width: 600px) {
			width: 100% !important;
		}

		@media only screen and (min-width: 600px) and (max-width: 720px) {
			width: 130px;
			height: 140px;
		}

		@media only screen and (min-width: 720px) and (max-width: 1600px) {
			width: 150px;
			height: 150px;
		}

		@media only screen and (min-width: 961px) and (max-width: 1824px) and (max-height: 933px) {
			width: 150px;
			height: 140px;
		}
	}
`;

export default NewImageUploadWrapper;
