import React, { useCallback, useMemo } from 'react';
import ReactTable from 'react-table-6';
import 'react-table-6/react-table.css';
import TableComponentStyle from './TableComponent.style';
import FormCheckBox from '../Form/FormCheckBox';

const TableComponent = ({
    minRows = 0,
    columns,
    internalID,
    selectedRows,
    setSelectedRows,
    data,
    loading,
    pageSize,
    manual = true,
    resizable = false,
    NoDataText,
    handleRowClick,
    checkbox = false,
}) => {
    const handleSelectAll = useCallback(
        (e) => {
            const checked = e.target.checked;
            if (checked) {
                const allIds = data.map((row) => row[internalID]);
                setSelectedRows(allIds);
            } else {
                setSelectedRows([]);
            }
        },
        [data, internalID, setSelectedRows]
    );

    const handleCheckboxChange = useCallback(
        (key) => {
            setSelectedRows((prevSelectedRows) => {
                if (prevSelectedRows.includes(key)) {
                    return prevSelectedRows.filter((id) => id !== key);
                } else {
                    return [...prevSelectedRows, key];
                }
            });
        },
        [setSelectedRows]
    );

    const checkBoxColumn = useMemo(
        () => ({
            id: 'select',
            Header: () => (
                <FormCheckBox
                    // className="myThemeCheckbox"
                    style={{height: '1.4em', width: '1.4em'}}
                    onChange={handleSelectAll}
                    checked={data.length !== 0 && selectedRows.length === data.length}
                    onClick={(e) => e.stopPropagation()}
                />
            ),
            Cell: ({ original }) => (
                <FormCheckBox
                    // className="myThemeCheckbox"
                    style={{height: '1.4em', width: '1.4em'}}
                    checked={selectedRows.includes(original[internalID])}
                    onChange={(e) => handleCheckboxChange(original[internalID], e)}
                    onClick={(e) => e.stopPropagation()}
                />
            ),
            sortable: false,
            minWidth: 15,
            maxWidth: 100
        }),
        [data, handleCheckboxChange, handleSelectAll, internalID, selectedRows]
    );

    return (
        <TableComponentStyle>
        <ReactTable
            minRows={minRows}
            columns={ checkbox ? [checkBoxColumn, ...columns] : [...columns]}
            data={data}
            loading={loading}
            pageSize={pageSize}
            // onSortedChange={onSortedChange}
            manual={manual}
            resizable={resizable}
            showPagination={false}
            NoDataComponent={() => (
                <span className="rtNoDataFound fs-12 regular-text">
                    {NoDataText ? NoDataText : 'No data found'}
                </span>
            )}
            getTrProps={(state, rowInfo) => ({
                onClick: () => rowInfo && handleRowClick(rowInfo?.row)
            })}
        />
        </TableComponentStyle>
    );
};

export default TableComponent;
