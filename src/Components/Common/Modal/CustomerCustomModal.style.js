import styled from 'styled-components';

const CustomerCustomModalWrapper = styled.div`
	.closeIcon {
		position: absolute;
		right: 14px;
		top: 4px;
		text-align: end;
	}

	.title-background{
		background-color: #f3f3f3 !important;
		border-radius: 16px 16px 0 0;
		font-family: nunitosans-bold !important;
		color: #303030 !important;
	}

	.objectGif {
		object-fit: cover;
		margin-top: 30px;
		height: 300px;
		width: 200px;
	}

	.borderButtonFullWidth {
		border: 1px solid ${(props) => props.layoutTheme.buttonColor2} !important;
		color: ${(props) => props.layoutTheme.buttonColor2} !important;
		background-color: #fff !important;
		// width: 30%;
		font-family: 'nunitosans-medium';
		font-size: 12px;
		padding-block: 6px;
		padding-inline: 12px;
	}

	.themeButtonFullWidth {
		background: linear-gradient(350.67deg, #f94d73 0%, #fd6461 92.95%);
		/* background: ${(props) => props.layoutTheme.buttonColor}; */
		color: ${(props) => props.layoutTheme.buttonTextColor};
		border: 0px;
		// width: 30%;
		font-family: 'nunitosans-medium';
		font-size: 12px;
		padding-block: 6px;
		padding-inline: 12px;
	}
`;

export default CustomerCustomModalWrapper;
