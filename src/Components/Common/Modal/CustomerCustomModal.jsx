import React from 'react';
import { useSelector } from 'react-redux';
import { Modal, ModalBody } from 'reactstrap';
import { CancelIcon } from '../../Icons';
import CustomerCustomModalWrapper from './CustomerCustomModal.style';

const CustomerCustomModal = ({
	isOpen,
	handleModal,
	title,
	autoHeightMin,
	autoHeightMax,
	children,
	modalClassName,
	modalFooter,
	modalBodyClassName,
	hideCloseButton = false,
	onClosed,
	width,
	height,
	titleTextSize = 24,
	...rest
}) => {
	const allThemeData = useSelector((state) => ({
		...state.themeChanger
	}));

	return (
		<Modal
			autoFocus={false}
			isOpen={isOpen}
			toggle={() => handleModal(!isOpen)}
			className={`contentModal ${modalClassName ?? ''}`}
			centered={true}
			id="CustomModal"
			backdrop="static"
			onClosed={onClosed}
			{...rest}
			style={{ width: width, height: height }}
		>
			<ModalBody
				className={`${modalBodyClassName} overflow-auto p-0`}
				id="adfdaf"
			>
				<CustomerCustomModalWrapper  {...allThemeData}>
					<div >
						{!hideCloseButton && (
							<div
								className="closeIcon"
								onClick={() => handleModal(!isOpen)}
							>
								<CancelIcon
									className="cursor-pointer"
									height={30}
									width={30}
								/>
							</div>
						)}

						<div
							className={`d-block title-background text-start themeText fs-${titleTextSize} bold-text p-2`}
						>
							{title && title}
						</div>
					</div>

					<hr className='m-0' />
					<div>
						<div className="w-100 h-100">{children}</div>

						<div className="d-flex align-items-center justify-content-center">
							{modalFooter}
						</div>
					</div>
				</CustomerCustomModalWrapper>
			</ModalBody>
		</Modal>
	);
};

export default CustomerCustomModal;
