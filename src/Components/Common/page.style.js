import styled from 'styled-components';

const PageWrapper = styled.div`
	background-color: #ffffff;
	height: 100%;
	color: ${(props) => props.layoutTheme.textColor} !important;
	padding: 12px 24px;
	@media only screen and (max-width: 600px) {
		padding: 0px;
	}
	/* padding: 12px 24px 108px; */
	/* @media (max-width: 600px) {
		padding: 4px 16px 95px;
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		padding: 12px 24px 92px;
	}
	@media only screen and (min-width: 1024px) and (max-width: 1824px) and (max-height: 900px) {
		padding: 12px 24px 107px;
	} */
	.page {
		box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.08);
		border-radius: 12px;
		padding: 18px 22px;
		height: 100%;
		display: flex;
		flex-direction: column;
		@media only screen and (max-width: 767px) {
			border-radius: 0px;
		}
		position: relative;
		.customPageLoaderWrapper {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			/* background-color: rgba(1, 1, 1, 0.2); */
			border-radius: 12px;
			display: flex;
			justify-content: center;
			align-items: center;
			z-index: 10;
		}
	}
	.page-header {
		padding-bottom: 24px;
	}
	.page-body {
		flex: 1 1 auto;
	}
	.children-wrapper {
		width: 100%;
		height: 100%;
	}
	.pageButton {
		background-color: #fffdfd !important;
		color: ${(props) => props.layoutTheme.buttonColor2} !important;
	}
	.boxCard {
		border: 1px solid #eaeaea;
		border-radius: 8px;
	}
	.thumb-horizontal {
		cursor: pointer;
		z-index: 3;
		position: relative;
		display: block;
		width: 100%;
		height: 321px;
		transform: translateY(0px);
		cursor: pointer;
		border-radius: inherit;
		background-color: rgba(0, 0, 0, 0.2);
	}
`;
export default PageWrapper;
