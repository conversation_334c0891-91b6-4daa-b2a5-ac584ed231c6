import React from 'react';
import { useState } from 'react';
import {
	<PERSON><PERSON>hart,
	Line,
	XAxis,
	YAxis,
	CartesianGrid,
	<PERSON>lt<PERSON>,
	Legend,
	ResponsiveContainer
} from 'recharts';
import useDevice from '../../../Hooks/useDevice';
import CustomPopover from '../../Common/Popover/CustomPopover';

const MultiSeriesLineChart = ({
	heading,
	chartData,
	yAxisData = [],
	xAxisData = [],
	className,
	id,
	showPopover = false,
	popOverContent,
	yAxisWidth
}) => {
	const { isMobile } = useDevice();
	const [isOpenPopOver, setIsOpenPopOver] = useState(false);
	const togglePopOver = () => {
		setIsOpenPopOver(true);
	};
	return (
		<div
			className={`pa-24 border-radius-16 bg-white d-flex w-100 h-100 flex-column ${className}`}
		>
			{heading && (
				<div className="pa-b-24">
					<div
						className="fs-20 medium-text"
						id={id}
						onMouseEnter={() =>
							showPopover && setIsOpenPopOver(true)
						}
						onMouseLeave={() =>
							showPopover && setIsOpenPopOver(false)
						}
						style={{ cursor: 'default' }}
					>
						{heading}
					</div>
				</div>
			)}
			<ResponsiveContainer width="100%" height={329}>
				<LineChart
					width={'100%'}
					height={'100%'}
					margin={{
						left: -30,
						right: 10
					}}
				>
					<XAxis
						dataKey="category"
						type="category"
						allowDuplicatedCategory={false}
						tickLine={false}
						axisLine={false}
						interval={0}
						ticks={
							xAxisData?.length > 0 &&
							xAxisData?.map((item) => item.value)
						}
						tick={(data) => {
							let label = xAxisData.find(
								(item) => item.value === data.payload.value
							).label;
							return (
								<g transform={`translate(${data.x},${data.y})`}>
									<text
										x={0}
										y={0}
										fontSize={10}
										dy={isMobile ? 14 : 4}
										textAnchor={'end'}
										transform={
											isMobile
												? 'rotate(-45)'
												: 'rotate(-90)'
										}
									>
										{label}
									</text>
								</g>
							);
						}}
						height={58}
					/>
					<YAxis
						tickLine={false}
						axisLine={false}
						ticks={
							yAxisData?.length > 0 &&
							yAxisData?.map((item) => item.value)
						}
						tickFormatter={(value, index) => {
							if (yAxisData[index]) {
								return yAxisData[index].label;
							}
						}}
						width={yAxisWidth}
					/>
					<Tooltip />
					<Legend
						verticalAlign="top"
						align="left"
						iconType="circle"
						iconSize={14}
						formatter={(value) => (
							<span
								style={{
									color: '#242424',
									fontSize: '14px'
								}}
							>
								{value}
							</span>
						)}
					/>
					<CartesianGrid horizontal vertical={false} />
					{chartData?.length > 0 &&
						chartData?.map((s) => {
							return (
								<Line
									dataKey="value"
									data={s.data}
									name={s.name}
									key={s.name}
									dot={{
										stroke: s.color,
										strokeWidth: 1,
										fill: s.color
									}}
									stroke={s.color}
								/>
							);
						})}
				</LineChart>
			</ResponsiveContainer>
			{showPopover && (
				<CustomPopover
					isOpen={isOpenPopOver}
					target={id}
					handleToggle={togglePopOver}
					placement="bottom-start"
					className="Venue-Popover"
				>
					<div className="pa-16">{popOverContent}</div>
				</CustomPopover>
			)}
		</div>
	);
};

export default MultiSeriesLineChart;
