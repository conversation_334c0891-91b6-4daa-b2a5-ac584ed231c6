import React from 'react';
import {
	<PERSON><PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	CartesianG<PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>onsive<PERSON><PERSON><PERSON>,
	<PERSON>
} from 'recharts';
import useDevice from '../../../Hooks/useDevice';

const VerticleStackedBarChart = ({
	heading,
	data,
	yAxisCount = 5,
	yAxisWidth
}) => {
	const { isDesktopOrLaptop, isMobile } = useDevice();

	return (
		<div
			className={`pa-24 border-radius-16 bg-white d-flex w-100 h-100 flex-column defaultBoxShadow`}
		>
			{heading && (
				<div className="pa-b-24">
					<div className="fs-20 medium-text">{heading}</div>
				</div>
			)}
			<ResponsiveContainer width="100%" height={329}>
				<BarChart
					width={'100%'}
					height={'100%'}
					data={data.chartData}
					margin={{
						left: -28,
						right: 5
					}}
				>
					<XAxis
						dataKey="name"
						axisLine={false}
						tickLine={false}
						height={38}
						tick={(data) => {
							return (
								<g transform={`translate(${data.x},${data.y})`}>
									<text
										x={0}
										y={0}
										fontSize={10}
										dy={isMobile ? 14 : 4}
										textAnchor={isMobile ? 'middle' : 'end'}
										transform={`${
											isDesktopOrLaptop
												? 'rotate(0)'
												: isMobile
												? 'rotate(-30)'
												: 'rotate(-90)'
										}`}
									>
										{data.payload.value}
									</text>
								</g>
							);
						}}
						interval={0}
					/>
					<YAxis
						type="number"
						axisLine={false}
						tickLine={false}
						tickCount={yAxisCount}
						width={yAxisWidth}
					/>
					<CartesianGrid horizontal vertical={false} />
					<Tooltip />
					<Legend
						verticalAlign="top"
						align="left"
						iconType="circle"
						iconSize={14}
						formatter={(value) => (
							<span
								style={{ color: '#242424', fontSize: '14px' }}
							>
								{value}
							</span>
						)}
					/>
					{data?.chartConfig?.length > 0 &&
						data?.chartConfig?.map((item, index) => {
							return (
								<Bar
									dataKey={item.dataKey}
									name={item.name}
									fill={item.fill}
									stackId="a"
									dot={false}
									maxBarSize={54}
									radius={
										index === data.chartConfig.length - 1
											? [7, 7, 0, 0]
											: ''
									}
								/>
							);
						})}
				</BarChart>
			</ResponsiveContainer>
		</div>
	);
};

export default VerticleStackedBarChart;
