import React from 'react';
import {
	<PERSON><PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Cartesian<PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
	<PERSON>
} from 'recharts';

const HorizontalStackedBarChart = ({
	heading,
	data,
	xAxisCount = 5,
	yAxisWidth
}) => {
	return (
		<div
			className={`pa-24 border-radius-16 bg-white d-flex w-100 h-100 flex-column defaultBoxShadow`}
		>
			{heading && (
				<div className="pa-b-24">
					<div className="fs-20 medium-text">{heading}</div>
				</div>
			)}
			<ResponsiveContainer width="100%" height={329}>
				<BarChart
					width={'100%'}
					height={'100%'}
					layout="vertical"
					data={data.chartData}
					margin={{
						left: -28,
						right: 14
					}}
				>
					<XAxis
						type="number"
						tickLine={false}
						axisLine={false}
						tickCount={xAxisCount}
					/>
					<YAxis
						type="category"
						dataKey="name"
						axisLine={false}
						tickLine={false}
						interval={0}
						tick={(data) => {
							return (
								<g transform={`translate(${data.x},${data.y})`}>
									<text
										x={0}
										y={0}
										fontSize={14}
										textAnchor={'end'}
										transform={'rotate(0)'}
										fill="#4F4F4F"
									>
										{data.payload.value}
									</text>
								</g>
							);
						}}
						width={yAxisWidth}
					/>
					<CartesianGrid horizontal={false} vertical={true} />
					<Tooltip />
					<Legend
						verticalAlign="top"
						align="left"
						iconType="circle"
						iconSize={14}
						formatter={(value) => (
							<span
								style={{ color: '#242424', fontSize: '14px' }}
							>
								{value}
							</span>
						)}
					/>
					{data?.chartConfig?.length > 0 &&
						data?.chartConfig?.map((item, index) => {
							return (
								<Bar
									dataKey={item.dataKey}
									name={item.name}
									fill={item.fill}
									stackId="a"
									dot={false}
									maxBarSize={54}
									radius={
										index === data.chartConfig.length - 1
											? [0, 7, 7, 0]
											: ''
									}
								/>
							);
						})}
				</BarChart>
			</ResponsiveContainer>
		</div>
	);
};

export default HorizontalStackedBarChart;
