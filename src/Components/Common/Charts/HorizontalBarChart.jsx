import React from 'react';
import {
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	CartesianGrid,
	<PERSON>lt<PERSON>,
	ResponsiveContainer,
	Bar,
	BarChart,
	Label
} from 'recharts';

const HorizontalBarChart = ({ heading, chartData, yAxisWidth }) => {
	return (
		<div className="pa-24 border-radius-16 bg-white d-flex w-100 h-100 flex-column defaultBoxShadow">
			{heading && (
				<div className="pa-b-24">
					<div className="fs-20 medium-text">{heading}</div>
				</div>
			)}
			<ResponsiveContainer width="100%" height={329}>
				<BarChart
					width={'100%'}
					height={'100%'}
					layout="vertical"
					data={chartData}
					margin={{ left: 5, right: 14 }}
				>
					<CartesianGrid horizontal={false} vertical={true} />
					<XAxis
						type="number"
						tickLine={false}
						axisLine={false}
						interval={0}
						tickCount={5}
						width={80}
					/>
					<YAxis
						type="category"
						dataKey="name"
						tickLine={false}
						axisLine={false}
						minTickGap={10}
						interval={0}
						width={yAxisWidth}
						tick={(data) => {
							return (
								<g transform={`translate(${data.x},${data.y})`}>
									<text
										x={0}
										y={0}
										fontSize={14}
										textAnchor={'end'}
										transform={'rotate(0)'}
										fill="#4F4F4F"
									>
										{data.payload.value}
									</text>
								</g>
							);
						}}
					/>
					<Tooltip />
					<Bar
						dataKey="value"
						barSize={35}
						radius={[0, 7, 7, 0]}
						fill="#FF5F5F"
					/>
				</BarChart>
			</ResponsiveContainer>
		</div>
	);
};

export default HorizontalBarChart;
