import React from 'react';
import { useSelector } from 'react-redux';
import { Navigate, useLocation } from 'react-router-dom';
import {
	AdminPanelRoutes,
	CommonRoutes,
	VenuePanelRoutes
} from '../../../Utils/routes';

const ProtectedRoute = ({ children }) => {
	const authDetails = useSelector((state) => state.auth);
	const { pathname } = useLocation();
	const AdminRoutes = Object.keys(AdminPanelRoutes).map((key) => {
		return AdminPanelRoutes[key];
	});
	const VenueRoutes = Object.keys(VenuePanelRoutes).map((key) => {
		return VenuePanelRoutes[key];
	});
	const NoVenueConnectedRoutes = [
		VenuePanelRoutes.connectVenue,
		VenuePanelRoutes.createVenueAccount,
		VenuePanelRoutes.support
	];
	if (authDetails?.isLogin) {
		if (authDetails?.login_type === 'admin') {
			let isFoundRoute = AdminRoutes.find((route) => {
				return pathname.includes(route);
			});
			if (isFoundRoute) {
				return children;
			} else {
				// Navigate to 404 Not Found Page
				return <Navigate to={CommonRoutes.pageNotFound} />;
			}
		}
		if (authDetails?.login_type === 'venue') {
			let isFoundRoute = VenueRoutes.find((route) => {
				return pathname.includes(route);
			});
			if (isFoundRoute) {
				if (authDetails?.subscriptions?.length === 0) {
					if (pathname === VenuePanelRoutes.registerSubscription) {
						return children;
					} else {
						return (
							<Navigate
								to={VenuePanelRoutes.registerSubscription}
							/>
						);
					}
				} else if (authDetails?.bars?.length === 0) {
					if (
						NoVenueConnectedRoutes.find((route) => {
							return pathname.includes(route);
						})
					) {
						return children;
					} else {
						return <Navigate to={VenuePanelRoutes.connectVenue} />;
					}
				} else {
					return children;
				}
			} else {
				// Navigate to 404 Not Found Page
				return <Navigate to={CommonRoutes.pageNotFound} />;
			}
		}
	} else {
		// Navigate to login page
		return <Navigate to={CommonRoutes.login} />;
	}
};

export default ProtectedRoute;
