import React from 'react';
import { Spinner } from 'reactstrap';
import { StylesWrapper } from './index.style';

const NewLoader = ({
	children,
	size = '32px',
	color = '#FF5F5F',
	loading = true,
	borderWidth = '3px',
	style,
	className
}) => {
	return children ? (
		<StylesWrapper>
			{children}
			{loading && (
				<div className="newLoader">
					<Spinner
						className={className}
						style={{
							width: size,
							height: size,
							color: color,
							borderWidth: borderWidth,
							...style
						}}
					/>
				</div>
			)}
		</StylesWrapper>
	) : (
		<Spinner
			className={className}
			style={{
				width: size,
				height: size,
				color: color,
				borderWidth: borderWidth,
				...style
			}}
		/>
	);
};

export default NewLoader;
