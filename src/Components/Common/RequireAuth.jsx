import React from 'react';
import { Navigate, useLocation, Outlet } from 'react-router-dom';
import { Cookies } from 'react-cookie';
import { useSelector } from 'react-redux';

const RequireAuth = () => {
	const location = useLocation();
	const { loginData } = useSelector((state) => state);
	const cookies = new Cookies();

	if (!cookies.get('teamworkstoken') || !Object.keys(loginData).length) {
		cookies.remove('teamworkstoken');
		return (
			<Navigate
				to="/login"
				state={{
					from: location.pathname,
					loggedOut: 'You need to be logged in to continue further.'
				}}
				replace={true}
			/>
		);
	}

	return <Outlet />;
};

export default RequireAuth;
