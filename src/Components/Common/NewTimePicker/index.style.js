import styled from 'styled-components';

export const StylesWrapper = styled.div`
	.newCustomTimePickerContainer {
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 13px;
		border: 1px solid rgba(49, 49, 50, 0.35);
		height: 53px;
		padding-inline: 13px;
		background-color: #ffffff;
		cursor: ${(props) => (props.disabled ? 'default' : 'pointer')};
	}
	.newCustomInput {
		width: 100% !important;
		height: 100% !important;
		outline: none !important;
		border: none !important;
		color: rgba(32, 34, 36, 1) !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 16px !important;
		line-height: 1 !important;
		cursor: ${(props) => (props.disabled ? 'default' : 'pointer')};
		&::placeholder {
			color: #979797 !important;
			font-family: 'nunitosans-regular' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
		&:disabled {
			background-color: transparent !important;
		}
	}
	.suffixContainer {
		align-self: stretch;
		display: flex;
		align-items: center;
		color: rgba(32, 34, 36, 1) !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 16px !important;
		line-height: 1 !important;
		.dropdownIconWrapper {
			display: flex;
			justify-content: center;
			align-items: center;
			.dropdownIcon {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 29px !important;
				height: 29px !important;
			}
		}
	}
	@media (max-width: 600px) {
		.newCustomTimePickerContainer {
			gap: 9px;
			height: 34px;
			padding-inline: 9px;
		}
		.newCustomInput {
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
		}
		.suffixContainer {
			font-size: 12px !important;
			.dropdownIconWrapper {
				.dropdownIcon {
					width: 19px !important;
					height: 19px !important;
				}
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.newCustomTimePickerContainer {
			gap: 10px;
			height: 40px;
			padding-inline: 10px;
		}
		.newCustomInput {
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
		}
		.suffixContainer {
			font-size: 12px !important;
			.dropdownIconWrapper {
				.dropdownIcon {
					width: 22px !important;
					height: 22px !important;
				}
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.newCustomTimePickerContainer {
			gap: 10px;
			height: 40px;
			padding-inline: 10px;
		}
		.newCustomInput {
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
		}
		.suffixContainer {
			font-size: 12px !important;
			.dropdownIconWrapper {
				.dropdownIcon {
					width: 22px !important;
					height: 22px !important;
				}
			}
		}
	}
`;

export const PopoverStyleWrraper = styled.div`
	background-color: white;
	width: 160px;
	height: 300px;
	border: 1px solid rgba(49, 49, 50, 0.35);
	display: flex;
	flex-direction: column;
	.hoursMinutesContainer {
		flex: 1;
		display: flex;
		overflow: auto;
		.hoursList {
			width: 33.33%;
			border-right: 1px solid rgba(49, 49, 50, 0.35);
			overflow-y: auto;
		}
		.minutesList {
			width: 33.33%;
			border-right: 1px solid rgba(49, 49, 50, 0.35);
			overflow-y: auto;
		}
		.amPm {
			width: 33.33%;
		}
		.hoursList .hoursItem,
		.minutesList .minutesItem,
		.amPm .amPmItem {
			color: rgba(32, 34, 36, 1) !important;
			font-family: 'nunitosans-regular' !important;
			font-size: 16px !important;
			line-height: 1 !important;
			padding-block: 6px;
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			&:hover {
				background-color: rgba(0, 0, 0, 0.1);
			}
		}
		.hoursList .hoursItem.selected,
		.minutesList .minutesItem.selected,
		.amPm .amPmItem.selected {
			color: #ffffff !important;
			background-color: #ff5a5f !important;
		}
	}
	.actionButtonContainer {
		padding: 8px;
		display: flex;
		justify-content: space-between;
		border-top: 1px solid rgba(49, 49, 50, 0.35);
		.nowButton {
			width: fit-content;
			height: 24px;
			border-radius: 6px;
			border: none;
			outline: none;
			border: 1px solid transparent;
			background-color: transparent;
			display: flex;
			justify-content: center;
			align-items: center;
			color: #ff5a5f;
			font-family: 'nunitosans-semi-bold';
			font-size: 14px;
			line-height: 1;
			&:hover {
				opacity: 0.8;
			}
		}
		.okButton {
			width: 38px;
			height: 24px;
			border-radius: 6px;
			border: none;
			outline: none;
			border: 1px solid #ff5a5f;
			background-color: #ff5a5f;
			display: flex;
			justify-content: center;
			align-items: center;
			color: #ffffff;
			font-family: 'nunitosans-semi-bold';
			font-size: 14px;
			line-height: 1;
			&:disabled {
				opacity: 0.6;
			}
		}
	}
	@media (max-width: 600px) {
		width: 140px;
		height: 240px;
		.hoursMinutesContainer {
			.hoursList .hoursItem,
			.minutesList .minutesItem,
			.amPm .amPmItem {
				font-size: 12px !important;
				padding-block: 5px;
			}
		}
		.actionButtonContainer {
			padding: 6px;
			.nowButton {
				height: 24px;
				font-size: 12px;
			}
			.okButton {
				width: 38px;
				height: 24px;
				font-size: 12px;
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		width: 140px;
		height: 240px;
		.hoursMinutesContainer {
			.hoursList .hoursItem,
			.minutesList .minutesItem,
			.amPm .amPmItem {
				font-size: 12px !important;
				padding-block: 5px;
			}
		}
		.actionButtonContainer {
			padding: 6px;
			.nowButton {
				height: 24px;
				font-size: 12px;
			}
			.okButton {
				width: 38px;
				height: 24px;
				font-size: 12px;
			}
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		width: 140px;
		height: 240px;
		.hoursMinutesContainer {
			.hoursList .hoursItem,
			.minutesList .minutesItem,
			.amPm .amPmItem {
				font-size: 12px !important;
				padding-block: 5px;
			}
		}
		.actionButtonContainer {
			padding: 6px;
			.nowButton {
				height: 24px;
				font-size: 12px;
			}
			.okButton {
				width: 38px;
				height: 24px;
				font-size: 12px;
			}
		}
	}
`;
