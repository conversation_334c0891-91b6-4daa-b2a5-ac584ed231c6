import { useEffect, useRef, useState } from 'react';
import Scrollbars from 'react-custom-scrollbars';
import { Popover } from 'react-tiny-popover';
import moment from 'moment';

import { PopoverStyleWrraper, StylesWrapper } from './index.style';
import { hoursList, minutesList, splitTimeString } from './utils';
import { CircleArrowDown } from '../../Icons';

const NewTimePicker = ({
	name,
	onChange,
	onBlur,
	value,
	suffix = (
		<div className="dropdownIconWrapper">
			<div className="dropdownIcon">
				<CircleArrowDown width="100%" height="100%" />
			</div>
		</div>
	),
	placeholder,
	wrapperClassName,
	popoverWrapperClassName,
	disabled
}) => {
	const popoverContentRef = useRef(null);
	const popoverButtonRef = useRef(null);
	const [isOpenPopover, setIsOpenPopover] = useState(false);
	const [timePickerValue, setTimePickerValue] = useState(null);
	const [selectedHour, setSelectedHour] = useState(null);
	const [selectedMinute, setSelectedMinute] = useState(null);
	const [selectedPeriod, setSelectedPeriod] = useState(null);

	const handleSelectHour = (hour) => {
		setSelectedHour(hour);
	};
	const handleSelectMinute = (minute) => {
		setSelectedMinute(minute);
	};
	const handleSelectPeriod = (period) => {
		setSelectedPeriod(period);
	};
	const handleOK = () => {
		let time = selectedHour + ':' + selectedMinute + ' ' + selectedPeriod;
		if (onChange) {
			onChange(time);
		} else {
			setTimePickerValue(time);
		}
		setIsOpenPopover(false);
	};
	const handleNow = () => {
		if (onChange) {
			onChange(moment().format('hh:mm A'));
		} else {
			let time = moment().format('hh:mm A');
			let [hour, minute, period] = splitTimeString(time);
			setSelectedHour(hour);
			setSelectedMinute(minute);
			setSelectedPeriod(period);
			setTimePickerValue(time);
		}
		setIsOpenPopover(false);
	};
	useEffect(() => {
		if (onChange && value) {
			let [hour, minute, period] = splitTimeString(value);
			setSelectedHour(hour);
			setSelectedMinute(minute);
			setSelectedPeriod(period);
			setTimePickerValue(value);
		} else {
			setSelectedHour(null);
			setSelectedMinute(null);
			setSelectedPeriod(null);
			setTimePickerValue(null);
		}
	}, [value, onChange]);
	useEffect(() => {
		const handleClickOutside = (event) => {
			if (
				!popoverButtonRef?.current?.contains(event?.target) &&
				!popoverContentRef?.current?.contains(event?.target)
			) {
				setIsOpenPopover(false);
			}
		};
		document.addEventListener('click', handleClickOutside, true);
		return () => {
			document.removeEventListener('click', handleClickOutside, true);
		};
	}, [setIsOpenPopover]);
	return (
		<StylesWrapper className={wrapperClassName} disabled={disabled}>
			<Popover
				isOpen={isOpenPopover}
				positions={['bottom', 'top', 'left', 'right']}
				align="start"
				onClickOutside={() => setIsOpenPopover(false)}
				content={
					<div ref={popoverContentRef}>
						<PopoverStyleWrraper
							className={popoverWrapperClassName}
						>
							<div className="hoursMinutesContainer">
								<div className="hoursList">
									<Scrollbars
										renderTrackVertical={({
											style,
											...props
										}) => (
											<div
												{...props}
												style={{
													...style,
													width: '6px',
													right: '1px',
													bottom: '2px',
													top: '2px',
													borderRadius: '3px'
												}}
											/>
										)}
										autoHide
									>
										{hoursList?.length > 0 &&
											hoursList?.map((item) => (
												<div
													key={item}
													className={
														selectedHour === item
															? 'hoursItem selected'
															: 'hoursItem'
													}
													onClick={() =>
														handleSelectHour(item)
													}
												>
													{item}
												</div>
											))}
									</Scrollbars>
								</div>
								<div className="minutesList">
									<Scrollbars
										renderTrackVertical={({
											style,
											...props
										}) => (
											<div
												{...props}
												style={{
													...style,
													width: '6px',
													right: '1px',
													bottom: '2px',
													top: '2px',
													borderRadius: '3px'
												}}
											/>
										)}
										autoHide
									>
										{minutesList?.length > 0 &&
											minutesList?.map((item) => (
												<div
													className={
														selectedMinute === item
															? 'minutesItem selected'
															: 'minutesItem'
													}
													onClick={() =>
														handleSelectMinute(item)
													}
												>
													{item}
												</div>
											))}
									</Scrollbars>
								</div>
								<div className="amPm">
									<div
										className={
											selectedPeriod === 'AM'
												? 'amPmItem selected'
												: 'amPmItem'
										}
										onClick={() => handleSelectPeriod('AM')}
									>
										AM
									</div>
									<div
										className={
											selectedPeriod === 'PM'
												? 'amPmItem selected'
												: 'amPmItem'
										}
										onClick={() => handleSelectPeriod('PM')}
									>
										PM
									</div>
								</div>
							</div>
							<div className="actionButtonContainer">
								<button
									className="nowButton"
									onClick={handleNow}
								>
									Now
								</button>
								<button
									className="okButton"
									onClick={handleOK}
									disabled={
										!(
											selectedHour &&
											selectedMinute &&
											selectedPeriod
										)
									}
								>
									Ok
								</button>
							</div>
						</PopoverStyleWrraper>
					</div>
				}
				ref={popoverButtonRef}
			>
				<div
					onClick={() =>
						!disabled && setIsOpenPopover((prev) => !prev)
					}
					onBlur={onBlur}
				>
					<div className="newCustomTimePickerContainer">
						<input
							className="newCustomInput"
							type={'text'}
							name={name}
							value={timePickerValue}
							placeholder={placeholder}
							readOnly
						/>
						{suffix && (
							<div className="suffixContainer">{suffix}</div>
						)}
					</div>
				</div>
			</Popover>
		</StylesWrapper>
	);
};

export default NewTimePicker;
