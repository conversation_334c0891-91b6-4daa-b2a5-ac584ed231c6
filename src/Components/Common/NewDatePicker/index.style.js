import styled from 'styled-components';

export const StylesWrapper = styled.div`
	.newCustomDatePickerContainer {
		gap: 18px;
		border: 0.6px solid rgb(213, 213, 213);
		border-radius: 2px;
		height: 53px;
		width: 100%;
		background-color: #ffffff;
		.newCustomDatePicker {
			width: 100%;
			height: 100%;
			.react-datepicker {
				border: 1px solid #eaeaea !important;
				background-color: #fff;
				border-radius: 2px !important;
				padding-block: 8px !important;
			}
			.react-datepicker__month-container {
				border: 0px !important;
				.react-datepicker__header {
					padding-top: 0px;
					border: 0px !important;
					background-color: #fff;
				}
				.react-datepicker__day-names {
					padding-top: 5px;
				}
				.react-datepicker__day-name {
					color: #f95c69 !important;
					font-family: 'nunitosans-semi-bold' !important;
					font-weight: normal !important;
				}
				.react-datepicker__current-month {
					display: none;
				}
				.react-datepicker__year-dropdown-container--scroll {
					margin: 0px 25px;
				}
				.react-datepicker__day {
					color: #2e2e2e !important;
					font-family: 'nunitosans-regular' !important;
					&:hover {
						border-radius: 50%;
					}
				}
				.react-datepicker__day--today {
					color: #2e2e2e !important;
					font-family: 'nunitosans-bold' !important;
					font-weight: normal !important;
				}
				.react-datepicker__day--selected {
					background-color: #f95c69 !important;
					color: #fff !important;
					border-radius: 50%;
					border: none !important;
				}
				.react-datepicker__day--disabled {
					color: #ccc !important;
				}
			}
			.customDropdown {
				border: 1px solid #ff5f5f !important;
				border-radius: 4px !important;
				.customDropdownToggle {
					display: flex !important;
					justify-content: space-between !important;
					align-items: center !important;
					outline: 0 !important;
					border: 0px !important;
					.customDropdownToggleText {
						color: #2e2e2e !important;
						font-family: 'nunitosans-regular' !important;
						font-size: 12px !important;
					}
				}
				.yearDropdownToggle {
					width: 60px !important;
					padding-inline: 8px !important;
				}
				.monthDropdownToggle {
					width: 100px !important;
					padding-inline: 10px !important;
				}
				.customDropdownMenu {
					padding-top: 0 !important;
					padding-bottom: 0 !important;
					margin-top: 2px !important;
					min-width: unset !important;
					max-height: 200px !important;
					overflow-x: hidden;
					/* Customize the scrollbar */
					scrollbar-width: thin; /* For Firefox */
					scrollbar-color: rgba(0, 0, 0, 0.2) #fff; /* For Firefox */
					/* Customize the scrollbar for Webkit browsers (Chrome, Safari, etc.) */
					::-webkit-scrollbar {
						width: 2px;
					}
					::-webkit-scrollbar-thumb {
						background-color: rgba(0, 0, 0, 0.2);
						border-radius: 3px !important;
					}
					::-webkit-scrollbar-track {
						background-color: #fff;
						border-radius: 18px !important;
					}
					.customDropdownItem {
						color: #2e2e2e !important;
						font-size: 12px !important;
						font-family: 'nunitosans-regular' !important;
						&:hover {
							background-color: #f95c69 !important;
							color: #fff !important;
						}
					}
					.customDropdownItem.active {
						background-color: #f95c69 !important;
						color: #fff !important;
					}
				}
				.monthDropdownMenu {
					max-height: 210px !important;
				}
			}
			.leftArrowIcon {
				margin-left: 12px !important;
			}
			.rightArrowIcon {
				margin-right: 12px !important;
			}
			.react-datepicker-wrapper {
				height: 100% !important;
				width: 100% !important;
				.react-datepicker__input-container {
					height: 100% !important;
					width: 100% !important;
					.customDateInput {
						width: 100%;
						height: 100%;
						border: none;
						outline: none;
						background-color: transparent !important;
						padding-inline: 18px;
						display: flex;
						justify-content: space-between;
						align-items: center;
						gap: 18px;
						.valueContainer {
							color: #2e2e2e !important;
							font-family: 'nunitosans-semi-bold' !important;
							font-size: 16px !important;
						}
						.placeholderContainer {
							color: #979797 !important;
							font-family: 'nunitosans-semi-bold' !important;
							font-size: 16px !important;
						}
						.suffixContainer {
							align-self: stretch;
							display: flex;
							align-items: center;
							justify-content: flex-end;
							.dropdownIconWrapper {
								display: flex;
								justify-content: center;
								align-items: center;
								.dropdownIcon {
									display: flex;
									justify-content: center;
									align-items: center;
									width: 29px !important;
									height: 29px !important;
								}
							}
						}
					}
				}
			}
			.react-datepicker-popper {
				line-height: 1 !important;
			}
			.react-datepicker-popper[data-placement^='top'] {
				padding-bottom: 0 !important;
			}
			.react-datepicker-popper[data-placement^='bottom'] {
				padding-top: 0 !important;
			}
		}
	}
	@media (max-width: 600px) {
		.newCustomDatePickerContainer {
			gap: 12px;
			height: 34px;
			.newCustomDatePicker {
				.react-datepicker-wrapper {
					.react-datepicker__input-container {
						.customDateInput {
							padding-inline: 12px;
							gap: 12px;
							.valueContainer {
								font-size: 12px !important;
							}
							.placeholderContainer {
								font-size: 12px !important;
							}
							.suffixContainer {
								.dropdownIconWrapper {
									.dropdownIcon {
										width: 19px !important;
										height: 19px !important;
									}
								}
							}
						}
					}
				}
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.newCustomDatePickerContainer {
			gap: 14px;
			height: 40px;
			.newCustomDatePicker {
				.react-datepicker-wrapper {
					.react-datepicker__input-container {
						.customDateInput {
							padding-inline: 14px;
							gap: 14px;
							.valueContainer {
								font-size: 12px !important;
							}
							.placeholderContainer {
								font-size: 12px !important;
							}
							.suffixContainer {
								.dropdownIconWrapper {
									.dropdownIcon {
										width: 22px !important;
										height: 22px !important;
									}
								}
							}
						}
					}
				}
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.newCustomDatePickerContainer {
			gap: 14px;
			height: 40px;
			.newCustomDatePicker {
				.react-datepicker-wrapper {
					.react-datepicker__input-container {
						.customDateInput {
							padding-inline: 14px;
							gap: 14px;
							.valueContainer {
								font-size: 12px !important;
							}
							.placeholderContainer {
								font-size: 12px !important;
							}
							.suffixContainer {
								.dropdownIconWrapper {
									.dropdownIcon {
										width: 22px !important;
										height: 22px !important;
									}
								}
							}
						}
					}
				}
			}
		}
	}
`;
