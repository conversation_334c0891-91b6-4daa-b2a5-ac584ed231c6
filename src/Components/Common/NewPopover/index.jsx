import React, { useState, useRef, useEffect } from 'react';
import { Popover } from 'react-tiny-popover';

const NewPopover = ({
	children,
	positions,
	align,
	content,
	onContentClick,
	containerStyle,
	reposition = false
}) => {
	const [isOpenPopover, setIsOpenPopover] = useState(false);
	const popoverContentRef = useRef(null);
	const popoverButtonRef = useRef(null);

	const closePopover = () => setIsOpenPopover(false);

	useEffect(() => {
		const handleClickOutside = (event) => {
			if (
				!popoverButtonRef?.current?.contains(event?.target) &&
				!popoverContentRef?.current?.contains(event?.target)
			) {
				setIsOpenPopover(false);
			}
		};
		document.addEventListener('click', handleClickOutside, true);
		return () => {
			document.removeEventListener('click', handleClickOutside, true);
		};
	}, [setIsOpenPopover]);

	return (
		<Popover
			isOpen={isOpenPopover}
			positions={positions}
			align={align}
			onClickOutside={() => setIsOpenPopover(false)}
			content={
				<div
					ref={popoverContentRef}
					onClick={() =>
						onContentClick && onContentClick(closePopover)
					}
				>
					{content}
				</div>
			}
			reposition={reposition}
			ref={popoverButtonRef}
			containerStyle={containerStyle}
		>
			<div onClick={() => setIsOpenPopover((prev) => !prev)}>
				{children}
			</div>
		</Popover>
	);
};

export default NewPopover;
