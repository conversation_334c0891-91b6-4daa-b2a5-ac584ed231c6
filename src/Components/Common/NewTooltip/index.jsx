import React, { useState } from 'react';
import { ArrowContainer, Popover } from 'react-tiny-popover';
import { StyleWrapper } from './index.style';

const NewTooltip = ({
	content,
	children,
	positions = ['right', 'top', 'bottom', 'left'],
	align = 'center'
}) => {
	const [isOpenPopover, setIsOpenPopover] = useState(false);

	return (
		<Popover
			isOpen={isOpenPopover}
			positions={positions}
			align={align}
			content={({ position, childRect, popoverRect }) => (
				<ArrowContainer
					position={position}
					childRect={childRect}
					popoverRect={popoverRect}
					arrowColor={'#000000'}
					arrowSize={4}
				>
					<StyleWrapper>{content}</StyleWrapper>
				</ArrowContainer>
			)}
		>
			<span
				onMouseEnter={() => setIsOpenPopover(true)}
				onMouseLeave={() => setIsOpenPopover(false)}
			>
				{children}
			</span>
		</Popover>
	);
};

export default NewTooltip;
