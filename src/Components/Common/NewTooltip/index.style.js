import styled from 'styled-components';

export const StyleWrapper = styled.div`
	background-color: rgba(1, 1, 1, 1);
	padding: 6px;
	border-radius: 4px;
	width: fit-content;
	max-width: 400px;
	color: #ffffff;
	font-family: 'nunitosans-regular' !important;
	font-size: 14px !important;
	line-height: 1.2 !important;
	@media (max-width: 600px) {
		font-size: 11px !important;
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		font-size: 13px !important;
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		font-size: 13px !important;
	}
`;
