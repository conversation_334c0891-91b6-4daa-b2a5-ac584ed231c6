import styled from 'styled-components';

const TableComponentStyle = styled.div`
	.myThemeCheckbox {
		border: 1px solid #ff5f5f !important;
	}

	.newThemeCheckbox {
		border: 1px solid #ff5f5f !important;
		height: 1.6em !important;
		width: 1.6em !important;

		// border-radius: 2px !important; /* Border radius for checkboxes */
		// border: 1px solid #fd7a7a !important; /* Border color */
		// float: none !important;
		// margin-left: 0 !important;
		// margin-top: 0 !important;
		// vertical-align: middle !important;
		// height: 28px; /* Set size for checkboxes */
		// width: 28px;  /* Set size for checkboxes */
		// appearance: none; /* Remove default styling */
		// background-color: transparent; /* Background color */
		// cursor: pointer; /* Pointer cursor for better UX */
		// position: relative; /* Relative positioning for pseudo-elements */
	}

	.newThemeCheckbox:focus {
		border: 1px solid #ff5f5f !important;

		// box-shadow: none !important;
		// outline: none !important;
		// border-color: #fd7a7a !important; /* Focus border color */
	}

	.newThemeCheckbox:checked {
		border: 1px solid #ff5f5f !important;

		// background-color: #fd7a7a !important; /* Background color when checked */
		// border: 2px solid #fd7a7a !important; /* Border color when checked */
	}

	.newThemeCheckbox:checked::before {
		border: 1px solid #ff5f5f !important;

		// content: ''; /* Content for the pseudo-element */
		// position: absolute;
		// top: 50%;
		// left: 48%;
		// width: 8px; /* Adjust width of the checkmark */
		// height: 13px; /* Adjust height of the checkmark */
		// border-radius: 1px;
		// border: solid #ffffff; /* Checkmark color */
		// border-width: 0 4px 4px 0; /* Create the checkmark shape */
		// transform: translate(-50%, -50%) rotate(45deg); /* Center and rotate to form checkmark */
		// box-sizing: border-box; /* Ensure border width is included in size */
	}

	.newThemeCheckbox:checked:focus,
	.newThemeCheckbox:checked:active {
		border: 1px solid #ff5f5f !important;

		// border: 2px solid #fd7a7a !important; /* Border color when checked */
		// box-shadow: none !important;
		// outline: none !important;
	}

	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.newThemeCheckbox {
			border: 1px solid #ff5f5f !important;
			height: 1.6em !important;
			width: 1.6em !important;

			// height: 21px; /* Set size for checkboxes */
			// width: 21px;  /* Set size for checkboxes */
		}

		.newThemeCheckbox:checked::before {
			border: 1px solid #ff5f5f !important;

			// width: 6px; /* Adjust width of the checkmark */
			// height: 10px; /* Adjust height of the checkmark */
		}
	}

	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.newThemeCheckbox {
			border: 1px solid #ff5f5f !important;
			height: 1.6em !important;
			width: 1.6em !important;

			// height: 21px; /* Set size for checkboxes */
			// width: 21px;  /* Set size for checkboxes */
		}

		.newThemeCheckbox:checked::before {
			border: 1px solid #ff5f5f !important;

			// width: 6px; /* Adjust width of the checkmark */
			// height: 10px; /* Adjust height of the checkmark */
		}
	}
`;

export default TableComponentStyle;
