import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Header, <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from 'reactstrap';
import { CancelIcon } from '../../Icons';
import NewLoader from '../NewLoader';

const NewModal = ({
	children,
	isOpen,
	toggle,
	centered = true,
	title,
	showFooter = true,
	showCancelButton = true,
	cancelButtonText = 'Cancel',
	submitButtonText = 'Submit',
	handleCancelButtonClick,
	handleSubmitButtonClick,
	submitButtonLoading = false,
	backdrop = 'static',
	className,
	size = 'md',
	style,
	modalLoading = false,
	...props
}) => {
	return (
		<Modal
			isOpen={isOpen}
			toggle={toggle}
			centered={centered}
			className={`newModal ${className ? className : ''}`}
			size={size}
			style={style}
			backdrop={backdrop}
			{...props}
		>
			{title && (
				<ModalHeader toggle={toggle}>
					{title}
					<div className="closeButton" onClick={toggle}>
						<CancelIcon width="100%" height="100%" />
					</div>
				</ModalHeader>
			)}
			<ModalBody>{children}</ModalBody>
			{showFooter && (
				<ModalFooter>
					<div className="submitCancelBtnWrapper">
						{showCancelButton && (
							<button
								type="button"
								className="newModalButtonOutlined"
								onClick={handleCancelButtonClick ? handleCancelButtonClick : toggle}
							>
								{cancelButtonText}
							</button>
						)}
						<button
							type="button"
							className="newModalButtonFilled"
							style={{ opacity: submitButtonLoading ? 0.5 : 1 }}
							onClick={handleSubmitButtonClick}
							disabled={submitButtonLoading}
						>
							{submitButtonText}
							{submitButtonLoading && (
								<div className="newSpinnerWrapper">
									<NewLoader className="newSpinner" color="#ffffff" />
								</div>
							)}
						</button>
					</div>
				</ModalFooter>
			)}
			{modalLoading && (
				<div className="newModalLoaderWrapper">
					<NewLoader />
				</div>
			)}
		</Modal>
	);
};

export default NewModal;
