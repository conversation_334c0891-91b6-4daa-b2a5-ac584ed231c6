import React from 'react';
import { useSelector } from 'react-redux';
import { Navigate } from 'react-router-dom';

import { CommonRoutes } from '../../../../Utils/routes';

const AdvertiserRoute = ({ children }) => {
	const authDetails = useSelector((state) => state.auth);

	if (authDetails?.isLogin) {
		if (authDetails?.login_type === 'advertiser') {
			return children;
		} else {
			return <Navigate to={CommonRoutes?.pageNotFound} />;
		}
	} else {
		return <Navigate to={CommonRoutes?.landingPage} />;
	}
};

export default AdvertiserRoute;
