import { Button } from 'reactstrap';
import CustomModal from '../../Common/Modal/CustomModal';
import CustomButton from '../../Common/CustomButton';

const DeleteConfirmModal = ({
    handleModal,
    isOpen,
    loading,
    handleDelete,
    title = "Delete Ad",
    message = "Are you sure you want to delete this ad?"
}) => {
    return (
        <CustomModal
            isOpen={isOpen}
            handleModal={handleModal}
            title={title}
            size="md"
        >
            <div className="w-100">
                <p className="text-center fs-12 medium-text pa-b-18">
                    {message}
                </p>
                <div className="d-flex" style={{ gap: '12px' }}>
                    <div className="flex-1">
                        <Button
                            className="borderButtonFullWidth"
                            onClick={handleModal}
                        >
                            No
                        </Button>
                    </div>
                    <div className="flex-1">
                        <CustomButton
                            className="themeButtonFullWidth"
                            loading={loading}
                            onClick={handleDelete}
                        >
                            Yes
                        </CustomButton>
                    </div>
                </div>
            </div>
        </CustomModal>
    );
};

export default DeleteConfirmModal;