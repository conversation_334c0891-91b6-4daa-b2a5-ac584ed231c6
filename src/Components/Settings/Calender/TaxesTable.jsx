import React, { useState } from 'react';
import TableStyle from '../../Common/TableStyle';
import TableV6 from '../../Common/TableV6';

const data = [
	{
		id: 1,
		name: 'Export Tax for State Transport',
		tax_amount: '45%'
	},
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		tax_amount: '80%'
	}
];

const TaxesTable = () => {
	const [tableData, setTableData] = useState(data);

	const columns = [
		{
			Header: 'Name',
			accessor: 'name',
			className: 'justify-content-start text-dark',
			filterable: false,
			sortable: false,
			headerClassName:
				'react-table-header-class fs-16 medium-text justify-content-start',
			Cell: ({ row }) => (
				<div className="d-flex flex-column gap-1 fs-16 regular-text pl-8">
					{row.name}
				</div>
			) // Custom cell components!
		},
		{
			Header: 'Tax Amount (%)',
			accessor: 'tax_amount',
			className: 'text-dark',
			filterable: false,
			sortable: false,
			headerClassName: 'react-table-header-class fs-16 medium-text'
		}
	];

	const handleSortBy = (sortBy) => {
		// setParams((prev) => ({ ...prev, sortBy: sortBy[0]?.id || "id", order: sortBy[0]?.desc ? "DESC" : "ASC" }));
	};

	return (
		<TableStyle version={6}>
			<TableV6
				columns={columns}
				data={tableData}
				handleSortBy={handleSortBy}
				key={'master-tax-table'}
			/>
		</TableStyle>
	);
};

export default TaxesTable;
