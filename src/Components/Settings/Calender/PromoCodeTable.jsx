import React, { useState } from 'react';
import TableStyle from '../../Common/TableStyle';
import TableV6 from '../../Common/TableV6';

const data = [
	{
		id: 1,
		name: '50% Off on new orders',
		code: '50NEW',
		discount: '8',
		expiry_date: '2023/02/01',
		status: true
	},
	{
		id: 2,
		name: '20% Flat Off on Referrals',
		code: '20REFR',
		discount: '12',
		expiry_date: '2023/08/01',
		status: true
	},
	{
		id: 3,
		name: 'Flat 10& Cashback on Friend Share',
		code: 'FRND10',
		discount: '3',
		expiry_date: '2023/12/01',
		status: true
	}
];

const PromoCodeTable = () => {
	const [tableData, setTableData] = useState(data);

	const columns = [
		{
			Header: 'Name',
			accessor: 'name',
			className: 'justify-content-start text-dark',
			filterable: false,
			sortable: false,
			minWidth: 400,
			headerClassName:
				'react-table-header-class fs-16 medium-text justify-content-start',
			Cell: ({ row }) => (
				<div className="d-flex flex-column gap-1 fs-16 regular-text pl-8">
					{row.name}
				</div>
			) // Custom cell components!
		},
		{
			Header: 'Code',
			accessor: 'code',
			className: 'text-dark',
			filterable: false,
			sortable: false,
			minWidth: 100,
			headerClassName: 'react-table-header-class fs-16 medium-text'
		},
		{
			Header: 'Discount Amount (%)',
			accessor: 'discount',
			className: 'text-dark',
			filterable: false,
			sortable: false,
			minWidth: 200,
			headerClassName: 'react-table-header-class fs-16 medium-text'
		},
		{
			Header: 'Expiry Date',
			accessor: 'expiry_date',
			className: 'text-dark',
			filterable: false,
			sortable: false,
			minWidth: 150,
			headerClassName: 'react-table-header-class fs-16 medium-text'
			// Cell: ({ row }) => (
			//   format(new Date(row.due_date), 'dd/MM/Y')
			// ), // Custom cell components!
		},
		{
			Header: 'Status',
			accessor: 'status',
			className: 'text-dark',
			filterable: false,
			sortable: false,
			headerClassName: 'react-table-header-class fs-16 medium-text',
			Cell: ({ row }) => (row.status ? 'Active' : 'Inactive') // Custom cell components!
		}
	];

	const handleSortBy = (sortBy) => {
		// setParams((prev) => ({ ...prev, sortBy: sortBy[0]?.id || "id", order: sortBy[0]?.desc ? "DESC" : "ASC" }));
	};

	return (
		<TableStyle version={6}>
			<TableV6
				columns={columns}
				data={tableData}
				handleSortBy={handleSortBy}
				key={'master-promocode-table'}
			/>
		</TableStyle>
	);
};

export default PromoCodeTable;
