import React, { useState } from 'react';
import TableStyle from '../../Common/TableStyle';
import TableV6 from '../../Common/TableV6';

const data = [
	{
		id: 1,
		from_time: '10:00 AM',
		to_time: '04:00 PM'
	}
];

const OpeningHoursTable = () => {
	const [tableData, setTableData] = useState(data);

	const columns = [
		{
			Header: 'From Time',
			accessor: 'from_time',
			className: 'justify-content-start text-dark',
			filterable: false,
			sortable: false,
			headerClassName:
				'react-table-header-class fs-16 medium-text justify-content-start'
		},
		{
			Header: 'To Time',
			accessor: 'to_time',
			className: 'justify-content-start text-dark',
			filterable: false,
			sortable: false,
			headerClassName:
				'react-table-header-class fs-16 medium-text justify-content-start'
		}
	];

	const handleSortBy = (sortBy) => {
		// setParams((prev) => ({ ...prev, sortBy: sortBy[0]?.id || "id", order: sortBy[0]?.desc ? "DESC" : "ASC" }));
	};

	return (
		<TableStyle version={6}>
			<TableV6
				columns={columns}
				data={tableData}
				handleSortBy={handleSortBy}
				key={'master-tax-table'}
			/>
		</TableStyle>
	);
};

export default OpeningHoursTable;
