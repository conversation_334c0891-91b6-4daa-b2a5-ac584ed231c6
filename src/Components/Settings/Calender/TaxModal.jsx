import { useState } from 'react';
import { Button, Col, Row } from 'reactstrap';
import CustomModal from '../../Common/Modal/CustomModal';
import FormDatePicker from '../../Form/FormDatePicker';
import FormInput from '../../Form/FormInput';

const TaxModal = ({ type, isOpen, handleModal }) => {
	const [operationalHours, setOperationalHours] = useState(false);
	return (
		<CustomModal
			title={`${type === 'add' ? 'Add New Tax' : 'Manage Tax'}`}
			size="md"
			isOpen={isOpen}
			handleModal={handleModal}
			autoHeightMin={150}
		>
			<form className="overflow-hidden">
				<Row>
					<Col sm={12}>
						<FormInput
							id="name"
							type="text"
							name="name"
							label="Name"
							placeholder="Enter Name"
						/>
					</Col>
				</Row>
				<Row>
					<Col sm={12}>
						<FormInput
							id="tax_amount"
							type="text"
							name="tax_amount"
							label="Tax Percentage"
							placeholder="Enter Tax Percentage"
						/>
					</Col>
				</Row>
				<Row>
					<Col sm={6}>
						<FormDatePicker
							id="start_date"
							name="start_date"
							label="Start Date"
						/>
					</Col>
					<Col sm={6}>
						<FormDatePicker
							id="end_date"
							name="end_date"
							label="End Date"
						/>
					</Col>
				</Row>
				<Row>
					<Col
						sm={12}
						className="d-flex flex-column align-items-center justify-content-center"
					>
						<Button
							type="button"
							className="fs-18 medium-text themeButtonFullWidth ptb-10 plr-60 mt-10 text-center"
						>
							Save
						</Button>
					</Col>
				</Row>
			</form>
		</CustomModal>
	);
};

export default TaxModal;
