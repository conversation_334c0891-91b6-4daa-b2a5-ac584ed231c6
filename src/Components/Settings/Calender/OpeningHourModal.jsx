import DatePicker from 'react-datepicker';

import { Button, Col, Label, Row } from 'reactstrap';
import CustomModal from '../../Common/Modal/CustomModal';

const OpeningHourModal = ({ type, isOpen, handleModal }) => {
	return (
		<CustomModal
			title={`${
				type === 'add' ? 'Add Opening Hours' : 'Manage Opening Hours'
			}`}
			size="md"
			isOpen={isOpen}
			handleModal={handleModal}
			autoHeightMin={100}
		>
			<form className="overflow-hidden">
				<Row>
					<Col sm={6}>
						<Label className="fs-14 medium-text themeText label-color">
							From Time
						</Label>
						<DatePicker
							placeholderText="HH:MM A"
							popperPlacement="bottom"
							popperProps={{ strategy: 'fixed' }}
							className={`inputBox no-border todoDatePicker border-radius-0 fs-16 medium-text flex-4 zIndex-3`}
							dateFormat="h:mm aa"
							showTimeSelect
							showTimeSelectOnly
						/>
					</Col>
					<Col sm={6}>
						<Label className="fs-14 medium-text themeText label-color">
							To Time
						</Label>
						<DatePicker
							placeholderText="HH:MM A"
							popperPlacement="bottom"
							popperProps={{ strategy: 'fixed' }}
							className={`inputBox no-border todoDatePicker border-radius-0 fs-16 medium-text flex-4 zIndex-3`}
							dateFormat="h:mm aa"
							showTimeSelect
							showTimeSelectOnly
						/>
					</Col>
				</Row>
				<Row>
					<Col
						sm={12}
						className="d-flex flex-column align-items-center justify-content-center"
					>
						<Button
							type="button"
							className="fs-18 medium-text themeButtonFullWidth ptb-10 plr-60 mt-10 text-center"
						>
							Save
						</Button>
					</Col>
				</Row>
			</form>
		</CustomModal>
	);
};

export default OpeningHourModal;
