import { useState } from 'react';
import { useSelector } from 'react-redux';
import { Tab, Tabs } from 'react-tabs-scrollable';
import { Button, TabContent, TabPane } from 'reactstrap';
import CustomModal from '../../Common/Modal/CustomModal';
import { PlusIcon } from '../../Icons';
import TaxModal from './TaxModal';
import OpeningHourModal from './OpeningHourModal';
import OpeningHoursTable from './OpeningHoursTable';
import ModalWrapper from './scheduleModal.style';
import TaxesTable from './TaxesTable';
import PromoCodeTable from './PromoCodeTable';
import PromoCodeModal from '../PromoCode/PromoCodeModal';

const ScheduleModal = ({ type, isOpen, handleModal }) => {
	const allThemeData = useSelector((state) => ({ ...state.themeChanger }));

	const [promoCodeModal, setPromoCodeModal] = useState(false);
	const handlePromoCodeModal = () => {
		setPromoCodeModal(!promoCodeModal);
	};

	const [taxModal, setTaxModal] = useState(false);
	const handleTaxModal = () => {
		setTaxModal((prev) => !prev);
	};

	const [openingHourModal, setOpeningHourModal] = useState(false);
	const handleOpeningHourModal = () => {
		setOpeningHourModal((prev) => !prev);
	};

	const [tabId, setTabId] = useState(0);

	// define an onClick function to bind the value on tab click
	const onTabClick = (e, index) => {
		setTabId(index);
	};

	return (
		<CustomModal
			title={`Your MyTab Venue Calendar Schedule`}
			size="xl"
			isOpen={isOpen}
			handleModal={handleModal}
			autoHeightMin={120}
		>
			<ModalWrapper {...allThemeData}>
				<div className="mt-2 customScrollableNavbar position-relative">
					<Tabs
						activeTab={tabId}
						onTabClick={onTabClick}
						leftBtnIcon={<i className="fa fa-angle-left"></i>}
						rightBtnIcon={<i className="fa fa-angle-right"></i>}
						navBtnsIconColor={'#4F4F4F'}
						className="customScrollableNavbar"
					>
						<Tab
							key={0}
							className={`customScrollableNavItem fs-16 regular-text ${
								tabId === 0 ? 'active' : ''
							}`}
						>
							Promo Codes
						</Tab>
						<Tab
							key={1}
							className={`customScrollableNavItem fs-16 regular-text ${
								tabId === 1 ? 'active' : ''
							}`}
						>
							Taxes
						</Tab>
						<Tab
							key={2}
							className={`customScrollableNavItem fs-16 regular-text ${
								tabId === 2 ? 'active' : ''
							}`}
						>
							Opening Hours
						</Tab>
					</Tabs>
					<div className="borderBottom position-absolute bottom-0 w-100 zIndex-1" />
				</div>
				<div>
					<TabContent activeTab={tabId} className="p-1">
						<TabPane tabId={0}>
							<Button
								type="button"
								className="fs-18 medium-text borderButton ptb-10 plr-20 mt-10"
								onClick={() => setPromoCodeModal(true)}
							>
								<PlusIcon
									height="18"
									width="18"
									className="mr-10"
								/>{' '}
								Add Promo Code
							</Button>
							<div className="pt-3">
								<PromoCodeTable />
							</div>
						</TabPane>
						<TabPane tabId={1}>
							<Button
								type="button"
								className="fs-18 medium-text borderButton ptb-10 plr-20 mt-10"
								onClick={() => setTaxModal(true)}
							>
								<PlusIcon
									height="18"
									width="18"
									className="mr-10"
								/>{' '}
								Add New Tax
							</Button>
							<div className="pt-3">
								<TaxesTable />
							</div>
						</TabPane>
						<TabPane tabId={2}>
							<Button
								type="button"
								className="fs-18 medium-text borderButton ptb-10 plr-20 mt-10"
								onClick={() => setOpeningHourModal(true)}
							>
								<PlusIcon
									height="18"
									width="18"
									className="mr-10"
								/>{' '}
								Add Opening Hours
							</Button>
							<div className="pt-3">
								<OpeningHoursTable />
							</div>
						</TabPane>
					</TabContent>
				</div>
				<PromoCodeModal
					// modalData={modalData}
					isOpen={promoCodeModal}
					handleModal={handlePromoCodeModal}
				/>
				<TaxModal
					// modalData={modalData}
					isOpen={taxModal}
					handleModal={handleTaxModal}
				/>
				<OpeningHourModal
					// modalData={modalData}
					isOpen={openingHourModal}
					handleModal={handleOpeningHourModal}
				/>
			</ModalWrapper>
		</CustomModal>
	);
};

export default ScheduleModal;
