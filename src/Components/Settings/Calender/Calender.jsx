import FullCalendar from '@fullcalendar/react';
import interactionPlugin from '@fullcalendar/interaction';
import dayGridPlugin from '@fullcalendar/daygrid'; // a plugin!
import { useEffect, useRef, useState } from 'react';
import ScheduleModal from './ScheduleModal';

const events = [
	{
		title: 'Promo Codes',
		start: '2023-02-01',
		end: '2024-02-01',
		allDay: true,
		borderColor: '#FFF8F3',
		textColor: '#AB5619',
		backgroundColor: '#FFF8F3'
	},
	{
		title: 'Taxes',
		start: '2023-02-01',
		allDay: true,
		borderColor: '#F8E5E5',
		textColor: '#AE3535',
		backgroundColor: '#F8E5E5'
	},
	{
		title: 'Opening Hours',
		start: '2023-02-01',
		allDay: true,
		borderColor: '#FCE0EA',
		textColor: '#8A0C39',
		backgroundColor: '#FCE0EA'
	},
	{
		title: 'Promo Codes',
		start: '2023-02-06',
		allDay: true,
		borderColor: '#FFF8F3',
		textColor: '#AB5619',
		backgroundColor: '#FFF8F3'
	},
	{
		title: 'Taxes',
		start: '2023-02-10',
		allDay: true,
		borderColor: '#F8E5E5',
		textColor: '#AE3535',
		backgroundColor: '#F8E5E5'
	},
	{
		title: 'Opening Hours',
		start: '2023-02-06',
		allDay: true,
		borderColor: '#FCE0EA',
		textColor: '#8A0C39',
		backgroundColor: '#FCE0EA'
	},
	{
		title: 'Promo Codes',
		start: '2023-02-15',
		allDay: true,
		borderColor: '#FFF8F3',
		textColor: '#AB5619',
		backgroundColor: '#FFF8F3'
	},
	{
		title: 'Taxes',
		start: '2023-02-15',
		allDay: true,
		borderColor: '#F8E5E5',
		textColor: '#AE3535',
		backgroundColor: '#F8E5E5'
	},
	{
		title: 'Opening Hours',
		start: '2023-02-14',
		allDay: true,
		borderColor: '#FCE0EA',
		textColor: '#8A0C39',
		backgroundColor: '#FCE0EA'
	}
];

const Calender = () => {
	const calendarRef = useRef();

	// useEffect(() => {
	//   const calendarApi = calendarRef.current.getApi()
	//   calendarApi.render()
	// }, [])

	const handleDateClick = (date) => {
		setScheduleModal(true);
	};

	const handleEventClick = (info) => {};

	const [scheduleModal, setScheduleModal] = useState(false);

	const handleScheduleModal = () => {
		setScheduleModal(!scheduleModal);
	};

	return (
		<>
			<FullCalendar
				headerToolbar={{
					left: '',
					center: '',
					right: 'prev title next'
				}}
				plugins={[dayGridPlugin, interactionPlugin]}
				initialView="dayGridMonth"
				ref={calendarRef}
				selectable={true}
				dateClick={handleDateClick}
				events={events}
				eventClick={handleEventClick}
			/>
			<ScheduleModal
				isOpen={scheduleModal}
				handleModal={handleScheduleModal}
			/>
		</>
	);
};

export default Calender;
