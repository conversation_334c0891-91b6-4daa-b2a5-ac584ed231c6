import { But<PERSON>, Col, Row } from 'reactstrap';
import CustomModal from '../Common/Modal/CustomModal';
import FormCheckBox from '../Form/FormCheckBox';
import ReactFormSelect from '../Form/ReactFormSelect';

const AssignSubHeadingModal = ({ type, isOpen, handleModal }) => {
	const printerModalOptions = [
		{
			value: '1',
			label: 'Printer 1'
		},
		{
			value: '2',
			label: 'Printer 2'
		},
		{
			value: '3',
			label: 'Printer 3'
		}
	];

	return (
		<CustomModal
			title="Subheading List"
			size="md"
			isOpen={isOpen}
			handleModal={handleModal}
			autoHeightMin={150}
		>
			<form className="overflow-hidden p-1">
				<Row>
					<Col sm={4} className="text-left d-flex pl-35 mb-3">
						<FormCheckBox
							name="status"
							id="status1"
							label="Pizza"
							// onChange={() => setMarkCompleted((prev) => !prev) }
						/>
					</Col>
					<Col sm={8}>
						<ReactFormSelect
							id="printer_modal"
							placeholder="Connected Printers"
							options={printerModalOptions}
							maxMenuHeight={150}
							isSearchable={false}
							// value={}
							// handleChange={handleChange}
						/>
					</Col>
				</Row>
				<Row>
					<Col
						sm={4}
						className="text-left d-flex pl-35 mb-3 align-items-center"
					>
						<FormCheckBox
							name="status"
							id="status2"
							label="Cold Drinks"
							// onChange={() => setMarkCompleted((prev) => !prev) }
						/>
					</Col>
					<Col sm={8}>
						<ReactFormSelect
							id="printer_modal"
							placeholder="Connected Printers"
							options={printerModalOptions}
							maxMenuHeight={150}
							isSearchable={false}
							// value={}
							// handleChange={handleChange}
						/>
					</Col>
				</Row>
				<Row>
					<Col
						sm={4}
						className="text-left d-flex pl-35 mb-3 align-items-center"
					>
						<FormCheckBox
							name="status"
							id="status3"
							label="Fast Food"
							// onChange={() => setMarkCompleted((prev) => !prev) }
						/>
					</Col>
					<Col sm={8}>
						<ReactFormSelect
							id="printer_modal"
							placeholder="Connected Printers"
							options={printerModalOptions}
							maxMenuHeight={150}
							isSearchable={false}
							menuPlacement="top"
							// value={}
							// handleChange={handleChange}
						/>
					</Col>
				</Row>
				<Row>
					<Col
						sm={12}
						className="d-flex align-items-center justify-content-center"
					>
						<Button
							type="button"
							className="fs-18 medium-text themeButtonFullWidth ptb-10 plr-60 mt-10 text-center mb-2"
						>
							save
						</Button>
					</Col>
				</Row>
			</form>
		</CustomModal>
	);
};

export default AssignSubHeadingModal;
