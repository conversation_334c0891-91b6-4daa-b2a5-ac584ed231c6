import React, { useState } from 'react';
import { Button } from 'reactstrap';
import TableStyle from '../Common/TableStyle';
import TableV6 from '../Common/TableV6';
import FormSwitch from '../Form/FormSwitch';
import { PlusIcon } from '../Icons';
import AssignSubHeadingModal from './AssignSubHeadingModal';
import PrinterModal from './PrinterModal';

const data = [
	{
		id: 1,
		name: 'PNT-001 Mark',
		ip_address: '***********',
		status: true
	},
	{
		id: 2,
		name: 'TM-OP65',
		ip_address: '************',
		status: false
	},
	{
		id: 3,
		name: 'TM-OP6-PP',
		ip_address: '***********',
		status: true
	}
];

const DocketPrinting = () => {
	const [type, setType] = useState('add');
	const [tableData, setTableData] = useState(data);
	const [printerModal, setPrinterModal] = useState(false);
	const [assignSubHeadingModal, setAssignSubHeadingModal] = useState(false);

	const columns = [
		{
			Header: 'Printer Name',
			accessor: 'name',
			className: 'justify-content-start text-dark',
			filterable: false,
			sortable: false,
			minWidth: 400,
			headerClassName:
				'react-table-header-class fs-16 medium-text justify-content-start',
			Cell: ({ row }) => (
				<div className="d-flex flex-column gap-1 fs-16 regular-text pl-8">
					{row.name}
				</div>
			) // Custom cell components!
		},
		{
			Header: 'IP Address',
			accessor: 'ip_address',
			className: 'text-dark',
			filterable: false,
			sortable: false,
			minWidth: 200,
			headerClassName: 'react-table-header-class fs-16 medium-text'
		},
		{
			Header: 'Print Docket',
			accessor: 'status',
			className: 'text-center',
			filterable: false,
			sortable: false,
			minWidth: 200,
			headerClassName: 'react-table-header-class fs-16 medium-text',
			Cell: ({ row }) => (
				<FormSwitch
					name="status"
					id="status"
					label={`${row.status === true ? 'Active' : 'Inactive'}`}
					checked={row.status}
					onChange={() => handleStatus(row._original.id)}
				/>
			)
		},
		{
			Header: 'Manage',
			accessor: 'action',
			className: 'text-center',
			headerClassName: 'react-table-header-class fs-16 medium-text',
			sortable: false,
			filterable: false,
			minWidth: 200,
			Cell: ({ row }) => (
				<div
					className="react-action-class"
					onClick={() => setAssignSubHeadingModal(true)}
				>
					<div className="fs-16 semi-bold-text headingTextColor2 cursor-pointer">
						Assign Subheading
					</div>
				</div>
			) // Custom cell components!
		}
	];

	const handleStatus = (id) => {
		const temp = tableData.map((item) =>
			item.id === id ? { ...item, status: !item.status } : { ...item }
		);
		setTableData([...temp]);
	};

	const handleSortBy = (sortBy) => {
		// setParams((prev) => ({ ...prev, sortBy: sortBy[0]?.id || "id", order: sortBy[0]?.desc ? "DESC" : "ASC" }));
	};

	const handlePrinterModal = () => {
		setPrinterModal((prev) => !prev);
	};

	const handleAssignSubHeadingModal = () => {
		setAssignSubHeadingModal((prev) => !prev);
	};

	return (
		<div className="d-flex flex-column">
			<div className="d-flex flex-column align-items-center flex-md-row justify-content-between mtb-10 mb-15">
				<p className="fs-20 semi-bold-text">Network Printers</p>
				<Button
					type="button"
					className="fs-18 medium-text themeBorderButton ptb-10 plr-20 mt-10"
					onClick={() => setPrinterModal(true)}
				>
					<PlusIcon height="18" width="18" className="mr-10" /> Add
					Printer
				</Button>
			</div>
			<div className="roe-card-body">
				<TableStyle version={6}>
					<TableV6
						columns={columns}
						data={tableData}
						handleSortBy={handleSortBy}
						key={'master-tax-table'}
					/>
				</TableStyle>
			</div>
			<PrinterModal
				type={type}
				// modalData={modalData}
				isOpen={printerModal}
				handleModal={handlePrinterModal}
			/>
			<AssignSubHeadingModal
				type={type}
				// modalData={modalData}
				isOpen={assignSubHeadingModal}
				handleModal={handleAssignSubHeadingModal}
			/>
		</div>
	);
};

export default DocketPrinting;
