import styled from 'styled-components';

const NewVenueProfileFormWrapper = styled.div`
	.checkBoxInput {
		width: 270px;
		height: 36px;
		display: flex;
		align-items: center;
		justify-content: start;
		padding-inline: 6px;
		color: #313132eb !important;
		background-color: #ffffff;
		font-family: 'nunitosans-regular';
		font-size: 16px;
		border: 0.6px solid #31313259 !important;
		outline: none;
		border-radius: 2px !important;
		@media (max-width: 600px) {
			font-size: 10px;
			height: 27px;
			width: 200px;
		}
		@media only screen and (min-width: 601px) and (max-width: 960px) {
			font-size: 10px;
			height: 27px;
			width: 200px;
		}
		@media only screen and (min-width: 961px) and (max-width: 1824px) and (max-height: 933px) {
			font-size: 12px;
			height: 30px;
			width: 202.5px;
		}
	}

	.containerClass {
		height: 210px !important;
		border-radius: 2px !important;
		border: 0.6px solid #31313259 !important;
		background-color: #FFFFFF;
		padding: 0.5em !important;
		@media (max-width: 600px) {
			height: 165px !important;
			border-radius: 2px !important;
		}
		@media only screen and (min-width: 601px) and (max-width: 960px) {
			height: 165px !important;
			border-radius: 2px !important;
		}
		@media only screen and (min-width: 961px) and (max-width: 1824px) and (max-height: 933px) {
			height: 155px !important;
			border-radius: 2px !important;
		}
	}
		.checkBoxCursor{
			cursor: pointer;
		}

		.isAlcoholCheckBoxInput {
		width: 100%;
		height: 65px;
		display: flex;
		align-items: center;
		justify-content: start;
		padding-inline: 16px;
		color: #313132eb !important;
		background-color: #ffffff;
		font-family: 'nunitosans-regular';
		font-size: 18px;
		border: 0.6px solid #31313259 !important;
		outline: none;
		border-radius: 2px !important;
		@media (max-width: 600px) {
			font-size: 12px;
			height: 45px;
		}
		@media only screen and (min-width: 601px) and (max-width: 960px) {
			font-size: 12px;
			height: 45px;
		}
		@media only screen and (min-width: 961px) and (max-width: 1824px) and (max-height: 933px) {
			font-size: 13.5px;
			height: 48.75px;
		}
	}

	.subTitleWrap {
		display: flex;
		padding-bottom: 2px;
		font-size: 20px;
		font-family: nunitosans-bold;
		color: #414142;
		@media (max-width: 600px) {
			font-size: 12px;
		}
		@media only screen and (min-width: 601px) and (max-width: 960px) {
			font-size: 13px;
		}
		@media only screen and (min-width: 961px) and (max-width: 1824px) and (max-height: 933px) {
			font-size: 15px;
		}
	}

	.disabledCheckbox {
		background: #f8f8f8;
	}
`;

export default NewVenueProfileFormWrapper;
