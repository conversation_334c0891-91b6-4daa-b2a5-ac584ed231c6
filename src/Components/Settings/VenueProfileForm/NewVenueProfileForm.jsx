import React, { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { Row, Col } from 'reactstrap';
import { toast } from 'react-toastify';
import { useFormik } from 'formik';
import { FilledButton } from '../../../Components/Layout/Buttons';
import { VenueApiRoutes, VenuePanelRoutes } from '../../../Utils/routes';
import Api from '../../../Helper/Api';
import { useDispatch } from 'react-redux';
import * as yup from 'yup';

import * as validation from '../../../Helper/YupValidation';
import NewVenueProfileFormWrapper from './newVenueProfileForm.style';
import NewDesignFormCheckBox from '../../Form/NewDesignFormCheckBox';
import { OpeningHoursModalDetails } from '../../UserProfile/ConnectVenue/utils';
import authActions from '../../../Redux/auth/actions';
import MapModal from './MapModal';
import NewDesignFormInput from '../../Form/NewDesignFormInput';
import NewImageUpload from '../../Form/NewImageUpload';
import NewDesignFormMobileNoInput from '../../Form/newDesignFormMobileNoInput';
import NewDesignCustomLabel from '../../Form/NewDesignCustomLabel';
import ServiceTypeConfirmModal from '../../../Components/Settings/ServiceType/ServiceTypeConfirmModal';

import { PopupButton } from '@typeform/embed-react';
import { useNavigate } from 'react-router-dom';
import NewPageTitle from '../../Common/NewPageTitle';
import NewPageWrapper from '../../Common/NewPageWrapper';
import { CustomerWrraper } from '../../../Pages/Customer/customer.style';

const NewVenueProfileForm = () => {
	const ref = useRef()
	const openPopup = () => ref.current?.open()
	const state = useSelector((state) => ({ ...state }));
	const allThemeData = state.themeChanger;
	const authData = state.auth;
	const bars = authData?.bars;
	const navigate = useNavigate();
	const dispatch = useDispatch();
	const [openingHoursModalData, setOpeningHoursModalData] = useState(
		OpeningHoursModalDetails
	);
	const [isOpenMapModal, setIsOpenMapModal] = useState(false);
	const [isAlcohol, setIsAlcohol] = useState('No');
	const [loading, setLoading] = useState(false);
	const [typeFormId, setTypeFormId] = useState('');
	const [saveLoading, setSaveLoading] = useState(false);
	const [abnAcnValid, setAbnAcnValid] = useState(null);
	const [serviceTypeConfirmModal, setServiceTypeConfirmModal] =
		useState(false);
	const [serviceTypeModalData, setServiceTypeModalData] = useState({
		type: '',
		content: ''
	});
	const [confirmAction, setConfirmAction] = useState(0);

	const validationSchema = yup.object().shape({
		image: validation.YUP_VALIDATION.IMAGE,
		venueName: validation.YUP_VALIDATION.VENUE_NAME,
		ownerManagerName: validation.YUP_VALIDATION.OWNER_MANAGER_NAME,
		email: validation.YUP_VALIDATION.EMAIL,
		countryCode: validation.YUP_VALIDATION.COUNTRY_CODE,
		address: validation.YUP_VALIDATION.ADDRESS,
		abnAcn: validation.YUP_VALIDATION.ABN_ACN_NUMBER,
		isAlcohol: validation.YUP_VALIDATION.IS_ALCOHOL,
		licenseNumber: isAlcohol === 'Yes' && validation.YUP_VALIDATION.LICENSE_NUMBER,
		// serviceType: validation.YUP_VALIDATION.SERVICE_TYPE
	});

	useEffect(() => {
		getFormData();
	}, [authData?.selectedVenue?.id]);

	const handleMapModal = () => {
		setIsOpenMapModal((prev) => !prev);
	};

	const handleServiceTypeConfirmModal = () => {
		setServiceTypeConfirmModal((prev) => !prev);
		getFormData(true);
	};

	const handleDeleteConfirmModal = () => {
		openPopup();
	};

	useEffect(() => {
		if (typeFormId !== '') {
			handleDeleteConfirmModal();
		}
	}, [typeFormId]);

	const handleReadyTypeForm = () => {
		console.log('Typeform is ready');
	};

	const handleSubmitTypeForm = (event) => {
		console.log('Typeform has been submitted');
		setTypeFormId('');
		const { formId, responseId } = event;
		if (formId) {
			ref.current?.close();
			handleVenueDelete();
		}
	};

	const handleCloseTypeForm = () => {
		setTypeFormId('');
		console.log('Typeform has been closed');
		if (ref.current) {
			ref.current = null;
		}
	};

	const handleErrorTypeForm = (error) => {
		console.error('An error occurred:', error);
	};

	const handleVenueDelete = async () => {
		try {
			let barId = authData?.selectedVenue?.id;
			const res = await Api('DELETE', VenueApiRoutes.delete, {
				bar_id: authData?.selectedVenue?.id?.toString()
			});
			if (res?.data?.status) {
				navigate(VenuePanelRoutes.connectVenue);
				let updatedBars = [];
				if (bars?.length > 0) {
					bars?.forEach((item) => {
						if (item?.id != barId) {
							updatedBars?.push(item);
						}
					});
				}
				if (updatedBars?.length > 0) {
					dispatch(authActions.set_selected_venue(updatedBars[0]));
				} else {
					dispatch(authActions.set_selected_venue(null));
				}
				dispatch(authActions.update_venue_list(updatedBars));
				toast.success(res?.data?.message);
			} else {
				toast.error(res?.data?.message);
			}
		} catch (err) {
			setLoading(false);
			if (err?.response?.data?.message) {
				toast.error(err?.response?.data?.message);
			}
		}
	};

	const getDeleteTypeFormId = async () => {
		setLoading(true);
		try {
			const res = await Api(
				'POST',
				VenueApiRoutes.getAccountDeleteTypeForm,
				{
					bar_id: authData?.selectedVenue?.id
				}
			);
			if (res?.data?.status) {
				const link = res?.data?.data?.link;
				setTypeFormId(link);
			} else {
				toast.error(res?.data?.message);
			}
		} catch (err) {
			setLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	const getFormData = async (updateSelectedVenue = false) => {
		setLoading(true);
		try {
			const res = await Api(
				'POST',
				VenueApiRoutes.getSingleConnectedVenue,
				{
					bar_id: authData?.selectedVenue?.id
				}
			);
			setLoading(false);
			let venueData = res?.data?.data;
			if (res?.data?.status) {
				setValues({
					image: venueData?.avatar,
					venueName: venueData?.restaurantName,
					ownerManagerName: venueData?.managerName,
					email: venueData?.email,
					countryCode: venueData?.countryCode,
					address: venueData?.address,
					abnAcn: venueData?.businessRegisterId,
					isAlcohol: venueData?.isVenueServeAlcohol,
					licenseNumber: venueData?.liquorLicenseNumber,
					mobile: venueData?.mobile,
					// serviceType: venueData?.serviceType,
					serviceType:
						venueData?.serviceType === 'BOTH'
							? ['PICKUP', 'TABLE']
							: [`${venueData?.serviceType}`],
					latitude: venueData?.latitude,
					longitude: venueData?.longitude
				});
				let openingHoursData = venueData?.operating_hours?.map(
					(item) => {
						return {
							id: item?.id,
							weekDay: item?.weekDay,
							activeHours: item?.openingHours,
							inActiveHours: item?.closingHours,
							isClosed: item?.isClosed
						};
					}
				);
				setAbnAcnValid(validateIdentifier(venueData?.businessRegisterId));
				setOpeningHoursModalData(openingHoursData);
				setIsAlcohol(venueData?.isVenueServeAlcohol);
				if (updateSelectedVenue) {
					dispatch(
						authActions.update_selected_venue({
							avatar: venueData?.avatar,
							restaurantName: venueData?.restaurantName,
							managerName: venueData?.managerName
						})
					);
				}
			} else {
				toast.error(res?.data?.message);
			}
		} catch (err) {
			setLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	const submitFormHandler = async (values) => {
		const payload = {
			venue_name: values?.venueName,
			email: values?.email,
			country_code: values?.countryCode,
			manager_name: values?.ownerManagerName,
			venue_address: values?.address,
			business_register_id: values?.abnAcn,
			is_venue_serve_alcohol: values?.isAlcohol,
			liquor_license_number: values?.licenseNumber,
			image: values?.image,
			mobile: values?.mobile,
			confirm_change_flag: confirmAction,
			service_type: values?.serviceType?.includes('PICKUP') && values?.serviceType?.includes('TABLE') ? 'BOTH' : values?.serviceType,
			// values?.serviceType === 'BOTH'
			// 	? ['PICKUP', 'TABLE']
			// 	: [`${values?.serviceType}`],
			bar_id: authData?.selectedVenue?.id,
			latitude: values?.latitude,
			longitude: values?.longitude
		};
		if (isAlcohol === 'No') {
			delete payload.liquor_license_number;
		}
		let formData = new FormData();
		for (let key in payload) {
			formData.append(key, payload[key]);
		}
		for (let i = 0; i < openingHoursModalData?.length; i++) {
			formData.append(
				`operating_hours[${i}][id]`,
				openingHoursModalData[i]?.id
			);
			formData.append(
				`operating_hours[${i}][opening_hours]`,
				openingHoursModalData[i]?.activeHours
			);
			formData.append(
				`operating_hours[${i}][closing_hours]`,
				openingHoursModalData[i]?.inActiveHours
			);
			formData.append(
				`operating_hours[${i}][is_closed]`,
				openingHoursModalData[i]?.isClosed
			);
		}
		try {
			setSaveLoading(true);
			const res = await Api('PUT', VenueApiRoutes.editVenue, formData);
			setSaveLoading(false);
			if (res?.data?.status) {
				if (res?.data?.data?.popUpFlag === 1) {
					setServiceTypeModalData({
						type: payload.service_type,
						content: res?.data?.message
					});
					setServiceTypeConfirmModal(true);
				} else {
					await getFormData(true);
					setConfirmAction(0);
					toast.success(res?.data?.message);
				}
			} else {
				toast.error(res?.data?.message);
			}
		} catch (err) {
			setSaveLoading(false);
			if (err?.response?.data?.message) {
				toast.error(err?.response?.data?.message);
			}
		}
	};

	function validateIdentifier(identifier) {
		// Remove spaces
		const cleanIdentifier = identifier.replace(/\s/g, '');

		// Check length for ABN (11 digits) or ACN (9 digits)
		if (cleanIdentifier.length === 11) {
			return validateAbn(cleanIdentifier);
		} else if (cleanIdentifier.length === 9) {
			return validateAcn(cleanIdentifier);
		} else {
			return false; // Invalid length
		}
	}

	function validateAbn(abn) {
		if (abn.length !== 11 || isNaN(abn)) {
			return false;
		}

		const weighting = [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19];
		const firstDigit = parseInt(abn.charAt(0), 10);

		if (isNaN(firstDigit)) {
			return false;
		}

		const firstDigitProcessed = firstDigit - 1;
		let weightedSum = firstDigitProcessed * weighting[0];

		for (let i = 1; i < abn.length; i++) {
			const digit = parseInt(abn.charAt(i), 10);
			if (isNaN(digit)) {
				return false;
			}
			weightedSum += digit * weighting[i];
		}

		return weightedSum % 89 === 0;
	}

	function validateAcn(acn) {
		const cleanAcn = acn.replace(/\s/g, '');

		if (cleanAcn.length !== 9 || isNaN(cleanAcn)) {
			return false;
		}

		const weighting = [8, 7, 6, 5, 4, 3, 2, 1];
		let weightedSum = 0;

		for (let i = 0; i < cleanAcn.length - 1; i++) {
			const digit = parseInt(cleanAcn.charAt(i), 10);
			if (isNaN(digit)) {
				return false;
			}
			weightedSum += digit * weighting[i];
		}

		const checkDigit = parseInt(cleanAcn.charAt(cleanAcn.length - 1), 10);
		if (isNaN(checkDigit)) {
			return false;
		}

		const remainder = (10 - (weightedSum % 10)) % 10;

		return checkDigit === remainder;
	}

	const {
		handleChange,
		handleBlur,
		setFieldValue,
		handleSubmit,
		values,
		touched,
		errors,
		setValues
	} = useFormik({
		initialValues: {
			image: null,
			venueName: '',
			ownerManagerName: '',
			email: '',
			countryCode: '+61',
			mobile: '',
			address: '',
			abnAcn: '',
			isAlcohol: 'No',
			licenseNumber: '',
			serviceType: [],
			latitude: '',
			longitude: ''
		},
		validationSchema,
		onSubmit: submitFormHandler
	});

	return (
		<NewPageWrapper>
			<CustomerWrraper>
				<NewVenueProfileFormWrapper {...allThemeData}>
					<form onSubmit={handleSubmit} className="overflow-hidden">
						<div className="titleWrap">
							<div className="headerClass">
								<NewPageTitle>Venue Profile</NewPageTitle>
								<div className="saveCancelBtnWrapper">
									<FilledButton
										onClick={handleSubmit}
										loading={saveLoading}
										buttonText={'Save'}
										background={'#caeee9'}
										color={'#00B69B'}
										style={{ width: '160px', heigth: '3.8em', border: '1px solid #00B69B' }}
									/>
									<FilledButton
										onClick={() => getDeleteTypeFormId()}
										deleteLoading={loading}
										buttonText={'Delete Venue Account'}
										background={'#fad5d2'}
										color={'#EF3826'}
										style={{ width: '160px', heigth: '3.8em', border: '1px solid #F95C69' }}
									/>
								</div>
							</div>
							<p className="subTitleWrap">
								Owner/Manager Details
							</p>
							<div>
								<Row>
									<Col>
										<NewDesignCustomLabel
											id={'ownerManagerName'}
											label={'Name'}
											tootlTipMessage={`Please enter the owner or manager's name`}
											showRequired
										/>
										<NewDesignFormInput
											type="text"
											name="ownerManagerName"
											placeholder="Enter Manager Name"
											value={values?.ownerManagerName}
											onChange={handleChange}
											onBlur={handleBlur}
											error={
												touched?.ownerManagerName &&
												!!errors?.ownerManagerName
											}
											errorMsg={errors?.ownerManagerName}
										/>
									</Col>
									<Col>
										<NewDesignCustomLabel
											id={'mobileNumber'}
											label={'MOBILE NUMBER'}
											tootlTipMessage={`Please enter the owner or manager's mobile number`}
											showRequired
										/>
										<NewDesignFormMobileNoInput
											name="mobile"
											placeholder="Enter Mobile Number"
											mobileNoValue={values?.mobile}
											onMobileNoChange={handleChange}
											style={{ fontSize: '16px' }}
											onMobileNoBlur={handleBlur}
											countryCodeValue={values?.countryCode}
											onSelectCountry={(_, countryDetails) => {
												setFieldValue(
													'countryCode',
													countryDetails.dialCode
												);
											}}
											error={
												(touched?.mobile &&
													!!errors?.mobile) ||
												(touched?.countryCode &&
													!!errors?.countryCode)
											}
											errorMsg={
												errors?.mobile || errors?.countryCode
											}
										/>
									</Col>
								</Row>
							</div>
							<p className="subTitleWrap pt-12">
								Venue Details
							</p>
							<div>
								<Row style={{ '--bs-gutter-x': '18px' }}>
									<Col className="pa-b-10">
										<NewDesignCustomLabel
											id={'image'}
											label={'VENUE PROFILE PHOTO'}
											tootlTipMessage={`Please add a photo of the front of your venue`}
											showRequired
										/>
										<NewImageUpload
											name={'image'}
											icon={
												<></>
											}
											// onChange={handleChange}
											// onBlur={handleBlur}
											venueProfile={true}
											handleReset={() => setFieldValue('image', null)}
											value={values?.image}
											error={
												touched?.image &&
												!!errors?.image
											}
											errorMsg={errors?.image}
											setFieldValue={setFieldValue}
											containerClass='containerClass'
										/></Col>
									<Col className="pa-b-10 d-flex flex-column justify-content-between">
										<div>

											<NewDesignCustomLabel
												id={'venueName'}
												label={'VENUE NAME'}
												tootlTipMessage={`Please enter the name of your venue`}
												showRequired
											/>
											<NewDesignFormInput
												type="text"
												name="venueName"
												placeholder="Enter venue name"
												value={values?.venueName}
												onChange={handleChange}
												onBlur={handleBlur}
												error={
													touched?.venueName &&
													!!errors?.venueName
												}
												errorMsg={errors?.venueName}
											/>
										</div>
										<div>
											<NewDesignCustomLabel
												id={'email'}
												label={'VENUE EMAIL ADDRESS'}
												tootlTipMessage={`Please enter email address`}
												showRequired
											/>
											<NewDesignFormInput
												type="text"
												name="email"
												placeholder="Enter email address"
												value={values?.email}
												onChange={handleChange}
												onBlur={handleBlur}
												error={
													touched?.email &&
													!!errors?.email
												}
												errorMsg={errors?.email}
											/>
										</div>
									</Col>
								</Row>
							</div>
							<div>
								<Row>
									<Col className="pa-b-12">
										<div>
											<NewDesignCustomLabel
												id={'address'}
												label={'VENUE ADDRESS'}
												tootlTipMessage={`Please enter the address of your venue`}
												showRequired
											/>
											<NewDesignFormInput
												type="text"
												name="address"
												placeholder="Enter venue address"
												value={values?.address}
												readOnly={true}
												onChange={handleChange}
												onBlur={handleBlur}
												error={
													touched?.address &&
													!!errors?.address
												}
												errorMsg={errors?.address}
												onClick={() => setIsOpenMapModal(true)}
											/>
										</div>
									</Col>
								</Row>
							</div>
							<div>
								<Row>
									<Col className="pa-b-10">
										<div>
											<NewDesignCustomLabel
												id={'abnAcn'}
												label={'VENUE ABN/ACN DETAILS'}
												tootlTipMessage={`Please enter the valid ABN/ACN details of your business`}
												showRequired
											/>
											<NewDesignFormInput
												type="text"
												name="abnAcn"
												placeholder="Enter ABN/ACN details"
												suggestionMatcher
												inputSuggestionMatched={abnAcnValid}
												value={values?.abnAcn}
												onChange={(e) => {
													handleChange(e);
													let abnOrAcnNumberFlag = validateIdentifier(e.target.value);
													setAbnAcnValid(abnOrAcnNumberFlag);
												}}
												onBlur={handleBlur}
												error={
													abnAcnValid !== null
														? !abnAcnValid
														: touched?.abnAcn && !!errors?.abnAcn
												}
												errorMsg={
													errors?.abnAcn ??
													(abnAcnValid !== null
														? !abnAcnValid && 'Invalid ABN/ACN'
														: '')
												}
											/>
										</div>
									</Col>
									<Col className="pa-b-10">
										<div>
											<NewDesignCustomLabel
												id={'timeZone'}
												label={'TIME ZONE'}
											/>
											<NewDesignFormInput
												type="text"
												name="timeZone"
												placeholder='Perth WA, Australia (AWST)'
												disabled
											/>
										</div>
									</Col>
								</Row>
							</div>
							<div>
								<NewDesignCustomLabel
									id={'serviceType'}
									label={'SELECT YOUR SERVICE TYPE/S'}
									tootlTipMessage={`Please select the service type/s your venue offers on MyTab`}
									showRequired
								/>
							</div>
							<div className='d-flex gap-3'>
								<div className="pa-b-10">
									<div className='checkBoxInput'>
										<NewDesignFormCheckBox
											label={'Pick Up (Collect Order)'}
											style={{ height: '1.1em', width: '1.1em' }}
											labelStyle={{ fontFamily: 'nunitosans-medium' }}
											name="serviceType"
											value={'PICKUP'}
											checked={values?.serviceType?.includes(
												'PICKUP'
											)}
											onChange={handleChange}
										/>
									</div>
								</div>
								<div className="pa-b-10">
									<div className='checkBoxInput'>
										<NewDesignFormCheckBox
											label={'Table Service'}
											style={{ height: '1.1em', width: '1.1em' }}
											labelStyle={{ fontFamily: 'nunitosans-medium' }}
											name="serviceType"
											value={'TABLE'}
											checked={values?.serviceType?.includes(
												'TABLE'
											)}
											onChange={handleChange}
										/>
									</div>
								</div>
								<div className="pa-b-10">
									<div className='checkBoxInput disabledCheckbox'>
										<NewDesignFormCheckBox
											label={'Catering (coming soon)'}
											style={{ height: '1.1em', width: '1.1em' }}
											labelStyle={{ fontFamily: 'nunitosans-medium' }}
											name="serviceType"
											value={'TABLE'}
											disabled
										// checked={values?.serviceType?.includes(
										// 	'TABLE'
										// )}
										// onChange={handleChange}
										/>
									</div>
								</div>
								<div className="pa-b-10">
									<div className='checkBoxInput disabledCheckbox'>
										<NewDesignFormCheckBox
											label={'Room Service (coming soon)'}
											style={{ height: '1.1em', width: '1.1em' }}
											labelStyle={{ fontFamily: 'nunitosans-medium' }}
											name="serviceType"
											value={'TABLE'}
											disabled
										// checked={values?.serviceType?.includes(
										// 	'TABLE'
										// )}
										// onChange={handleChange}
										/>
									</div>
								</div>
							</div>
							<div>
								<Row>
									<Col lg={6} className="pa-b-10">
										<Row>
											<div>
												<NewDesignCustomLabel
													id={'isAlcohol'}
													label={'WILL YOUR VENUE BE SERVING ALCOHOL?'}
												/>
											</div>
											<Col className="pa-b-10">
												<div className='isAlcoholCheckBoxInput'>
													<NewDesignFormCheckBox
														label={'Yes'}
														style={{ height: '1.2em', width: '1.2em' }}
														labelStyle={{ fontFamily: 'nunitosans-medium' }}
														name="isAlcohol"
														value={'Yes'}
														checked={values?.isAlcohol?.includes(
															'Yes'
														)}
														onChange={() => { handleChange({ target: { name: 'isAlcohol', value: 'Yes' } }); setIsAlcohol('Yes'); }}
													/>
												</div>
											</Col>
											<Col className="pa-b-10">
												<div className='isAlcoholCheckBoxInput'>
													<NewDesignFormCheckBox
														label={'No'}
														style={{ height: '1.2em', width: '1.2em' }}
														labelStyle={{ fontFamily: 'nunitosans-medium' }}
														name="isAlcohol"
														value={'No'}
														checked={values?.isAlcohol?.includes(
															'No'
														)}
														onChange={() => { handleChange({ target: { name: 'isAlcohol', value: 'No' } }); setIsAlcohol('No'); }}
													/>
												</div>
											</Col>
										</Row>
									</Col>
									{isAlcohol === 'Yes' && (
										<Col className="pa-b-10">
											<div>
												<NewDesignCustomLabel
													id={'licenseNumber'}
													label={'LIQUOR LICENSE NUMBER'}
													tootlTipMessage={`Please enter the valid Liquor Licence number of your business`}
													showRequired
												/>
												<NewDesignFormInput
													type="text"
													name="licenseNumber"
													placeholder="Enter Liquor License Number"
													value={values?.licenseNumber}
													onChange={handleChange}
													onBlur={handleBlur}
													error={
														touched?.licenseNumber &&
														!!errors?.licenseNumber
													}
													errorMsg={errors?.licenseNumber}
													disabled={!values?.isAlcohol || values?.isAlcohol === 'No'}
												/>
											</div>
										</Col>
									)}
								</Row>
							</div>
						</div>
					</form>
					<ServiceTypeConfirmModal
						isOpen={serviceTypeConfirmModal}
						handleModal={handleServiceTypeConfirmModal}
						handleConfirm={() => { setConfirmAction(1); handleSubmit() }}
						modalData={serviceTypeModalData}
					/>
					{isOpenMapModal && (
						<MapModal
							isOpen={isOpenMapModal}
							handleModal={handleMapModal}
							setFieldValue={setFieldValue}
						/>
					)}
					{typeFormId && <PopupButton
						id={typeFormId}
						style={{
							position: 'absolute',
							opacity: 0,
							pointerEvents: 'none',
						}}
						embedRef={ref}
						onReady={handleReadyTypeForm}
						onSubmit={handleSubmitTypeForm}
						onClose={handleCloseTypeForm}
						onError={handleErrorTypeForm}
					>
					</PopupButton>}
				</NewVenueProfileFormWrapper>
			</CustomerWrraper>
		</NewPageWrapper>
	);
};

export default NewVenueProfileForm;