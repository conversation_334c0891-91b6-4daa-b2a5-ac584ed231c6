import React from 'react';
import { Col, Row } from 'reactstrap';
import FormSwitch from '../Form/FormSwitch';

const Permission = () => {
	return (
		<div className="d-flex flex-column mt-3">
			<div>
				<p className="fs-20 sem-bold-text fw-bold themeText">
					Manage Access Rights
				</p>
			</div>
			<div className="mt-32">
				<Row>
					<Col sm={4}>
						<div className="permissionCard d-flex align-items-center justify-content-between p-3">
							<p className="fs-18 semi-bold-text text-dark">
								Manage Menu
							</p>
							<FormSwitch
								name="status"
								id="status"
								// checked={row.status}
								// onChange={() => handleStatus(row._original.id)}
							/>
						</div>
					</Col>
					<Col sm={4}>
						<div className="permissionCard d-flex align-items-center justify-content-between p-3">
							<p className="fs-18 semi-bold-text text-dark">
								Order History
							</p>
							<FormSwitch
								name="status"
								id="status"
								// checked={row.status}
								// onChange={() => handleStatus(row._original.id)}
							/>
						</div>
					</Col>
					<Col sm={4}>
						<div className="permissionCard d-flex align-items-center justify-content-between p-3">
							<p className="fs-18 semi-bold-text text-dark">
								Pick Up Locations
							</p>
							<FormSwitch
								name="status"
								id="status"
								// checked={row.status}
								// onChange={() => handleStatus(row._original.id)}
							/>
						</div>
					</Col>
				</Row>
				<Row>
					<Col sm={4}>
						<div className="permissionCard d-flex align-items-center justify-content-between p-3">
							<p className="fs-18 semi-bold-text text-dark">
								Reports
							</p>
							<FormSwitch
								name="status"
								id="status"
								// checked={row.status}
								// onChange={() => handleStatus(row._original.id)}
							/>
						</div>
					</Col>
					<Col sm={4}>
						<div className="permissionCard d-flex align-items-center justify-content-between p-3">
							<p className="fs-18 semi-bold-text text-dark">
								Pick Up Locations - Bar
							</p>
							<FormSwitch
								name="status"
								id="status"
								// checked={row.status}
								// onChange={() => handleStatus(row._original.id)}
							/>
						</div>
					</Col>
					<Col sm={4}>
						<div className="permissionCard d-flex align-items-center justify-content-between p-3">
							<p className="fs-18 semi-bold-text text-dark">
								Pick Up Locations - Kitchen
							</p>
							<FormSwitch
								name="status"
								id="status"
								// checked={row.status}
								// onChange={() => handleStatus(row._original.id)}
							/>
						</div>
					</Col>
				</Row>
			</div>
		</div>
	);
};

export default Permission;
