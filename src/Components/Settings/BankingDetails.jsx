import React from 'react';
import { <PERSON><PERSON>, Col, Row } from 'reactstrap';

import { BankIcon, ThreeDotIcon } from '../Icons';

const BankingDetails = () => {
	return (
		<div className="d-flex flex-column">
			<div className="d-flex justify-content-center justify-content-md-end mtb-10 mb-15">
				<Button
					type="button"
					className="fs-18 medium-text themeBorderButton ptb-10 plr-20 mt-10"
				>
					Update Stripe Profile
				</Button>
			</div>
			<div className="roe-card-body plr-30">
				<Row>
					<Col
						md={3}
						sm={12}
						className="bank-card d-flex justify-content-between gap-4 p-3"
					>
						<div className="d-flex align-items-center ">
							<BankIcon height={40} width={40} />
						</div>
						<div className="d-flex flex-column gap-2 flex-1">
							<p className="fs-18 semi-bold-text text-dark">
								HDF Bank
							</p>
							<p className="fs-16 regular-text  text-dark">
								xxxx xxxx xxxx 2652
							</p>
							<p className="fs-14 regular-text city">Australia</p>
						</div>
						<div className="d-flex align-items-start justify-content-start">
							<ThreeDotIcon
								height={32}
								width={32}
								fill={'#4F4F4F'}
							/>
						</div>
					</Col>
				</Row>
			</div>
		</div>
	);
};

export default BankingDetails;
