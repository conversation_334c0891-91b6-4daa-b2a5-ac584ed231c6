import React from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON> } from 'reactstrap';
import { useFormik } from 'formik';
import * as yup from 'yup';

import * as validation from '../../Helper/YupValidation';
import FormInput from '../Form/FormInput';

const ContactUs = () => {
	const validationSchema = yup.object().shape({
		name: validation.YUP_VALIDATION.FULL_NAME,
		email: validation.YUP_VALIDATION.EMAIL,
		message: validation.YUP_VALIDATION.MESSAGE
	});

	const submitFormHandler = () => {
		// const payload = {
		//   old_password: changePasswordFormik.values.currentPassword,
		//   new_password: changePasswordFormik.values.newPassword
		// };
		// Api("PUT", "auth/change-password", params)
		//   .then(response => {
		//     if (response.data.status) {
		//       setTimeout(() => {
		//         // Api("PUT","auth/change-password",params)
		//         navigate("/login");
		//       }, 500);
		//     } else {
		//     }
		//   })
		//   .catch(error => {
		//   });
	};

	const changePasswordFormik = useFormik({
		initialValues: {
			name: '',
			email: '',
			message: ''
		},
		validationSchema,
		onSubmit: submitFormHandler
	});

	return (
		<div className="mt-3">
			<form onSubmit={changePasswordFormik.handleSubmit}>
				<Row>
					<Col sm={6}>
						<FormInput
							id="name"
							type="text"
							name="name"
							label="Name"
							placeholder="Enter Name"
							value={changePasswordFormik.values.name}
							onChange={changePasswordFormik.handleChange}
							error={
								changePasswordFormik.touched.name &&
								!!changePasswordFormik.errors.name
							}
							errorMsg={changePasswordFormik.errors.name}
						/>
					</Col>
					<Col sm={6}>
						<FormInput
							id="email"
							type="email"
							name="email"
							label="Email"
							placeholder="Enter Email"
							value={changePasswordFormik.values.email}
							onChange={changePasswordFormik.handleChange}
							error={
								changePasswordFormik.touched.email &&
								!!changePasswordFormik.errors.email
							}
							errorMsg={changePasswordFormik.errors.email}
						/>
					</Col>
				</Row>
				<Row>
					<Col sm={12}>
						<FormInput
							id="message"
							type="text"
							name="message"
							label="Message"
							placeholder="Enter Message"
							value={changePasswordFormik.values.message}
							onChange={changePasswordFormik.handleChange}
							error={
								changePasswordFormik.touched.message &&
								!!changePasswordFormik.errors.message
							}
							errorMsg={changePasswordFormik.errors.message}
						/>
					</Col>
				</Row>
				<Button
					type="submit"
					className="fs-18 medium-text themeButton plr-40 mt-10"
				>
					Send Message
				</Button>
			</form>
		</div>
	);
};

export default ContactUs;
