import React, { useEffect, useState } from 'react';
import { Col, Row } from 'reactstrap';
import { useFormik } from 'formik';
import { toast } from 'react-toastify';
import { useSelector } from 'react-redux';
import * as yup from 'yup';
// import FormInput from '../../Form/FormInput';
import * as validation from '../../../Helper/YupValidation';
import CustomButton from '../../Common/CustomButton';
import Api from '../../../Helper/Api';
import { VenueApiRoutes } from '../../../Utils/routes';
// import Multiselect from 'multiselect-react-dropdown';
import { formatSubCategoryOption } from '../../ManageMenu/utils';
import NewCustomModal from '../../Common/Modal/NewCustomModal';
import NewVerifyModalWrapper from '../../Authentication/newVerifyOtp.style';
// import crossImg from '../../../Assets/images/cross.png';
import NewFormInput from '../../Form/NewFormInput';
import CustomLabel from '../../Form/CustomLabel';
import NewFormCheckbox from '../../Form/NewFormCheckbox/NewFormCheckbox';

const NewPickupLocationModal = ({
	isOpen,
	handleModal,
	modalData,
	getPickupLocationList
}) => {
	const authData = useSelector((state) => ({ ...state.auth }));
	const [loading, setLoading] = useState(false);
	const [selectedValues, setSelectedValues] = useState([]);
	const [subCategoryData, setSubCategoryData] = useState([]);
	const [subCategoryOptionsLoading, setSubCategoryOptionsLoading] =
		useState(false);

	let isEdit = modalData?.type === 'edit';
	const validationSchema = yup.object().shape({
		name: validation.YUP_VALIDATION.PICKUP_LOCATION_NAME
	});

	const {
		handleSubmit,
		touched,
		errors,
		handleChange,
		closeHandler,
		values,
		resetForm,
		handleBlur,
		setFieldValue
	} = useFormik({
		initialValues: {
			name: '',
			updateCategoryProducts: false
		},
		validationSchema: validationSchema,
		onSubmit: async (values) => {
			let url = !isEdit
				? VenueApiRoutes.addPickupLocation
				: VenueApiRoutes.editPickupLocation;
			let payload = !isEdit
				? {
						bar_id: authData?.selectedVenue?.id?.toString(),
						address: values?.name,
						sub_category_ids: selectedValues
				  }
				: {
						bar_id: authData?.selectedVenue?.id?.toString(),
						address: values?.name,
						id: modalData?.data?.id?.toString(),
						sub_category_ids: selectedValues,
						updateCategoryProducts: values?.updateCategoryProducts
				  };
			try {
				setLoading(true);
				const res = await Api('POST', url, payload);
				setLoading(false);
				if (res?.data?.status) {
					resetForm();
					toast.success(res?.data?.message);
					handleModal();
					await getPickupLocationList();
				} else {
					toast.error(res?.data?.message);
				}
			} catch (err) {
				setLoading(false);
				if (err?.response?.data?.message) {
					toast.error(err?.response?.data?.message);
				}
			}
		}
	});
	useEffect(() => {
		if (!isEdit) {
			setSelectedValues([]);
		}

		if (isEdit) {
			if (modalData?.data?.pickup_location_sub_categories) {
				const clearData =
					modalData?.data?.pickup_location_sub_categories.map(
						(item) => item.subCategoryId
					);
				setSelectedValues(clearData);
			}
		}

		if (modalData?.data?.address) {
			setFieldValue('name', modalData?.data?.address);
		} else {
			setFieldValue('name', '');
		}
	}, [modalData, modalData?.data, modalData?.data?.address]);

	const onSelect = (selectedList, selectedItem) => {
		setSelectedValues([...selectedList]);
	};

	// const onRemove = (selectedList, removedItem) => {
	// 	setSelectedValues(
	// 		selectedValues.filter((item) => item.value !== removedItem.value)
	// 	);
	// };

	const getSubCategoryOptions = async () => {
		try {
			setSubCategoryOptionsLoading(true);
			const res = await Api('POST', VenueApiRoutes.getSubCategory, {
				bar_id: authData?.selectedVenue?.id
			});
			if (res?.data?.status) {
				let formatedData = formatSubCategoryOption(res?.data?.data);
				setSubCategoryData(
					formatedData?.sort((item1, item2) =>
						item1?.label?.localeCompare(item2?.label)
					)
				);
			} else {
				toast.error(res?.data?.message);
			}
			setSubCategoryOptionsLoading(false);
		} catch (err) {
			setSubCategoryOptionsLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	useEffect(() => {
		getSubCategoryOptions();
	}, [authData?.selectedVenue?.id]);

	return (
		<NewCustomModal
			size="md"
			onClosed={closeHandler}
			isOpen={isOpen}
			handleModal={handleModal}
			modalClassName="verifyOtp"
		>
			<NewVerifyModalWrapper style={{ overflow: 'hidden' }}>
				<form onSubmit={handleSubmit}>
					<p className="verifyOtpHeading">
						{modalData?.type === 'edit'
							? 'Edit Pick Up Location'
							: 'Add Pick Up Location'}
					</p>
					<Row className="my-3">
						<Col xs={12} className="mb-3">
							<NewFormInput
								type="text"
								name="name"
								label="Pick Up Location Name"
								placeholder="Enter Pick Up Location Name"
								value={values?.name}
								onChange={handleChange}
								onBlur={handleBlur}
								error={touched.name && !!errors.name}
								errorMsg={errors.name}
							/>
						</Col>
						<Col xs={12} className="mb-3">
							<CustomLabel label={'ASSIGN CATEGORIES'} />
							<Row className="mt-3">
								{subCategoryData?.map((values, index) => (
									<Col xs={2} md={4} lg={4} xl={4}>
										<div
											className="mb-3 d-flex align-items-center gap-3"
											// onClick={() => {
											// 	selectedValues.includes(
											// 		values?.subCategoryID
											// 	); ?

											// 	:
											// 	setSelectedValues(
											// 		selectedValues.concat(
											// 			values?.subCategoryID
											// 		)
											// 	);
											// }}
										>
											<NewFormCheckbox
												id={`checkbox_${index}`}
												label={values.label}
												name="acceptTerms"
												onChange={(e) => {
													if (e.target.checked) {
														setSelectedValues(
															selectedValues.concat(
																values?.subCategoryID
															)
														);
													} else {
														setSelectedValues(
															selectedValues.filter(
																(i) =>
																	i !==
																	values?.subCategoryID
															)
														);
													}
												}}
												checked={selectedValues.includes(
													values?.subCategoryID
												)}
												// disabled={state ? true : false}
											/>
										</div>
									</Col>
								))}
							</Row>
							{/* <Multiselect
								style={{
									multiselectContainer: {
										height: '100%',
										borderRadius: '0px !important'
									},
									inputField: {
										borderRadius: '0px !important',
										height: '100%',
										width: '100%'
									},
									chips: {
										// To change css chips(Selected options)
										borderRadius: '2px',
										background: '#FF5454'
									},
									optionContainer: {
										// To change css for option container
										border: '0.2px solid #31313259',
										borderRadius: '0px !important'
									},
									option: {
										fontSize: '11px'
									}
								}}
								placeholder=""
								options={subCategoryData} // Options to display in the dropdown
								selectedValues={selectedValues} // Preselected value to persist in dropdown
								onSelect={onSelect} // Function will trigger on select event
								onRemove={onRemove} // Function will trigger on remove event
								displayValue="label" // Property name to display in the dropdown options
								customCloseIcon={
									<img
										src={crossImg}
										height={18}
										alt="close"
										style={{ cursor: 'pointer' }}
									/>
								}
							/> */}
						</Col>
						{isEdit && (
							<Col xs={12} className="mb-3">
								<NewFormCheckbox
									id="updatedExistingProducts"
									label={
										'Update Pickup Location For Existing Category Products'
									}
									name="updatedExistingProducts"
									onChange={() =>
										setFieldValue(
											'updateCategoryProducts',
											!values.updateCategoryProducts
										)
									}
									labelClassName={'bold-text'}
									checked={values?.updateCategoryProducts}
								/>
							</Col>
						)}
						<Col xs={12}>
							<CustomButton
								type="submit"
								className="newThemeButtonFullWidth"
								loading={loading}
							>
								Save
							</CustomButton>
						</Col>
					</Row>
				</form>
			</NewVerifyModalWrapper>
		</NewCustomModal>
	);
};

export default NewPickupLocationModal;
