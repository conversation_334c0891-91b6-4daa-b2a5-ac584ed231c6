import React from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { Row, Col } from "reactstrap";

const NewPickupLocationSkeleton = () => {
	return (
		<>
			<hr className="customeHrClass" />

			<div>
				{Array.from({ length: 3 }).map((_, index) => (
					<div key={index}>
						<Row>
							<Col>
								<Skeleton height={40} />
							</Col>
							<Col>
								<div className="d-flex align-items-center">
									<Skeleton height={40} width={250} />
									<div style={{ marginLeft: "10px" }}>
										<Skeleton circle={true} height={25} width={25} />
									</div>
								</div>
							</Col>
						</Row>
						<hr className="customeHrClass" />
					</div>
				))}
			</div>
		</>
	);
};

export default NewPickupLocationSkeleton;
