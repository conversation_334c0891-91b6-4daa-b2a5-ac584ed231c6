import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Row } from 'reactstrap';
import FormInput from '../Form/FormInput';
import ReactFormSelect from '../Form/ReactFormSelect';
import { PlusIcon } from '../Icons';

const PosIntegration = () => {
	return (
		<Row className="mt-3">
			<Col lg={6}>
				<form>
					<FormInput
						id="venue_name"
						type="text"
						name="venue_name"
						label="Venue Name"
						value="Dummy Venue Name"
						disabled
					/>
					<FormInput
						id="pos"
						type="text"
						name="pos"
						label="Integrated POS"
						value="N/A"
						disabled
					/>
					<FormInput
						id="status"
						type="text"
						name="status"
						label="Status"
						value="Un-configured"
						disabled
					/>
					<ReactFormSelect
						id="printer_modal"
						label="Your POS"
						placeholder="Choose Your POS"
						options={[]}
						isSearchable={false}
						// value={}
						// handleChange={handleChange}
					/>
					<Button
						type="submit"
						className="fs-18 medium-text themeButton plr-40 mt-10"
					>
						Start POS Configuration
					</Button>
				</form>
			</Col>
			<Col
				lg={6}
				className="d-flex justify-content-start justify-content-lg-end align-items-start"
			>
				<Button
					type="button"
					className="fs-18 medium-text borderButton plr-40 mt-10"
				>
					<PlusIcon height="18" width="18" className="mr-10" /> Sync
					POS
				</Button>
			</Col>
		</Row>
	);
};

export default PosIntegration;
