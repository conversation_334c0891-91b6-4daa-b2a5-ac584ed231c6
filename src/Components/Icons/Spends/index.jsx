export const Spends = ({ width, height, fill, ...props }) => {
	return (
		<svg
			width={width}
			height={height}
			viewBox="0 0 23 22"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M22.8754 9.16524C22.8758 10.7201 22.8762 12.2751 22.8754 13.83C22.875 14.186 22.6562 14.4312 22.3356 14.4399C22.0091 14.4491 21.7672 14.2108 21.766 13.8651C21.7624 13.0028 21.7644 12.1405 21.7644 11.2786V10.3533C21.6873 10.3633 21.661 10.4164 21.6243 10.4527C19.9743 12.1006 18.3248 13.7497 16.6757 15.3984C16.3428 15.7314 16.0769 15.731 15.7396 15.3968C14.4912 14.1577 13.2417 12.9194 11.9962 11.677C11.9096 11.5908 11.8673 11.5872 11.7782 11.6758C9.84729 13.6068 7.91273 15.5346 5.97818 17.4619C4.82727 18.6085 3.67517 19.7538 2.52266 20.8991C2.26437 21.1558 1.96696 21.1733 1.72864 20.9494C1.47554 20.7115 1.47434 20.3957 1.73423 20.1334C2.20808 19.6556 2.68593 19.1821 3.16219 18.7067C4.33346 17.5382 5.50433 16.3693 6.67639 15.2016C8.27521 13.608 9.87483 12.0152 11.4744 10.4219C11.7639 10.1341 12.0705 10.1345 12.3615 10.4275C13.605 11.6786 14.8481 12.9305 16.0881 14.1857C16.1807 14.2795 16.2286 14.2894 16.3284 14.1896C17.8158 12.6958 19.3081 11.2068 20.7991 9.71654C20.8306 9.68501 20.8606 9.65227 20.9169 9.59319H20.6765C19.635 9.59319 18.5935 9.59399 17.552 9.59279C17.2138 9.59239 16.9923 9.41395 16.948 9.11334C16.9028 8.80516 17.118 8.52292 17.427 8.48819C17.4861 8.4818 17.5464 8.4826 17.6062 8.4826C19.136 8.4826 20.6662 8.4822 22.1959 8.4826C22.6738 8.4826 22.8754 8.6854 22.8754 9.16524Z"
				fill={fill}
			/>
			<path
				d="M14.2525 11.6977C14.1758 11.853 14.1746 11.8526 14.0513 11.7292C13.8469 11.5249 13.6469 11.3157 13.4365 11.1181C13.3531 11.0394 13.3483 10.9763 13.3922 10.875C13.7319 10.0941 13.9235 9.27613 13.9212 8.42542C13.918 7.08609 13.5535 5.84975 12.7742 4.75034C11.8006 3.37707 10.4864 2.49802 8.83887 2.16668C6.94344 1.78584 5.2053 2.19502 3.66875 3.37068C2.42603 4.32119 1.63201 5.57869 1.3338 7.11483C0.954553 9.06775 1.3845 10.8446 2.638 12.3987C3.57214 13.5568 4.78733 14.2754 6.23644 14.6015C6.32387 14.6211 6.41289 14.6335 6.50112 14.6486C6.58256 14.663 6.66439 14.6762 6.77777 14.6953C6.69114 14.7828 6.62327 14.8522 6.55501 14.9209C6.34024 15.1361 6.12347 15.3496 5.91149 15.5672C5.856 15.6239 5.8065 15.6375 5.72746 15.6167C3.98333 15.1584 2.58172 14.1963 1.52622 12.7388C0.691477 11.5859 0.224406 10.2941 0.140573 8.87054C-0.00433867 6.40624 0.863135 4.35273 2.69908 2.71758C3.83003 1.70999 5.16657 1.12715 6.67437 0.947902C9.84247 0.571052 12.8796 2.22456 14.282 5.09685C14.7212 5.99706 14.9575 6.95116 15.0186 7.95396C15.0984 9.27334 14.8337 10.5165 14.2525 11.6977Z"
				fill={fill}
			/>
			<path
				d="M8.37645 11.7684C8.20984 11.8302 8.12654 11.9482 8.12654 12.1225C8.12654 12.3365 8.13014 12.5513 8.12535 12.7652C8.11976 13.0012 8.02834 13.1928 7.80718 13.2994C7.61756 13.3908 7.42873 13.3768 7.25268 13.2614C7.06226 13.1365 7.00637 12.9441 7.00438 12.7305C7.00158 12.4814 6.99759 12.2319 7.00637 11.9832C7.00997 11.8778 6.97124 11.8395 6.87543 11.8075C6.10616 11.5532 5.60955 11.0407 5.42871 10.243C5.39358 10.0894 5.36883 9.93007 5.40316 9.76959C5.46145 9.49813 5.6854 9.31689 5.95846 9.32168C6.22313 9.32647 6.4415 9.5141 6.4886 9.77997C6.50617 9.87777 6.50776 9.97917 6.53092 10.075C6.64469 10.5496 7.17723 10.8866 7.68582 10.8119C7.90139 10.78 8.10139 10.7129 8.27305 10.5736C8.81557 10.1349 8.7022 9.31928 8.05828 9.02826C7.84431 8.93166 7.61556 8.92008 7.38642 8.9109C6.47383 8.87417 5.6363 8.17636 5.43829 7.28653C5.21833 6.29889 5.7884 5.30807 6.77922 4.95676C7.00358 4.87732 7.00038 4.87692 7.00438 4.64299C7.00837 4.3839 6.98402 4.12442 7.01835 3.86653C7.05587 3.5823 7.27464 3.3831 7.55009 3.37431C7.82115 3.36553 8.05109 3.55755 8.10858 3.83859C8.14491 4.01663 8.11417 4.19668 8.13054 4.38271C8.13054 4.5344 8.08463 4.71405 8.14331 4.84778C8.20159 4.98112 8.40918 4.96674 8.54451 5.03581C9.27586 5.40906 9.68145 5.9967 9.73894 6.81746C9.75969 7.11447 9.53135 7.38034 9.24232 7.40948C8.93294 7.44062 8.66986 7.24102 8.63753 6.92405C8.60519 6.60748 8.50779 6.33722 8.24631 6.13482C7.77764 5.77274 7.01316 5.87374 6.66665 6.35079C6.31175 6.83902 6.56645 7.53284 7.16965 7.73923C7.3968 7.81707 7.63352 7.79152 7.86387 7.82626C8.58723 7.93484 9.16208 8.27416 9.51139 8.92806C10.0854 10.0039 9.54732 11.3341 8.37645 11.7684Z"
				fill={fill}
			/>
			<path
				d="M13.1287 13.3189C11.8852 14.6862 10.3582 15.5145 8.51989 15.7565C8.44125 15.7668 8.3622 15.7764 8.28316 15.7836C8.20931 15.7908 8.13506 15.7944 8.04883 15.8004C8.0648 15.7329 8.1107 15.707 8.14464 15.673C8.5175 15.2994 8.89315 14.9281 9.26481 14.5533C9.33706 14.4806 9.41531 14.4315 9.51351 14.3988C10.6189 14.0323 11.5658 13.4215 12.3407 12.5492C12.3978 12.485 12.4301 12.4666 12.4996 12.5397C12.702 12.7528 12.9116 12.9588 13.1223 13.1636C13.181 13.2199 13.1874 13.2546 13.1287 13.3189Z"
				fill={fill}
			/>
		</svg>
	);
};
