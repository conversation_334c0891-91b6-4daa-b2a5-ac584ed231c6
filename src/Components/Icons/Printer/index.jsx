export const Printer = ({ width, height, fill, ...props }) => {
	return (
		<svg
			width={width ?? '32'}
			height={height ?? '32'}
			viewBox="0 0 200 200"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<g mask="url(#mask0_7289_19566)">
				<path
					d="M40.6289 150.004H29.3008C16.3555 150.004 5.8633 139.512 5.8633 126.566V79.6914C5.8633 66.7461 16.3555 56.2539 29.3008 56.2539H170.707C183.652 56.2539 194.145 66.7461 194.145 79.6914V126.566C194.145 139.512 183.652 150.004 170.707 150.004H159.379"
					stroke={fill ?? '#FFFFFF'}
					stroke-width="11.7187"
					stroke-miterlimit="10"
					stroke-linecap="round"
					stroke-linejoin="round"
				/>
				<path
					d="M31.2539 118.754H168.754"
					stroke={fill ?? '#FFFFFF'}
					stroke-width="11.7187"
					stroke-miterlimit="10"
					stroke-linecap="round"
					stroke-linejoin="round"
				/>
				<path
					d="M147.66 194.145H52.3477C45.8758 194.145 40.6289 188.898 40.6289 182.426V118.754H159.379V182.426C159.379 188.898 154.132 194.145 147.66 194.145Z"
					stroke={fill ?? '#FFFFFF'}
					stroke-width="11.7187"
					stroke-miterlimit="10"
					stroke-linecap="round"
					stroke-linejoin="round"
				/>
				<path
					d="M84.3789 143.754H115.629"
					stroke={fill ?? '#FFFFFF'}
					stroke-width="11.7187"
					stroke-miterlimit="10"
					stroke-linecap="round"
					stroke-linejoin="round"
				/>
				<path
					d="M84.3789 168.754H115.629"
					stroke={fill ?? '#FFFFFF'}
					stroke-width="11.7187"
					stroke-miterlimit="10"
					stroke-linecap="round"
					stroke-linejoin="round"
				/>
				<path
					d="M150.004 81.2539H168.754"
					stroke={fill ?? '#FFFFFF'}
					stroke-width="11.7187"
					stroke-miterlimit="10"
					stroke-linecap="round"
					stroke-linejoin="round"
				/>
				<path
					d="M135.941 5.86328H64.0664C51.1223 5.86328 40.6289 16.3566 40.6289 29.3008V56.2539H159.379V29.3008C159.379 16.3566 148.886 5.86328 135.941 5.86328Z"
					stroke={fill ?? '#FFFFFF'}
					stroke-width="11.7187"
					stroke-miterlimit="10"
					stroke-linecap="round"
					stroke-linejoin="round"
				/>
			</g>
		</svg>
	);
};
