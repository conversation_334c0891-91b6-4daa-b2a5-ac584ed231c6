export const CircleDollarIcon2 = ({ width, height, fill, ...props }) => {
	return (
		<svg
			width={width}
			height={height}
			viewBox="0 0 24 24"
			fill="none"
			{...props}
		>
			<path
				d="M12.1928 0.627447C5.81298 0.535701 0.743236 5.60591 0.627274 11.6877C0.498271 18.4405 5.87725 23.3798 11.972 23.3738C18.2289 23.3863 23.3708 18.3473 23.375 12.0775C23.3792 5.48809 18.1395 0.712672 12.1928 0.627447ZM14.5488 14.1573C14.3113 15.0659 13.6686 15.5596 12.7498 15.7026C12.3968 15.7575 12.3968 15.7552 12.3968 16.1133C12.3968 16.4388 12.3935 16.7644 12.3982 17.0899C12.4 17.1947 12.3646 17.2282 12.258 17.231C11.8039 17.2417 11.8039 17.2455 11.8039 16.7974C11.8039 16.5068 11.7997 16.2158 11.8058 15.9256C11.8081 15.8134 11.7704 15.771 11.6577 15.7682C10.8329 15.7473 10.1278 15.4278 9.50189 14.9057C9.41667 14.8345 9.38779 14.7828 9.45346 14.678C9.58013 14.4768 9.69935 14.27 9.806 14.0572C9.87399 13.9212 9.92755 13.945 10.0207 14.0297C10.2698 14.257 10.5325 14.4675 10.824 14.6403C11.0723 14.7879 11.3405 14.875 11.6232 14.923C11.7392 14.943 11.7895 14.9118 11.7871 14.78C11.7797 14.3385 11.7843 13.8965 11.7843 13.455C11.7843 13.0135 11.7788 12.5944 11.7876 12.1645C11.7904 12.0341 11.7527 11.9736 11.6269 11.9261C11.131 11.7403 10.6452 11.5288 10.2503 11.1619C9.6579 10.6119 9.39757 9.94309 9.61878 9.14905C9.83441 8.3755 10.3881 7.94425 11.1598 7.78125C11.3242 7.74633 11.4928 7.72118 11.66 7.71699C11.7918 7.71326 11.8095 7.65738 11.8062 7.54654C11.7988 7.33184 11.8076 7.11668 11.8025 6.90152C11.8002 6.80466 11.8323 6.77299 11.9315 6.76973C12.3968 6.75296 12.3968 6.75017 12.3968 7.21215C12.3968 7.3053 12.4089 7.4003 12.394 7.49065C12.3632 7.67693 12.4554 7.72211 12.6189 7.7426C13.2034 7.81479 13.7445 8.01504 14.2419 8.33173C14.2563 8.34104 14.2712 8.34989 14.2857 8.35967C14.4217 8.45592 14.4423 8.56971 14.3476 8.70104C14.2149 8.88453 14.0757 9.06383 13.9499 9.25197C13.8819 9.35397 13.8349 9.34698 13.7557 9.26501C13.4251 8.92178 13.0264 8.69685 12.5607 8.5874C12.434 8.5576 12.3912 8.57762 12.393 8.72153C12.4005 9.45968 12.3996 10.1978 12.393 10.936C12.3921 11.0636 12.4387 11.1195 12.55 11.1661C12.984 11.3467 13.4185 11.526 13.793 11.8227C14.5646 12.4337 14.7891 13.2375 14.5488 14.1573Z"
				fill={fill ?? '#FF5F5F'}
			/>
		</svg>
	);
};
