import React from 'react';

export const CircleArrowDown = ({
	width = '28px',
	height = '28px',
	...props
}) => {
	return (
		<svg
			width={width}
			height={height}
			viewBox="0 0 30 30"
			fill="none"
			{...props}
		>
			<path
				d="M15.0358 29.1717C22.8428 29.1717 29.1717 22.8428 29.1717 15.0358C29.1717 7.22883 22.8428 0.9 15.0358 0.9C7.22883 0.9 0.9 7.22883 0.9 15.0358C0.9 22.8428 7.22883 29.1717 15.0358 29.1717Z"
				stroke="#565656"
				stroke-width="0.2"
			/>
			<path
				d="M15.0355 16.2716L11.4979 12.1444C11.2369 11.8399 10.8137 11.8399 10.5527 12.1444C10.2917 12.4489 10.2917 12.9426 10.5527 13.2472L14.5629 17.9258C14.824 18.2303 15.2471 18.2303 15.5082 17.9258L19.5184 13.2472C19.7794 12.9426 19.7794 12.4489 19.5184 12.1444C19.2574 11.8399 18.8342 11.8399 18.5732 12.1444L15.0355 16.2716Z"
				fill="#565656"
			/>
		</svg>
	);
};
