import React from 'react';

export const CircleArrowRight = ({
	width = '18px',
	height = '18px',
	strokeColor = '#5C5C5C',
	caretDownColor = '#565656',
	strokeWidth = '0.2'
}) => {
	return (
		<svg
			width={width}
			height={height}
			viewBox="0 0 24 22"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M23.5717 10.9316C23.5267 5.08632 18.3982 0.399104 12.1323 0.447339C5.86646 0.495575 0.810771 5.26119 0.855768 11.1065C0.900765 16.9517 6.02922 21.639 12.2951 21.5907C18.561 21.5425 23.6167 16.7769 23.5717 10.9316Z"
				stroke={strokeColor}
				strokeWidth={strokeWidth}
			/>
			<path
				d="M13.2057 11.0053L9.91565 13.6701C9.6729 13.8667 9.67533 14.1824 9.92108 14.3752C10.1668 14.5681 10.5628 14.565 10.8056 14.3684L14.5352 11.3477C14.778 11.1511 14.7755 10.8353 14.5298 10.6425L10.7541 7.67949C10.5083 7.48664 10.1123 7.48969 9.86959 7.6863C9.62683 7.88291 9.62926 8.19864 9.87501 8.39149L13.2057 11.0053Z"
				fill={caretDownColor}
			/>
			<mask
				id="mask0_1_11972"
				style={{ maskType: 'luminance' }}
				maskUnits="userSpaceOnUse"
				x="9"
				y="7"
				width="6"
				height="8"
			>
				<path
					d="M13.2057 11.0053L9.91565 13.6701C9.6729 13.8667 9.67533 14.1824 9.92108 14.3752C10.1668 14.5681 10.5628 14.565 10.8056 14.3684L14.5352 11.3477C14.778 11.1511 14.7755 10.8353 14.5298 10.6425L10.7541 7.67949C10.5083 7.48664 10.1123 7.48969 9.86959 7.6863C9.62683 7.88291 9.62926 8.19864 9.87501 8.39149L13.2057 11.0053Z"
					fill="white"
				/>
			</mask>
			<g mask="url(#mask0_1_11972)"></g>
		</svg>
	);
};
