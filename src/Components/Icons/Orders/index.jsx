export const Orders = ({ width, height, fill, ...props }) => {
	return (
		<svg
			width={width}
			height={height}
			viewBox="0 0 20 24"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}
		>
			<path
				d="M19.0163 12.5369C17.6618 10.2099 15.6221 8.97365 12.9339 8.8164C12.7848 8.80734 12.7481 8.77154 12.7486 8.62381C12.7536 6.85873 12.7508 5.09366 12.7526 3.32859C12.7531 2.99778 12.7155 2.67422 12.604 2.36245C12.2234 1.2957 11.279 0.630002 10.1202 0.627737C7.63643 0.622752 5.1531 0.625924 2.66931 0.62683C2.52248 0.62683 2.37702 0.639066 2.23019 0.658552C1.03565 0.818518 0.0518341 1.92424 0.0504746 3.12648C0.0477556 5.76299 0.0495683 8.39904 0.0495683 11.0355C0.0495683 13.6829 0.0468493 16.3303 0.0518341 18.9781C0.0531936 19.5967 0.28476 20.1382 0.693514 20.5968C1.22417 21.1914 1.89848 21.4751 2.69378 21.4751C4.32879 21.4751 5.96381 21.476 7.59882 21.4728C7.71438 21.4724 7.79776 21.5068 7.88522 21.582C9.2003 22.7172 10.7297 23.3158 12.4644 23.3711C13.954 23.4187 15.3316 23.0299 16.5778 22.2183C18.6701 20.856 19.7867 18.9106 19.9362 16.425C20.0196 15.0438 19.7128 13.7328 19.0163 12.5369ZM7.61876 9.94296C7.29022 9.7549 6.95669 9.57545 6.62406 9.39509C6.43283 9.29132 6.29643 9.29721 6.17 9.41458C6.03586 9.5392 6.0345 9.68919 6.09432 9.85097C6.25519 10.2856 6.40519 10.7238 6.57512 11.1543C6.72875 11.5431 6.69159 11.8997 6.4709 12.2527C5.88949 13.1826 5.54282 14.1982 5.42273 15.2858C5.26729 16.6969 5.50566 18.0437 6.13329 19.3194C6.16274 19.3783 6.21123 19.4309 6.20806 19.5179H6.03948C4.86851 19.5179 3.69753 19.5183 2.52656 19.5179C2.06025 19.5179 1.78609 19.2482 1.78609 18.7842C1.78518 13.5402 1.78518 8.29617 1.78609 3.05216C1.78609 2.63842 2.07385 2.35293 2.49393 2.35112C3.1048 2.3484 3.71566 2.3552 4.32653 2.34704C4.47018 2.34477 4.49918 2.39825 4.49148 2.52423C4.46383 2.95926 4.60431 3.32542 4.95869 3.59324C5.16261 3.74777 5.39373 3.82526 5.65339 3.82299C6.11154 3.81891 6.56968 3.82209 7.02783 3.82209C7.72435 3.82163 8.22781 3.32633 8.23642 2.63253C8.23884 2.44432 8.33597 2.35021 8.52781 2.35021H10.2421C10.7343 2.35066 11.0089 2.62075 11.0102 3.11606C11.013 4.07767 11.0111 5.03928 11.0111 6.0009C11.0111 6.96251 11.0089 7.89059 11.0134 8.83498C11.0143 8.95235 10.9826 8.99948 10.8643 9.02939C10.1306 9.21383 9.44138 9.50657 8.80242 9.91442C8.41315 10.1632 8.02162 10.1727 7.61876 9.94296ZM16.9512 17.5892C16.9113 17.9014 16.6526 18.1375 16.3231 18.1461C16.0068 18.1538 15.6901 18.1479 15.3733 18.1479C14.4683 18.1484 13.5638 18.1479 12.6588 18.1479C11.4598 18.1479 10.2607 18.1489 9.06208 18.1475C8.7059 18.147 8.45938 17.9576 8.39412 17.6418C8.32932 17.3304 8.51058 17.0168 8.82191 16.9307C8.92931 16.9013 8.96465 16.8591 8.97281 16.7504C9.09018 15.2006 10.3368 13.8044 11.8703 13.4994C11.9809 13.4776 12.091 13.4477 12.203 13.4382C12.304 13.4296 12.3117 13.3829 12.2891 13.3041C12.2723 13.2447 12.2578 13.184 12.2428 13.1242C12.1948 12.9357 12.2551 12.7716 12.4119 12.6638C12.5555 12.5654 12.7826 12.5681 12.9235 12.6697C13.0812 12.7834 13.1365 12.9384 13.0871 13.1292C13.0381 13.3174 13.1096 13.4296 13.3014 13.4659C14.97 13.784 16.1595 15.0673 16.3444 16.7536C16.3553 16.8505 16.3843 16.899 16.4872 16.9267C16.7931 17.0073 16.9884 17.2969 16.9512 17.5892Z"
				fill={fill}
			/>
			<path
				d="M16.174 6.12011C16.0167 6.63762 15.8459 7.15105 15.6691 7.66222C15.5853 7.90421 15.3623 7.98804 15.051 7.92234C14.7828 7.86569 14.6427 7.69077 14.6486 7.437C14.6967 6.89094 14.7374 6.36209 14.7927 5.83507C14.8235 5.54142 15.0759 5.37239 15.3655 5.42948C15.5092 5.45803 15.6524 5.48885 15.796 5.51921C16.1033 5.58492 16.2646 5.82192 16.174 6.12011Z"
				fill={fill}
			/>
			<path
				d="M18.8693 7.93187C18.6287 8.2663 18.3876 8.60074 18.1388 8.92928C18.011 9.09831 17.8343 9.11961 17.6403 9.00994C17.4214 8.88623 17.3476 8.72853 17.4332 8.52098C17.59 8.14032 17.7545 7.76238 17.9208 7.3858C17.9802 7.25076 18.0926 7.18143 18.2802 7.17554C18.4918 7.24261 18.7247 7.34457 18.8847 7.55076C18.9726 7.66405 18.9567 7.81087 18.8693 7.93187Z"
				fill={fill}
			/>
		</svg>
	);
};
