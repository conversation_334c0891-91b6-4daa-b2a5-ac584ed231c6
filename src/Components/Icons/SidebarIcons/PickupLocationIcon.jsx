export const PickupLocationIcon = ({ fill, ...props }) => {
	return (
		<svg {...props} viewBox="0 0 32 32" fill="none">
			<path
				d="M21.125 9.625C21.125 12.2031 17.6562 16.75 16.1562 18.625C15.8281 19.0938 15.125 19.0938 14.7969 18.625C13.2969 16.75 9.875 12.2031 9.875 9.625C9.875 6.53125 12.3594 4 15.5 4C18.5938 4 21.125 6.53125 21.125 9.625ZM15.5 5.5C13.2031 5.5 11.375 7.375 11.375 9.625C11.375 10 11.4688 10.5156 11.7969 11.2656C12.0781 12.0156 12.5469 12.8125 13.0156 13.6094C13.8125 14.9219 14.75 16.1875 15.5 17.125C16.2031 16.1875 17.1406 14.9219 17.9375 13.6094C18.4062 12.8125 18.875 12.0156 19.1562 11.2656C19.4844 10.5156 19.625 10 19.625 9.625C19.625 7.375 17.75 5.5 15.5 5.5ZM21.9219 12.4844L27.4531 10.2812C28.1562 10 29 10.5156 29 11.3125V24.0156C29 24.4844 28.7188 24.9062 28.25 25.0469L20.9844 27.9531C20.8438 28.0469 20.7031 28.0469 20.5156 28L10.25 25.0469L3.5 27.7656C2.79688 28.0469 2 27.5312 2 26.7344V14.0312C2 13.5625 2.23438 13.1406 2.70312 13L8.46875 10.6562C8.5625 11.1719 8.70312 11.6406 8.89062 12.1094L3.5 14.2656V26.1719L9.5 23.7812V18.25C9.5 17.875 9.82812 17.5 10.25 17.5C10.625 17.5 11 17.875 11 18.25V23.6875L20 26.2656V18.25C20 17.875 20.3281 17.5 20.75 17.5C21.125 17.5 21.5 17.875 21.5 18.25V26.1719L27.5 23.7812V11.875L20.9844 14.4531C20.9844 14.5 20.9375 14.5 20.8906 14.5C21.2656 13.8438 21.6406 13.1406 21.9219 12.4844Z"
				fill={fill ?? '#4F4F4F'}
			/>
		</svg>
	);
};
