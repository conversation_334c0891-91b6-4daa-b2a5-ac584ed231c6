export const SettingIcon = ({ fill, ...props }) => {
	return (
		<svg {...props} viewBox="0 0 32 32" fill="none">
			<path
				d="M10.875 16C10.875 13.75 12.7031 11.875 15 11.875C17.25 11.875 19.125 13.75 19.125 16C19.125 18.2969 17.25 20.125 15 20.125C12.7031 20.125 10.875 18.2969 10.875 16ZM15 13.375C13.5469 13.375 12.375 14.5938 12.375 16C12.375 17.4531 13.5469 18.625 15 18.625C16.4062 18.625 17.625 17.4531 17.625 16C17.625 14.5938 16.4062 13.375 15 13.375ZM6.04688 14.8281L4.17188 13.0938C3.65625 12.625 3.51562 11.875 3.89062 11.2656L5.29688 8.78125C5.625 8.17188 6.375 7.89062 7.03125 8.125L9.46875 8.875C10.125 8.40625 10.8281 7.98438 11.5312 7.70312L12.0938 5.21875C12.2344 4.51562 12.8438 4 13.5469 4H16.4062C17.1094 4 17.7188 4.51562 17.8594 5.21875L18.4219 7.70312C19.125 7.98438 19.8281 8.40625 20.4844 8.875L22.9219 8.125C23.5781 7.89062 24.3281 8.17188 24.6562 8.78125L26.0625 11.2656C26.4375 11.875 26.2969 12.625 25.7812 13.0938L23.9062 14.8281C23.9531 15.2031 24 15.625 24 16C24 16.4219 23.9531 16.8438 23.9062 17.2188L25.7812 18.9531C26.2969 19.4219 26.4375 20.1719 26.0625 20.7812L24.6562 23.2656C24.3281 23.875 23.5781 24.1562 22.9219 23.9219L20.4844 23.1719C19.8281 23.6406 19.125 24.0156 18.4219 24.3438L17.8594 26.8281C17.7188 27.5312 17.1094 28 16.4062 28H13.5469C12.8438 28 12.2344 27.5312 12.0938 26.8281L11.5312 24.3438C10.8281 24.0156 10.125 23.6406 9.46875 23.1719L7.03125 23.9219C6.375 24.1562 5.625 23.875 5.29688 23.2656L3.89062 20.7812C3.51562 20.1719 3.65625 19.4219 4.17188 18.9531L6.04688 17.2188C6 16.8438 6 16.4219 6 16C6 15.625 6 15.2031 6.04688 14.8281ZM10.4062 10.0938L9.79688 10.5625L6.60938 9.53125L5.15625 12.0156L7.64062 14.2656L7.54688 15.0156C7.5 15.3438 7.5 15.6719 7.5 16C7.5 16.375 7.5 16.7031 7.54688 17.0312L7.64062 17.7812L5.15625 20.0312L6.60938 22.5156L9.79688 21.4844L10.4062 21.9531C10.9219 22.375 11.4844 22.7031 12.1406 22.9375L12.8438 23.2656L13.5469 26.5H16.4062L17.1094 23.2656L17.8125 22.9375C18.4688 22.7031 19.0312 22.375 19.5469 21.9531L20.1562 21.4844L23.3438 22.5156L24.7969 20.0312L22.3125 17.7812L22.4062 17.0312C22.4531 16.7031 22.5 16.375 22.5 16C22.5 15.6719 22.4531 15.3438 22.4062 15.0156L22.3125 14.2656L24.7969 12.0156L23.3438 9.53125L20.1562 10.5625L19.5469 10.0938C19.0312 9.67188 18.4688 9.34375 17.8125 9.0625L17.1094 8.78125L16.4062 5.5H13.5469L12.8438 8.78125L12.1406 9.0625C11.4844 9.34375 10.9219 9.67188 10.4062 10.0938Z"
				fill={fill ?? '#242424'}
			/>
		</svg>
	);
};
