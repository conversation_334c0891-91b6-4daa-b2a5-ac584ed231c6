export const InAppAdvertisingIcon = ({ fill, ...props }) => {
	return (
		<svg {...props} viewBox="0 0 32 32" fill="none">
			<path
				d="M28 4H4C2.3125 4 1 5.35938 1 7V22C1 23.6875 2.3125 25 4 25H28C29.6406 25 31 23.6875 31 22V7C31 5.35938 29.6406 4 28 4ZM29.5 22C29.5 22.8438 28.7969 23.5 28 23.5H4C3.15625 23.5 2.5 22.8438 2.5 22V7C2.5 6.20312 3.15625 5.5 4 5.5H28C28.7969 5.5 29.5 6.20312 29.5 7V22ZM25.75 26.5H6.25C5.82812 26.5 5.5 26.875 5.5 27.25C5.5 27.6719 5.82812 28 6.25 28H25.75C26.125 28 26.5 27.6719 26.5 27.25C26.5 26.875 26.125 26.5 25.75 26.5Z"
				fill={fill ?? '#242424'}
			/>
			<path
				d="M19 17.5C18.5625 17.5 18.25 17.8438 18.25 18.25V20.75C18.25 21.1875 18.5625 21.5 18.9688 21.5C19.3438 21.5 19.75 21.1875 19.75 20.75V18.25C19.75 17.8438 19.4062 17.5 19 17.5ZM23 14.5C22.5625 14.5 22.25 14.8438 22.25 15.25V20.75C22.25 21.1875 22.5625 21.5 23 21.5C23.4062 21.5 23.75 21.1875 23.75 20.75V15.25C23.75 14.8438 23.4062 14.5 23 14.5ZM27 11.5C26.5625 11.5 26.25 11.8438 26.25 12.25V20.75C26.25 21.1875 26.5625 21.5 27 21.5C27.4062 21.5 27.75 21.1875 27.75 20.75V12.25C27.75 11.8438 27.4062 11.5 27 11.5Z"
				fill={fill ?? '#242424'}
			/>
			<path
				d="M9.36133 14.1895L9.01953 13.291H6.10938L5.76758 14.209C5.63411 14.5671 5.52018 14.8096 5.42578 14.9365C5.33138 15.0602 5.17676 15.1221 4.96191 15.1221C4.77962 15.1221 4.61849 15.0553 4.47852 14.9219C4.33854 14.7884 4.26855 14.637 4.26855 14.4678C4.26855 14.3701 4.28483 14.2692 4.31738 14.165C4.34993 14.0609 4.40365 13.916 4.47852 13.7305L6.30957 9.08203C6.36165 8.94857 6.4235 8.78906 6.49512 8.60352C6.56999 8.41471 6.64811 8.25846 6.72949 8.13477C6.81413 8.01107 6.92318 7.91178 7.05664 7.83691C7.19336 7.75879 7.361 7.71973 7.55957 7.71973C7.76139 7.71973 7.92904 7.75879 8.0625 7.83691C8.19922 7.91178 8.30827 8.00944 8.38965 8.12988C8.47428 8.25033 8.54427 8.38053 8.59961 8.52051C8.6582 8.65723 8.73145 8.84115 8.81934 9.07227L10.6895 13.6914C10.8359 14.043 10.9092 14.2985 10.9092 14.458C10.9092 14.624 10.8392 14.777 10.6992 14.917C10.5625 15.0537 10.3965 15.1221 10.2012 15.1221C10.0872 15.1221 9.98958 15.1009 9.9082 15.0586C9.82682 15.0195 9.75846 14.9658 9.70312 14.8975C9.64779 14.8258 9.58756 14.7184 9.52246 14.5752C9.46061 14.4287 9.4069 14.3001 9.36133 14.1895ZM6.49023 12.2021H8.62891L7.5498 9.24805L6.49023 12.2021Z"
				fill={fill ?? '#242424'}
			/>
			<path
				d="M15.5381 14.3604V14.2285C15.359 14.4336 15.1816 14.6012 15.0059 14.7314C14.8333 14.8584 14.6445 14.9544 14.4395 15.0195C14.2376 15.0879 14.0163 15.1221 13.7754 15.1221C13.4564 15.1221 13.1602 15.0553 12.8867 14.9219C12.6165 14.7852 12.3822 14.5915 12.1836 14.3408C11.985 14.0902 11.8337 13.7956 11.7295 13.457C11.6286 13.1185 11.5781 12.7523 11.5781 12.3584C11.5781 11.5251 11.7816 10.8757 12.1885 10.4102C12.5954 9.94466 13.1309 9.71191 13.7949 9.71191C14.179 9.71191 14.5029 9.77865 14.7666 9.91211C15.0303 10.0423 15.2874 10.2441 15.5381 10.5176V8.5498C15.5381 8.27637 15.5918 8.06966 15.6992 7.92969C15.8099 7.78971 15.9661 7.71973 16.168 7.71973C16.3698 7.71973 16.5244 7.78483 16.6318 7.91504C16.7425 8.04199 16.7979 8.23079 16.7979 8.48145V14.3604C16.7979 14.6143 16.7393 14.8047 16.6221 14.9316C16.5049 15.0586 16.3535 15.1221 16.168 15.1221C15.9857 15.1221 15.8343 15.057 15.7139 14.9268C15.5967 14.7933 15.5381 14.6045 15.5381 14.3604ZM12.916 12.4023C12.916 12.7637 12.9714 13.0713 13.082 13.3252C13.196 13.5791 13.3506 13.7712 13.5459 13.9014C13.7412 14.0283 13.9544 14.0918 14.1855 14.0918C14.4199 14.0918 14.6331 14.0316 14.8252 13.9111C15.0205 13.7874 15.1751 13.6003 15.2891 13.3496C15.4062 13.0957 15.4648 12.7799 15.4648 12.4023C15.4648 12.0475 15.4062 11.7432 15.2891 11.4893C15.1751 11.2321 15.0189 11.0368 14.8203 10.9033C14.625 10.7666 14.4102 10.6982 14.1758 10.6982C13.9316 10.6982 13.7135 10.7682 13.5215 10.9082C13.3294 11.0449 13.1797 11.2435 13.0723 11.5039C12.9681 11.7611 12.916 12.0605 12.916 12.4023Z"
				fill={fill ?? '#242424'}
			/>
		</svg>
	);
};
