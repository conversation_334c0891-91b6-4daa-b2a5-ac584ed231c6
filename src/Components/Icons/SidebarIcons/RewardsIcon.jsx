export const RewardsIcon = ({ fill, ...props }) => {
	return (
		<svg {...props} viewBox="0 0 32 32" fill="none">
			<path
				d="M15.9531 4C16.375 4 16.75 4.375 16.75 4.75V5.54688C17.2188 5.59375 17.7344 5.73438 18.2031 5.875C18.5781 5.96875 18.8125 6.39062 18.7188 6.76562C18.625 7.1875 18.2031 7.42188 17.7812 7.28125C17.2188 7.14062 16.6562 7.04688 16.1406 7.04688C15.7188 7 15.25 7.14062 14.9219 7.32812C14.6406 7.51562 14.5 7.75 14.5 8.07812C14.5 8.3125 14.5938 8.45312 14.875 8.64062C15.25 8.875 15.7656 9.01562 16.375 9.25H16.4219C16.9844 9.4375 17.6875 9.625 18.25 10C18.8594 10.375 19.3281 10.9844 19.3281 11.9219C19.375 12.8594 18.9062 13.5625 18.2031 13.9844C17.7812 14.2656 17.2656 14.4531 16.75 14.5V15.25C16.75 15.6719 16.375 16 16 16C15.5781 16 15.25 15.6719 15.25 15.25V14.4062C14.7344 14.2656 14.2188 14.125 13.7969 13.9844C13.7031 13.9375 13.5625 13.8906 13.4688 13.8438C13.0938 13.7031 12.9062 13.2812 13 12.9062C13.1406 12.5312 13.5625 12.2969 13.9844 12.4375C14.0781 12.4844 14.2188 12.5312 14.3125 12.5781C15.0156 12.8125 15.5781 13 16.1875 13C16.6562 13.0469 17.125 12.9531 17.4062 12.7188C17.6875 12.5781 17.875 12.2969 17.8281 11.9219C17.8281 11.6406 17.7344 11.4531 17.4062 11.2656C17.0781 11.0312 16.5625 10.8438 15.9531 10.6562H15.8594C15.2969 10.4688 14.6406 10.2812 14.0781 9.95312C13.5156 9.57812 13 8.96875 13 8.07812C12.9531 7.09375 13.5156 6.4375 14.1719 6.01562C14.5 5.82812 14.875 5.6875 15.2031 5.64062V4.75C15.2031 4.375 15.5781 4 15.9531 4ZM4 7C5.64062 7 7 8.35938 7 10V15.8594C7 16.0469 7 16.2812 7.04688 16.4688C7.14062 16.3281 7.23438 16.1875 7.375 16.0469C8.40625 15.0156 10.0469 15.0156 11.0781 16.0469L15.3438 20.3125C15.5781 20.5938 15.8125 20.8281 16 21.1094C16.1406 20.8281 16.375 20.5938 16.6094 20.3125L20.875 16.0469C21.9062 15.0156 23.5469 15.0156 24.5781 16.0469C24.7188 16.1875 24.8125 16.3281 24.9062 16.4688C24.9531 16.2812 25 16.0469 25 15.8594V10C25 8.35938 26.3125 7 28 7C29.6406 7 31 8.35938 31 10V20.1719C31 21.5312 30.4375 22.8906 29.4531 23.875L25.5156 27.8125C25.2344 28.0938 24.7188 28.0938 24.4375 27.8125C24.1562 27.5312 24.1562 27.0156 24.4375 26.7344L28.375 22.7969C29.0781 22.0938 29.5 21.1562 29.5 20.1719V10C29.5 9.20312 28.7969 8.5 28 8.5C27.1562 8.5 26.5 9.20312 26.5 10V15.8594C26.5 17.125 25.9844 18.3438 25.0938 19.2344L21.7656 22.5625C21.4844 22.8438 20.9688 22.8438 20.6875 22.5625C20.4062 22.2812 20.4062 21.7656 20.6875 21.4844L23.5 18.6719C23.9219 18.25 23.9219 17.5469 23.5 17.125C23.0781 16.7031 22.375 16.7031 21.9531 17.125L17.6875 21.3906C17.0781 22 16.75 22.7969 16.75 23.6406V27.25C16.75 27.6719 16.375 28 16 28C15.5781 28 15.25 27.6719 15.25 27.25V23.6406C15.25 22.7969 14.875 22 14.2656 21.3906L10 17.125C9.57812 16.7031 8.875 16.7031 8.45312 17.125C8.03125 17.5469 8.03125 18.25 8.45312 18.6719L11.2656 21.4844C11.5469 21.7656 11.5469 22.2812 11.2656 22.5625C10.9844 22.8438 10.4688 22.8438 10.1875 22.5625L6.85938 19.2344C5.96875 18.3438 5.5 17.125 5.5 15.8594V10C5.5 9.20312 4.79688 8.5 4 8.5C3.15625 8.5 2.5 9.20312 2.5 10V20.1719C2.5 21.1562 2.875 22.0938 3.57812 22.7969L7.51562 26.7344C7.79688 27.0156 7.79688 27.5312 7.51562 27.8125C7.23438 28.0938 6.71875 28.0938 6.4375 27.8125L2.5 23.875C1.51562 22.8906 1 21.5312 1 20.1719V10C1 8.35938 2.3125 7 4 7Z"
				fill={fill ?? '#242424'}
			/>
		</svg>
	);
};
