export const TaxCircle = ({ width, height, fill, ...props }) => {
	return (
		<svg
			width={width ?? 30}
			height={height ?? 36}
			viewBox="0 0 30 36"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}
		>
			<path
				d="M28.7051 22.819C27.6664 23.86 26.6247 24.8987 25.5843 25.938C23.658 27.8632 21.731 29.7872 19.807 31.7141C19.1603 32.3614 18.398 32.7872 17.4929 32.9353C17.2217 32.9796 16.9429 32.9954 16.667 32.996C14.6725 33.0006 12.6773 33.0076 10.6821 32.9942C9.76125 32.9884 8.99608 33.3127 8.35514 33.96C7.91948 34.3998 7.48207 34.8378 7.04408 35.2746C6.76064 35.5574 6.4113 35.5749 6.15702 35.323C5.91382 35.0815 5.93365 34.7094 6.21592 34.433C6.78805 33.8725 7.31936 33.2689 7.93348 32.7539C8.66599 32.1404 9.5198 31.8208 10.4821 31.8202C12.5134 31.8179 14.5447 31.8056 16.5761 31.8249C17.5605 31.8342 18.3653 31.4983 19.0576 30.8031C21.9807 27.8672 24.9159 24.943 27.8454 22.0136C28.5161 21.3429 28.3376 20.3036 27.4902 19.9951C27.0464 19.8341 26.6154 19.9029 26.2742 20.2424C24.5223 21.9815 22.7785 23.7288 21.03 25.4714C20.9618 25.5391 20.9367 25.6149 20.9215 25.7012C20.7343 26.7918 19.7376 27.5611 18.7054 27.5634C16.4775 27.5686 14.2496 27.5832 12.0224 27.5558C11.6538 27.5511 11.4222 27.3085 11.4205 26.9749C11.4193 26.6454 11.6532 26.4168 12.0224 26.3929C12.153 26.3847 12.2842 26.3801 12.4149 26.3801C14.4683 26.3789 16.5218 26.3766 18.5753 26.3818C18.9964 26.3824 19.3498 26.2617 19.5907 25.9012C20.0485 25.216 19.5977 24.2939 18.7748 24.228C18.6879 24.221 18.6004 24.2233 18.5129 24.2233C15.2148 24.2228 11.9162 24.2286 8.61758 24.2193C7.68328 24.2169 6.90936 24.5424 6.25383 25.2026C4.72058 26.7486 3.1774 28.2836 1.64006 29.8257C1.44702 30.0187 1.24057 30.1388 0.958293 30.0379C0.570459 29.8997 0.429323 29.4687 0.667272 29.1322C0.72151 29.0558 0.791495 28.9893 0.857981 28.9228C2.4204 27.3651 3.98107 25.8062 5.5464 24.2513C6.35415 23.4494 7.33219 23.0447 8.47119 23.0441C11.7914 23.0423 15.1122 23.0546 18.4318 23.0365C19.3854 23.0307 20.1313 23.352 20.6696 24.1907C21.7363 23.1187 22.7831 22.0655 23.8312 21.0139C24.3607 20.4826 24.8856 19.9467 25.4245 19.4253C26.6895 18.2017 28.7575 18.6869 29.3116 20.3532C29.6207 21.2822 29.402 22.1203 28.7051 22.819Z"
				fill={fill ?? '#FF5F5F'}
			/>
			<path
				d="M16.5308 0.500051C10.4735 0.519297 5.88486 5.47366 5.91052 11.0532C5.86211 16.9635 10.6427 21.7831 16.5523 21.7656C22.4847 21.7481 27.1796 16.9675 27.1842 11.143C27.1889 5.24621 22.4037 0.481972 16.5308 0.500051ZM16.5436 20.5893C11.3035 20.5834 7.04136 16.3301 7.08394 10.9815C7.12476 5.9064 11.3909 1.68455 16.5477 1.68513C21.7196 1.72071 25.9799 5.81775 26.0015 11.178C26.0219 16.3517 21.7324 20.5957 16.5436 20.5893Z"
				fill={fill ?? '#FF5F5F'}
			/>
			<path
				d="M23.4849 13.1025C23.7124 13.4075 23.6757 13.7656 23.4004 13.9697C23.1216 14.1756 22.7711 14.1132 22.5396 13.8076C22.1441 13.2856 21.7534 12.7607 21.3603 12.237C21.3347 12.2032 21.3055 12.1722 21.2693 12.1297C20.8576 12.6796 20.4523 13.2209 20.0481 13.7627C19.9274 13.9248 19.7909 14.0636 19.5763 14.0823C19.3214 14.1038 19.1155 14.0076 18.989 13.7848C18.8618 13.5609 18.8887 13.3357 19.0421 13.1305C19.4818 12.542 19.9163 11.9495 20.3689 11.3709C20.5036 11.1989 20.5152 11.0886 20.3747 10.9102C19.9204 10.3328 19.487 9.73968 19.0485 9.15005C18.891 8.93835 18.8642 8.70565 18.9954 8.47411C19.1179 8.25891 19.3173 8.16559 19.5646 8.18601C19.7787 8.20409 19.918 8.33706 20.0393 8.49977C20.3957 8.97801 20.7544 9.45449 21.1125 9.93155C21.1638 9.99979 21.2174 10.0663 21.2798 10.1468C21.712 9.57055 22.1342 9.00658 22.5576 8.44262C22.7169 8.23033 22.928 8.13818 23.1881 8.20059C23.44 8.26066 23.5853 8.43387 23.6302 8.68582C23.664 8.87886 23.5858 9.04099 23.4733 9.19088C23.0277 9.784 22.5868 10.3812 22.1342 10.9691C22.0368 11.0962 22.0374 11.1785 22.1342 11.3056C22.5903 11.8999 23.037 12.5018 23.4849 13.1025Z"
				fill={fill ?? '#FF5F5F'}
			/>
			<path
				d="M18.2566 13.221C17.681 11.6767 17.1031 10.1335 16.5257 8.59033C16.426 8.3238 16.2183 8.17625 15.9466 8.17975C15.6759 8.18266 15.4916 8.31739 15.3908 8.58625C14.818 10.116 14.2447 11.6458 13.6715 13.1755C13.633 13.2776 13.591 13.3791 13.5986 13.4765C13.5974 13.7686 13.7986 14.0264 14.0587 14.0807C14.3258 14.1366 14.6046 13.999 14.7206 13.7342C14.8169 13.5149 14.8956 13.2881 14.9738 13.0612C15.0117 12.951 15.0665 12.902 15.1925 12.9037C15.7016 12.9113 16.2107 12.9119 16.7199 12.9037C16.847 12.9014 16.8983 12.951 16.9363 13.0612C17.0086 13.2741 17.0885 13.4852 17.1736 13.6934C17.2617 13.9098 17.4162 14.0556 17.6571 14.0812C18.1388 14.132 18.4374 13.7057 18.2566 13.221ZM15.4829 11.7175C15.6357 11.3092 15.7821 10.9173 15.9535 10.4578C16.1244 10.9086 16.2743 11.304 16.4312 11.7175H15.4829Z"
				fill={fill ?? '#FF5F5F'}
			/>
			<path
				d="M14.1767 8.75016C14.1843 9.09309 13.923 9.35611 13.5509 9.36603C13.2599 9.37419 12.9677 9.38119 12.6779 9.3637C12.4732 9.35086 12.4078 9.4016 12.4096 9.62089C12.4218 10.8579 12.416 12.0949 12.4143 13.3324C12.4143 13.4263 12.409 13.522 12.3898 13.6135C12.3262 13.9139 12.0678 14.1081 11.7733 14.0842C11.4747 14.0603 11.2432 13.8322 11.2391 13.5237C11.2303 12.8542 11.2362 12.1847 11.2362 11.5152H11.2338C11.2338 10.8602 11.2303 10.2053 11.2362 9.55032C11.2379 9.40977 11.2006 9.35961 11.053 9.36486C10.7259 9.37536 10.3981 9.37478 10.0709 9.36603C9.76066 9.35728 9.51221 9.14616 9.46147 8.86855C9.41481 8.61194 9.5682 8.33433 9.81081 8.22644C9.90063 8.18678 9.99394 8.1827 10.0884 8.1827C11.2385 8.18211 12.388 8.18095 13.5381 8.18328C13.9189 8.18386 14.1697 8.41073 14.1767 8.75016Z"
				fill={fill ?? '#FF5F5F'}
			/>
		</svg>
	);
};
