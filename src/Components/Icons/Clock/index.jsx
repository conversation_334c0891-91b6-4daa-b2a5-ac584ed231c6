export const Clock = ({ width, height, fill, ...props }) => {
	return (
		<svg
			width={width}
			height={height}
			viewBox="0 0 36 36"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M17.6735 0.5C7.82987 0.516403 0.37092 8.46914 0.172384 17.5158C-0.0465142 27.4816 7.90227 35.5147 17.6639 35.5C27.3373 35.4858 35.1729 27.6717 35.1644 18.0713C35.1554 8.09922 27.1704 0.50905 17.6735 0.5ZM17.6571 31.9976C10.0929 32.0163 3.50168 25.8798 3.6708 17.599C3.82239 10.157 10.0285 3.86606 17.9416 4.00124C25.3904 4.12794 31.6875 10.1706 31.67 18.1323C31.6525 25.77 25.3123 32.0265 17.6571 31.9976Z"
				fill={fill ?? '#FF5F5F'}
			/>
			<path
				d="M26.3445 23.2445C25.9554 23.8667 25.5724 24.4923 25.1963 25.1224C25.1233 25.2451 25.0724 25.2508 24.9542 25.1789C21.9937 23.3944 19.0315 21.6132 16.0665 19.8354C15.9426 19.7608 15.9177 19.6731 15.9183 19.5424C15.9217 17.8625 15.9205 16.182 15.9205 14.5016C15.9205 12.8211 15.9234 11.1689 15.916 9.50253C15.9155 9.30569 15.9579 9.23499 16.1689 9.23838C16.8821 9.25196 17.5954 9.25196 18.3081 9.23838C18.5117 9.23442 18.5496 9.3023 18.549 9.48896C18.5439 12.3912 18.5473 15.2929 18.5417 18.1951C18.5411 18.3739 18.5982 18.4672 18.7526 18.5582C21.2674 20.0425 23.7777 21.5352 26.2913 23.0216C26.4062 23.0895 26.4135 23.1336 26.3445 23.2445Z"
				fill={fill ?? '#FF5F5F'}
			/>
		</svg>
	);
};
