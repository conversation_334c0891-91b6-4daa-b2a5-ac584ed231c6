export const UsersIcon = ({ fill, ...props }) => {
	return (
		<svg {...props} viewBox="0 0 32 32" fill="none">
			<path
				d="M15.9531 19C18.6719 19 20.8281 16.8438 20.8281 14.125C20.8281 11.4531 18.625 9.25 15.9531 9.25C13.2812 9.25 11.125 11.4531 11.125 14.125C11.0781 16.8438 13.2812 19 15.9531 19ZM15.9531 10.75C17.8281 10.75 19.3281 12.2969 19.3281 14.125C19.3281 16 17.8281 17.5 15.9531 17.5C14.125 17.5 12.5781 16 12.5781 14.125C12.5781 12.2969 14.125 10.75 15.9531 10.75ZM18.2969 20.5H13.6562C9.95312 20.5 7 23.3125 7 26.7812C7 27.4844 7.5625 28 8.3125 28H23.6406C24.3906 28 25 27.4844 25 26.7812C25 23.3125 22 20.5 18.2969 20.5ZM8.5 26.5C8.64062 24.0156 10.8906 22 13.6094 22H18.2969C21.0625 22 23.3125 24.0156 23.4531 26.5H8.5ZM25 11.5C27.0625 11.5 28.75 9.85938 28.75 7.75C28.75 5.6875 27.0625 4 25 4C22.8906 4 21.25 5.6875 21.25 7.75C21.25 9.85938 22.8906 11.5 25 11.5ZM25 5.5C26.2188 5.5 27.25 6.53125 27.25 7.75C27.25 9.01562 26.2188 10 25 10C23.7344 10 22.75 9.01562 22.75 7.75C22.75 6.53125 23.7344 5.5 25 5.5ZM7 11.5C9.0625 11.5 10.75 9.85938 10.75 7.75C10.75 5.6875 9.0625 4 7 4C4.89062 4 3.25 5.6875 3.25 7.75C3.25 9.85938 4.89062 11.5 7 11.5ZM7 5.5C8.21875 5.5 9.25 6.53125 9.25 7.75C9.25 9.01562 8.21875 10 7 10C5.73438 10 4.75 9.01562 4.75 7.75C4.75 6.53125 5.73438 5.5 7 5.5ZM27.2969 13H24.25C23.6875 13 23.1719 13.1406 22.7031 13.375C22.3281 13.5625 22.1875 13.9844 22.3281 14.3594C22.5156 14.7344 22.9844 14.9219 23.3594 14.7344C23.6406 14.5938 23.9219 14.5 24.25 14.5H27.2969C28.4688 14.5 29.5 15.5781 29.5 16.8906V17.5C29.5 17.9219 29.8281 18.25 30.25 18.25C30.625 18.25 31 17.9219 31 17.5V16.8906C31 14.7812 29.3125 13 27.2969 13ZM8.59375 14.7344C8.96875 14.9219 9.4375 14.7344 9.625 14.3594C9.76562 13.9844 9.625 13.5625 9.25 13.375C8.78125 13.1406 8.26562 13 7.75 13H4.65625C2.64062 13 1 14.7812 1 16.8906V17.5C1 17.9219 1.32812 18.25 1.75 18.25C2.125 18.25 2.5 17.9219 2.5 17.5V16.8906C2.5 15.5781 3.48438 14.5 4.65625 14.5H7.75C8.03125 14.5 8.3125 14.5938 8.59375 14.7344Z"
				fill={fill ?? '#242424'}
			/>
		</svg>
	);
};
