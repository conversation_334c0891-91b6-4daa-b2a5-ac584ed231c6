export const UserCircleProfileIcon = ({ fill, stroke, ...props }) => {
	return (
		<svg {...props} viewBox="0 0 25 25" fill="none">
			<path
				opacity="0.2"
				d="M12.5 3.34326C10.6794 3.34328 8.90167 3.89542 7.40142 4.9268C5.90117 5.95818 4.74899 7.42029 4.09698 9.12011C3.44497 10.8199 3.32379 12.6775 3.74946 14.4476C4.17512 16.2177 5.12759 17.8171 6.48114 19.0347V19.0346C7.04583 17.9236 7.90707 16.9907 8.96942 16.3392C10.0318 15.6876 11.2538 15.3429 12.5 15.3433C11.7583 15.3433 11.0333 15.1233 10.4166 14.7113C9.79993 14.2992 9.31928 13.7135 9.03545 13.0283C8.75162 12.3431 8.67736 11.5891 8.82206 10.8617C8.96675 10.1342 9.3239 9.46606 9.84835 8.94161C10.3728 8.41716 11.041 8.06001 11.7684 7.91532C12.4958 7.77062 13.2498 7.84488 13.9351 8.12871C14.6203 8.41254 15.206 8.89319 15.618 9.50987C16.0301 10.1266 16.25 10.8516 16.25 11.5933C16.25 12.5878 15.8549 13.5416 15.1516 14.2449C14.4484 14.9482 13.4946 15.3433 12.5 15.3433C13.7462 15.3429 14.9682 15.6876 16.0306 16.3392C17.0929 16.9907 17.9542 17.9236 18.5189 19.0346C19.8724 17.817 20.8249 16.2176 21.2505 14.4475C21.6762 12.6774 21.555 10.8199 20.903 9.12005C20.251 7.42024 19.0988 5.95814 17.5986 4.92677C16.0983 3.8954 14.3206 3.34327 12.5 3.34326Z"
				fill={fill ?? '#FD6461'}
			/>
			<path
				d="M12.5 21.3433C17.4706 21.3433 21.5 17.3138 21.5 12.3433C21.5 7.3727 17.4706 3.34326 12.5 3.34326C7.52944 3.34326 3.5 7.3727 3.5 12.3433C3.5 17.3138 7.52944 21.3433 12.5 21.3433Z"
				stroke={stroke ?? '#FD6461'}
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
			<path
				d="M12.5 15.3433C14.5711 15.3433 16.25 13.6643 16.25 11.5933C16.25 9.52219 14.5711 7.84326 12.5 7.84326C10.4289 7.84326 8.75 9.52219 8.75 11.5933C8.75 13.6643 10.4289 15.3433 12.5 15.3433Z"
				stroke={stroke ?? '#FD6461'}
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
			<path
				d="M6.48145 19.0346C7.04639 17.9238 7.90768 16.9911 8.96997 16.3396C10.0323 15.6881 11.2541 15.3433 12.5003 15.3433C13.7464 15.3433 14.9683 15.6881 16.0306 16.3396C17.0929 16.9911 17.9542 17.9238 18.5191 19.0346"
				stroke={stroke ?? '#FD6461'}
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
		</svg>
	);
};
