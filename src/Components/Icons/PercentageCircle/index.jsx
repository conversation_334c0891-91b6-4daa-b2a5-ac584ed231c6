export const PercentageCircle = ({ width, height, fill, ...props }) => {
	return (
		<svg
			width={width ?? 36}
			height={height ?? 36}
			viewBox="0 0 36 36"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}
		>
			<path
				d="M18.118 0.507648C8.43857 0.448941 0.544836 8.37081 0.500194 17.9188C0.45494 27.5505 8.34806 35.5488 17.9859 35.4925C27.5553 35.5561 35.3536 27.737 35.4979 18.2906C35.6484 8.46376 27.6476 0.565745 18.118 0.507648ZM17.7615 33.1717C9.4629 33.0537 2.73475 26.2106 2.83566 17.8026C2.93473 9.57248 9.72953 2.74099 18.1938 2.84189C26.4716 2.94096 33.1991 9.74616 33.1557 18.0044C33.2126 26.2332 26.4404 33.2953 17.7615 33.1717Z"
				fill={fill ?? '#FF5F5F'}
			/>
			<path
				d="M22.3741 12.3055C22.376 12.5421 22.2928 12.7525 22.1766 12.9537C19.8656 16.955 17.5546 20.9569 15.243 24.9582C14.9745 25.423 14.4896 25.6615 14.0162 25.5716C13.4903 25.4713 13.1301 25.0933 13.0476 24.5485C13.0017 24.2451 13.113 23.9803 13.261 23.7247C14.322 21.8889 15.3818 20.053 16.4416 18.2172C17.6812 16.0707 18.922 13.9254 20.1597 11.7783C20.4117 11.3405 20.7768 11.1124 21.2856 11.1411C21.7369 11.1668 22.0573 11.3985 22.2592 11.7973C22.3393 11.955 22.3784 12.1263 22.3741 12.3055Z"
				fill={fill ?? '#FF5F5F'}
			/>
			<path
				d="M13.0463 10.4231C11.1292 10.417 9.54896 11.9929 9.5459 13.9144C9.54346 15.8377 11.1059 17.4142 13.0274 17.4252C14.9476 17.4368 16.556 15.8364 16.5486 13.9217C16.5413 12.0015 14.9672 10.4286 13.0463 10.4231ZM13.05 15.0891C12.4097 15.0922 11.8844 14.5712 11.8826 13.9303C11.8808 13.2814 12.392 12.7616 13.0359 12.7592C13.6726 12.7561 14.2125 13.2876 14.2125 13.9168C14.2119 14.5547 13.6854 15.0855 13.05 15.0891Z"
				fill={fill ?? '#FF5F5F'}
			/>
			<path
				d="M22.9495 18.5957C20.9504 18.6018 19.4754 20.1796 19.4601 22.0949C19.4442 23.9956 21.0287 25.5795 22.9355 25.5862C24.8801 25.5936 26.45 24.0372 26.4549 22.0974C26.4634 20.2683 25.0239 18.5896 22.9495 18.5957ZM22.9581 23.2501C22.3147 23.2495 21.7839 22.7205 21.7931 22.0882C21.8023 21.4455 22.3239 20.9232 22.9617 20.9202C23.5922 20.9165 24.12 21.4498 24.1212 22.0919C24.1218 22.7389 23.6081 23.2507 22.9581 23.2501Z"
				fill={fill ?? '#FF5F5F'}
			/>
		</svg>
	);
};
