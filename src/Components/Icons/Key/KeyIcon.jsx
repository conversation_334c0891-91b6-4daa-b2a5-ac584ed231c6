export const KeyIcon = ({ fill, stroke, ...props }) => {
	return (
		<svg {...props} viewBox="0 0 24 24" fill="none">
			<path
				opacity="0.2"
				d="M9.23433 11.8588C8.65618 10.4167 8.59367 8.81925 9.05738 7.3364C9.52108 5.85354 10.4826 4.57627 11.7793 3.72048C13.076 2.86468 14.6285 2.48287 16.1742 2.63956C17.72 2.79626 19.1642 3.48185 20.2628 4.58047C21.3614 5.67908 22.047 7.12332 22.2037 8.66907C22.3604 10.2148 21.9786 11.7673 21.1228 13.064C20.267 14.3607 18.9897 15.3222 17.5069 15.7859C16.024 16.2496 14.4265 16.1871 12.9844 15.6089L12.9845 15.6088L11.75 16.8433H9.5V19.0933H7.25V21.3433H3.5V17.5933L9.2345 11.8588L9.23433 11.8588Z"
				fill={fill ?? '#FD6461'}
			/>
			<path
				d="M9.23433 11.8588C8.65618 10.4167 8.59367 8.81925 9.05738 7.3364C9.52108 5.85354 10.4826 4.57627 11.7793 3.72048C13.076 2.86468 14.6285 2.48287 16.1742 2.63956C17.72 2.79626 19.1642 3.48185 20.2628 4.58047C21.3614 5.67908 22.047 7.12332 22.2037 8.66907C22.3604 10.2148 21.9786 11.7673 21.1228 13.064C20.267 14.3607 18.9897 15.3222 17.5069 15.7859C16.024 16.2496 14.4265 16.1871 12.9844 15.6089L12.9845 15.6088L11.75 16.8433H9.5V19.0933H7.25V21.3433H3.5V17.5933L9.2345 11.8588L9.23433 11.8588Z"
				stroke={stroke ?? '#FD6461'}
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
			<path
				opacity="0.5"
				d="M17.375 7.84326C17.5821 7.84326 17.75 7.67537 17.75 7.46826C17.75 7.26115 17.5821 7.09326 17.375 7.09326C17.1679 7.09326 17 7.26115 17 7.46826C17 7.67537 17.1679 7.84326 17.375 7.84326Z"
				stroke={stroke ?? '#FD6461'}
				strokeWidth="2"
				strokeLinecap="round"
				strokeLinejoin="round"
			/>
			<path
				d="M17.375 8.59326C17.9963 8.59326 18.5 8.08958 18.5 7.46826C18.5 6.84694 17.9963 6.34326 17.375 6.34326C16.7537 6.34326 16.25 6.84694 16.25 7.46826C16.25 8.08958 16.7537 8.59326 17.375 8.59326Z"
				fill={fill ?? '#FD6461'}
			/>
		</svg>
	);
};
