export const Cross = ({ width, height, fill, ...props }) => {
	return (
		<svg
			width={width}
			height={height}
			viewBox="0 0 36 36"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}
		>
			<path
				d="M35.4866 17.607C35.2469 8.15957 27.5032 0.323619 17.6562 0.503021C7.84807 0.681795 0.441169 8.80567 0.508915 17.8874C0.399141 27.6773 8.50232 35.8137 18.5682 35.4907C28.1399 35.1827 35.7287 27.1554 35.4866 17.607ZM17.7459 33.9281C9.16096 33.8221 1.89144 26.8073 2.06456 17.5782C2.22389 9.12119 9.21929 2.05052 18.0081 2.06494C26.5635 2.04173 33.9246 8.92485 33.9221 18.0894C33.9196 26.7552 26.7925 34.0392 17.7459 33.9281Z"
				fill={fill ?? '#FF5F5F'}
			/>
			<path
				d="M24.3795 23.2537C24.705 23.5787 24.7771 23.9281 24.5946 24.2718C24.4491 24.5453 24.2195 24.6821 23.8369 24.6846C23.6361 24.6902 23.4385 24.5484 23.256 24.3647C22.2887 23.3936 21.319 22.4251 20.3498 21.456C19.6303 20.7358 18.9065 20.0195 18.1939 19.2925C18.049 19.1444 17.9706 19.1281 17.8137 19.2862C16.1257 20.9874 14.4283 22.6792 12.7353 24.3753C12.5822 24.5284 12.4204 24.6551 12.1965 24.6795C11.8464 24.7166 11.5485 24.5616 11.3992 24.2611C11.2399 23.9412 11.29 23.6 11.5548 23.334C12.4781 22.4075 13.4046 21.4848 14.3305 20.5608C15.134 19.7585 15.9344 18.9531 16.7442 18.1571C16.8709 18.0323 16.8659 17.9696 16.7423 17.8466C15.0437 16.1561 13.3513 14.4599 11.6564 12.7657C11.4694 12.5787 11.3139 12.3799 11.3145 12.0963C11.3158 11.7978 11.4312 11.5663 11.6934 11.4151C11.9706 11.2564 12.2485 11.2608 12.5145 11.4333C12.6249 11.5055 12.7221 11.5995 12.8156 11.693C14.4892 13.3647 16.1634 15.0351 17.8288 16.7144C17.9812 16.8674 18.0496 16.8467 18.1888 16.7068C19.8838 15.0019 21.5849 13.3032 23.2861 11.6039C23.6142 11.2759 23.9529 11.2144 24.3029 11.4107C24.6749 11.619 24.8041 12.1001 24.589 12.4696C24.5212 12.5856 24.4296 12.6816 24.3355 12.7757C22.6513 14.4593 20.9708 16.1448 19.2809 17.8221C19.1316 17.9708 19.1398 18.0404 19.2834 18.1828C20.9865 19.8689 22.6827 21.562 24.3795 23.2537Z"
				fill={fill ?? '#FF5F5F'}
			/>
		</svg>
	);
};
