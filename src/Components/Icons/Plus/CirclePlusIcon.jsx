export const CirclePlusIcon = ({
	fill = '#ffffff',
	width = '28px',
	height = '28px',
	...props
}) => {
	return (
		<>
			<svg
				width={width}
				height={height}
				viewBox="0 0 31 32"
				fill="none"
				style={{ rotate: '45deg' }}
				{...props}
			>
				<path
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M15.5594 31C23.6003 31 30.1188 24.2843 30.1188 16C30.1188 7.71573 23.6003 1 15.5594 1C7.51847 1 1 7.71573 1 16C1 24.2843 7.51847 31 15.5594 31Z"
					fill={fill}
					stroke="#565656"
					stroke-width="0.2"
				/>
				<path
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M15.5606 15.1763L18.5574 12.0888C18.7785 11.861 19.137 11.861 19.3581 12.0888C19.5792 12.3166 19.5792 12.686 19.3581 12.9138L16.3613 16.0013L19.3581 19.0888C19.5792 19.3166 19.5792 19.686 19.3581 19.9138C19.137 20.1416 18.7785 20.1416 18.5574 19.9138L15.5606 16.8263L12.5637 19.9138C12.3426 20.1416 11.9841 20.1416 11.763 19.9138C11.5419 19.686 11.5419 19.3166 11.763 19.0888L14.7598 16.0013L11.763 12.9138C11.5419 12.686 11.5419 12.3166 11.763 12.0888C11.9841 11.861 12.3426 11.861 12.5637 12.0888L15.5606 15.1763Z"
					fill="#565656"
				/>
			</svg>
		</>
	);
};
