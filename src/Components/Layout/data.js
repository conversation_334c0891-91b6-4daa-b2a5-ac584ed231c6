import { AdvertiserPanelRoutes, VenuePanelRoutes } from '../../Utils/routes';

const SidebarMenus = [
    {
        title: '',
        menus: [
            {
                title: 'Home',
                path: VenuePanelRoutes.home
            },
            {
                title: 'Live Order Dashboard',
                path: VenuePanelRoutes.dashboard
            }
            // {
            // 	title: 'Scheduled Orders',
            // 	path: VenuePanelRoutes.venueScheduleOrders
            // }
        ]
    },
    {
        title: 'VENUE SETTINGS',
        menus: [
            {
                title: 'Venue Profile',
                path: VenuePanelRoutes.venueProfile
            },
            {
                title: 'Opening Hours',
                path: VenuePanelRoutes.venueOpeningHours
            },
            {
                title: 'Manage Menu',
                path: VenuePanelRoutes.manageMenu
            },
            {
                title: 'Menu Pick Up Locations',
                path: VenuePanelRoutes.menuPickupLocation
            },
            {
                title: 'Menu Wait Times',
                path: VenuePanelRoutes.menuWaitTimes
            },
            {
            	title: 'Discounts',
            	path: VenuePanelRoutes.newDiscounts
            },
            {
                title: 'Surcharges',
                path: VenuePanelRoutes.surcharges
            },
            // {
            //     title: 'Discounts',
            //     path: VenuePanelRoutes.promocode
            // },
            {
                title: 'Product Taxes',
                path: VenuePanelRoutes.taxes
            },
            {
                title: 'Integrations',
                path: VenuePanelRoutes.posIntegration
            },
            {
                title: 'Docket Printing',
                path: VenuePanelRoutes.docketFeatures
            }
        ]
    },
    {
        title: 'FEATURES',
        menus: [
            {
                title: 'Customers',
                path: VenuePanelRoutes.customers
            },
            {
                title: 'Segments',
                path: VenuePanelRoutes.segments
            },
            {
            	title: 'MyTab Ads',
            	path: VenuePanelRoutes.myTabAds
            },
            {
                title: 'In-App Advertising',
                path: VenuePanelRoutes.inAppAdvertising
            }
        ]
    },
    {
        title: 'REPORTS',
        menus: [
            {
                title: 'Order History',
                path: VenuePanelRoutes.orderHistory
            },
            {
                title: 'Financial Report',
                path: VenuePanelRoutes.financialReport
            },
            {
                title: 'Item Summary Report',
                path: VenuePanelRoutes.summaryReport
            },
            {
                title: 'Venue Analytics',
                path: VenuePanelRoutes.salesAnalytics
            }
            // {
            // 	title: 'Customer Analytics',
            // 	path: VenuePanelRoutes.customerAnalytics
            // }
        ]
    },
    {
        title: 'VENUE ACCOUNT SECURITY',
        menus: [
            {
                title: 'Venue Account Password',
                path: VenuePanelRoutes.venueAccountPassword
            },
            {
                title: 'iPad Access Passcode',
                path: VenuePanelRoutes.venueIpadAccessPasscode
            },
            {
                title: 'Refund PIN',
                path: VenuePanelRoutes.venueRefundPin
            }
        ]
    },
    {
        title: 'SUPPORT',
        menus: [
            {
                title: 'FAQ Support Forum',
                path: VenuePanelRoutes.support
            },
            {
                title: 'Live Support',
                path: VenuePanelRoutes.liveSupport
            },
            {
                title: 'Email Us',
                path: VenuePanelRoutes.emailUs
            }
        ]
    },
    {
        title: 'LEGAL',
        menus: [
            {
                title: 'Privacy Policy',
                path: 'https://mytabinfo.com/policies/privacy-policy',
                target: '_blank'
            },
            {
                title: 'Terms & Conditions',
                path: 'https://mytabinfo.com/policies/terms-of-service',
                target: '_blank'
            }
        ]
    }
];
export const AdvertiserSidebarMenus = [
    {
        title: '',
        menus: [
            {
                title: 'MyTab Ads',
                path: AdvertiserPanelRoutes?.myTabAds
            }
        ]
    },
    {
        title: 'SUPPORT',
        menus: [
            {
                title: 'FAQ Support Forum',
                path: AdvertiserPanelRoutes?.support
            },
            {
                title: 'Email Us',
                path: AdvertiserPanelRoutes?.emailUs
            }
        ]
    },
    {
        title: 'LEGAL',
        menus: [
            {
                title: 'Privacy Policy',
                path: 'https://mytabinfo.com/policies/privacy-policy',
                target: '_blank'
            },
            {
                title: 'Terms & Conditions',
                path: 'https://mytabinfo.com/policies/terms-of-service',
                target: '_blank'
            }
        ]
    }
];

export default SidebarMenus;
