import React from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';

const Styles = styled.div`
	.menuTitle {
		font-size: 10px;
		color: #9b9b9d !important;
		font-weight: 700;
		height: 25px;
		padding-left: 42px;
	}
	.menusWrraper {
		display: flex;
		margin-bottom: 0.3rem;
		align-items: center;
		padding-right: 12%;
		user-select: none;
		cursor: pointer;
		.menusTitle {
			display: flex;
			align-items: center;
			padding-left: 4rem;
			height: 2rem;
			font-size: 12px;
			font-weight: 600;
			color: rgba(32, 34, 36, 1);
			border-radius: 3px;
			width: 100%;
		}

		.menusActive {
			display: flex;
			width: 39px;
			height: 2rem;
		}

		.menusActiveLine {
			height: 2rem;
			padding: 1px;
			border-top-right-radius: 10px;
			border-bottom-right-radius: 10px;
		}
	}
	.menusWrraperActive {
		display: flex;
		margin-bottom: 0.3rem;
		align-items: center;
		padding-right: 12%;
		user-select: none;
		cursor: pointer;
		.menusTitle {
			display: flex;
			align-items: center;
			padding-left: 4rem;
			height: 2rem;
			font-size: 12px;
			font-weight: 600;
			border-radius: 3px;
			width: 100%;
			color: #ffffff;
			background: rgba(249, 92, 105, 0.85);
		}

		.menusActive {
			display: flex;
			width: 39px;
			height: 2rem;
		}

		.menusActiveLine {
			height: 2rem;
			padding: 1px;
			border-top-right-radius: 10px;
			border-bottom-right-radius: 10px;
			background: rgba(249, 92, 105, 0.85);
		}
	}
	.menusWrraper:hover {
		.menusTitle {
			color: #ffffff;
			background: rgba(249, 92, 105, 0.85);
		}
		.menus {
			color: rgba(255, 255, 255, 1);
			background: rgba(249, 92, 105, 0.85);
		}
		.menusActiveLine {
			background: rgba(249, 92, 105, 0.85);
		}
	}
	.hrLine {
		height: 2px;
		background: rgba(224, 224, 224, 1);
		margin-block: 1.5rem;
	}
`;

const MobileMenu = ({ Menus, datas, location, index, onClose }) => {
	const navigate = useNavigate();
	return (
		<Styles>
			<div>
				{datas?.title && <h6 className="menuTitle">{datas.title}</h6>}
				{datas.menus.map((items) => (
					<div
						onClick={() => {
							navigate(items.path);
							onClose();
						}}
						className={
							location.pathname === items?.path
								? 'menusWrraperActive'
								: 'menusWrraper'
						}
					>
						<div className="menusActive">
							<span className="menusActiveLine"></span>
						</div>
						<p className="menusTitle">{items.title}</p>
					</div>
				))}
			</div>
			{!(Menus.length - 1 === index) && <div className="hrLine" />}
		</Styles>
	);
};

export default MobileMenu;
