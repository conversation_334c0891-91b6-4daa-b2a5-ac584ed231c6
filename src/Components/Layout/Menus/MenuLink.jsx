import React from 'react';

const MenuLink = ({ datas, handleMenuLink, SidebarMenus, index }) => {
	return (
		<div>
			<div>
				{datas?.title && <h6 className="menuTitle">{datas.title}</h6>}
				{datas.menus.map((items) => (
					<div
						onClick={() => handleMenuLink(items)}
						className={
							window.location.pathname === items?.path
								? 'menusWrraperActive'
								: 'menusWrraper'
						}
					>
						<div className="menusActive">
							<span className="menusActiveLine"></span>
						</div>
						<p className="menus">{items.title}</p>
					</div>
				))}
			</div>
			{!(SidebarMenus.length - 1 === index) && <div className="hrLine" />}
		</div>
	);
};

export default MenuLink;
