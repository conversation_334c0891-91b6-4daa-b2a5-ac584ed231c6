import React from 'react';
import { Offcanvas, OffcanvasBody, OffcanvasHeader } from 'reactstrap';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';

import MobileMenu from './MobileMenu';
import { AdvertiserPanelRoutes, VenuePanelRoutes } from '../../../Utils/routes';
import SidebarMenus, { AdvertiserSidebarMenus } from '../data';

const ResponsiveMenu = ({
	openSidebar,
	noRefCheck,
	appNameLogo,
	location,
	onclose
}) => {
	const navigate = useNavigate();
	const authDetails = useSelector((state) => state.auth);

	return (
		<Offcanvas
			isOpen={openSidebar}
			onClosed={onclose}
			className="w-100 customOffcanvas"
			toggle={noRefCheck}
		>
			<OffcanvasHeader toggle={noRefCheck}>
				<img
					src={appNameLogo}
					onClick={() => {
						navigate(
							authDetails?.login_type === 'venue'
								? VenuePanelRoutes.home
								: AdvertiserPanelRoutes?.myTabAds
						);
						onclose();
					}}
					alt="app-name"
					style={{ cursor: 'pointer' }}
					width={90}
					height={28}
				/>
			</OffcanvasHeader>
			<OffcanvasBody className="p-0">
				{authDetails?.login_type === 'venue' && (
					<>
						{SidebarMenus?.length > 0 &&
							SidebarMenus?.map((datas, index) => (
								<MobileMenu
									onClose={() => onclose()}
									Menus={SidebarMenus}
									datas={datas}
									index={index}
									location={location}
								/>
							))}
					</>
				)}
				{authDetails?.login_type === 'advertiser' && (
					<>
						{AdvertiserSidebarMenus?.length > 0 &&
							AdvertiserSidebarMenus?.map((datas, index) => (
								<MobileMenu
									onClose={() => onclose()}
									Menus={AdvertiserSidebarMenus}
									datas={datas}
									index={index}
									location={location}
								/>
							))}
					</>
				)}
				<div className="pb-70" />
			</OffcanvasBody>
		</Offcanvas>
	);
};

export default ResponsiveMenu;
