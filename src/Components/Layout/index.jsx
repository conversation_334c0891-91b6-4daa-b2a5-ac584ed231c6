import React, { useEffect, useRef } from 'react';
import LayoutWrraper from './index.style';
import Scrollbars from 'react-custom-scrollbars';
import appNameLogo from '../../Assets/images/Logo.svg';

import { useLocation, useNavigate } from 'react-router-dom';

import { useSelector } from 'react-redux';

import LayoutHeader from './Header';
import LayoutFooter from './Footer';
import SidebarMenus, { AdvertiserSidebarMenus } from './data';
import GlobalWrapper from './global.style';
import { AdvertiserPanelRoutes, VenuePanelRoutes } from '../../Utils/routes';
import MenuLink from './Menus/MenuLink';
import { MYTAB_LOGO_2024 } from '../../Helper/constant';

const Layout = ({ children }) => {
	const location = useLocation();
	const navigate = useNavigate();
	const scrollbarRef = useRef(null);

	useEffect(() => {
		if (scrollbarRef?.current) {
			scrollbarRef.current.scrollTop(0);
		}
	}, [location, scrollbarRef]);

	const allthemedata = useSelector((state) => ({
		...state?.themeChanger,
		themeSetting: state?.themeSetting
	}));
	const authDetails = useSelector((state) => state.auth);

	const handleMenuLink = (items) => {
		if (items.hasOwnProperty('target')) {
			window.open(items.path, '_blank');
		} else {
			navigate(items.path);
		}
	};

	return (
		<GlobalWrapper {...allthemedata}>
			<LayoutWrraper>
				<div className="sidebarWrraper">
					{authDetails?.login_type === 'venue' &&
						authDetails?.bars?.length == 0 && (
							<div className="disableSidebar"></div>
						)}
					{/* sidebar */}
					<Scrollbars>
						<div className="position-sticky top-0 logoWrraper">
							<img
								onClick={() =>
									navigate(
										authDetails?.login_type === 'venue'
											? VenuePanelRoutes.home
											: AdvertiserPanelRoutes?.myTabAds
									)
								}
								src={MYTAB_LOGO_2024}
								style={{ cursor: 'pointer' }}
								alt="app-name"
								className="logoImage"
							/>
						</div>
						{authDetails?.login_type === 'venue' && (
							<>
								{SidebarMenus.map((datas, index) => (
									<MenuLink
										key={datas.title}
										SidebarMenus={SidebarMenus}
										datas={datas}
										index={index}
										handleMenuLink={handleMenuLink}
									/>
								))}
							</>
						)}
						{authDetails?.login_type === 'advertiser' && (
							<>
								{AdvertiserSidebarMenus?.length > 0 &&
									AdvertiserSidebarMenus?.map(
										(datas, index) => (
											<MenuLink
												key={datas.title}
												SidebarMenus={
													AdvertiserSidebarMenus
												}
												datas={datas}
												index={index}
												handleMenuLink={handleMenuLink}
											/>
										)
									)}
							</>
						)}
						<div className="pb-70" />
					</Scrollbars>
				</div>

				<div className="w-100 workspaceWrapper">
					{/* Header */}
					<LayoutHeader />
					{/* content */}
					<div style={{ flex: '1' }}>
						<Scrollbars autoHide ref={scrollbarRef}>
							<div
								style={{
									width: '100%',
									height: '100%',
									display: 'flex',
									flexDirection: 'column'
								}}
							>
								<div style={{ width: '100%', flex: 1 }}>
									{children}
								</div>
								<LayoutFooter />
							</div>
						</Scrollbars>
					</div>
				</div>
			</LayoutWrraper>
		</GlobalWrapper>
	);
};

export default Layout;
