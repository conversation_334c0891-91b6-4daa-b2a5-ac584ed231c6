import styled from 'styled-components';

const FooterLayoutWrraper = styled.div`
	background: #242424;
	color: #ffffff;
	padding: 4px 0;
	font-size: 12px;
	font-family: nunitosans-regular;
	display: flex;
	align-items: center;
	justify-content: center;

	/* bottom: 0;
	background: #242424;
	color: #ffffff;
	height: 5.3vh;
	display: flex;
	font-size: 14px;
	font-family: nunitosans-regular;
	align-items: center;
	justify-content: center;

	@media only screen and (min-width: 1024px) and (max-width: 1824px) and (max-height: 900px) {
		bottom: 0;
		background: #242424;
		color: #ffffff;
		height: 3.5vh;
		display: flex;
		font-size: 11px;
		font-family: nunitosans-light;
		align-items: center;
		justify-content: center;
	} */
`;

export default FooterLayoutWrraper;
