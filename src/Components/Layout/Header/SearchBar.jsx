import React, { useEffect, useRef, useState } from 'react';
import SidebarMenus, { AdvertiserSidebarMenus } from '../data';
import searchIcon from '../../../Assets/images/searchNew.svg';
import {
	DropdownItem,
	DropdownMenu,
	DropdownToggle,
	PopoverBody,
	PopoverHeader,
	UncontrolledDropdown,
	UncontrolledPopover
} from 'reactstrap';
import { useNavigate } from 'react-router-dom';
import Scrollbars from 'react-custom-scrollbars';
import { useSelector } from 'react-redux';

const GlobalSearchBar = () => {
	const authDetails = useSelector((state) => state.auth);
	const [searchResultData, setSearchResultData] = useState([]);
	const [searchResultOpen, setSearchResultOpen] = useState(false);

	const navigate = useNavigate();
	const searchRef = useRef(null);
	const searchWrapperRef = useRef(null);

	function displaySearchResults(results) {
		setSearchResultOpen(true);
		if (results.length === 0) {
			setSearchResultData([]);
		} else {
			const searchResult = [];
			results.forEach((result) => {
				searchResult.push({
					title: result.title,
					path: result.path
				});
			});
			setSearchResultData(searchResult);
		}
	}

	useEffect(() => {
		// Function to handle click outside
		const handleClickOutside = (event) => {
			if (
				searchWrapperRef.current &&
				!searchWrapperRef.current.contains(event.target)
			) {
				setSearchResultOpen(false);
			}
		};

		// Bind the event listener
		document.addEventListener('mousedown', handleClickOutside);

		// Clean up the event listener on unmount
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, []);

	return (
		<div className="position-relative w-100">
			<div className="searchBar w-100 d-none d-xl-flex d-md-flex">
				<img alt="search" src={searchIcon} />
				<input
					autoComplete="off"
					ref={searchRef}
					id="UncontrolledPopover"
					type="search"
					placeholder={
						authDetails?.login_type === 'venue'
							? 'Search your Management Portal'
							: 'Search your Advertiser Portal'
					}
					onChange={(e) => {
						const query = e.target.value.trim();

						// Clear previous results

						if (query === '') {
							setSearchResultOpen(false);
							// If search input is empty, clear results and return
							setSearchResultData([]);
							return;
						}

						const regex = new RegExp(query, 'i'); // 'i' flag for case-insensitive matching
						const results = [];

						// Perform search through SidebarMenus
						if (
							authDetails?.login_type === 'venue' &&
							authDetails?.bars?.length > 0
						) {
							SidebarMenus.forEach((section) => {
								section.menus.forEach((menu) => {
									if (
										regex.test(menu.title) ||
										regex.test(menu.path)
									) {
										results.push(menu);
									}
								});
							});
						} else if (authDetails?.login_type === 'advertiser') {
							AdvertiserSidebarMenus.forEach((section) => {
								section.menus.forEach((menu) => {
									if (
										regex.test(menu.title) ||
										regex.test(menu.path)
									) {
										results.push(menu);
									}
								});
							});
						}

						// Display results
						setTimeout(() => {
							displaySearchResults(results);
						}, 1000);
					}}
				/>
			</div>
			{searchResultOpen && (
				<div
					ref={searchWrapperRef}
					className="position-absolute searchResultWrraper"
				>
					{searchResultData.length > 0}
					{searchResultData.length > 0 ? (
						searchResultData.map((results) => (
							<div
								type="button"
								tabindex="0"
								role="menuitem"
								class="dropdown-item"
								onKeyDown={(e) => {
									if (e.code === 'Enter') {
										navigate(results?.path);
										setSearchResultData([]);
										searchRef.current.value = '';
										setSearchResultOpen(false);
									}
								}}
								onClick={(e) => {
									navigate(results?.path);
									setSearchResultData([]);
									searchRef.current.value = '';
									setSearchResultOpen(false);
								}}
							>
								{results?.title}
							</div>
						))
					) : (
						<div>No Result Found</div>
					)}
				</div>
			)}
		</div>
	);
};

export default GlobalSearchBar;
