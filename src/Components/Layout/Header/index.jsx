import React, { useState } from 'react';
import {
	DropdownItem,
	DropdownMenu,
	DropdownToggle,
	UncontrolledDropdown
} from 'reactstrap';
import { HamburgerIcon, UserCircleIcon } from '../../Icons';
import appNameLogo from '../../../Assets/images/Logo.svg';
import notificationIcon from '../../../Assets/images/notification-icon.svg';
import DropdownIcon from '../../../Assets/images/newDropdownIcon.svg';

import {
	AdvertiserApiRoutes,
	AdvertiserPanelRoutes,
	CommonRoutes,
	VenueApiRoutes,
	VenuePanelRoutes
} from '../../../Utils/routes';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import LogoutConfirmModal from '../../Header/LogoutConfirmModal';
import Api from '../../../Helper/Api';
import { toast } from 'react-toastify';
import { useDispatch } from 'react-redux';
import authActions from '../../../Redux/auth/actions';
import UserName from './UserName';
import ResponsiveMenu from '../Menus';
import SidebarMenus from '../data';
import GlobalSearchBar from './SearchBar';
import styled from 'styled-components';
import { MYTAB_LOGO_2024 } from '../../../Helper/constant';

const LayoutHeader = () => {
	const location = useLocation();
	const navigate = useNavigate();
	const dispatch = useDispatch();
	const authData = useSelector((state) => state.auth);
	const profileData = useSelector((state) => ({ ...state.auth }));

	const [openLogoutModal, setOpenLogoutModal] = useState(false);
	const [openSidebar, setOpenSidebar] = useState(false);
	const [loading, setLoading] = useState(false);

	const noRefCheck = () => setOpenSidebar(!openSidebar);

	const handleLogoutModal = () => {
		setOpenLogoutModal((prev) => !prev);
	};

	const userSignOut = async () => {
		let api = '';
		if (profileData?.login_type === 'venue') {
			api = VenueApiRoutes?.logout;
		} else if (profileData?.login_type === 'advertiser') {
			api = AdvertiserApiRoutes?.logout;
		}
		setLoading(true);
		try {
			const res = await Api('POST', api);
			if (res?.data?.status) {
				if (profileData?.login_type === 'venue') {
					dispatch(authActions.logout());
					navigate(CommonRoutes.login);
				} else if (profileData?.login_type === 'advertiser') {
					dispatch(authActions.advertiser_logout());
					navigate(CommonRoutes.landingPage);
				}
				toast.success(res?.data?.message);
				setOpenLogoutModal(false);
			} else {
				toast.error(res?.data?.message);
			}
			setLoading(false);
		} catch (err) {
			toast.error(err.response);
			setLoading(false);
		}
	};

	const Div = styled.div`
		.UserCircleIcon {
			height: 34px;
			width: 34px;
		}

		@media only screen and (min-width: 1024px) and (max-width: 1824px) and (max-height: 900px) {
			.UserCircleIcon {
				height: 26px;
				width: 26px;
			}
		}
	`;

	return (
		<div className="headerWrraper">
			<div className="row align-items-center h-100">
				<div className="col-6">
					<div className="d-flex align-items-center"></div>

					<div className="d-flex align-items-center gap-3">
						<div className="pl-25 menuIcon">
							<HamburgerIcon
								width={28}
								height={28}
								onClick={noRefCheck}
							/>
						</div>
						<div className="position-sticky top-0 displayAppIcon">
							<img
								onClick={() =>
									navigate(
										profileData?.login_type === 'venue'
											? VenuePanelRoutes.home
											: AdvertiserPanelRoutes?.myTabAds
									)
								}
								src={MYTAB_LOGO_2024}
								alt="app-name"
								className="logoImage"
								style={{
									objectFit: 'contain',
									cursor: 'pointer'
								}}
							/>
						</div>
						<GlobalSearchBar />
					</div>
				</div>
				<div className="col-6  text-end">
					<div className="d-flex gap-4 justify-content-end align-items-center">
						{/* <div className="d-none d-md-none d-lg-block d-xl-block">
							<div className="position-relative">
								<span className="position-absolute notificationCount">
									6
								</span>
								<img
									src={NotificationIcon}
									height={25}
									width={25}
									alt="menu icon"
									className="cursor-pointer"
								/>
							</div>
						</div> */}

						<div className="pr-30 profile-menu d-block d-md-block d-lg-block d-xl-block">
							<div className="d-flex align-items-center gap-3">
								{/* <div className="d-block d-md-block d-lg-block d-xl-block">
									{
										<div className="notification-icon">
											<img
												onClick={() =>
													navigate(
														VenuePanelRoutes.home
													)
												}
												src={notificationIcon}
												alt="app-name"
												width={93}
												height={33}
												style={{ objectFit: 'contain' }}
											/>
										</div>
									}
								</div> */}
								<UncontrolledDropdown>
									<DropdownToggle
										style={{
											paddingInline: 0,
											background: 'transparent',
											border: 'none',
											outline: 'none'
										}}
									>
										<div className="d-flex align-items-center gap-2">
											<div className="d-block d-md-block d-lg-block d-xl-block">
												{profileData.profile_image ? (
													<div className="userCircleIconWrapper">
														<img
															src={
																profileData.profile_image
															}
															alt="menu icon"
														/>
													</div>
												) : (
													<div className="userCircleIconWrapper">
														<Div>
															<UserCircleIcon className="UserCircleIcon" />
														</Div>
													</div>
												)}
											</div>
											<div className="displayAvatarControl">
												<UserName />
											</div>
											<img
												caret
												src={DropdownIcon}
												alt="menu icon"
												className="cursor-pointer displayAvatarControl-image"
											/>
										</div>
									</DropdownToggle>
									{profileData.login_type === 'venue' && (
										<DropdownMenu
											right
											className="profile-dropdown-menu"
										>
											{authData?.bars?.length !== 0 && (
												<DropdownItem
													onClick={() => {
														navigate(
															`${
																location.pathname.includes(
																	'admin'
																)
																	? '/admin/edit-profile'
																	: VenuePanelRoutes.manageAccount
															}`
														);
													}}
												>
													Manage Account
												</DropdownItem>
											)}
											<DropdownItem
												onClick={() => {
													navigate(
														`${
															location.pathname.includes(
																'admin'
															)
																? '/admin/connect-venue'
																: VenuePanelRoutes.connectVenue
														}`
													);
												}}
											>
												Connect Your Venue
											</DropdownItem>
											{/* {authData?.bars?.length !== 0 && (
											<DropdownItem
												onClick={() => {
													navigate(
														VenuePanelRoutes.mytabStaff
													);
												}}
											>
												MyTab Staff
											</DropdownItem>
										)} */}
											{authData?.bars?.length !== 0 && (
												<DropdownItem
													onClick={() => {
														navigate(
															`${
																location.pathname.includes(
																	'admin'
																)
																	? '/admin/security'
																	: VenuePanelRoutes.security
															}`
														);
													}}
												>
													Security
												</DropdownItem>
											)}
											<DropdownItem
												onClick={() =>
													setOpenLogoutModal(true)
												}
											>
												Logout
											</DropdownItem>
										</DropdownMenu>
									)}
									{profileData.login_type ===
										'advertiser' && (
										<DropdownMenu
											right
											className="profile-dropdown-menu"
										>
											<DropdownItem
												onClick={() =>
													navigate(
														`${AdvertiserPanelRoutes?.manageAccount}`
													)
												}
											>
												Manage Account
											</DropdownItem>
											<DropdownItem
												onClick={() =>
													navigate(
														`${AdvertiserPanelRoutes?.security}`
													)
												}
											>
												Security
											</DropdownItem>
											<DropdownItem
												onClick={() =>
													setOpenLogoutModal(true)
												}
											>
												Logout
											</DropdownItem>
										</DropdownMenu>
									)}
								</UncontrolledDropdown>
							</div>
						</div>
					</div>
				</div>
			</div>
			<ResponsiveMenu
				appNameLogo={MYTAB_LOGO_2024}
				location={location}
				noRefCheck={noRefCheck}
				openSidebar={openSidebar}
				onclose={() => setOpenSidebar(false)}
			/>
			{openLogoutModal && (
				<LogoutConfirmModal
					isOpen={openLogoutModal}
					handleModal={handleLogoutModal}
					loading={loading}
					userSignOut={userSignOut}
				/>
			)}
		</div>
	);
};

export default LayoutHeader;
