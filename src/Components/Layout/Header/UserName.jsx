import React from 'react';
import { useSelector } from 'react-redux';

const UserName = ({ className, transparent }) => {
	const profileData = useSelector((state) => ({ ...state.auth }));

	return (
		<>
			{transparent ? (
				<>
					{profileData.login_type === 'venue'
						? (profileData.first_name
								? profileData.first_name
								: '') +
						  ' ' +
						  (profileData.last_name ? profileData.last_name : '')
						: (profileData.first_name
								? profileData.first_name
								: '') +
						  ' ' +
						  (profileData.last_name ? profileData.last_name : '')}
				</>
			) : (
				<text className={`userName ${className}`}>
					{profileData.login_type === 'venue' && (
						<>
							{(profileData.first_name
								? profileData.first_name
								: '') +
								' ' +
								(profileData.last_name
									? profileData.last_name
									: '')}
						</>
					)}
					{profileData.login_type === 'advertiser' &&
						(profileData.business_name
							? profileData.business_name
							: '')}
				</text>
			)}
		</>
	);
};

export default UserName;
