import React from 'react';
import NewThemeButtonWrraper from './index.style';

export const OutlineButton = ({ buttonText, onClick }) => {
    return (
        <div onClick={onClick}>
            <NewThemeButtonWrraper>
                <div className="outlineButtonNewTheme">{buttonText}</div>
            </NewThemeButtonWrraper>
        </div>
    );
};

export const FilledButton = ({
    buttonText,
    onClick,
    color,
    background,
    style = {},
    loading = false,
    icon,
    showLoadingText = true
}) => {
	let disabledStyle = { pointerEvents: 'none', opacity: 0.5 };
	return (
		<div
			onClick={onClick}
			className="d-flex align-items-end"
			style={loading ? { ...disabledStyle } : {}}
		>
			<NewThemeButtonWrraper
				primary="red"
				color={color}
				background={background}
			>
				<div className="filledButtonNewTheme" style={style}>
					{loading ? (
						<>
							<span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
							{showLoadingText && "Loading..."}
						</>
					) : (
						<>
							{buttonText}
							{icon && icon}
						</>
					)}
				</div>
			</NewThemeButtonWrraper>
		</div>
	);
};
