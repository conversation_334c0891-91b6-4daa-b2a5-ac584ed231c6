import styled from 'styled-components';

const NewThemeButtonWrraper = styled.div`
	.outlineButtonNewTheme {
		width: 160px;
		height: 46px;
		border: 0.5px solid #000000;
		border-radius: 4.5px;
		background: #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
		font-family: nunitosans-bold;
		font-size: 12px;
		color: #202224;
		cursor: pointer;
		user-select: none;
	}

	.filledButtonNewTheme {
		width: 160px;
		height: 46px;
		background: ${(props) => props.background};
		color: ${(props) => props.color};
		border-radius: 4.5px;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 4px;
		cursor: pointer;
		user-select: none;
		font-size: 12px;
		font-family: nunitosans-bold;
	}

	@media (max-width: 600px) {
		.outlineButtonNewTheme,
		.filledButtonNewTheme {
			width: 140px;
			height: 35px;
			font-size: 10px;
			font-family: nunitosans-bold;
		}
	}

	@media only screen and (min-width: 600px) and (max-width: 1024px) {
		.outlineButtonNewTheme,
		.filledButtonNewTheme {
			width: 120px;
			height: 38px;
			font-size: 10px;
			font-family: nunitosans-bold;
		}
	}

	@media only screen and (min-width: 1024px) and (max-width: 1824px) and (max-height: 900px) {
		.outlineButtonNewTheme,
		.filledButtonNewTheme {
			width: 120px;
			height: 38px;
			font-size: 10px;
			font-family: nunitosans-bold;
		}
	}
`;

export default NewThemeButtonWrraper;
