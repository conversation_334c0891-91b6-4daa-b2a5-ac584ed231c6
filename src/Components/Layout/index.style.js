import styled from 'styled-components';

const LayoutWrraper = styled.div`
	display: flex;
	overflow: hidden;
	.logoWrraper {
		height: 7.4vh;
		background: #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
		padding-right: 1vw;
	}
	
	.logoImage {
		width:133px;
		height: 41px;
	}

	.searchBar {
		margin-left: 30px;
		padding: 0.5rem;
		gap: 10px;
		display: flex;
		align-items: center;
		background: #f5f6fa;
		border: 0.6px solid #d5d5d5;
		border-radius: 20px;
		height: 38px;
		max-width: 415.82px;
		fkex-direction: column;

		input {
			width: 100%;
			border: none;
			background: transparent;
			outline: none;
			font-size: 14px;
			font-family: nunitosans-regular;
		}

		img {
			width: 16px;
			height: 15px;
		}
	}

	.searchResultWrraper {
		overflow: hidden;
		overflow-y: auto;
		border-radius: 2px;
		max-height: 400px;
		max-width: 21vw;
		display: flex;
		flex-direction: column;
		gap: 6px;
		background: white;
		width: 100%;
		z-index: 10;
		left: 38px;
		top: 3rem;
		font-size: 14px;
		font-family: nunitosans-regular;
		border: 1px solid #31313259;
		div {
			padding: 3px 20px;
		}
		div:hover,
		div:focus {
			color: white;
			background-color: rgb(250, 116, 128);
		}
	}

	.sidebarWrraper {
		width: 20%;
		height: 100vh;
	}

	/* .headerWrraper {
		height: 4vw;
		width: auto;
		z-index: 100;
		@media only screen and (max-width: 1200px) {
			height: 5vw !important;
		}
		@media only screen and (max-width: 1000px) {
			height: 6vw !important;
		}
		@media only screen and (max-width: 768px) {
			height: 6.5vw !important;
		}
		@media only screen and (max-width: 680px) {
			height: 48px !important;
		}
	} */

	/* .contentWrraper {
		height: 95vh;
		background: #f5f5f559;
	} */

	.footer-space {
		padding-bottom: 24px;
		background-color: #242424;
	}

	.menuTitle {
		font-size: 12px;
		color: #9b9b9d !important;
		font-family: nunitosans-bold;
		height: 25px;
		padding-left: 42px;
	}

	.menusWrraper {
		display: flex;
		align-items: center;
		margin: 2px;

		user-select: none;
		cursor: pointer;
		.menus {
			display: flex;
			align-items: center;
			padding-left: 3vw;
			height: 50px;
			font-size: 14px;
			font-family: nunitosans-semi-bold;
			color: rgba(32, 34, 36, 1);
			border-radius: 6px;
			width: 75%;
		}

		.menusActive {
			display: flex;
			width: 39px;
		}

		.menusActiveLine {
			height: 50px;
			padding: 3px;
			border-top-right-radius: 10px;
			border-bottom-right-radius: 10px;
		}
	}

	.dropdown-item,
	.dropdownToggle {
		font-size: 14px !important;
		font-family: nunitosans-regular;
	}
	.dropdown-menu {
		min-width: 9.5vw !important;
		// width: 100%;
	}
	.dropButton .dropdown-item.active,
	.dropdown-item:active,
	.dropdown-item:hover {
		color: var(--bs-dropdown-link-active-color);
		text-decoration: none;
		background-color: #fa7480;
	}

	.menusWrraperActive {
		display: flex;
		align-items: center;
		margin: 2px;

		user-select: none;
		cursor: pointer;
		.menus {
			display: flex;
			align-items: center;
			padding-left: 3vw;
			height: 50px;
			font-size: 14px;
			font-family: nunitosans-semi-bold;
			border-radius: 6px;
			width: 75%;
			color: rgba(255, 255, 255, 1);
			background: rgba(249, 92, 105, 0.85);
		}

		.menusActive {
			display: flex;
			width: 39px;
		}

		.menusActiveLine {
			height: 50px;
			padding: 3px;
			border-top-right-radius: 6px;
			border-bottom-right-radius: 10px;
			background: rgba(249, 92, 105, 0.85);
		}
	}
	.hrLine {
		height: 1px;
		width: 90%;
		background: rgba(224, 224, 224, 1);
		margin-block: 1.5rem;
	}

	.menusWrraper:hover {
		.menus {
			color: rgba(255, 255, 255, 1);
			background: #ffa1a5;
		}
		.menusActiveLine {
			background: #ffa1a5;
		}
	}

	.userName {
		font-size: 14px;
		font-family: nunitosans-bold;
		color: #404040;
	}

	.userNameNormal {
		font-size: 14px;
		font-weight: 400;
		color: #202224;

		@media (max-width: 600px) {
			font-size: 16px;
		}
		@media only screen and (min-width: 1024px) and (max-width: 1824px) and (max-height: 900px) {
			font-size: 14px;
		}
	}

	.cursor-pointer {
		cursor: pointer;
	}

	.notificationCount {
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 100%;
		width: 19.29px;
		height: 18px;
		top: -8px;
		right: -8px;
		color: #feeaef;
		background: #f93c65;
		font-size: 12px;
		font-weight: 700;
		box-shadow: 0px 0px -0px 2px #feeaef;
	}

	.userCircleIconWrapper {
		img {
			height: 30px;
			width: 30px;
			border-radius: 100%;
		}
	}

	.notification-icon {
		img {
			height: 30px;
			width: 30px;
		}
	}

	.footer {
		bottom: 0;
		background: '#242424';
		color: '#ffffff';
		font-size: 20px;
		height: 57px;
	}
	.menuIcon,
	.displayAppIcon {
		display: none;
	}

	.tabLogoImage {
		width:133px;
		height: 41px;
	}

	.displayAvatarControl {
		display: block;
	}

	.displayAvatarControl-image {
		height: 18px;
		width: 20px;
	}

	@media (max-width: 600px) {
		.sidebarWrraper {
			display: none;
		}
		.menuIcon,
		.displayAppIcon {
			display: block;
		}
		.displayAvatarControl {
			display: none;
		}
		.logoImage {
			width:90px;
			height: 28px;
		}
	}

	@media only screen and (min-width: 601px) and (max-width: 800px) {
		.sidebarWrraper {
			padding-top: 0.5em;
			padding-bottom: 0rem;
			width: 47%;
		}
		.menuIcon,
		.displayAppIcon {
			display: none;
		}

		.displayAvatarControl-image {
			height: 14.5px;
			width: 15px;
		}

		.userCircleIconWrapper {
			img {
				height: 20px;
				width: 20px;
				border-radius: 100%;
			}
		}

		.notification-icon {
			img {
				height: 20px;
				width: 20px;
			}
		}

		.searchBar {
			/* margin-top: 8px; */
			margin-left: 30px;
			padding-inline: 0.2rem;
			gap: 10px;
			display: flex;
			align-items: center;
			background: #f5f6fa;
			border: 0.6px solid #d5d5d5;
			border-radius: 20px;
			height: 2.8vh;
			max-width: 31vw;
			fkex-direction: column;

			input {
				width: 100%;
				border: none;
				background: transparent;
				outline: none;
				font-size: 11px;
				font-family: nunitosans-regular;
			}

			img {
				width: 12px;
				height: 12.15px;
			}
		}

		.userName {
			font-size: 10px;
		}

		.searchResultWrraper {
			overflow: hidden;
			overflow-y: auto;
			border-radius: 2px;
			max-height: 400px;
			max-width: 21vw;
			display: flex;
			flex-direction: column;
			gap: 6px;
			background: white;
			width: 100%;
			z-index: 10;
			left: 35px;
			top: 2.5rem;
			font-size: 11px;
			font-family: nunitosans-regular;
			border: 1px solid #31313259;
			div {
				padding: 3px 20px;
			}
			div:hover,
			div:focus {
				color: white;
				background-color: rgb(250, 116, 128);
			}
		}

		.dropdown-item,
		.dropdownToggle {
			font-size: 11px !important;
			font-family: nunitosans-regular;
		}
		/* .profile-dropdown-menu {
			min-width: 9.5vw !important;
		} */
		.dropdown-menu {
			min-width: 9.5vw !important;
			// width: 100%;
		}
		.dropButton .dropdown-item.active,
		.dropdown-item:active,
		.dropdown-item:hover {
			color: var(--bs-dropdown-link-active-color);
			text-decoration: none;
			background-color: #fa7480;
		}

		.footer-space {
			padding-bottom: 26px;
			background-color: #242424;
		}

		.menuTitle {
			font-size: 9px;
			font-family: nunitosans-bold;
			height: 22px;
			padding-left: 24px;
			padding-bottom: 1.5rem;
		}

		.displayAvatarControl-image {
			height: 14.5px;
			width: 15px;
		}

		.menusWrraper,
		.menusWrraperActive {
			margin: 2px;
			.menus {
				padding-left: 3vw;
				height: 36px;
				font-size: 11px;
				width: 80%;
				font-family: nunitosans-semi-bold;
			}

			.menusActive {
				width: 26px;
			}

			.menusActiveLine {
				height: 36px;
				padding: 3px;
			}
		}
		.logoImage {
			width:100px;
			height: 31px;
		}
	}

	@media only screen and (min-width: 800px) and (max-width: 1024px) {
		.sidebarWrraper {
			padding-top: 0.5em;
			padding-bottom: 0rem;
			width: 30%;
		}

		.searchResultWrraper {
			overflow: hidden;
			overflow-y: auto;
			border-radius: 2px;
			max-height: 400px;
			max-width: 21vw;
			display: flex;
			flex-direction: column;
			gap: 6px;
			background: white;
			width: 100%;
			z-index: 10;
			left: 35px;
			top: 2.5rem;
			font-size: 11px;
			font-family: nunitosans-regular;
			border: 1px solid #31313259;
			div {
				padding: 3px 20px;
			}
			div:hover,
			div:focus {
				color: white;
				background-color: rgb(250, 116, 128);
			}
		}

		.dropdown-item,
		.dropdownToggle {
			font-size: 11px !important;
			font-family: nunitosans-regular;
		}
		/* .profile-dropdown-menu {
			min-width: 9.5vw !important;
		} */
		.dropdown-menu {
			min-width: 9.5vw !important;
			// width: 100%;
		}
		.dropButton .dropdown-item.active,
		.dropdown-item:active,
		.dropdown-item:hover {
			color: var(--bs-dropdown-link-active-color);
			text-decoration: none;
			background-color: #fa7480;
		}

		.footer-space {
			padding-bottom: 26px;
			background-color: #242424;
		}

		.menuTitle {
			font-size: 9px;
			font-family: nunitosans-bold;
			height: 22px;
			padding-left: 24px;
			padding-bottom: 1.5rem;
		}

		.displayAvatarControl-image {
			height: 14.5px;
			width: 15px;
		}

		.menusWrraper,
		.menusWrraperActive {
			margin: 2px;
			.menus {
				padding-left: 3vw;
				height: 36px;
				font-size: 11px;
				width: 80%;
				font-family: nunitosans-semi-bold;
			}

			.menusActive {
				width: 26px;
			}

			.menusActiveLine {
				height: 36px;
				padding: 3px;
			}
		}

		.userName {
			font-size: 10px;
		}

		.userCircleIconWrapper {
			img {
				height: 22px;
				width: 22px;
				border-radius: 100%;
			}
		}

		.notification-icon {
			img {
				height: 22px;
				width: 22px;
			}
		}

		.searchBar {
			/* margin-top: 8px; */
			margin-left: 30px;
			padding-inline: 1rem;
			gap: 10px;
			display: flex;
			align-items: center;
			background: #f5f6fa;
			border: 0.6px solid #d5d5d5;
			border-radius: 20px;
			height: 3.8vh;
			max-width: 30vw;
			fkex-direction: column;

			input {
				width: 100%;
				border: none;
				background: transparent;
				outline: none;
				font-size: 11px;
				font-family: nunitosans-regular;
			}

			img {
				width: 12px;
				height: 12.15px;
			}
		}

		.hrLine {
			margin-block: 1rem;
		}
		.footer {
			font-size: 14px;
		}

		.logoWrraper {
			height: 7.2vh;
			padding-left: 1vw;
		}
		.logoImage {
			width:100px;
			height: 31px;
		}
	}

	@media only screen and (min-width: 1024px) and (max-width: 1200px) {
		.sidebarWrraper {
			padding-bottom: 0rem;
			padding-top: 0.5em;
			width: 26% !important;
		}
	}

	@media only screen and (min-width: 1024px) and (max-width: 1824px) and (max-height: 900px) {
		.sidebarWrraper {
			padding-bottom: 0rem;
			width: 21%;
		}

		.searchResultWrraper {
			overflow: hidden;
			overflow-y: auto;
			border-radius: 2px;
			max-height: 400px;
			max-width: 21vw;
			display: flex;
			flex-direction: column;
			gap: 6px;
			background: white;
			width: 100%;
			z-index: 10;
			left: 35px;
			top: 2.5rem;
			font-size: 11px;
			font-family: nunitosans-regular;
			border: 1px solid #31313259;
			div {
				padding: 3px 20px;
			}
			div:hover,
			div:focus {
				color: white;
				background-color: rgb(250, 116, 128);
			}
		}

		.dropdown-item,
		.dropdownToggle {
			font-size: 11px !important;
			font-family: nunitosans-regular;
		}
		/* .profile-dropdown-menu {
			min-width: 9.5vw !important;
		} */
		.dropdown-menu {
			min-width: 9.5vw !important;
			// width: 100%;
		}
		.dropButton .dropdown-item.active,
		.dropdown-item:active,
		.dropdown-item:hover {
			color: var(--bs-dropdown-link-active-color);
			text-decoration: none;
			background-color: #fa7480;
		}

		.footer-space {
			padding-bottom: 26px;
			background-color: #242424;
		}

		.menuTitle {
			font-size: 9px;
			font-family: nunitosans-bold;
			height: 22px;
			padding-left: 24px;
			padding-bottom: 1.5rem;
		}

		.displayAvatarControl-image {
			height: 14.5px;
			width: 15px;
		}

		.menusWrraper,
		.menusWrraperActive {
			margin: 2px;
			.menus {
				padding-left: 3vw;
				height: 36px;
				font-size: 11px;
				width: 80%;
				font-family: nunitosans-semi-bold;
			}

			.menusActive {
				width: 26px;
			}

			.menusActiveLine {
				height: 36px;
				padding: 3px;
			}
		}

		.userName {
			font-size: 10px;
		}

		.userCircleIconWrapper {
			img {
				height: 22px;
				width: 22px;
				border-radius: 100%;
			}
		}

		.notification-icon {
			img {
				height: 22px;
				width: 22px;
			}
		}

		.searchBar {
			margin-left: 30px;
			padding-inline: 1rem;
			gap: 10px;
			display: flex;
			align-items: center;
			background: #f5f6fa;
			border: 0.6px solid #d5d5d5;
			border-radius: 20px;
			height: 3.8vh;
			max-width: 21.6vw;
			fkex-direction: column;

			input {
				width: 100%;
				border: none;
				background: transparent;
				outline: none;
				font-size: 11px;
				font-family: nunitosans-regular;
			}

			img {
				width: 12px;
				height: 12.15px;
			}
		}

		.hrLine {
			margin-block: 1rem;
		}
		.footer {
			font-size: 14px;
		}

		.logoWrraper {
			height: 7.2vh;
			padding-left: 1vw;
		}
		.logoImage {
			width:100px;
			height: 31px;
		}
	}

	.sidebarWrraper {
		position: relative;
		.disableSidebar {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			/* background-color: rgba(1, 1, 1, 0.5); */
			z-index: 1;
		}
	}
	.profile-dropdown-menu {
		width: fit-content !important;
		min-width: fit-content !important;
		max-width: fit-content !important;
	}
	.workspaceWrapper {
		height: 100vh;
		display: flex;
		flex-direction: column;
		.headerWrraper {
			height: 4vw;
			/* width: auto; */
			z-index: 100;
			@media only screen and (max-width: 1200px) {
				height: 5vw !important;
			}
			@media only screen and (max-width: 1000px) {
				height: 6vw !important;
			}
			@media only screen and (max-width: 768px) {
				height: 6.5vw !important;
			}
			@media only screen and (max-width: 680px) {
				height: 48px !important;
			}
		}
	}
`;
export default LayoutWrraper;
