import { useState } from 'react';

import TableStyle from '../Common/TableStyle';
import TableV6 from '../Common/TableV6';
import { DeleteIcon, EditIcon } from '../Icons';

const data = [
	{
		sr_no: 1,
		name: '<PERSON>gan',
		email: 'g<PERSON><PERSON>@domain.com',
		mobile_number: '****** 241 5221',
		role: 'Manager',
		action: (
			<span className="d-flex">
				<EditIcon width={'28px'} height={'28px'} />
				<DeleteIcon width={'28px'} height={'28px'} className="ml-16" />
			</span>
		)
	},
	{
		sr_no: 2,
		name: '<PERSON>',
		email: '<EMAIL>',
		mobile_number: '****** 241 5681',
		role: 'Sub Manager',
		action: (
			<span className="d-flex">
				<EditIcon width={'28px'} height={'28px'} />
				<DeleteIcon width={'28px'} height={'28px'} className="ml-16" />
			</span>
		)
	},
	{
		sr_no: 3,
		name: '<PERSON>',
		email: '<EMAIL>',
		mobile_number: '****** 568 9885',
		role: 'Manager',
		action: (
			<span className="d-flex">
				<EditIcon width={'28px'} height={'28px'} />
				<DeleteIcon width={'28px'} height={'28px'} className="ml-16" />
			</span>
		)
	},
	{
		sr_no: 4,
		name: 'Milano Curren',
		email: '<EMAIL>',
		mobile_number: '****** 241 0220',
		role: 'Sub Manager',
		action: (
			<span className="d-flex">
				<EditIcon width={'28px'} height={'28px'} />
				<DeleteIcon width={'28px'} height={'28px'} className="ml-16" />
			</span>
		)
	}
];

const MytabStaffTable = () => {
	const [tableData, setTableData] = useState(data);

	const columns = [
		{
			Header: <span className="fs-16 medium-text pl-14">Sr. No.</span>,
			accessor: 'sr_no',
			className: '',
			minWidth: 90,
			filterable: false,
			sortable: false,
			headerClassName: 'react-table-header-class justify-content-start'
		},
		{
			Header: <span className="fs-16 medium-text pl-14">Name</span>,
			accessor: 'name',
			className: 'justify-content-start pl-24',
			minWidth: 250,
			filterable: false,
			sortable: false,
			headerClassName: 'react-table-header-class justify-content-start'
		},
		{
			Header: <span className="fs-16 medium-text pl-14">Email</span>,
			accessor: 'email',
			className: 'justify-content-start pl-24',
			minWidth: 250,
			filterable: false,
			sortable: false,
			headerClassName: 'react-table-header-class justify-content-start'
		},
		{
			Header: (
				<span className="fs-16 medium-text pl-14">Mobile Number</span>
			),
			accessor: 'mobile_number',
			className: 'justify-content-start pl-24',
			minWidth: 250,
			filterable: false,
			sortable: false,
			headerClassName: 'react-table-header-class justify-content-start'
		},
		{
			Header: <span className="fs-16 medium-text pl-14">Role</span>,
			accessor: 'role',
			className: 'justify-content-start pl-24',
			minWidth: 250,
			filterable: false,
			sortable: false,
			headerClassName: 'react-table-header-class justify-content-start'
		},
		{
			Header: 'Action',
			accessor: 'action',
			className: '',
			minWidth: 125,
			filterable: false,
			sortable: false,
			headerClassName: 'react-table-header-class fs-16 medium-text'
		}
	];

	const handleSortBy = (sortBy) => {
		// setParams((prev) => ({ ...prev, sortBy: sortBy[0]?.id || "id", order: sortBy[0]?.desc ? "DESC" : "ASC" }));
	};

	return (
		<div>
			<TableStyle version={6}>
				<TableV6
					columns={columns}
					data={tableData}
					handleSortBy={handleSortBy}
					key={'master-todo-table'}
				/>
			</TableStyle>
		</div>
	);
};

export default MytabStaffTable;
