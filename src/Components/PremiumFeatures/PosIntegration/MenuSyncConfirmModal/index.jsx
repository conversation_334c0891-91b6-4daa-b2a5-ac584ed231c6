import { Button } from 'reactstrap';
import CustomModal from '../../../Common/Modal/CustomModal';
import CustomButton from '../../../Common/CustomButton';

const MenuSyncConfirmModal = ({
	isOpen,
	handleModal,
	handleManualMenuSync,
	manualMenuSyncLoading
}) => {
	return (
		<CustomModal
			isOpen={isOpen}
			handleModal={handleModal}
			title="Manual Menu Sync"
			size="md"
		>
			<div className="w-100">
				<p className="fs-12 regular-text pa-b-18">
					Are you sure you would like to sync your filtered POS menu?
					Please note: this update will override any changes you have
					completed within your MyTab Venue menu and can not be
					undone, thank you.
				</p>
				<div className="d-flex" style={{ gap: '12px' }}>
					<div className="flex-1">
						<Button
							className="borderButtonFullWidth"
							onClick={handleModal}
						>
							No
						</Button>
					</div>
					<div className="flex-1">
						<CustomButton
							type="button"
							className="themeButtonFullWidth"
							onClick={handleManualMenuSync}
							loading={manualMenuSyncLoading}
						>
							Yes
						</CustomButton>
					</div>
				</div>
			</div>
		</CustomModal>
	);
};

export default MenuSyncConfirmModal;
