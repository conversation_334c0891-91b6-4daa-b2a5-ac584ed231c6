import {
	useStripe,
	useElements,
	PaymentElement
} from '@stripe/react-stripe-js';
import CustomButton from '../../Common/CustomButton';
import { toast } from 'react-toastify';
import { useState } from 'react';

const CheckoutForm = () => {
	const stripe = useStripe();
	const elements = useElements();
	const [activateLoading, setActivateLoading] = useState(false);

	const handleSubmit = async (event) => {
		// We don't want to let default form submission happen here,
		// which would refresh the page.
		event.preventDefault();
		setActivateLoading(true);
		if (!stripe || !elements) {
			// Stripe.js hasn't yet loaded.
			// Make sure to disable form submission until Stripe.js has loaded.
			return;
		}
		const result = await stripe.confirmPayment({
			//`Elements` instance that was used to create the Payment Element
			elements,
			confirmParams: {
				return_url: window.location.href
			}
		});
		setActivateLoading(false);
		if (result.error) {
			// Show error to your customer (for example, payment details incomplete)
			toast.error(result.error.message);
		}
	};

	return (
		<form>
			<PaymentElement />
			<CustomButton
				type="submit"
				className="fs-18 medium-text themeButtonFullWidth w-100 mb-1 outline-none mt-2"
				onClick={handleSubmit}
				loading={activateLoading}
			>
				Submit
			</CustomButton>
		</form>
	);
};

export default CheckoutForm;
