import { useSelector } from 'react-redux';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import CustomModal from '../../Common/Modal/CustomModal';
import ModalWrapper from '../../Common/Modal/modal.style';
import CheckoutForm from './CheckoutForm';
import { stripePublicKey } from '../../../Helper/constant';

const stripePromise = loadStripe(stripePublicKey);

const PaymentModal = ({ isOpen, handleModal, modalData }) => {
	const state = useSelector((state) => ({ ...state }));
	const allThemeData = state.themeChanger;
	const authDetails = state.auth;
	const options = {
		// passing the client secret obtained from the server
		clientSecret: modalData?.clientSecret
	};

	return (
		<>
			<CustomModal
				isOpen={isOpen}
				size="xs"
				handleModal={handleModal}
				title="POS Integration Subscription"
				autoHeightMin={100}
				modalClassName="verifyOtp"
			>
				<ModalWrapper {...allThemeData}>
					<p className="fs-12 medium-text pa-b-18">
						By activating the MyTab Premium+ POS Integration
						Feature, you agree to an additional fee of $30/month,
						which will be deducted through the payment details
						provided below, and{' '}
						{(authDetails?.selectedVenue?.posFee
							? authDetails?.selectedVenue?.posFee
							: '1.5') + '%'}{' '}
						commission fee per transaction, which will be
						automatically deducted through Stripe. You certify that
						you are either an account holder or an authorised
						signatory on the account listed.
					</p>
					<Elements stripe={stripePromise} options={options}>
						<CheckoutForm />
					</Elements>
				</ModalWrapper>
			</CustomModal>
		</>
	);
};

export default PaymentModal;
