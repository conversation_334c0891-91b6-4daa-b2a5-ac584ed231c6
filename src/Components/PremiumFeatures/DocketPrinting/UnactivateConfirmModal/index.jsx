import { Button } from 'reactstrap';
import CustomModal from '../../../Common/Modal/CustomModal';
import CustomButton from '../../../Common/CustomButton';

const UnactivateConfirmModal = ({
	handleModal,
	isOpen,
	activateLoading,
	handleDocketPrintingFeatStatus
}) => {
	return (
		<CustomModal
			isOpen={isOpen}
			handleModal={handleModal}
			title="Deactivate Feature"
			size="md"
		>
			<div className="w-100">
				<p className="fs-12 regular-text text-left pa-b-18">
					Are you sure you want to deactivate the Docket Printing
					Feature?
				</p>
				<div className="d-flex" style={{ gap: '12px' }}>
					<div className="flex-1">
						<Button
							className="borderButtonFullWidth"
							onClick={handleModal}
						>
							No
						</Button>
					</div>
					<div className="flex-1">
						<CustomButton
							type="button"
							className="themeButtonFullWidth"
							loading={activateLoading}
							onClick={() => {
								handleDocketPrintingFeatStatus();
							}}
						>
							Yes
						</CustomButton>
					</div>
				</div>
			</div>
		</CustomModal>
	);
};

export default UnactivateConfirmModal;
