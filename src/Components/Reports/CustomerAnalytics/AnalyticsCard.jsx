const AnalyticsCard = ({ heading, count, icon }) => {
	let supText = '';
	if (count === '1') {
		supText = 'st';
	} else if (count === '2') {
		supText = 'nd';
	} else if (count === '3') {
		supText = 'rd';
	} else {
		supText = 'th';
	}
	return (
		<div className="d-flex align-items-center pa-24 border-radius-16 defaultBoxShadow h-100 analyticsCard">
			<div className="pr-26">
				<div>{icon}</div>
			</div>
			<div className="">
				<p className="fs-30 medium-text">{count}</p>
				<p className="pa-t-8 fs-14 medium-text headingTextColor">
					{heading}
				</p>
			</div>
		</div>
	);
};
export default AnalyticsCard;
