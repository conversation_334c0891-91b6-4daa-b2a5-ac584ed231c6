import React from 'react';
import { useState } from 'react';
import {
	Dropdown,
	DropdownItem,
	DropdownMenu,
	DropdownToggle
} from 'reactstrap';
import { defineds } from '../../../Helper/DateHelper';
import useDevice from '../../../Hooks/useDevice';
import CustomDateRangePicker from '../../Common/DateRangePicker/CustomDateRangePicker';
import { DownArrowIcon, UpArrowIcon } from '../../Icons';
import { useSelector } from 'react-redux';
import { DropDownWrapper } from './index.style';

const ReportsDropDown = () => {
	const allThemeData = useSelector((state) => ({ ...state.themeChanger }));
	const [dateRangePickerLabel, setDateRangePickerLabel] = useState('Today');
	const [dateDropdown, setDateDropdown] = useState(false);
	const [orderTypeDropdown, setOrderTypeDropdown] = useState(false);
	const [orderTypeLabel, setOrderTypeLabel] = useState('Past Orders');
	const [dateRange, setDateRange] = useState([null, null]);
	const { isTablet } = useDevice();

	const handleDateRange = (ranges) => {
		if (ranges[0] && ranges[1]) {
			setDateDropdown((prev) => !prev);
			setDateRangePickerLabel(
				ranges[0].toLocaleDateString() +
					' - ' +
					ranges[1].toLocaleDateString()
			);
		}
		setDateRange(ranges);
	};

	const handleDateRangeLabel = (label) => {
		setDateRangePickerLabel(label);
		if (label === 'Today') {
			setDateRange([defineds.startOfToday, defineds.endOfToday]);
		} else if (label === 'Yesterday') {
			setDateRange([defineds.startOfYesterday, defineds.endOfYesterday]);
		} else if (label === 'This Week') {
			setDateRange([defineds.startOfWeek, defineds.endOfWeek]);
		} else if (label === 'This Month') {
			setDateRange([defineds.startOfMonth, defineds.endOfToday]);
		} else if (label === 'This Quarter') {
			setDateRange([defineds.startOfQuarter, defineds.endOfToday]);
		} else if (label === 'This Calendar Year') {
			setDateRange([defineds.startOfYear, defineds.endOfToday]);
		} else {
			setDateRange([null, null]);
		}
	};
	return (
		<DropDownWrapper {...allThemeData} className="d-flex">
			<div className="dateDropdownBox d-flex align-items-center">
				<Dropdown
					isOpen={dateDropdown}
					toggle={() => setDateDropdown((prev) => !prev)}
					direction="down"
					className="dashboard-dropdown w-100"
				>
					<DropdownToggle
						color="#fff"
						className="ptb-0 dropdownToggle d-flex justify-content-between w-100 align-items-center"
					>
						<span
							className={`dropdown-name fs-14 medium-text flex-1`}
						>
							{dateRangePickerLabel}
						</span>
						<span className="pl-10">
							{dateDropdown ? (
								<UpArrowIcon height={7} width={12} />
							) : (
								<DownArrowIcon height={7} width={12} />
							)}
						</span>
					</DropdownToggle>
					<DropdownMenu
						className={`p-2 datePickerDropdown ${
							isTablet ? 'w-100' : ''
						}`}
						end={isTablet}
					>
						<div className="d-flex flex-column flex-sm-row">
							<div className="dropdownCalenderParent">
								<div className="p-0 dropdown-item dropdownCalender">
									<CustomDateRangePicker
										dateRange={dateRange}
										handleDateRange={handleDateRange}
									/>
								</div>
							</div>
							<div>
								<DropdownItem
									className={`${
										dateRangePickerLabel ===
										'Current Orders'
											? 'active'
											: ''
									}`}
									onClick={() =>
										handleDateRangeLabel('Current Orders')
									}
								>
									Current Orders
								</DropdownItem>
								<DropdownItem
									className={`${
										dateRangePickerLabel === 'Yesterday'
											? 'active'
											: ''
									}`}
									onClick={() =>
										handleDateRangeLabel('Yesterday')
									}
								>
									Yesterday
								</DropdownItem>
								<DropdownItem
									className={`${
										dateRangePickerLabel === 'This Week'
											? 'active'
											: ''
									}`}
									onClick={() =>
										handleDateRangeLabel('This Week')
									}
								>
									This Week
								</DropdownItem>
								<DropdownItem
									className={`${
										dateRangePickerLabel === 'This Month'
											? 'active'
											: ''
									}`}
									onClick={() =>
										handleDateRangeLabel('This Month')
									}
								>
									This Month
								</DropdownItem>
								<DropdownItem
									className={`${
										dateRangePickerLabel === 'This Quarter'
											? 'active'
											: ''
									}`}
									onClick={() =>
										handleDateRangeLabel('This Quarter')
									}
								>
									This Quarter
								</DropdownItem>
								<DropdownItem
									className={`${
										dateRangePickerLabel ===
										'This Calendar Year'
											? 'active'
											: ''
									}`}
									onClick={() =>
										handleDateRangeLabel(
											'This Calendar Year'
										)
									}
								>
									This Calendar Year
								</DropdownItem>
								<DropdownItem
									className={`${
										dateRangePickerLabel === 'All'
											? 'active'
											: ''
									}`}
									onClick={() => handleDateRangeLabel('All')}
								>
									All
								</DropdownItem>
							</div>
						</div>
					</DropdownMenu>
				</Dropdown>
			</div>
			<div
				className="d-flex align-items-center ml-16 pa-16 fs-14 semi-bold-text border-radius-8"
				style={{ backgroundColor: '#F9F9F9' }}
			>
				Compare:
				<span className="fs-14 medium-text pl-4">Yesterday</span>
			</div>
		</DropDownWrapper>
	);
};

export default ReportsDropDown;
