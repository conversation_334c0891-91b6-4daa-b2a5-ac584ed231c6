const SubPerformanceCard = ({ heading, count, icon }) => {
	let supText = '';
	if (count === '1') {
		supText = 'st';
	} else if (count === '2') {
		supText = 'nd';
	} else if (count === '3') {
		supText = 'rd';
	} else {
		supText = 'th';
	}
	return (
		<div className="d-flex align-items-center medium-text pa-24 subPerformanceCard">
			<div className="pr-26">
				<div>{icon}</div>
			</div>
			<div className="">
				<p className="fs-38 semi-bold-text">
					{count}
					<sup className="fs-16 medium-text">{supText}</sup>
				</p>
				<p className="pa-t-8 fs-14 medium-text headingTextColor">
					{heading}
				</p>
			</div>
		</div>
	);
};
export default SubPerformanceCard;
