import React from 'react';

const TotalPerformingCard = ({ heading, data, icon }) => {
	return (
		<div className="d-flex flex-column medium-text pa-24 defaultBoxShadow totalPerformingCard">
			<div className="pr-26 d-flex align-items-center">
				{icon}
				<span className="pl-26 fs-20 medium-text">{heading}</span>
			</div>
			<div className="pa-t-16">
				{data?.length > 0 &&
					data?.map((item) => (
						<div className="d-flex justify-content-between pa-b-16">
							<span className="fs-14 medium-text">
								{item.country}
							</span>
							<div className="d-flex">
								<span className="pr-58 fs-14 medium-text flex-1">
									{item.count}
								</span>
								<div>
									<span className="fs-14 medium-text flex-1 percentage">
										<span className="pr-16">
											{item.icon}
										</span>
										<span>{item.percentage}</span>
									</span>
								</div>
							</div>
						</div>
					))}
			</div>
		</div>
	);
};

export default TotalPerformingCard;
