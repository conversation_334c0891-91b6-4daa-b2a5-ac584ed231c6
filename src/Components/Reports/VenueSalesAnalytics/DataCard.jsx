import React from 'react';
import { Cross } from '../../Icons';

const DataCard = ({ count, heading }) => {
	return (
		<div className="d-flex align-items-center medium-text pa-24 defaultBoxShadow dataCard">
			<div className="pr-26">
				<Cross width={'36px'} height={'36px'} fill={'#FF5F5F'} />
			</div>
			<div className="">
				<p className="fs-28 medium-text">{count}</p>
				<p className="pa-t-8 fs-14 medium-text headingTextColor">
					{heading}
				</p>
			</div>
		</div>
	);
};

export default DataCard;
