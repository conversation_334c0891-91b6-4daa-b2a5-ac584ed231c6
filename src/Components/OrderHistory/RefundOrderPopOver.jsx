import CustomPopover from '../Common/Popover/CustomPopover';

const RefundOrderPopOver = ({
	popover,
	popoverToggle,
	popoverId,
	handleRefundModal
}) => {
	return (
		<CustomPopover
			isOpen={popover}
			target={popoverId}
			handleToggle={popoverToggle}
			placement="bottom-end"
			className="Order-Popover"
		>
			<div className="container-fluid grid-popover pa-0">
				<ul
					className="list-inline language-list-hover ma-0"
					onClick={() => {
						popoverToggle();
						handleRefundModal();
					}}
				>
					<li className="list-inline-item pa-5 themeText fs-16 medium-text cursor-pointer">
						Refunded Amount
					</li>
				</ul>
			</div>
		</CustomPopover>
	);
};

export default RefundOrderPopOver;
