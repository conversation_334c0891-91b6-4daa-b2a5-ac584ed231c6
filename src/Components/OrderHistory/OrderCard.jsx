import { useState } from 'react';
import { BoxIcon, PickupLocationIcon, ThreeDotIcon } from '../Icons';
import OrderPopOver from './OrderPopOver';
import RefundAmountModal from './RefundAmountModal';
import RefundInitiateModal from './RefundInitiateModal';
import RefundOrderPopOver from './RefundOrderPopOver';

const OrderCard = ({ id, type }) => {
	const [refundModal, setRefundModal] = useState(false);
	const [orderPopover, setOrderPopover] = useState(false);

	const orderPopoverToggle = () => {
		setOrderPopover((prev) => !prev);
	};

	const handleRefundModal = () => {
		setRefundModal((prev) => !prev);
	};

	return (
		<>
			<div className="orderCard d-flex flex-column p-3">
				<div className="orderCardHeader d-flex p-1">
					<div className="d-flex align-items-center gap-3 flex-1">
						<BoxIcon height={20} width={18} />
						<div className="flex-1 d-flex flex-column flex-sm-row justify-content-between align-items-center gap-2">
							<div>
								<p className="fs-14 semi-bold-text text-dark">
									Table 55
								</p>
								<p className="fs-14 regular-text themeText text-light pt-2">
									16/08/2013
								</p>
							</div>
							<p className="fs-14 semi-bold-text text-dark">
								Order #VEN7644558
							</p>
						</div>
						{type === 'pastOrders' && (
							<ThreeDotIcon
								height={32}
								width={32}
								fill="#4F4F4F"
								className="cursor-pointer"
								id={`orderPopover_${id}`}
							/>
						)}
						{type === 'refundedOrders' && (
							<ThreeDotIcon
								height={32}
								width={32}
								fill="#4F4F4F"
								className="cursor-pointer"
								id={`RefundOrderPopover_${id}`}
							/>
						)}
					</div>
				</div>
				<hr className="themeHr mt-2 mb-2" />
				<div className="orderCardBody p-1">
					<div className="d-flex flex-column flex-1">
						<p className="fs-14 medium-text text-dark pt-2">
							1. Kids Hot Chips, Thick Cut
						</p>
						<p className="fs-14 medium-text text-dark pt-2">
							2. Summer Mojito
						</p>
						<p className="fs-14 medium-text text-dark pt-2">
							3. Kids Veg Nuggets
						</p>
					</div>
				</div>
				<hr className="themeHr mt-3 mb-2" />
				<div className="orderCardPickup p-1">
					<div className="d-flex justify-content-between gap-2">
						<div className="d-flex flex-row align-items-center gap-1">
							<PickupLocationIcon
								height={16}
								width={18}
								fill={'#FF5F5F'}
							/>
							<p className="fs-12 regular-text text-dark">
								Pick Up Location
							</p>
						</div>
						<div>
							<p className="fs-14 semi-bold-text text-dark">
								Collect Inside Cafe
							</p>
						</div>
					</div>
				</div>
				<hr className="themeHr mt-2 mb-2" />
				<div className="orderCardTotal p-1">
					<div className="d-flex justify-content-between gap-2">
						<div className="d-flex flex-row align-items-center gap-1">
							<p className="fs-12 regular-text text-dark">
								Total Amount (Inc. GST)
							</p>
						</div>
						<div>
							<p className="fs-18 semi-bold-text text-dark">
								$ 4.5
							</p>
						</div>
					</div>
				</div>
			</div>
			{type === 'pastOrders' && (
				<>
					<OrderPopOver
						popover={orderPopover}
						popoverToggle={orderPopoverToggle}
						popoverId={`orderPopover_${id}`}
						handleRefundModal={handleRefundModal}
					/>
					<RefundInitiateModal
						isOpen={refundModal}
						handleModal={handleRefundModal}
					/>
				</>
			)}
			{type === 'refundedOrders' && (
				<>
					<RefundOrderPopOver
						popover={orderPopover}
						popoverToggle={orderPopoverToggle}
						popoverId={`RefundOrderPopover_${id}`}
						handleRefundModal={handleRefundModal}
					/>
					<RefundAmountModal
						isOpen={refundModal}
						handleModal={handleRefundModal}
					/>
				</>
			)}
		</>
	);
};

export default OrderCard;
