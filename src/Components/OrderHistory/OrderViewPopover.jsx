import CustomPopover from '../Common/Popover/CustomPopover';

const OrderViewPopover = ({ popover, popoverToggle, popoverId }) => {
	return (
		<CustomPopover
			isOpen={popover}
			target={popoverId}
			handleToggle={popoverToggle}
			placement="bottom-end"
		>
			<div className="container-fluid grid-popover pa-0">
				<ul
					className="list-inline language-list-hover ma-0"
					onClick={() => {
						popoverToggle();
					}}
				>
					<li className="list-inline-item pa-5 themeText fs-16 medium-text cursor-pointer">
						Print
					</li>
				</ul>
			</div>
		</CustomPopover>
	);
};

export default OrderViewPopover;
