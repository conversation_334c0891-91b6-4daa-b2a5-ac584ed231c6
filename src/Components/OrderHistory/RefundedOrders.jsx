import Masonry from 'react-masonry-css';
import OrderCard from './OrderCard';

const breakpointObj = {
	default: 2,
	500: 1
};

const RefundedOrders = () => {
	return (
		<Masonry className="d-flex gap-3" breakpointCols={breakpointObj}>
			{Array.from({ length: 10 }, (value, index) => (
				<OrderCard key={index} id={index} type="refundedOrders" />
			))}
		</Masonry>
	);
};

export default RefundedOrders;
