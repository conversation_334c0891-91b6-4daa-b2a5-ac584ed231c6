import StyleWrapper from './index.style';

const NewFormSwitch = ({
	id,
	name,
	value = false,
	onChange,
	disabled = false,
	wrapperClassName,
	...props
}) => {
	return (
		<StyleWrapper className={wrapperClassName}>
			<label className="customSwitch">
				<input
					type="checkbox"
					name={name}
					className="customInput"
					checked={Boolean(value)}
					onChange={(e) => onChange(e.target.checked)}
					disabled={disabled}
					{...props}
				/>
				<span className="customSlider"></span>
			</label>
		</StyleWrapper>
	);
};

export default NewFormSwitch;