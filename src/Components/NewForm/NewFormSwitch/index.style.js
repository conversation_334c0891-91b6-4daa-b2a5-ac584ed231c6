import styled from 'styled-components';

const StyleWrapper = styled.div`
	display: inline-block;
	cursor: pointer;
	.customSwitch {
		--activeCircleColor: rgba(52, 199, 89, 1);
		--inActiveCircleColor: #fafafa;
		--activeTrackColor: rgba(223, 245, 228, 1);
		--inActiveTrackColor: #9e9e9e;
		padding-left: 0;
		position: relative;
		display: flex;
		align-items: center;
		margin-bottom: 0 !important;
	}

	.customSwitch .customInput {
		opacity: 0;
		width: 0;
		height: 0;
		position: absolute;
	}

	.customSwitch .customSlider {
		display: inline-block;
		position: relative;
		width: 36px;
		height: 14px;
		background: var(--inActiveTrackColor);
		border-radius: 60px;
		transition: 0.3s;
		cursor: pointer;
	}

	.customSwitch .customInput:checked ~ .customSlider {
		background-color: var(--activeTrackColor);
	}

	.customSwitch .customSlider::after {
		content: '';
		display: block;
		position: absolute;
		width: 20px;
		height: 20px;
		border-radius: 50%;
		transition: 0.3s;
		top: 50%;
		left: 0;
		transform: translateY(-50%);
		background: var(--inActiveCircleColor);
		box-shadow: 0 2px 10px #aaa;
	}

	.customSwitch .customInput:checked ~ .customSlider::after {
		background-color: var(--activeCircleColor);
		left: 100%;
		transform: translate(-100%, -50%);
	}

	@media (max-width: 600px) {
		.customSwitch .customSlider {
			width: 24px;
			height: 10px;
		}
		.customSwitch .customSlider::after {
			width: 13px;
			height: 13px;
		}
	}

	@media (min-width: 601px) and (max-width: 1299px) {
		.customSwitch .customSlider {
			width: 27px;
			height: 11px;
		}
		.customSwitch .customSlider::after {
			width: 15px;
			height: 15px;
		}
	}

	@media (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.customSwitch .customSlider {
			width: 27px;
			height: 11px;
		}
		.customSwitch .customSlider::after {
			width: 15px;
			height: 15px;
		}
	}
`;

export default StyleWrapper;
