import { StyleWrapper } from './index.style';

const NewFormTextarea = ({
	id,
	name,
	value,
	onChange,
	onBlur,
	placeholder,
	disabled = false,
	rows = 4,
	cols,
	wrapperClassName,
	maxLength, // Optional prop, defaults to undefined
	...props
}) => {
	return (
		<StyleWrapper className={wrapperClassName}>
			<textarea
				className="newCustomTextarea"
				id={id}
				name={name}
				value={value}
				onChange={onChange}
				onBlur={onBlur}
				placeholder={placeholder}
				disabled={disabled}
				rows={rows}
				cols={cols}
				maxLength={maxLength} // Only applied if passed
				{...props}
			/>
		</StyleWrapper>
	);
};

export default NewFormTextarea;
