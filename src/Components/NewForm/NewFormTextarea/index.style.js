import styled from 'styled-components';

export const StyleWrapper = styled.div`
	.newCustomTextarea {
		width: 100% !important;
		outline: none !important;
		border: 1px solid rgba(49, 49, 50, 0.35) !important;
		border-radius: 0 !important;
		color: rgba(32, 34, 36, 1) !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 16px !important;
		line-height: 1.3 !important;
		&::placeholder {
			color: #979797 !important;
			font-family: 'nunitosans-regular' !important;
			font-size: 16px !important;
			line-height: 24px !important;
			padding-top: 5px !important;
		}
		padding: 13px !important;
		&::-webkit-scrollbar {
			width: 4px !important;
		}
		&::-webkit-scrollbar-thumb {
			background-color: rgba(1, 1, 1, 0.3) !important;
			border-radius: 2px !important;
		}
	}
	@media (max-width: 600px) {
		.newCustomTextarea {
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
			padding: 9px !important;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.newCustomTextarea {
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
			padding: 10px !important;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.newCustomTextarea {
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
			padding: 10px !important;
		}
	}
`;
