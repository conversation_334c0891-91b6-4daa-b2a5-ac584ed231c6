import styled from 'styled-components';

const StyleWrapper = styled.div`
	.customSwitch {
		position: relative;
		display: inline-block;
		padding-inline: 4px;
		min-width: 51px;
		width: fit-content;
		max-width: fit-content;
		height: 31px;
		border-radius: 100px;
		background-color: #f95c69;
		display: flex;
		flex-direction: row-reverse;
		justify-content: space-between;
		align-items: center;
		gap: 4px;
		cursor: ${(props) => (props.disabled ? 'default' : 'pointer')};
		transition: 0.3s;
		.customSwitchChildrenWrapper {
			position: relative;
			.customSwitchChildrenHidden {
				font-family: 'nunitosans-bold' !important;
				font-size: 16px !important;
				line-height: 1 !important;
				visibility: hidden;
			}
			.customSwitchChildren {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				color: #ffffff;
				font-family: 'nunitosans-bold' !important;
				font-size: 16px !important;
				line-height: 1 !important;
			}
		}
		.customSwitchDot {
			height: 23px;
			width: 23px;
			background-color: #ffffff;
			border-radius: 100px;
		}
	}
	.customSwitch.isChecked {
		flex-direction: row;
		background-color: rgba(52, 199, 89, 1);
		transition: 0.3s;
	}
	@media (max-width: 600px) {
		.customSwitch {
			padding: 3px;
			min-width: 34px;
			height: 21px;
			gap: 3px;
			.customSwitchChildrenWrapper {
				.customSwitchChildrenHidden {
					font-size: 12px !important;
				}
				.customSwitchChildren {
					font-size: 12px !important;
				}
			}
			.customSwitchDot {
				height: 15px;
				width: 15px;
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.customSwitch {
			padding: 3px;
			min-width: 38px;
			height: 23px;
			gap: 3px;
			.customSwitchChildrenWrapper {
				.customSwitchChildrenHidden {
					font-size: 12px !important;
				}
				.customSwitchChildren {
					font-size: 12px !important;
				}
			}
			.customSwitchDot {
				height: 17px;
				width: 17px;
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.customSwitch {
			padding: 3px;
			min-width: 38px;
			height: 23px;
			gap: 3px;
			.customSwitchChildrenWrapper {
				.customSwitchChildrenHidden {
					font-size: 12px !important;
				}
				.customSwitchChildren {
					font-size: 12px !important;
				}
			}
			.customSwitchDot {
				height: 17px;
				width: 17px;
			}
		}
	}
`;

export default StyleWrapper;
