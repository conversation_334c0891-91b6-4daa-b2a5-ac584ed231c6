import { useState, useRef } from 'react';
import StyleWrapper from './index.style';

const NewFormSwitch2 = ({
	id,
	name,
	value,
	onChange,
	checked = true,
	disabled = false,
	wrapperClassName,
	checkedChildren,
	unCheckedChildren,
	...props
}) => {
	const [isChecked, setIsChecked] = useState(checked);
	const inputRef = useRef();
	const handleClick = () => {
		if (disabled) return;
		inputRef?.current?.click();
		setIsChecked(!isChecked);
	};

	return (
		<StyleWrapper className={wrapperClassName} disabled={disabled}>
			<div
				className={`customSwitch ${isChecked ? 'isChecked' : ''}`}
				onClick={handleClick}
			>
				<input
					ref={inputRef}
					type="checkbox"
					name={name}
					value={value}
					checked={checked}
					onChange={(e) => {
						if (onChange) {
							onChange(e);
						}
						setIsChecked(e?.target?.checked);
					}}
					{...props}
					hidden
				/>
				<div className="customSwitchChildrenWrapper">
					<div className="customSwitchChildrenHidden">
						{checkedChildren}
					</div>
					<div className="customSwitchChildrenHidden">
						{unCheckedChildren}
					</div>
					<div className="customSwitchChildren">
						{isChecked ? checkedChildren : unCheckedChildren}
					</div>
				</div>
				<div className="customSwitchDot" />
			</div>
		</StyleWrapper>
	);
};

export default NewFormSwitch2;
