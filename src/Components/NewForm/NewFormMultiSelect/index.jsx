import { useEffect, useRef, useState } from 'react';
import Select, { components } from 'react-select';
import { StyleWrapper } from './index.style';
import { CircleArrowDown } from '../../Icons';
import NewFormCheckbox from '../NewFormCheckbox';

const DropdownIndicator = (props) => {
	return (
		<components.DropdownIndicator {...props}>
			<CircleArrowDown width="100%" height="100%" />
		</components.DropdownIndicator>
	);
};

const NewFormMultiSelect = ({
	id,
	name,
	value,
	onChange,
	options,
	placeholder = '',
	isLoading = false,
	isSearchable = false,
	disabled = false,
	isShowSelectedOptions = true,
	wrapperClassName,
	onMenuClose
}) => {
	const [menuIsOpen, setMenuIsOpen] = useState(false);
	const [selectedOptions, setSelectedOptions] = useState([]);
	const newFormMultiSelectRef = useRef(null);

	const handleMenuOpen = () => setMenuIsOpen(true);
	const handleMenuClose = () => {
		if (onMenuClose) {
			onMenuClose();
		}
		setMenuIsOpen(false);
	};
	const handleChange = (value) => {
		if (onChange) {
			onChange(value);
			setSelectedOptions(value);
		} else {
			setSelectedOptions(value);
		}
	};

	const CheckboxOption = (props) => {
		return (
			<components.Option {...props}>
				<NewFormCheckbox checked={props?.isSelected} readOnly={true} />
				{props.label}
			</components.Option>
		);
	};

	const CustomValueContainer = ({ children, ...props }) => {
		const getPlaceholder = () => {
			if (!isSearchable) {
				return <div className="customPlaceholder">{placeholder}</div>;
			}
			if (isSearchable && !props?.selectProps?.menuIsOpen) {
				return <div className="customPlaceholder">{placeholder}</div>;
			}
		};
		return (
			<components.ValueContainer {...props}>
				{getPlaceholder()}
				{children}
			</components.ValueContainer>
		);
	};
	useEffect(() => {
		const handleClickOutside = (event) => {
			if (
				menuIsOpen &&
				!newFormMultiSelectRef?.current?.contains(event?.target)
			) {
				if (onMenuClose) {
					onMenuClose();
				}
				setMenuIsOpen(false);
			}
		};
		document.addEventListener('click', handleClickOutside, true);
		return () => {
			document.removeEventListener('click', handleClickOutside, true);
		};
	}, [setMenuIsOpen, menuIsOpen]);
	useEffect(() => {
		if (value?.length > 0) {
			setSelectedOptions(value);
		} else {
			setSelectedOptions([]);
		}
	}, [value]);
	return (
		<StyleWrapper className={wrapperClassName}>
			<div style={{ paddingLeft: '1px' }} ref={newFormMultiSelectRef}>
				<Select
					isMulti
					name={name}
					options={options}
					value={selectedOptions}
					onChange={handleChange}
					closeMenuOnSelect={false}
					hideSelectedOptions={false}
					isLoading={isLoading}
					isDisabled={disabled}
					isSearchable={isSearchable}
					isClearable={false}
					backspaceRemovesValue={false}
					components={{
						IndicatorSeparator: () => null,
						Option: CheckboxOption,
						DropdownIndicator: DropdownIndicator,
						ValueContainer: CustomValueContainer,
						MultiValue: () => null,
						Placeholder: () => null
					}}
					classNames={{
						container: () => 'customContainer',
						control: () => 'customControl',
						valueContainer: () => 'customValue',
						input: () => 'customInput',
						indicatorsContainer: () => 'customIndicatorsContainer',
						dropdownIndicator: () => 'customDropdownIndicator',
						menu: () => 'customMenu',
						menuList: () => 'customMenuList',
						option: () => 'customOption',
						noOptionsMessage: () => 'customNoOptionsMessage'
					}}
					theme={(theme) => ({
						...theme,
						borderRadius: '2px',
						colors: {
							...theme.colors,
							primary25: 'rgba(255,255,255,1)',
							primary: 'rgba(255,255,255,1)',
							primary50: 'rgba(255,255,255,1)'
						}
					})}
					menuIsOpen={menuIsOpen}
					onMenuOpen={handleMenuOpen}
					onMenuClose={handleMenuClose}
					menuPlacement="auto"
				/>
			</div>
			{isShowSelectedOptions && selectedOptions?.length > 0 && (
				<div className="selectedOptionsContainer">
					{selectedOptions?.map((item, i) => {
						return (
							<div key={i} className="selectedOptionsItem">
								{item?.label}
							</div>
						);
					})}
				</div>
			)}
		</StyleWrapper>
	);
};

export default NewFormMultiSelect;
