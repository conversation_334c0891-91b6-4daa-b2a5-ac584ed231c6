import styled from 'styled-components';

export const StyleWrapper = styled.div`
	.customContainer {
		height: 53px !important;
	}
	.customControl {
		border: 1px solid rgba(49, 49, 50, 0.35) !important;
		box-shadow: none !important;
		height: 100% !important;
		padding-inline: 13px !important;
		min-height: 0 !important;
		cursor: pointer;
	}
	.customValue {
		height: 100% !important;
		padding: 0 !important;
		display: flex !important;
	}
	.customPlaceholder {
		height: 100% !important;
		color: #979797 !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 16px !important;
		line-height: 1 !important;
		overflow: hidden !important;
		text-overflow: ellipsis !important;
		white-space: nowrap !important;
		margin: 0 !important;
		display: flex !important;
		align-items: center !important;
	}
	.customInput {
		height: 100% !important;
		margin: 0 !important;
		padding: 0 !important;
		input {
			color: rgba(32, 34, 36, 1) !important;
			font-family: 'nunitosans-regular' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
	}
	.customIndicatorsContainer {
		height: 100% !important;
		display: flex !important;
		justify-content: center !important;
		align-items: center !important;
	}
	.customDropdownIndicator {
		padding: 0 !important;
		width: 28px !important;
		height: 28px !important;
	}
	.customMenu {
		margin-top: 0 !important;
		margin-bottom: 0 !important;
		border: 1px solid rgba(49, 49, 50, 0.35) !important;
		border-top: none !important;
		box-shadow: none !important;
	}
	.customMenuList {
		&::-webkit-scrollbar {
			width: 4px !important;
		}
		&::-webkit-scrollbar-thumb {
			border-radius: 4px !important;
			background: rgba(0, 0, 0, 0.2) !important;
		}
	}
	.customMenuList > div:last-child {
		border-bottom: none !important;
	}
	.customOption {
		display: flex;
		align-items: center;
		gap: 13px;
		padding-inline: 13px;
		padding-block: 10px;
		color: #2e2e2e !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 16px !important;
		line-height: 1 !important;
		border-bottom: 1px solid rgba(0, 0, 0, 0.17) !important;
		cursor: pointer;
	}
	.customNoOptionsMessage {
		padding: 13px;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #979797 !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 14px !important;
	}
	.selectedOptionsContainer {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 10px;
		padding-top: 14px;
		.selectedOptionsItem {
			background: rgba(130, 128, 255, 0.2);
			border-radius: 5px;
			display: flex;
			justify-content: center;
			align-items: center;
			min-width: 122px;
			max-width: 300px;
			height: 35px;
			padding-inline: 8px;
			color: #8280ff;
			font-family: 'nunitosans-bold' !important;
			line-height: 1;
			font-size: 12px;
		}
	}
	@media (max-width: 600px) {
		.customContainer {
			height: 34px !important;
		}
		.customControl {
			padding-inline: 9px !important;
		}
		.customPlaceholder {
			font-size: 11px !important;
		}
		.customInput {
			input {
				font-size: 11px !important;
			}
		}
		.customDropdownIndicator {
			width: 19px !important;
			height: 19px !important;
		}
		.customOption {
			gap: 9px;
			padding-inline: 9px;
			padding-block: 6px;
			font-size: 11px !important;
		}
		.customNoOptionsMessage {
			font-size: 11px !important;
		}
		.selectedOptionsContainer {
			gap: 6px;
			padding-top: 9px;
			.selectedOptionsItem {
				min-width: 92px;
				max-width: 225px;
				height: 28px;
				padding-inline: 8px;
				font-size: 10px;
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.customContainer {
			height: 40px !important;
		}
		.customControl {
			padding-inline: 10px !important;
		}
		.customPlaceholder {
			font-size: 12px !important;
		}
		.customInput {
			input {
				font-size: 12px !important;
			}
		}
		.customDropdownIndicator {
			width: 21px !important;
			height: 21px !important;
		}
		.customOption {
			gap: 10px;
			padding-inline: 10px;
			padding-block: 8px;
			font-size: 12px !important;
		}
		.customNoOptionsMessage {
			font-size: 11px !important;
		}
		.selectedOptionsContainer {
			gap: 8px;
			padding-top: 11px;
			.selectedOptionsItem {
				min-width: 92px;
				max-width: 225px;
				height: 28px;
				padding-inline: 8px;
				font-size: 10px;
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.customContainer {
			height: 40px !important;
		}
		.customControl {
			padding-inline: 10px !important;
		}
		.customPlaceholder {
			font-size: 12px !important;
		}
		.customInput {
			input {
				font-size: 12px !important;
			}
		}
		.customDropdownIndicator {
			width: 21px !important;
			height: 21px !important;
		}
		.customOption {
			gap: 10px;
			padding-inline: 10px;
			padding-block: 8px;
			font-size: 12px !important;
		}
		.customNoOptionsMessage {
			font-size: 11px !important;
		}
		.selectedOptionsContainer {
			gap: 8px;
			padding-top: 11px;
			.selectedOptionsItem {
				min-width: 92px;
				max-width: 225px;
				height: 28px;
				padding-inline: 8px;
				font-size: 11px;
			}
		}
	}
`;
