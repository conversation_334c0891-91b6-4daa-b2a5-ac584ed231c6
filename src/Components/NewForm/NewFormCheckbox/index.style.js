import styled from 'styled-components';

export const StyleWrapper = styled.div`
	.newCustomCheckboxContainer {
		display: flex;
		align-items: center;
		gap: 13px;
		border: 1px solid rgba(49, 49, 50, 0.35);
		height: 53px;
		padding-inline: 13px;
		background-color: #ffffff;
	}
	.newCheckboxLabel {
		color: rgba(49, 49, 50, 0.92) !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 16px !important;
		line-height: 1 !important;
	}
	@media (max-width: 600px) {
		.newCustomCheckboxContainer {
			gap: 9px;
			height: 34px;
			padding-inline: 9px;
		}
		.newCheckboxLabel {
			font-size: 12px !important;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.newCustomCheckboxContainer {
			gap: 10px;
			height: 40px;
			padding-inline: 10px;
		}
		.newCheckboxLabel {
			font-size: 12px !important;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.newCustomCheckboxContainer {
			gap: 10px;
			height: 40px;
			padding-inline: 10px;
		}
		.newCheckboxLabel {
			font-size: 12px !important;
		}
	}
`;

export const CheckboxStyleWrapper = styled.div`
	width: 21px !important;
	height: 21px !important;
	display: flex;
	justify-content: center;
	align-items: center;
	.newFormCheckbox {
		border-radius: 0 !important;
		border: 1px solid rgba(49, 49, 50, 0.35) !important;
		float: unset !important;
		margin-left: unset !important;
		margin-top: unset !important;
		vertical-align: unset !important;
		width: 100% !important;
		height: 100% !important;
		cursor: pointer !important;
	}
	.newFormCheckbox:focus {
		box-shadow: none !important;
		outline: none !important;
		border-color: #00000040 !important;
	}
	.newFormCheckbox:checked {
		background-color: #f95c69 !important;
		border: 0px !important;
		box-shadow: none !important;
		&:active,
		&:focus {
			border: 0px !important;
		}
	}
	.newFormCheckbox:active,
	.newFormCheckbox:focus {
		box-shadow: none !important;
		outline: none !important;
		border-color: #00000040 !important;
	}
	@media (max-width: 600px) {
		width: 14px !important;
		height: 14px !important;
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		width: 17px !important;
		height: 17px !important;
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		width: 17px !important;
		height: 17px !important;
	}
`;
