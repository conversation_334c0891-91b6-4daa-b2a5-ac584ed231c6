import React from 'react';
import { Input } from 'reactstrap';
import { CheckboxStyleWrapper, StyleWrapper } from './index.style';

const NewFormCheckbox = ({
	id,
	name,
	value,
	onChange,
	checked,
	style,
	readOnly,
	children,
	disabled,
	wrapperClassName,
	checkboxClassName,
	...props
}) => {
	if (children) {
		return (
			<StyleWrapper className={wrapperClassName}>
				<div className="newCustomCheckboxContainer">
					<CheckboxStyleWrapper className={checkboxClassName}>
						<Input
							className="newFormCheckbox"
							style={style}
							type="checkbox"
							id={id}
							name={name}
							value={value}
							onChange={onChange}
							checked={checked}
							readOnly={readOnly}
							disabled={disabled}
							{...props}
						/>
					</CheckboxStyleWrapper>
					<span className="newCheckboxLabel">{children}</span>
				</div>
			</StyleWrapper>
		);
	}
	return (
		<CheckboxStyleWrapper className={checkboxClassName}>
			<Input
				className="newFormCheckbox"
				style={style}
				type="checkbox"
				id={id}
				name={name}
				value={value}
				onChange={onChange}
				checked={checked}
				readOnly={readOnly}
				disabled={disabled}
				{...props}
			/>
		</CheckboxStyleWrapper>
	);
};

export default NewFormCheckbox;
