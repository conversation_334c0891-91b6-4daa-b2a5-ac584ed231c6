import Select, { components } from 'react-select';
import { StyleWrapper } from './index.style';
import { CircleArrowDown, CancelIcon } from '../../Icons';

const DropdownIndicator = (props) => {
	return (
		<components.DropdownIndicator {...props}>
			<CircleArrowDown width="100%" height="100%" />
		</components.DropdownIndicator>
	);
};

const ClearIndicator = (props) => {
	return (
		<components.ClearIndicator {...props}>
			<CancelIcon width="100%" height="100%" stroke="#979797" />
		</components.ClearIndicator>
	);
};

const NewFormSelect = ({
    id,
    name,
    value,
    onChange,
    options,
    placeholder = '',
    isLoading = false,
    isSearchable = false,
    disabled = false,
    isClearable = false,
    wrapperClassName,
    // Add these new props with safe defaults
    menuPlacement = "auto", // Keep existing default - no breaking change
    menuPosition = "absolute",
    closeMenuOnScroll = false,
    menuShouldBlockScroll = false,
    menuShouldScrollIntoView = false,
    menuPortalTarget = null,
    styles = {},
    ...restProps // Allow any other react-select props
}) => {
    return (
        <StyleWrapper className={wrapperClassName}>
            <div style={{ paddingLeft: '1px' }}>
                <Select
                    name={name}
                    value={value}
                    options={options}
                    onChange={onChange}
                    placeholder={placeholder}
                    isLoading={isLoading}
                    isDisabled={disabled}
                    isSearchable={isSearchable}
                    isClearable={isClearable}
                    // Apply the new props
                    menuPlacement={menuPlacement}
                    menuPosition={menuPosition}
                    closeMenuOnScroll={closeMenuOnScroll}
                    menuShouldBlockScroll={menuShouldBlockScroll}
                    menuShouldScrollIntoView={menuShouldScrollIntoView}
                    menuPortalTarget={menuPortalTarget}
                    styles={styles}
                    {...restProps}
                    components={{
                        IndicatorSeparator: () => null,
                        DropdownIndicator,
                        ClearIndicator
                    }}
                    classNames={{
                        container: () => 'customContainer',
                        control: () => 'customControl',
                        valueContainer: () => 'customValue',
                        placeholder: () => 'customPlaceholder',
                        singleValue: () => 'customSingleValue',
                        input: () => 'customInput',
                        indicatorsContainer: () => 'customIndicatorsContainer',
                        clearIndicator: () => 'customClearIndicator',
                        dropdownIndicator: () => 'customDropdownIndicator',
                        menu: () => 'customMenu',
                        menuList: () => 'customMenuList',
                        option: (state) =>
                            state?.isSelected
                                ? 'customOption isSelected'
                                : 'customOption',
                        noOptionsMessage: () => 'customNoOptionsMessage'
                    }}
                    theme={(theme) => ({
                        ...theme,
                        borderRadius: '2px',
                        colors: {
                            ...theme.colors,
                            primary25: 'rgba(1,1,1,0.1)',
                            primary: '#ff5f5f',
                            primary50: 'rgba(1,1,1,0.1)'
                        }
                    })}
                />
            </div>
        </StyleWrapper>
    );
};

export default NewFormSelect;