import NewTooltip from '../../Common/NewTooltip';
import { StyleWrapper } from './index.style';
import infoIconImg from '../../../Assets/images/formRequired.png';

const NewFormLabel = ({ label, tooltip, className }) => {
	return (
		<StyleWrapper className={className}>
			<div className="newLabelContainer">
				<span className="newLabel">{label}</span>
				<span style={{ visibility: tooltip ? 'visible' : 'hidden' }}>
					<NewTooltip content={tooltip}>
						<img
							src={infoIconImg}
							alt="info-icon-image"
							className="infoIconImage"
						/>
					</NewTooltip>
				</span>
			</div>
		</StyleWrapper>
	);
};

export default NewFormLabel;
