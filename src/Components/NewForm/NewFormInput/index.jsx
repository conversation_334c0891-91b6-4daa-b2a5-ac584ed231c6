import { StyleWrapper } from './index.style';
import validIcon from '../../../Assets/images/accept.svg';
import invalidIcon from '../../../Assets/images/not-accept.svg';

const NewFormInput = ({
	type = 'text',
	id,
	name,
	value,
	onChange,
	onBlur,
	placeholder,
	disabled = false,
	isShowValidInvalidIcon = false,
	isValid = false,
	prefix,
	suffix,
	wrapperClassName,
	readOnly,
	...props
}) => {
	return (
		<StyleWrapper className={wrapperClassName}>
			<div className="newCustomInputContainer">
				{prefix && <div className="prefixContainer">{prefix}</div>}
				<input
					className="newCustomInput"
					type={type}
					id={id}
					name={name}
					value={value}
					onChange={onChange}
					onBlur={onBlur}
					placeholder={placeholder}
					disabled={disabled}
					readOnly={readOnly}
					{...props}
				/>
				{suffix && <div className="suffixContainer">{suffix}</div>}
				{isShowValidInvalidIcon && (
					<div className="validInvalidIconContainer">
						<img
							src={isValid ? validIcon : invalidIcon}
							alt="valid-icon"
							className="validInvalidIcon"
						/>
					</div>
				)}
			</div>
		</StyleWrapper>
	);
};

export default NewFormInput;
