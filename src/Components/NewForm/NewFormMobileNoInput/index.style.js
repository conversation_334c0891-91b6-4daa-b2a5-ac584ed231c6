import styled from 'styled-components';

const StyleWrapper = styled.div`
	.newCustomMobileNoInputContainer {
		display: flex;
		height: 53px;
		border: 1px solid rgba(49, 49, 50, 0.35);
	}
	.newCustomInput {
		width: 100% !important;
		height: 100% !important;
		outline: none !important;
		border: none !important;
		padding-inline: 13px !important;
		color: rgba(32, 34, 36, 1) !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 16px !important;
		line-height: 1 !important;
		&::placeholder {
			color: #979797 !important;
			font-family: 'nunitosans-regular' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
		&:disabled {
			background-color: transparent !important;
		}
	}
	.libraryInputWrapper {
		height: 100% !important;
		background-color: #fff !important;
		color: rgba(32, 34, 36, 1) !important;
		font-family: 'nunitosans-regular' !important;
		border-radius: 0 !important;
	}
	.libraryInput {
		display: none !important;
	}
	.intl-tel-input {
		width: fit-content !important;
		height: 100% !important;
	}
	.flag-container {
		position: unset !important;
		height: 100% !important;
		background-color: transparent !important;
	}
	.intl-tel-input.allow-dropdown.separate-dial-code .selected-flag {
		width: 100%;
		background-color: transparent !important;
		padding-inline: 13px !important;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		position: relative;
		.selected-dial-code {
			color: rgba(32, 34, 36, 1) !important;
			font-family: 'nunitosans-regular' !important;
			font-size: 16px !important;
			display: block;
			vertical-align: unset;
			padding-left: 0 !important;
			pointer-events: none;
		}
		.iti-flag {
			display: none !important;
		}
		&::after {
			content: '|';
			position: absolute;
			right: -3px;
			top: 50%;
			transform: translateY(-50%);
			color: rgba(32, 34, 36, 1);
			font-family: 'nunitosans-regular';
			font-size: 16px;
		}
	}
	.intl-tel-input .flag-container .arrow {
		font-size: 16px !important;
		margin-left: 0 !important;
		flex: 1;
		display: flex;
		justify-content: flex-end;
		pointer-events: none;
		display: none !important;
	}
	.country-list {
		.country-name,
		.dial-code {
			color: rgba(32, 34, 36, 1) !important;
			font-family: 'nunitosans-regular' !important;
			font-size: 16px !important;
		}
		border: 1px solid rgba(49, 49, 50, 0.35) !important;
		box-shadow: unset !important;
		border-radius: 0 !important;
		margin-top: 4px !important;
		/* Customize the scrollbar */
		scrollbar-width: thin; /* For Firefox */
		scrollbar-color: rgba(0, 0, 0, 0.2) #fff; /* For Firefox */
		/* Customize the scrollbar for Webkit browsers (Chrome, Safari, etc.) */
		::-webkit-scrollbar {
			width: 2px;
		}
		::-webkit-scrollbar-thumb {
			background-color: rgba(0, 0, 0, 0.2);
			border-radius: 3px !important;
		}
		::-webkit-scrollbar-track {
			background-color: #fff;
			border-radius: 18px !important;
		}
	}
	@media (max-width: 600px) {
		.newCustomMobileNoInputContainer {
			height: 36px;
		}
		.newCustomInput {
			padding-inline: 9px !important;
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
		}
		.intl-tel-input {
			width: fit-content !important;
		}
		.intl-tel-input.allow-dropdown.separate-dial-code .selected-flag {
			padding-inline: 9px !important;
			.selected-dial-code {
				font-size: 12px !important;
			}
			&::after {
				font-size: 12px;
			}
		}
		.intl-tel-input .flag-container .arrow {
			font-size: 12px !important;
		}
		.country-list {
			.country-name,
			.dial-code {
				font-size: 12px !important;
			}
			margin-top: 3px !important;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.newCustomMobileNoInputContainer {
			height: 40px;
		}
		.newCustomInput {
			padding-inline: 10px !important;
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
		}
		.intl-tel-input {
			width: fit-content !important;
		}
		.intl-tel-input.allow-dropdown.separate-dial-code .selected-flag {
			padding-inline: 10px !important;
			.selected-dial-code {
				font-size: 12px !important;
			}
			&::after {
				font-size: 12px;
			}
		}
		.intl-tel-input .flag-container .arrow {
			font-size: 12px !important;
		}
		.country-list {
			.country-name,
			.dial-code {
				font-size: 12px !important;
			}
			margin-top: 3px !important;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.newCustomMobileNoInputContainer {
			height: 40px;
		}
		.newCustomInput {
			padding-inline: 10px !important;
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
		}
		.intl-tel-input {
			width: fit-content !important;
		}
		.intl-tel-input.allow-dropdown.separate-dial-code .selected-flag {
			padding-inline: 10px !important;
			.selected-dial-code {
				font-size: 12px !important;
			}
			&::after {
				font-size: 12px;
			}
		}
		.intl-tel-input .flag-container .arrow {
			font-size: 12px !important;
		}
		.country-list {
			.country-name,
			.dial-code {
				font-size: 12px !important;
			}
			margin-top: 3px !important;
		}
	}
`;

export default StyleWrapper;
