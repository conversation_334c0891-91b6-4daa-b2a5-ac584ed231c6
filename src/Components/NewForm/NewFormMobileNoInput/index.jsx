import { useState, useEffect } from 'react';
import IntlTelInput from 'react-intl-tel-input';
import 'react-intl-tel-input/dist/main.css';
import StyleWrapper from './index.style';

const NewFormMobileNoInput = ({
	name,
	placeholder,
	countryCodeValue,
	mobileNoValue,
	onSelectCountry,
	onMobileNoChange,
	onMobileNoBlur,
	disabledCountry,
	disabled,
	wrapperClassName
}) => {
	const [fullMobileNoValue, setFullMobileNoValue] = useState('');
	useEffect(() => {
		if (countryCodeValue) {
			setFullMobileNoValue(`+${countryCodeValue}${mobileNoValue}`);
		}
	}, [countryCodeValue]);
	return (
		<StyleWrapper className={wrapperClassName}>
			<div className="newCustomMobileNoInputContainer">
				<div className={`libraryInputWrapper`}>
					<IntlTelInput
						inputClassName={`libraryInput`}
						preferredCountries={[]}
						defaultCountry={'au'}
						separateDialCode={true}
						value={fullMobileNoValue}
						onSelectFlag={onSelectCountry}
						disabled={disabledCountry}
					/>
				</div>
				<input
					className="newCustomInput"
					name={name}
					placeholder={placeholder}
					value={mobileNoValue}
					onChange={onMobileNoChange}
					onBlur={onMobileNoBlur}
					disabled={disabled}
				/>
			</div>
		</StyleWrapper>
	);
};

export default NewFormMobileNoInput;
