import styled from 'styled-components';

export const StyleWrapper = styled.div`
	.newCustomInputContainer {
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 13px;
		border: 1px solid rgba(49, 49, 50, 0.35);
		height: 53px;
		padding-inline: 13px;
		background-color: #ffffff;
	}
	.newCustomInput {
		width: 100% !important;
		height: 100% !important;
		outline: none !important;
		border: none !important;
		color: rgba(32, 34, 36, 1) !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 16px !important;
		line-height: 1 !important;
		&::placeholder {
			color: #979797 !important;
			font-family: 'nunitosans-regular' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
		&:disabled {
			background-color: transparent !important;
		}
	}
	.prefixContainer {
		align-self: stretch;
		display: flex;
		align-items: center;
		color: rgba(32, 34, 36, 1) !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 16px !important;
		line-height: 1 !important;
	}
	.eyeIconContainer {
		align-self: stretch;
		display: flex;
		align-items: center;
		.eyeIcon {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 24px;
			height: 24px;
			cursor: pointer;
		}
	}
	.validInvalidIconContainer {
		align-self: stretch;
		display: flex;
		align-items: center;
		.validInvalidIcon {
			display: block;
			width: 24px;
			height: 24px;
		}
	}
	@media (max-width: 600px) {
		.newCustomInputContainer {
			gap: 9px;
			height: 34px;
			padding-inline: 9px;
		}
		.newCustomInput {
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
		}
		.prefixContainer {
			font-size: 12px !important;
		}
		.eyeIconContainer {
			.eyeIcon {
				width: 15px;
				height: 15px;
			}
		}
		.validInvalidIconContainer {
			.validInvalidIcon {
				width: 15px;
				height: 15px;
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.newCustomInputContainer {
			gap: 10px;
			height: 40px;
			padding-inline: 10px;
		}
		.newCustomInput {
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
		}
		.prefixContainer {
			font-size: 12px !important;
		}
		.eyeIconContainer {
			.eyeIcon {
				width: 18px;
				height: 18px;
			}
		}
		.validInvalidIconContainer {
			.validInvalidIcon {
				width: 18px;
				height: 18px;
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.newCustomInputContainer {
			gap: 10px;
			height: 40px;
			padding-inline: 10px;
		}
		.newCustomInput {
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
		}
		.prefixContainer {
			font-size: 12px !important;
		}
		.eyeIconContainer {
			.eyeIcon {
				width: 18px;
				height: 18px;
			}
		}
		.validInvalidIconContainer {
			.validInvalidIcon {
				width: 18px;
				height: 18px;
			}
		}
	}
`;
