import { useState } from 'react';
import { StyleWrapper } from './index.style';
import validIcon from '../../../Assets/images/accept.svg';
import invalidIcon from '../../../Assets/images/not-accept.svg';
import { NewEyeSlashIcon } from '../../Icons/NewEye/NewEyeSlashIcon';
import { NewEyeIcon } from '../../Icons/NewEye/NewEyeIcon';

const NewFormPasswordInput = ({
	id,
	name,
	value,
	onChange,
	onBlur,
	placeholder,
	disabled = false,
	isShowValidInvalidIcon = false,
	isValid = false,
	prefix,
	wrapperClassName,
	readOnly,
	eyeIconStrokeWidth = '1.5',
	...props
}) => {
	const [isShowPassword, setIsShowPassword] = useState(false);
	return (
		<StyleWrapper className={wrapperClassName}>
			<div className="newCustomInputContainer">
				{prefix && <div className="prefixContainer">{prefix}</div>}
				<input
					className="newCustomInput"
					type={isShowPassword ? 'text' : 'password'}
					id={id}
					name={name}
					value={value}
					onChange={onChange}
					onBlur={onBlur}
					placeholder={placeholder}
					disabled={disabled}
					readOnly={readOnly}
					{...props}
				/>
				<div className="eyeIconContainer">
					<div
						className="eyeIcon"
						onClick={() => setIsShowPassword((prev) => !prev)}
					>
						{isShowPassword ? (
							<NewEyeIcon
								width={'100%'}
								height={'100%'}
								strokeWidth={eyeIconStrokeWidth}
							/>
						) : (
							<NewEyeSlashIcon
								width={'100%'}
								height={'100%'}
								strokeWidth={eyeIconStrokeWidth}
							/>
						)}
					</div>
				</div>
				{isShowValidInvalidIcon && (
					<div className="validInvalidIconContainer">
						<img
							src={isValid ? validIcon : invalidIcon}
							alt="valid-icon"
							className="validInvalidIcon"
						/>
					</div>
				)}
			</div>
		</StyleWrapper>
	);
};

export default NewFormPasswordInput;
