import { CommonRoutes, VenueSetupRoutes } from '../Utils/routes';
import PageNotFound from '../Pages/PageNotFound';

import AddNewMenu from '../Pages/VenueSetup/AddNewMenu';
import PickUpLocation from '../Pages/VenueSetup/PickUpLocation';
import WaitTime from '../Pages/VenueSetup/WaitTime';
import DocketPrinting from '../Pages/VenueSetup/DocketPrinting';
import VenueRoute from '../Components/Common/RouteWrappers/VenueRoute';
import ManageMenu from '../Pages/VenueSetup/ManageMenu';
import AssignCategoriesPage from '../Pages/VenueSetup/AssignCategories/AssignCategoriesPage';

const venueSetupRoutes = [
	{
		path: VenueSetupRoutes.svManageMenu,
		component: (
			<VenueRoute>
				{/* <VenueProtectedLayout forPublic={false} /> */}
				<ManageMenu />
			</VenueRoute>
		)
	},
	{
		path: VenueSetupRoutes.svAssignCategories,
		component: (
			<VenueRoute>
				{/* <VenueProtectedLayout forPublic={false} /> */}
				<AssignCategoriesPage />
			</VenueRoute>
		)
	},
	{
		path: VenueSetupRoutes.svNewMenuItem,
		component: (
			<VenueRoute>
				{/* <VenueProtectedLayout forPublic={false} /> */}
				<AddNewMenu />
			</VenueRoute>
		)
	},
	{
		path: VenueSetupRoutes.svPickupLocation,
		component: (
			<VenueRoute>
				{/* <VenueProtectedLayout forPublic={false} /> */}
				<PickUpLocation />
			</VenueRoute>
		)
	},
	{
		path: VenueSetupRoutes.svWaitTimes,
		component: (
			<VenueRoute>
				{/* <VenueProtectedLayout forPublic={false} /> */}
				<WaitTime />
			</VenueRoute>
		)
	},
	{
		path: VenueSetupRoutes.svDocketPrinting,
		component: (
			<VenueRoute>
				{/* <VenueProtectedLayout forPublic={false} /> */}
				<DocketPrinting />
			</VenueRoute>
		)
	},
	{ path: CommonRoutes.pageNotFound, component: <PageNotFound /> }
];

export default venueSetupRoutes;
