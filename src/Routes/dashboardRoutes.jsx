import ComingSoon from '../Components/Common/ComingSoon';
import {
	Security,
	StaticPage,
	RoleAndPermission,
	Dashboard,
	Live,
	Todo,
	OrderHistory,
	ManageMenu,
	SummaryReport,
	FinancialReport,
	Settings,
	VenueSecurity,
	Legal,
	ConnectVenue,
	Support,
	PosIntegration,
	AddStaff,
	DocketFeatures,
	DocketPrinters,
	VenueAccount,
	ManageMenuItemForm,
	VenueDashboard
} from '../Pages';
import {
	AdminPanelRoutes,
	AdvertiserPanelRoutes,
	VenuePanelRoutes
} from '../Utils/routes';
import { LAYOUT } from '../Helper/constant';
import AdminRoute from '../Components/Common/RouteWrappers/AdminRoute';
import VenueRoute from '../Components/Common/RouteWrappers/VenueRoute';
import AdvertiserRoute from '../Components/Common/RouteWrappers/AdvertiserRoute';
import { ManageAccount } from '../Pages/UserProfile/ManageAccount';
import NewHome from '../Pages/Home/NewHome';
import Customer from '../Pages/Customer/Customer';
import CustomerDetails from '../Pages/Customer/CustomerDetails/CustomerDetails';
import Segments from '../Pages/Segments';
import SegmentDetails from '../Pages/Segments/SegmentDetails';
import NewManageMenu from '../Pages/NewManageMenu';
import AddEditProduct2 from '../Pages/NewManageMenu/AddEditProduct2';
import ProductTaxes from '../Pages/ProductTaxes';
import MenuPickUpLocation from '../Pages/MenuPickUpLocation';
import Discounts from '../Pages/Discounts';
import CreateDiscount from '../Pages/Discounts/CreateDiscount';
import OpeningHours from '../Pages/OpeningHours';
import ContactUs from '../Components/Support/ContactUs';
import NewVenueProfile from '../Pages/NewVenueProfile';
import MyTabAds from '../Pages/MyTabAds';
import AdvertiserManageAccount from '../Pages/AdvertiserManageAccount';

export const adminRoutes = [
	{
		layout: LAYOUT.dashboard,
		path: AdminPanelRoutes.live,
		component: (
			<AdminRoute>
				<Live />
			</AdminRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: AdminPanelRoutes.dashboard,
		component: (
			<AdminRoute>
				<Dashboard />
			</AdminRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: AdminPanelRoutes.editProfile,
		component: (
			<AdminRoute>
				<ManageAccount />
			</AdminRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: AdminPanelRoutes.security,
		component: (
			<AdminRoute>
				<Security />
			</AdminRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: AdminPanelRoutes.staticPage,
		component: (
			<AdminRoute>
				<StaticPage />
			</AdminRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: AdminPanelRoutes.rolePermission,
		component: (
			<AdminRoute>
				<RoleAndPermission />
			</AdminRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: AdminPanelRoutes.todo,
		component: (
			<AdminRoute>
				<Todo />
			</AdminRoute>
		)
	}
];

export const venueRoutes = [
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.home,
		component: (
			<VenueRoute>
				<NewHome />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.dashboard,
		component: (
			<VenueRoute>
				<VenueDashboard />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.manageAccount,
		component: (
			<VenueRoute>
				<ManageAccount />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.venueProfile,
		component: (
			<VenueRoute>
				<NewVenueProfile />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.connectVenue,
		component: (
			<VenueRoute>
				<ConnectVenue />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.createVenueAccount,
		component: (
			<VenueRoute>
				<VenueAccount />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.mytabStaff,
		component: (
			<VenueRoute>
				<ComingSoon pageTitle="MyTab Staff" />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.addMytabStaff,
		component: (
			<VenueRoute>
				<AddStaff />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.security,
		component: (
			<VenueRoute>
				<Security />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.staticPage,
		component: (
			<VenueRoute>
				<StaticPage />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.orderHistory,
		component: (
			<VenueRoute>
				<OrderHistory />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.financialReport,
		component: (
			<VenueRoute>
				<FinancialReport />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.summaryReport,
		component: (
			<VenueRoute>
				<SummaryReport />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.salesAnalytics,
		component: (
			<VenueRoute>
				<ComingSoon pageTitle="Venue Analytics" />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.performanceAnalytics,
		component: (
			<VenueRoute>
				<ComingSoon pageTitle="Performance Analytics" />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.customerAnalytics,
		component: (
			<VenueRoute>
				<ComingSoon pageTitle="Customer Analytics" />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.settings,
		component: (
			<VenueRoute>
				<Settings />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.calender,
		component: (
			<VenueRoute>
				<ComingSoon pageTitle="Calender" />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.venueSecurity,
		component: (
			<VenueRoute>
				<VenueSecurity />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.legal,
		component: (
			<VenueRoute>
				<Legal />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.support,
		component: (
			<VenueRoute>
				<Support reqTabId={0} />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.docketFeatures,
		component: (
			<VenueRoute>
				<DocketFeatures />
			</VenueRoute>
		)
	},
	{
		path: VenuePanelRoutes.docketPrinters,
		component: <DocketPrinters />
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.posIntegration,
		component: (
			<VenueRoute>
				<PosIntegration />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.rewards,
		component: (
			<VenueRoute>
				<ComingSoon pageTitle={'Rewards'} />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.advertising,
		component: (
			<VenueRoute>
				<ComingSoon pageTitle={'In App Advertising'} />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.manageMenu,
		component: (
			<VenueRoute>
				<NewManageMenu />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.addManageMenu,
		component: (
			<VenueRoute>
				<AddEditProduct2 />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.editManageMenu + '/:id',
		component: (
			<VenueRoute>
				<AddEditProduct2 />
			</VenueRoute>
		)
	},
	//
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.venueOpeningHours,
		component: (
			<VenueRoute>
				{/* <VenueProfile /> */}
				<OpeningHours />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.venueScheduleOrders,
		component: (
			<VenueRoute>
				<ComingSoon pageTitle="Scheduled Orders" />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.menuPickupLocation,
		component: (
			<VenueRoute>
				{/* <Settings reqTabId={2} /> */}
				<MenuPickUpLocation />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.menuWaitTimes,
		component: (
			<VenueRoute>
				{/* <Settings reqTabId={1} /> */}
				<Settings selectedMenu="waitTime" title="Menu Wait Times" />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.discounts,
		component: (
			<VenueRoute>
				{/* <Settings reqTabId={3} /> */}
				<Settings selectedMenu="promoCode" title="Discounts" />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.newDiscounts,
		component: (
			<VenueRoute>
				<Discounts />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.createNewDiscount,
		component: (
			<VenueRoute>
				<CreateDiscount />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.viewNewDiscount,
		component: (
			<VenueRoute>
				<CreateDiscount />
			</VenueRoute>
		)
	},
	// {
	// 	layout: LAYOUT.dashboard,
	// 	path: VenuePanelRoutes.surcharges,
	// 	component: (
	// 		<VenueRoute>
	// 			<ComingSoon pageTitle={'Surcharges'} />
	// 		</VenueRoute>
	// 	)
	// },
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.surcharges,
		component: (
			<VenueRoute>
				{/* <Settings reqTabId={4} /> */}
				<Settings selectedMenu="taxes" title="Surcharges" />
			</VenueRoute>
		)
	},
    {
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.promocode,
		component: (
			<VenueRoute>
				{/* <Settings reqTabId={4} /> */}
				<Settings selectedMenu="promoCode" title="Discounts" />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.taxes,
		component: (
			<VenueRoute>
				<ProductTaxes />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.customers,
		component: (
			<VenueRoute>
				<Customer />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.customerDetails + '/:id',
		component: (
			<VenueRoute>
				<CustomerDetails />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.segments,
		component: (
			<VenueRoute>
				<Segments />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.segmentsDetails + '/:id',
		component: (
			<VenueRoute>
				<SegmentDetails />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.myTabAds,
		component: (
			<VenueRoute>
				<MyTabAds />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.inAppAdvertising,
		component: (
			<VenueRoute>
				<ComingSoon pageTitle={'App Advertising'} />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.venueAccountPassword,
		component: (
			<VenueRoute>
				<VenueSecurity reqTabId={0} title="Change Password" />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.venueIpadAccessPasscode,
		component: (
			<VenueRoute>
				<VenueSecurity reqTabId={1} title="Ipad Access Passcode" />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.venueRefundPin,
		component: (
			<VenueRoute>
				<ComingSoon pageTitle={'Refund Pin'} />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.liveSupport,
		component: (
			<VenueRoute>
				<ComingSoon pageTitle={'Live Support'} />
			</VenueRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.emailUs,
		component: (
			<VenueRoute>
				<ContactUs pageTitle={'Email Us'} />
			</VenueRoute>
		)
	},
	//
	{
		layout: LAYOUT.dashboard,
		path: VenuePanelRoutes.comingSoon,
		component: (
			<VenueRoute>
				<ComingSoon />
			</VenueRoute>
		)
	}
];

export const advertiserRoutes = [
	{
		layout: LAYOUT.dashboard,
		path: AdvertiserPanelRoutes?.myTabAds,
		component: (
			<AdvertiserRoute>
				<MyTabAds />
			</AdvertiserRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: AdvertiserPanelRoutes?.manageAccount,
		component: (
			<AdvertiserRoute>
				<AdvertiserManageAccount />
			</AdvertiserRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: AdvertiserPanelRoutes?.security,
		component: (
			<AdvertiserRoute>
				<Security />
			</AdvertiserRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: AdvertiserPanelRoutes?.support,
		component: (
			<AdvertiserRoute>
				<Support reqTabId={0} />
			</AdvertiserRoute>
		)
	},
	{
		layout: LAYOUT.dashboard,
		path: AdvertiserPanelRoutes?.emailUs,
		component: (
			<AdvertiserRoute>
				<ContactUs pageTitle={'Email Us'} />
			</AdvertiserRoute>
		)
	}
];
