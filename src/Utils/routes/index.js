// Browser Routes
export const AdminPanelRoutes = {
    live: '/admin/live',
    dashboard: '/admin/dashboard',
    editProfile: '/admin/edit-profile',
    security: '/admin/security',
    staticPage: '/admin/static-page',
    rolePermission: '/admin/role-permission',
    todo: '/admin/todo'
};

export const VenuePanelRoutes = {
    home: '/venue/home',
    subscription: '/venue/subscription',
    dashboard: '/venue/dashboard',
    manageAccount: '/venue/manage-account',
    venueProfile: '/venue/profile',
    connectVenue: '/venue/connect-venue',
    createVenueAccount: '/venue/venue-account/create',
    mytabStaff: '/venue/mytab-staff',
    addMytabStaff: '/venue/mytab-staff/add',
    security: '/venue/security',
    staticPage: '/venue/static-page',
    orderHistory: '/venue/order-history',
    financialReport: '/venue/reports/financial-report',
    summaryReport: '/venue/reports/summary-report',
    salesAnalytics: '/venue/reports/sales-analytics',
    performanceAnalytics: '/venue/reports/performance-analytics',
    customerAnalytics: '/venue/reports/customer-analytics',
    settings: '/venue/settings',
    calender: '/venue/calender',
    comingSoon: '/coming-soon',
    venueSecurity: '/venue/venue-security',

    legal: '/venue/legal',
    support: '/venue/support',
    docketFeatures: '/venue/premium-features/docket/features',
    posIntegration: '/venue/premium-features/pos',
    rewards: '/venue/rewards',
    advertising: '/venue/advertising',
    manageMenu: '/venue/manage-menu',
    addManageMenu: '/venue/manage-menu/add',
    editManageMenu: '/venue/manage-menu/edit',
    registerSubscription: '/venue/register-subscription',

    // opening-hours for venue plus menu category
    getVenueOpeningHours: '/v2/venue/opening-hours/getVenueOpeningHours',
    updateVenueOpeningHours: '/v2/venue/opening-hours/updateVenueOpeningHours',
    updateVenueCategoryOpeningHours:
        '/v2/venue/opening-hours/updateVenueCategoryOpeningHours',
    //

    addCampaign: '/advertiser/campaign/add-campaign',

    venueOpeningHours: '/venue/opening-hours',
    venueScheduleOrders: '/venue/schedule-orders',
    menuOpeningHours: '/venue/menu-opening-hours',
    menuPickupLocation: '/venue/setting/pickup-location',
    menuWaitTimes: '/venue/setting/wait-times',
    // discounts: '/venue/setting/discounts',
    newDiscounts: '/venue/discounts',
    createNewDiscount: '/venue/discounts/create-discount',
    viewNewDiscount: '/venue/discounts/:id',
    surcharges: '/venue/surcharges',
    promocode: '/venue/promocode',
    taxes: '/venue/setting/taxes',
    customers: '/venue/customers',
    customerDetails: '/venue/customer-details',
    segments: '/venue/segments',
    segmentsDetails: '/venue/segments-details',
    myTabAds: '/venue/mytab-ads',
    inAppAdvertising: '/in-app-advertising',
    venueAccountPassword: '/venue/venue-security/venue-account-password',
    venueIpadAccessPasscode: '/venue/venue-security/venue-ipad-access-passcode',
    venueRefundPin: '/venue/venue-security/venue-refund-pin',
    liveSupport: '/venue/live-support',
    emailUs: '/venue/email-us'
};

export const AdvertiserPanelRoutes = {
    myTabAds: '/advertiser/mytab-ads',
    manageAccount: '/advertiser/manage-account',
    security: '/advertiser/security',
    support: '/advertiser/support',
    emailUs: '/advertiser/email-us'
};

export const CommonRoutes = {
    landingPage: '/',
    login: '/login',
    forgotPassword: '/forgot-password',
    resetPassword: '/reset-password/:loginRole/:otpToken',
    register: '/register',
    setUp2Fa: '/setup-2fa',
    // Advertiser
    advertiserLogin: '/advertiser/login',
    advertiserForgotPassword: '/advertiser/forgot-password',
    advertiserResetPassword: '/advertiser/reset-password/:token',
    advertiserRegister: '/advertiser/register',
    pageNotFound: '/404'
};

export const VenueProtectedRoutes = {
    connectVenue: '/connect-venue',
    connectExistingVenue: '/connect-existing-venue',
    createVenuePortal: '/create-venue-portal',
    createStripeAccount: '/create-stripe-account',
    stripeAccountProcess: '/stripe-account-process',
    stripeAccountPageClose: '/stripe-account-process-closed',
    posIntegration: '/pos-integration',
    pageNotFound: '/404'
};

export const VenueSetupRoutes = {
    svManageMenu: '/venue-setup/manage-menu',
    svNewMenuItem: '/venue-setup/new-menu-item',
    svPickupLocation: '/venue-setup/pickup-location',
    svWaitTimes: '/venue-setup/wait-times',
    svAssignCategories: '/venue-setup/pickup-location',
    svDocketPrinting: '/venue-setup/docket-printing',
    pageNotFound: '/404'
};

export const NotRedirectAfterCreate = [
    '/connect-venue',
    '/connect-existing-venue',
    '/create-venue-portal'
];

// API Routes
export const VenueApiRoutes = {
    // Auth Api
    register: '/venue/auth/register',
    verifyOtp: '/venue/auth/verify-mfa',
    forgotPassword: '/venue/auth/forgot-password',
    resetPassword: '/venue/auth/reset-password',
    changePassword: '/venue/security/change-password',
    connectVenue: '/venue/bar/connectVenue',
    connectVenueOtpVerification: '/venue/bar/venue-otp-verify',
    subscription: '/venue/auth/save-subscription',
    createVenue: '/venue/bar/createVenue',
    editVenue: '/venue/bar/updateVenueDetails',
    deleteVenue: '/venue/bar/delete', // Delink Venue
    delete: '/venue/bar/deleteVenue', // Delete Venue
    getConnectedVenue: '/venue/auth/myVenueDetails',
    getSingleConnectedVenue: '/venue/bar/getvenuedetails',
    getAccountDeleteTypeForm: '/v2/venue/bar/getAccountDeleteForm',
    editProfile: '/venue/auth/edit-profile',
    getProfileDetails: '/venue/auth/get-profile-details',
    setUpGoogleAuthenticator: '/venue/auth/setup-mfa',
    getSummaryReportTableData: '/venue/report/itemsummaryreport',
    exportSummaryReportData: '/venue/report/downloaditemsummaryreport',
    getCategoryList: '/venue/report/getCategory',
    deleteAccount: 'venue/auth/delete',
    verifyPassword: '/venue/auth/verify-password',
    updateVenueMobileNo: '/venue/bar/updateVenueMobile',
    logout: '/venue/auth/logout',
    //Home Page Api
    getVenueStatistics: '/venue/dashboard/get-venue-statistics',
    getVenueStatisticsNew: '/venue/dashboard/get-venue-statistics-new',
    getTotalRevenue: '/venue/dashboard/total-revenue',
    serviceTypePercentage: '/venue/dashboard/service-type-percentage',
    ordersPerHours: '/venue/dashboard/orders-per-hours',
    serviceTypeRevenue: '/venue/dashboard/service-type-revenue',
    customerCountByTime: '/venue/dashboard/customer-count-by-time',
    mostOrderdItems: '/venue/dashboard/most-orderd-items',
    // Customers Api
    getCustomerList: '/venue/customer/list',
    getCustomerDetails: '/venue/customer/details',
    getCustomerOrderList: '/venue/customer/order-list',
    getCustomerSegmentList: '/venue/customer/segment-list',
    exportCustomerTableData: '/venue/customer/download',
    // Segments Api
    getSegmentList: '/venue/segment/list',
    getSegmentDetails: '/venue/segment/details',
    exportSegmentTableData: '/venue/segment/export-list',
    getSegmentCustomerList: '/venue/segment/customer-list',
    exportSegmentCustomerTableData: '/venue/segment/export-customer-list',
    // Order History Api
    getOrderHistoryTableData: '/v2/venue/order/barOrderHistory ',
    exportOrderHistoryTableData: '/venue/order/downloadOrderHistory',
    viewOrderDetails: '/venue/order/orderView',
    cancelOrder: '/venue/order/cancel',
    // Live Dashboard Api
    venueOrderList: '/v2/venue/order/barOrderList',
    updateOrderItemWaitTime: '/venue/order/updateOrderItemWaitTime',
    updateOrderItemStatus: '/venue/order/updateOrderItemStatus',
    orderReadyForPickupAlert: '/venue/order/readyForPickupAlert',
    orderIntoxicated: '/venue/order/orderIntoxicated',
    // Manage Menu Api
    getFoodOptionList: '/venue/product/foodOptionList',
    getProductList: '/venue/product/getProductList',
    getMenuList: '/venue/category/getSubCategoryForManageMenu',
    getMenuItemList: '/venue/product/getProductListV2',
    updateProductSequence: '/venue/product/productSequence',
    changeProductStatus: '/venue/product/change-status',
    deleteProduct: '/venue/product/delete',
    updateSubcategorySequence: '/venue/category/barSubCategorySequence',
    getSubCategory: '/venue/category/getSubCategory',
    getSubCategoryList: '/venue/category/getSubCategoryList',
    getPickupLocation: '/venue/product/pickuplocation-list',
    addProduct: '/venue/product/add',
    getSingleProduct: '/venue/product/getSingleProduct',
    editProduct: '/venue/product/edit',
    getProductItemActiveHours: '/venue/product/getItemActiveHours',
    addUpdateProductItemActiveHours: '/venue/product/addUpdateItemActiveHours',
    deleteProductItemActiveHours: '/venue/product/deleteActiveHours',
    // New Manage Menu Api
    getSegmentTagsList: '/v2/venue/product/getSegmentTags',
    getMenuCategoryList: '/v2/venue/product/getVenueSubCategories',
    getUpsellCategoryList: '/v2/venue/upsell/list',
    upsellCategory: '/v2/venue/upsell',
    globalPriceUpdate: '/v2/venue/product/globalPriceUpdate',

    // Docket Printing Api
    docketPrintingFeatStatus: '/venue/feature/change-status',
    // POS Api
    createSubscription: '/doshi/stripe/subscription',
    completeSubscription: '/doshi/stripe/subscription/complete',
    manualMenuSync: '/doshi/getMenuFromDoshii',
    enablePOS: '/doshi/pos/enable',
    disablePOS: '/doshi/pos/disable',
    // Account Settings Api
    //Service Type
    getServiceType: '/venue/bar/getServiceType',
    updateServiceType: '/venue/bar/updateServiceType',
    // Wait Time
    getWaitTimeServiceType: '/venue/bar/getWaitTimeServiceType',
    updateWaitTimeServiceType: '/venue/bar/updateWaitTimeServiceType',
    getWaitTime: '/venue/bar/getSubHeadingWaitTime',
    updateWaitTime: '/venue/bar/updateSubHeadingWaitTime',
    getWaitTimev2: '/v2/venue/bar/getSubHeadingWaitTime',
    updateWaitTimev2: '/v2/venue/bar/updateSubHeadingWaitTime',
    // Pickup Location
    getPickupLocationList: '/venue/pickup-location/list',
    addPickupLocation: '/venue/pickup-location/add',
    editPickupLocation: '/venue/pickup-location/edit',
    deletePickupLocation: '/venue/pickup-location/delete',
    getPickupLocationListv2: '/v2/venue/pickup-location/list',
    addPickupLocationv2: '/v2/venue/pickup-location/add',
    editPickupLocationv2: '/v2/venue/pickup-location/edit',
    deletePickupLocationv2: '/v2/venue/pickup-location/delete',
    // Promocode
    getPromocodeList: '/venue/promocode/list',
    addPromocode: '/venue/promocode/add',
    editPromocode: '/venue/promocode/edit',
    deletePromocode: '/venue/promocode/delete',
    //Discount
    getDiscountList: '/v2/venue/discount/list',
    addDiscount: '/v2/venue/discount',
    editDiscount: '/v2/venue/discount',
    deleteDiscount: '/v2/venue/discount',
    viewDiscount: '/v2/venue/discount/details',
    // Taxes
    getTaxList: '/venue/tax/list',
    addTax: '/venue/tax/add',
    editTax: '/venue/tax/edit',
    deleteTax: '/venue/tax/delete',
    getProductTax: '/v2/venue/bar/getProductTax',
    // Legal
    termsAndConditions: '/venue/page/terms',
    privacyPolicy: '/venue/page/barPrivacyPolicy',
    // Support
    faqs: '/venue/page/faq',
    supportContactUs: '/venue/bar/contactUs',
    // Passcode
    changePassCode: '/venue/bar/change-passCode',
    updatePassCodeStatus: '/venue/bar/updatePassCodeStatus',
    setPassCode: '/venue/bar/setPassCode',
    verifyPassCode: '/venue/bar/verifyPassCode',
    // Change Password
    changeVenuePassword: '/venue/bar/change-password',
    // Security Api
    securityVerifyOtp: '/venue/security/verify-otp'
};

export const CommonApiRoutes = {
    addCampaign: '/advertiser/campaign/add-campaign',
    campaignList: '/advertiser/campaign/campaign-list',
    deleteCampaign: '/advertiser/campaign/delete-campaign',
    createAds: '/advertiser/ads/add-ads',
    getAllSegmentList: '/venue/segment/all-segment-list',
    createCard: '/add-card',
    getCardList: '/advertiser/ads/saved-cards',
    create_ad_payment: "/advertiser/ads/createAdWithPayment",
    updateCard: "/advertiser/ads/update-card",
    deleteAd: "/advertiser/ads/delete-ad",
    updatePauseStatus: "/advertiser/ads/update-pause-status",
    campaignPerformanceCount: `/advertiser/reports/campaign-performance-count`,
    reportsCampaignList: `/advertiser/reports/campaign-list`,
    advertiserPerformanceList: `/advertiser/reports/ads-list`,
    exportAdReportTableData: '/advertiser/reports/export-campaign-ads-report',
};

export const AdminApiRoutes = {
    verifyOtp: '/cms/admin/verify-mfa',
    forgotPassword: '/cms/admin/forgot-password',
    resetPassword: '/cms/admin/reset-password',
    changePassword: '/cms/security/change-password',
    editProfile: '/cms/admin/edit-profile',
    getProfileDetails: '/cms/admin/get-profile-details'
};

export const AdvertiserApiRoutes = {
    login: '/advertiser/auth/login',
    register: '/advertiser/auth/register',
    sendVerificationCode: '/advertiser/auth/send-verification-code',
    verifyEmailOtp: '/advertiser/auth/verify-email-otp',
    setupMfa: '/advertiser/auth/setup-mfa',
    forgotPassword: '/advertiser/auth/forgot-password',
    resetPassword: '/advertiser/auth/reset-password',
    editProfile: '/advertiser/auth/edit-profile',
    logout: '/advertiser/auth/logout',
    // Security Api
    changePassword: '/advertiser/security/change-password',
    securityVerifyOtp: '/advertiser/security/verify-otp',
    getTimezones: '/advertiser/auth/getTimezones'
};
