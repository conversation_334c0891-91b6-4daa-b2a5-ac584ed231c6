import moment from 'moment-timezone';

/**
 * Converts local date and time to UTC based on user's timezone
 * 
 * @param {Date|string} dateValue - The date value (can be Date object or string)
 * @param {string} timeValue - The time value (format: "HH:MM" or "HH:MM AM/PM")
 * @param {string} timezone - The user's timezone (e.g., "Australia/Sydney")
 * @returns {Object} - Object containing UTC date and time as strings
 */
export const convertLocalToUTC = (dateValue, timeValue, timezone) => {
  if (!dateValue) return { date: '', time: '' };
  
    // Format the date to YYYY-MM-DD
    const dateStr = moment(dateValue).format('YYYY-MM-DD');
  
    // Normalize time format
    let timeStr = '00:00:00';
    if (timeValue) {
        // Check if time is in 12-hour format (e.g., "09:18 PM")
        if (/^\d{1,2}:\d{2}\s?[AP]M$/i.test(timeValue)) {
        timeStr = moment(timeValue, 'hh:mm A').format('HH:mm:ss');
        } else if (/^\d{1,2}:\d{2}$/.test(timeValue)) {
        // Add seconds if they're missing
        timeStr = timeValue + ':00';
        } else {
        timeStr = timeValue;
        }
    }
    
  // Create datetime in local timezone
  const localDateTime = moment.tz(`${dateStr} ${timeStr}`, 'YYYY-MM-DD HH:mm:ss', timezone);
  
  // Convert to UTC
  const utcDateTime = localDateTime.clone().tz('UTC');
  
  return {
    date: utcDateTime.format('YYYY-MM-DD'),
    time: utcDateTime.format('hh:mm A') // Format as "HH:MM AM/PM"
  };
};

/**
 * Converts UTC date and time to local timezone
 * 
 * @param {string} dateValue - The UTC date (format: "YYYY-MM-DD")
 * @param {string} timeValue - The UTC time (format: "HH:MM:SS" or "hh:mm A")
 * @param {string} timezone - The user's timezone (e.g., "Australia/Sydney")
 * @returns {Object} - Object containing local date and time as strings
 */
export const convertUTCToLocal = (dateValue, timeValue, timezone) => {
  if (!dateValue) return { date: '', time: '' };
  
  // Normalize time format - handle both 24h and 12h formats
  let normalizedTime = timeValue || '00:00:00';
  if (/^\d{1,2}:\d{2}\s?[AP]M$/i.test(normalizedTime)) {
    // Convert 12h format to 24h format
    normalizedTime = moment(normalizedTime, 'hh:mm A').format('HH:mm:ss');
  } else if (/^\d{1,2}:\d{2}$/.test(normalizedTime)) {
    // Add seconds if they're missing
    normalizedTime = normalizedTime + ':00';
  }
  
  // Create datetime in UTC
  const utcDateTime = moment.utc(`${dateValue} ${normalizedTime}`, 'YYYY-MM-DD HH:mm:ss');
  
  // Convert to local timezone
  const localDateTime = utcDateTime.clone().tz(timezone || 'UTC');
  
  return {
    date: localDateTime.format('YYYY-MM-DD'),
    time: localDateTime.format('hh:mm A')
  };
};

/**
 * Formats a schedule display string from UTC dates and times to local timezone
 * 
 * @param {string} startDate - Start date in YYYY-MM-DD format
 * @param {string} endDate - End date in YYYY-MM-DD format
 * @param {string} startTime - Start time
 * @param {string} endTime - End time
 * @param {string} timezone - User's timezone
 * @returns {string} - Formatted schedule string
 */
export const formatScheduleDisplay = (startDate, endDate, startTime, endTime, timezone) => {
    if (!startDate) return 'Schedule not set';
    
    // Convert UTC dates to local timezone
    const startLocal = convertUTCToLocal(startDate, startTime, timezone);
    const endLocal = convertUTCToLocal(endDate, endTime, timezone);
    
    // Format dates for display
    const formattedStart = moment(startLocal.date).format('MMM D, YYYY') + ' ' + startLocal.time;
    const formattedEnd = moment(endLocal.date).format('MMM D, YYYY') + ' ' + endLocal.time;
    
    return `${formattedStart} - ${formattedEnd}`;
};

/**
 * Converts UTC date string to local date in DD/MM/YYYY format based on user's timezone
 * 
 * @param {string} utcDateString - The UTC date string (e.g., "2025-06-20T13:13:54.000Z")
 * @param {string} timezone - The user's timezone (e.g., "Australia/Perth")
 * @returns {string} - Formatted date string in DD/MM/YYYY format
 */
export const formatUTCToLocalDate = (utcDateString, timezone) => {
  if (!utcDateString) return '';
  
  // Default to UTC if no timezone is provided
  const tz = timezone || 'UTC';
  
  // Create moment object in UTC, then convert to specified timezone
  const localDate = moment.utc(utcDateString).tz(tz);
  
  // Format as DD/MM/YYYY
  return localDate.format('DD/MM/YYYY');
};

/**
 * Converts a date in user's timezone to UTC ISO format
 * 
 * @param {string} dateStr - The date string in YYYY-MM-DD format
 * @param {string} timeStr - The time string (e.g., "00:00:00" or "23:59:59")
 * @param {string} timezone - The user's timezone (e.g., "Australia/Sydney")
 * @returns {string} - UTC date in ISO format (e.g., "2025-05-31T16:00:00Z")
 */
export const convertUserTimezoneToUTC = (dateStr, timeStr, timezone) => {
  if (!dateStr) return '';
  
  // Create datetime in user's timezone
  const localDateTime = moment.tz(`${dateStr} ${timeStr}`, 'YYYY-MM-DD HH:mm:ss', timezone);
  
  // Convert to UTC ISO format
  return localDateTime.utc().format(); // Returns ISO format like 2025-05-31T16:00:00Z
};

/**
 * Formats a date in user's timezone to YYYY-MM-DD format without UTC conversion
 * 
 * @param {Date|string} dateValue - The date value (can be Date object or string)
 * @param {string} timezone - The user's timezone (e.g., "Australia/Sydney")
 * @returns {string} - Date formatted as YYYY-MM-DD in user's timezone
 */
export const formatDateInUserTimezone = (dateValue, timezone) => {
  if (!dateValue) return '';
  
  // Default to UTC if no timezone is provided
  const tz = timezone || 'UTC';
  
  // Create moment object in the user's timezone and format it
  return moment.tz(dateValue, tz).format('YYYY-MM-DD');
};

