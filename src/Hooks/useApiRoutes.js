// Custom hook approach - hooks/useApiRoutes.js
import { useMemo } from 'react';
import { useSelector } from 'react-redux';

export const useApiRoutes = () => {
    const authData = useSelector((state) => ({ ...state.auth }));

    const apiRoutes = useMemo(() => {
        const userType = authData?.login_type;
        const baseRoute = userType === 'venue' ? '/venue' : '/advertiser';

        return {
            addCampaign: `${baseRoute}/campaign/add-campaign`,
            campaignList: `${baseRoute}/campaign/campaign-list`,
            deleteCampaign: `${baseRoute}/ads/delete-campaign`,
            createAds: `${baseRoute}/ads/add-ads`,
            getAllSegmentList: '/venue/segment/all-segment-list',
            createCard: `${baseRoute}/ads/add-card`,
            getCardList: `${baseRoute}/ads/saved-cards`,
            create_ad_payment: `${baseRoute}/ads/createAdWithPayment`,
            updateCard: `${baseRoute}/ads/update-card`,
            deleteAd: `${baseRoute}/ads/delete-ad`,
            campaignPerformanceCount: `${baseRoute}/reports/campaign-performance-count`,
            reportsCampaignList: `${baseRoute}/reports/campaign-list`,
            advertiserPerformanceList: `${baseRoute}/reports/ads-list`
        };
    }, [authData?.login_type]);

    return apiRoutes;
};
