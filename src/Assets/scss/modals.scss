body {
	.manageMenuImageCropModal {
		.modal-content {
			padding: 24px !important;
			border-radius: 16px !important;
			border: 1px solid rgba(255, 255, 255, 1) !important;
		}
	}
	.newModal {
		.modal-content {
			border-radius: 16px !important;
			.modal-header {
				background: #f3f3f3 !important;
				border-bottom: 1px solid rgba(0, 0, 0, 0.17) !important;
				border-top-left-radius: 16px !important;
				border-top-right-radius: 16px !important;
				position: relative;
				.modal-title {
					font-family: 'nunitosans-semi-bold' !important;
					font-size: 18px !important;
					line-height: 1 !important;
					color: #202224 !important;
				}
				.btn-close {
					display: none !important;
				}
				.closeButton {
					position: absolute;
					top: 50%;
					right: 8px;
					transform: translateY(-50%);
					border-radius: 8px;
					width: 32px;
					height: 32px;
					display: flex;
					justify-content: center;
					align-items: center;
					cursor: pointer;
					&:hover {
						background-color: rgba(1, 1, 1, 0.1);
						transition: all 0.5s;
					}
				}
			}
			.modal-body {
				padding: 0 !important;
			}
			.modal-footer {
				border-top: 1px solid rgba(0, 0, 0, 0.17) !important;
				padding: 12px 16px !important;
				.submitCancelBtnWrapper {
					margin: 0 !important;
					display: flex;
					gap: 12px;
					.newModalButtonOutlined {
						margin: 0 !important;
						border: none;
						border-radius: 6px;
						border: 1px solid rgb(249, 92, 105);
						background: none;
						color: rgb(249, 92, 105);
						height: 36px;
						padding-inline: 16px;
						font-family: 'nunitosans-semi-bold';
						font-size: 14px;
						line-height: 1;
						display: flex;
						align-items: center;
						justify-content: center;
					}
					.newModalButtonFilled {
						margin: 0 !important;
						border: none;
						border-radius: 6px;
						border: 1px solid rgb(249, 92, 105);
						background: rgb(249, 92, 105);
						color: #ffffff;
						height: 36px;
						padding-inline: 16px;
						font-family: 'nunitosans-semi-bold';
						font-size: 14px;
						line-height: 1;
						display: flex;
						align-items: center;
						justify-content: center;
						gap: 8px;
						.newSpinnerWrapper {
							width: 14px;
							height: 14px;
							.newSpinner {
								width: 100% !important;
								height: 100% !important;
								border-width: 2px !important;
							}
						}
					}
				}
			}
			position: relative;
			.newModalLoaderWrapper {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background-color: rgba(1, 1, 1, 0.1);
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
		@media (max-width: 600px) {
			.modal-content {
				border-radius: 12px !important;
				.modal-header {
					border-top-left-radius: 12px !important;
					border-top-right-radius: 12px !important;
					.modal-title {
						font-size: 13px !important;
					}
					.closeButton {
						right: 8px;
						width: 24px;
						height: 24px;
					}
				}
				.modal-footer {
					.submitCancelBtnWrapper {
						gap: 12px;
						.newModalButtonOutlined {
							height: 28px;
							padding-inline: 12px;
							font-size: 12px;
						}
						.newModalButtonFilled {
							height: 28px;
							padding-inline: 12px;
							font-size: 12px;
							.newSpinnerWrapper {
								width: 12px;
								height: 12px;
							}
						}
					}
				}
			}
		}
		@media only screen and (min-width: 601px) and (max-width: 1299px) {
			.modal-content {
				.modal-header {
					.modal-title {
						font-size: 14px !important;
					}
					.closeButton {
						right: 8px;
					}
				}
				.modal-footer {
					.submitCancelBtnWrapper {
						gap: 12px;
						.newModalButtonOutlined {
							height: 32px;
							padding-inline: 14px;
							font-size: 13px;
						}
						.newModalButtonFilled {
							height: 32px;
							padding-inline: 14px;
							font-size: 13px;
							.newSpinnerWrapper {
								width: 13px;
								height: 13px;
							}
						}
					}
				}
			}
		}
		@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
			.modal-content {
				.modal-header {
					.modal-title {
						font-size: 14px !important;
					}
					.closeButton {
						right: 8px;
					}
				}
				.modal-footer {
					.submitCancelBtnWrapper {
						gap: 12px;
						.newModalButtonOutlined {
							height: 32px;
							padding-inline: 14px;
							font-size: 13px;
						}
						.newModalButtonFilled {
							height: 32px;
							padding-inline: 14px;
							font-size: 13px;
							.newSpinnerWrapper {
								width: 13px;
								height: 13px;
							}
						}
					}
				}
			}
		}
	}
	.globalPriceUpdateModal {
		width: 380px !important;
		min-width: 380px !important;
		max-width: 380px !important;
		.modal-content {
			border-radius: 9px !important;
			.modal-header {
				background: #fff !important;
				border-top-left-radius: 9px !important;
				border-top-right-radius: 9px !important;
			}
		}
		@media (max-width: 600px) {
			width: 256px !important;
			min-width: 256px !important;
			max-width: 256px !important;
			margin: 0 auto !important;
		}
		@media only screen and (min-width: 601px) and (max-width: 1299px) {
			width: 285px !important;
			min-width: 285px !important;
			max-width: 285px !important;
		}
		@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
			width: 285px !important;
			min-width: 285px !important;
			max-width: 285px !important;
		}
	}
	.discountTypeModal {
		width: 688px !important;
		min-width: 688px !important;
		max-width: 688px !important;
		@media (max-width: 600px) {
			width: auto !important;
			min-width: auto !important;
			max-width: auto !important;
		}
		@media only screen and (min-width: 601px) and (max-width: 1299px) {
			width: 516px !important;
			min-width: 516px !important;
			max-width: 516px !important;
		}
		@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
			width: 516px !important;
			min-width: 516px !important;
			max-width: 516px !important;
		}
	}
	.browseModal {
		width: 648px !important;
		min-width: 648px !important;
		max-width: 648px !important;
		@media (max-width: 660px) {
			width: auto !important;
			min-width: auto !important;
			max-width: auto !important;
		}
		@media only screen and (min-width: 661px) and (max-width: 1299px) {
			width: 648px !important;
			min-width: 648px !important;
			max-width: 648px !important;
		}
		@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
			width: 648px !important;
			min-width: 648px !important;
			max-width: 648px !important;
		}
	}
	.discountLimitModal {
		width: 502px !important;
		min-width: 502px !important;
		max-width: 502px !important;
		@media (max-width: 660px) {
			width: auto !important;
			min-width: auto !important;
			max-width: auto !important;
		}
		@media only screen and (min-width: 661px) and (max-width: 1299px) {
			width: 377px !important;
			min-width: 377px !important;
			max-width: 377px !important;
		}
		@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
			width: 377px !important;
			min-width: 377px !important;
			max-width: 377px !important;
		}
	}
	.venueProfileImageCropModal {
		width: 634px !important;
		min-width: 634px !important;
		max-width: 634px !important;
	}
	.venueOpeningHoursModal {
		width: 791px !important;
		min-width: 791px !important;
		max-width: 791px !important;

		@media (max-width: 660px) {
			width: auto !important;
			min-width: auto !important;
			max-width: auto !important;
		}

		@media only screen and (min-width: 661px) and (max-width: 1299px) {
			width: 593px !important;
			min-width: 593px !important;
			max-width: 593px !important;
		}

		@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
			width: 593px !important;
			min-width: 593px !important;
			max-width: 593px !important;
		}
	}
	.advertiserProfileImageCropModal {
		width: 500px !important;
		min-width: 500px !important;
		max-width: 500px !important;
	}
}
