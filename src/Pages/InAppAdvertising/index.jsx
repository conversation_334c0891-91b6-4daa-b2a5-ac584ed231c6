import { useSelector } from 'react-redux';
import ComingSoon from '../../Components/Common/ComingSoon';

import PageTitle from '../../Components/Common/PageTitle';
import PageWrapper from './index.style';

export const InAppAdvertising = () => {
	const allThemeData = useSelector((state) => ({ ...state.themeChanger }));

	return (
		<PageWrapper {...allThemeData}>
			<div className="page-header">
				<PageTitle className="pb-10" title="In App Advertising" />
			</div>
		</PageWrapper>
	);
};
