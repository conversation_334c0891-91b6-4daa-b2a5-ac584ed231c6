import { useSelector } from 'react-redux';
import { Button } from 'reactstrap';
import Scrollbars from 'react-custom-scrollbars';
import 'react-tabs-scrollable/dist/rts.css';

import PageWrapper from './index.style';
import PageTitle from '../../Components/Common/PageTitle';
import FormInputGroup from '../../Components/Form/FormInputGroup';
import { MagnifyingGlassIcon, PlusIcon } from '../../Components/Icons';
import MytabStaffTable from '../../Components/MyTabStaff/MytabStaffTable';
import { useNavigate } from 'react-router-dom';
import { VenuePanelRoutes } from '../../Utils/routes';

export const MyTabStaff = () => {
	const allThemeData = useSelector((state) => ({ ...state.themeChanger }));
	const navigate = useNavigate();
	return (
		<PageWrapper {...allThemeData}>
			<div className="page">
				<div className="page-header">
					<PageTitle title="MyTab Staff" />
					<div className="pa-t-32 d-flex flex-column flex-lg-row gap-2 pa-b-32">
						<div className="flex-1">
							<FormInputGroup
								id="search"
								type="text"
								name="search"
								placeholder="Search Order ID"
								icon={
									<MagnifyingGlassIcon
										height={24}
										width={24}
									/>
								}
								iconPlacement="start"
								className="min-height-52"
								formGroupClassName="mb-0"
							/>
						</div>
						<div className="d-flex justify-content-end">
							<Button
								className="no-border pageButton d-flex justify-content-between align-items-center fs-18 semi-bold-text bg-transparent themeLinkText"
								onClick={() =>
									navigate(VenuePanelRoutes.addMytabStaff)
								}
							>
								<PlusIcon height={24} width={24} />{' '}
								<span className="pl-10 fs-18 medium-text">
									Add Staff
								</span>
							</Button>
						</div>
					</div>
				</div>
				<div className="page-body">
					<Scrollbars autoHide>
						<MytabStaffTable />
					</Scrollbars>
				</div>
			</div>
		</PageWrapper>
	);
};
