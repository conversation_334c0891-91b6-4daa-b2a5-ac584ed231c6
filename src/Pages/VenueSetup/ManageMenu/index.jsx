/* eslint-disable no-unused-vars */
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useCachedData } from '../../../Hooks/useCachedData';
import { VenueApiRoutes, VenuePanelRoutes } from '../../../Utils/routes';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import uiActions from '../../../Redux/ui/actions';
import PageStructure from '../../../Components/Common/PageStructure';
import { CameraIcon, Filter } from '../../../Components/Icons';
import { PageWrapper, TabsWrapper } from '../../ManageMenu/index.style';
import TabsSkeleton from '../../../Components/ManageMenu/Skeleton/TabsSkeleton';
import { Tab, Tabs } from 'react-tabs-scrollable';
import Rearrange from '../../../Components/Icons/Rearrange';
import FilterPopover from '../../../Components/ManageMenu/FilterPopover';
import ProductItemSkeleton from '../../../Components/ManageMenu/Skeleton/ProductItemSkeleton';
import EmptyData from '../../../Components/Common/EmptyData';
import {
	Accordion,
	AccordionBody,
	AccordionHeader,
	AccordionItem,
	Button,
	Col,
	FormGroup,
	Offcanvas,
	OffcanvasBody,
	Row,
	TabContent,
	TabPane
} from 'reactstrap';
import Menuitems from '../../../Components/ManageMenu/Menuitems';
import RearrangeMenuModal2 from '../../../Components/ManageMenu/RearrangeMenuModal2';
import OperatingHoursModal from '../../../Components/ManageMenu/OperatingHoursModal';
import OutlinedButton from '../../../Components/Common/CustomButton/OutlinedButton';
import Agreement from '../../../Components/Authentication/Agreement';
import FormInput from '../../../Components/Form/FormInput';
import NewFormInput from '../../../Components/Form/NewFormInput';
import CustomButton from '../../../Components/Common/CustomButton';
import {
	createFormData,
	formatFoodOptionListData,
	formatPickupLocationOption,
	formatSingleProductDetails,
	formatSubCategoryOption
} from '../../../Components/ManageMenu/utils';
import Api from '../../../Helper/Api';
import { useFormik } from 'formik';
import * as validation from '../../../Helper/YupValidation';
import * as yup from 'yup';
import NewCheckBox from '../../../Components/Form/NewCheckBox';
import CustomLabel from '../../../Components/Form/CustomLabel';
import NewFormSelect from '../../../Components/Form/NewFormSelect/FormSelect';
import NewImageUpload from '../../../Components/Form/NewImageUpload';
import { useDebounce } from '../../../Hooks/useDebounce';
import Scrollbars from 'react-custom-scrollbars';
import barActions from '../../../Redux/bar/actions';

const ManageMenu = () => {
	const navigate = useNavigate();
	const dispatch = useDispatch();
	const state = useSelector((state) => ({ ...state }));
	const currentProductId = useSelector((state) => state.bar);

	const allThemeData = state.themeChanger;
	const authData = state.auth;
	const [tabId, setTabId] = useState(0);
	const [openSideMenu, setOpenSideMenu] = useState(false);
	const [tabMenuLoading, setTabMenuLoading] = useState(false);
	const [getProductLoading, setGetProductLoading] = useState(false);
	const [tabMenuData, setTabMenuData] = useState([]);
	const [dietaryRequirements, setDietaryRequirements] = useState([]);
	const [subCategoryId, setSubCategoryId] = useState(null);
	const [isPopularExist, setIsPopularExist] = useState(false);
	const [rearrangeMenuModal, setRearrangeMenuModal] = useState(false);
	const [operatingHoursModal, setOperatingHoursModal] = useState(false);
	const [filterPopover, setFilterPopover] = useState(false);
	const [deletedRequiredOptions, setDeletedRequiredOptions] = useState([]);
	const [submitFormLoading, setSubmitFormLoading] = useState(false);
	const [subCategoryData, setSubCategoryData] = useState([]);
	const [isErrors, setIsErrors] = useState(false);
	const [searchTerm, setSearchTerm] = useState('');
	const debounceSearchTerm = useDebounce(searchTerm, 500);

	const [subCategoryOptionsLoading, setSubCategoryOptionsLoading] =
		useState(false);

	const [deletedRequiredOptionsItems, setDeletedRequiredOptionsItems] =
		useState([]);
	const [deletedAdditionalExtras, setDeletedAdditionalExtras] = useState([]);

	const [
		initialSelectedDietaryRequirements,
		setInitialSelectedDietaryRequirements
	] = useState([]);
	const [initialFormData, setInitialFormData] = useState(null);

	const [posStatus, setPosStatus] = useState(false);
	const [pickupLocationData, setPickupLocationData] = useState([]);

	const { pathname } = useLocation();
	const params = useParams();
	const isEdit = currentProductId.currentProduct !== null;

	const [serviceType, setServiceType] = useState('BOTH');

	const handleSearchInputChange = async (value) => {
		setSearchTerm(value);
	};

	const getPickupLocation = async () => {
		try {
			setSubmitFormLoading(true);
			const res = await Api('POST', VenueApiRoutes.getPickupLocation, {
				bar_id: authData?.selectedVenue?.id
			});
			if (res?.data?.status) {
				let formatedData = formatPickupLocationOption(res?.data?.data);
				setPickupLocationData(
					formatedData?.sort((item1, item2) =>
						item1?.label?.localeCompare(item2?.label)
					)
				);
			} else {
				toast.error(res?.data?.message);
			}
			setSubmitFormLoading(false);
		} catch (err) {
			setSubmitFormLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	const submitFormHandler = async (values) => {
		let formData = createFormData({
			values: values,
			barId: authData?.selectedVenue?.id,
			productId: currentProductId.currentProduct,
			initialSelectedDietaryRequirements:
				initialSelectedDietaryRequirements,
			deletedRequiredOptions: deletedRequiredOptions,
			deletedRequiredOptionsItems: deletedRequiredOptionsItems,
			deletedAdditionalExtras: deletedAdditionalExtras,
			isEdit: isEdit,
			initialFormData: initialFormData
		});
		try {
			setSubmitFormLoading(true);
			const res = await Api(
				'POST',
				!isEdit
					? VenueApiRoutes.addProduct
					: VenueApiRoutes.editProduct,
				formData
			);
			if (res?.data?.status) {
				toast.success(res?.data?.message);
				setOpenSideMenu(false);
			} else {
				toast.error(res?.data?.message);
			}
			setSubmitFormLoading(false);
		} catch (err) {
			setSubmitFormLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	useEffect(() => {
		if (currentProductId?.currentProduct !== null) {
			dispatch(
				barActions.setCurrentProduct({
					currentProduct: null
				})
			);
		}
		dispatch(
			barActions.setCurrentProduct({
				currentProduct: null
			})
		);
	}, []);

	useEffect(() => {
		if (currentProductId !== null) {
			getProductDetails();
		}
	}, [currentProductId]);

	const getProductDetails = async () => {
		if (currentProductId?.currentProduct !== null) {
			setOpenSideMenu(true);

			try {
				setGetProductLoading(true);
				const res = await Api('POST', VenueApiRoutes.getSingleProduct, {
					id: currentProductId?.currentProduct,
					bar_id: authData?.selectedVenue?.id
				});
				if (res?.data?.status) {
					let formatedData = formatSingleProductDetails(
						res?.data?.data
					);
					setValues(formatedData);
					setInitialSelectedDietaryRequirements(
						formatedData?.dietaryRequirements
					);
					setInitialFormData(formatedData);
				} else {
					toast.error(res?.data?.message);
				}
				setGetProductLoading(false);
			} catch (err) {
				setGetProductLoading(false);
				if (err?.message) {
					toast.error(err?.message);
				}
			}
		}
	};

	const validationSchemaProduct = yup.object().shape({
		image: validation.YUP_VALIDATION.IMAGE,
		category: validation.YUP_VALIDATION.CATEGORY,
		itemName: validation.YUP_VALIDATION.ITEM_NAME,
		basePrice: validation.YUP_VALIDATION.BASE_PRICE,
		pickupLocation: validation.YUP_VALIDATION.PICKUP_LOCATION,
		description: validation.YUP_VALIDATION.DESCRIPTION,
		serviceType: validation.YUP_VALIDATION.ADD_PRODUCT_SERVICE_TYPE,
		stockQuantity: validation.YUP_VALIDATION.STOCK_QUANTITY,
		requiredOptions: validation.YUP_VALIDATION.REQUIRED_OPTIONS,
		additionalExtras: validation.YUP_VALIDATION.ADDITIONAL_EXTRAS,
		calorie: validation.YUP_VALIDATION.CALORIE
	});

	const handleFilterPopover = () => {
		setFilterPopover((prev) => !prev);
	};

	const paramsData = {
		bar_id: authData?.selectedVenue?.id,
		serviceType: serviceType,
		showPopular: 0
	};

	const paramWithSearch = {
		bar_id: authData?.selectedVenue?.id,
		serviceType: serviceType,
		search: debounceSearchTerm,
		showPopular: 0
	};

	// Check if debounceSearchTerm is not empty
	const finalParams =
		debounceSearchTerm !== '' ? paramWithSearch : paramsData;

	const cachedProductListData = useCachedData(
		'POST',
		VenueApiRoutes.getProductList,
		finalParams
	);
	const { data, isLoading, isError, error, refetch, isFetching } =
		cachedProductListData;

	const handleRearrangeMenuModal = () => {
		setRearrangeMenuModal(!rearrangeMenuModal);
	};

	const handleOperatingHoursModal = () => {
		setOperatingHoursModal(!operatingHoursModal);
	};
	const handleStockLimitChange = () => {
		setFieldValue('stockLimit', !values?.stockLimit);
	};
	// define an onClick function to bind the value on tab click
	const onTabClick = (e, index) => {
		setTabId(index);
	};

	const getDietaryRequirements = async () => {
		try {
			const res = await Api('POST', VenueApiRoutes.getFoodOptionList);
			if (res?.data?.status) {
				let formatedData = formatFoodOptionListData(res?.data?.data);
				setDietaryRequirements(formatedData);
			} else {
				toast.error(res?.data?.message);
			}
		} catch (err) {
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	const handleStockLevelChange = () => {
		setFieldValue('stockLevel', !values?.stockLevel);
	};

	const getSubCategoryOptions = async () => {
		try {
			setSubCategoryOptionsLoading(true);
			const res = await Api('POST', VenueApiRoutes.getSubCategory, {
				bar_id: authData?.selectedVenue?.id
			});
			if (res?.data?.status) {
				let formatedData = formatSubCategoryOption(res?.data?.data);
				setSubCategoryData(
					formatedData?.sort((item1, item2) =>
						item1?.label?.localeCompare(item2?.label)
					)
				);
			} else {
				toast.error(res?.data?.message);
			}
			setSubCategoryOptionsLoading(false);
		} catch (err) {
			setSubCategoryOptionsLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	useEffect(() => {
		if (data?.data) {
			if (data?.data[0]?.categoryName === 'Popular') {
				setIsPopularExist(true);
			} else {
				setIsPopularExist(false);
			}
			setTabMenuData(data?.data);
		}
		if (data?.status == 0) {
			toast.error(data?.message);
		}
	}, [data, data?.data]);

	useEffect(() => {
		if (isLoading) {
			setTabMenuLoading(isLoading);
		} else {
			setTabMenuLoading(false);
		}
		if (isError) {
			toast.error(error);
		}
		if (!isLoading) {
			if (isFetching) {
				dispatch(uiActions.page_loading(true));
			} else {
				dispatch(uiActions.page_loading(false));
			}
		}
	}, [isLoading, isError, error, isFetching]);

	useEffect(() => {
		if (authData?.selectedVenue?.posStatus === '1') {
			setPosStatus(true);
		} else {
			setPosStatus(false);
		}
	}, [authData?.selectedVenue?.posStatus]);

	useEffect(() => {
		getDietaryRequirements();
		getPickupLocation();
		getSubCategoryOptions();
	}, [authData?.selectedVenue?.id]);

	useEffect(() => {
		if (refetch) refetch();
	}, [authData?.selectedVenue?.id, serviceType, debounceSearchTerm]);

	const [open, setOpen] = useState('1');

	const toggle = (id) => {
		if (open === id) {
			setOpen();
		} else {
			setOpen(id);
		}
	};

	const initialValue = {
		image: null,
		mainCategoryId: '',
		category: '',
		itemName: '',
		basePrice: '',
		pickupLocation: '',
		description: '',
		serviceType: [], //["PICKUP","TABLE"]
		dietaryRequirements: [],
		stockLevel: false,
		stockLimit: false,
		stockQuantity: 0,
		stockRemaining: 0,
		calorie: 0,
		fat: null,
		carbohydrates: null,
		protein: null,
		requiredOptions: [], //{ serviceType: null || "PICKUP" || "TABLE" || "BOTH", data: [] }
		additionalExtras: []
	};

	const {
		values,
		setFieldValue,
		touched,
		errors,
		handleSubmit,
		handleChange,
		setValues,
		isValid,
		isValidating,
		validateForm,
		isSubmitting
	} = useFormik({
		initialValues: initialValue,
		validationSchema: validationSchemaProduct,
		onSubmit: submitFormHandler
	});

	const handleSave = async () => {
		let res = await validateForm(values);
		if (Object.keys(res).length === 0) {
			setIsErrors(false);
		} else {
			setIsErrors(true);
		}
		handleSubmit();
	};

	return (
		<>
			<Offcanvas
				isOpen={openSideMenu}
				direction="end"
				className="manageMenuOffcanvas"
				onClosed={() => {
					setValues(initialValue);
					dispatch(
						barActions.setCurrentProduct({
							currentProduct: null
						})
					);
				}}
				toggle={() => setOpenSideMenu(!openSideMenu)}
			>
				<OffcanvasBody>
					<div>
						<h1 className="headingStyle pt-0">
							Let’s create a new menu item
						</h1>
					</div>
					<form className="overflow-hidden">
						<Row className="mt-3">
							<Col xs={12} sm={12} md={3} xl={3}>
								<CustomLabel
									id={'image'}
									showRequired
									label={'MENU ITEM IMAGE'}
								/>
								<NewImageUpload
									className={''}
									name={'image'}
									defaultImage={
										authData?.selectedVenue?.avatar
									}
									icon={
										<CameraIcon height={14} weight={14} />
									}
									value={values.image}
									setFieldValue={setFieldValue}
									error={touched.image && !!errors.image}
									errorMsg={errors.image}
								/>
							</Col>
							<Col xs={12} sm={12} md={9} xl={9}>
								<Col
									sm={12}
									className="mt-3 mt-xl-0 mt-lg-0 mt-md-0"
								>
									<CustomLabel
										style={{ color: '#313132EB' }}
										id={'category'}
										showRequired
										name="category"
										label="Category"
									/>
									<NewFormSelect
										name="category"
										className={'custom-textarea'}
										placeholder="Select Category"
										options={subCategoryData}
										value={subCategoryData?.find(
											(item) =>
												item?.value == values.category
										)}
										onChange={(item) => {
											setFieldValue(
												'category',
												item?.value
											);
											setFieldValue(
												'mainCategoryId',
												item?.categoryId
											);
											setFieldValue(
												'pickupLocation',
												item?.pickupLocationId
											);
										}}
										error={
											touched.category &&
											!!errors.category
										}
										errorMsg={errors.category}
										isLoading={subCategoryOptionsLoading}
										isSearchable={true}
										disabled={posStatus}
									/>
								</Col>
								<Col md={12} className="pa-b-35">
									<NewFormInput
										type="text"
										name="itemName"
										label="MENU ITEM NAME"
										placeholder="Enter menu item name"
										showRequired
										tootlTipMessage="Enter menu item name"
										value={values.itemName}
										onChange={handleChange}
										error={
											touched.itemName &&
											!!errors.itemName
										}
										errorMsg={errors.itemName}
										disabled={posStatus}
									/>
								</Col>
							</Col>

							<Col md={12} className="pa-b-20">
								<NewFormInput
									type="number"
									name="basePrice"
									label="MENU ITEM BASE PRICE"
									placeholder="Enter menu item base price"
									showRequired
									tootlTipMessage="Enter menu item base price"
									value={values.basePrice}
									onChange={handleChange}
									error={
										touched.basePrice && !!errors.basePrice
									}
									errorMsg={errors.basePrice}
									disabled={posStatus}
								/>
							</Col>
							<Col md={12} className="pa-b-20">
								<CustomLabel
									style={{ color: '#313132EB' }}
									id={'description'}
									label={'MENU ITEM DESCRIPTION'}
									showRequired
								/>
								<FormInput
									standardVersion
									className={'custom-textarea'}
									type="textarea"
									name="description"
									rows={5}
									placeholder="Add a delicious menu item description to entice customers to add this item to their cart. It is important to list all ingredients to cater to customers with allergies"
									value={values.description}
									onChange={handleChange}
									error={
										touched.description &&
										!!errors.description
									}
									errorMsg={errors.description}
									disabled={posStatus}
								/>
							</Col>

							<div>
								<FormGroup className="mb-0">
									<CustomLabel
										style={{ color: '#313132EB' }}
										label={'Service Type'}
									/>
									<Row>
										<Col
											md={6}
											xl={3}
											className="mb-4 mb-xl-0"
										>
											<NewCheckBox
												label={'Takeaway'}
												checkBoxProps={{
													name: 'serviceType',
													value: 'PICKUP',
													onChange: handleChange,
													checked:
														values?.serviceType?.includes(
															'PICKUP'
														)
												}}
											/>
										</Col>
										<Col
											md={6}
											xl={3}
											className="mb-4 mb-xl-0"
										>
											<NewCheckBox
												label={'Table Service'}
												checkBoxProps={{
													name: 'serviceType',
													value: 'TABLE',
													onChange: handleChange,
													checked:
														values?.serviceType?.includes(
															'TABLE'
														)
												}}
											/>
										</Col>
										<Col
											md={6}
											xl={3}
											className="mb-4 mb-xl-0"
										>
											<NewCheckBox
												checkBoxProps={{
													disabled: true
												}}
												label={'Room Service'}
												name="serviceType"
												value={'PICKUP'}
												checked={values?.serviceType?.includes(
													'PICKUP'
												)}
												onChange={handleChange}
											/>
										</Col>
										<Col
											md={6}
											xl={3}
											className="mb-4 mb-xl-0"
										>
											<NewCheckBox
												checkBoxProps={{
													disabled: true
												}}
												label={'Scheduled'}
												name="serviceType"
												value={'TABLE'}
												checked={values?.serviceType?.includes(
													'TABLE'
												)}
												onChange={handleChange}
											/>
										</Col>
										<Col sm={12} md={12}>
											{touched.serviceType &&
												!!errors.serviceType && (
													<p className="error">
														{errors?.serviceType}
													</p>
												)}
										</Col>
									</Row>
								</FormGroup>
							</div>
							<Col md={12} className="pa-b-20">
								<CustomLabel
									style={{ color: '#313132EB' }}
									id={'description'}
									label={'PICK UP LOCATION'}
									showRequired
								/>
								<NewFormSelect
									name="pickupLocation"
									className={'custom-textarea'}
									placeholder="Select Pickup Location"
									options={pickupLocationData}
									value={pickupLocationData?.find(
										(item) =>
											item?.value ===
											values.pickupLocation
									)}
									onChange={(item) => {
										setFieldValue(
											'pickupLocation',
											item?.value
										);
									}}
									error={
										touched.pickupLocation &&
										!!errors.pickupLocation
									}
									errorMsg={errors.pickupLocation}
									isSearchable={true}
								/>
							</Col>
							<div className="d-flex gap-4 flex-column pb-100">
								<Accordion open={open} toggle={toggle}>
									<AccordionItem
										style={{
											border: '2px solid #31313259'
										}}
									>
										<AccordionHeader targetId="1">
											<CustomLabel
												style={{
													color: '#313132EB',
													padding: 0
												}}
												id={'dietaryRequirements'}
												showRequired
												label={'Dietary Requirements'}
											/>
										</AccordionHeader>
										<AccordionBody accordionId="1">
											<Row>
												{dietaryRequirements?.length >
												0 ? (
													<>
														{dietaryRequirements?.map(
															(item) => {
																return (
																	<Col
																		xs={6}
																		className="mb-4"
																	>
																		<NewCheckBox
																			label={
																				item?.label
																			}
																			checkBoxProps={{
																				name: 'dietaryRequirements',
																				value: item?.value,
																				onChange:
																					handleChange,
																				checked:
																					values?.dietaryRequirements?.includes(
																						item?.value?.toString()
																					)
																			}}
																			checked={values?.dietaryRequirements?.includes(
																				item?.value?.toString()
																			)}
																			onChange={
																				handleChange
																			}
																		/>
																	</Col>
																);
															}
														)}
													</>
												) : (
													<EmptyData content="No Dietary Requirements Options" />
												)}
											</Row>
										</AccordionBody>
									</AccordionItem>
								</Accordion>

								<Accordion open={open} toggle={toggle}>
									<AccordionItem
										style={{
											border: '2px solid #31313259'
										}}
									>
										<AccordionHeader targetId="2">
											<CustomLabel
												style={{
													color: '#313132EB',
													padding: 0
												}}
												id={'nutritional'}
												showRequired
												label={
													'NUTRITIONAL INFORMATION'
												}
											/>
										</AccordionHeader>
										<AccordionBody accordionId="2">
											<Row>
												<Col md={12}>
													<NewFormInput
														type="number"
														name="calorie"
														rows={3}
														placeholder="Enter calorie amount"
														value={values.calorie}
														onChange={handleChange}
														error={
															touched.calorie &&
															!!errors.calorie
														}
														errorMsg={
															errors.calorie
														}
													/>
												</Col>
												<Col md={12} className="mt-3">
													<CustomLabel
														style={{
															color: '#313132EB'
														}}
														label={'OPTIONAL'}
													/>
												</Col>
												<Col
													md={4}
													className="mb-3 mb-xl-0"
												>
													<NewFormInput
														type="number"
														name="fat"
														rows={3}
														placeholder="Total Fat (g) "
														value={values.fat}
														onChange={handleChange}
													/>
												</Col>
												<Col
													md={4}
													className="mb-3 mb-xl-0"
												>
													<NewFormInput
														type="number"
														name="carbohydrates"
														rows={3}
														placeholder="Total Carbohydrates (g)"
														value={
															values.carbohydrates
														}
														onChange={handleChange}
													/>
												</Col>
												<Col md={4}>
													<NewFormInput
														type="number"
														name="protein"
														rows={3}
														placeholder="Protein (g)"
														value={values.protein}
														onChange={handleChange}
													/>
												</Col>
											</Row>
										</AccordionBody>
									</AccordionItem>
								</Accordion>

								<Accordion open={open} toggle={toggle}>
									<AccordionItem
										style={{
											border: '2px solid #31313259'
										}}
									>
										<AccordionHeader targetId="3">
											<CustomLabel
												style={{
													color: '#313132EB',
													padding: 0
												}}
												id={'stockLevel'}
												showRequired
												label={'STOCK LEVEL'}
											/>
										</AccordionHeader>
										<AccordionBody accordionId="3">
											<Row>
												<Col md={4} className="mb-4">
													<NewCheckBox
														name="stocklevel"
														label={
															'Enable Stock Level'
														}
														checkBoxProps={{
															name: 'stocklevel',
															onChange: () =>
																handleStockLevelChange(),
															checked:
																values.stockLevel
														}}
													/>
												</Col>
											</Row>
											{values?.stockLevel && (
												<Row>
													<Col
														md={6}
														className="mb-4"
													>
														<NewFormInput
															showRequired
															label={
																'TOTAL STOCK LEVEL QUANTITY '
															}
															type="number"
															name="stockQuantity"
															placeholder="Enter quantity here"
															value={
																values.stockQuantity
															}
															onChange={(e) => {
																setFieldValue(
																	'stockQuantity',
																	e?.target
																		?.value
																);
																setFieldValue(
																	'stockRemaining',
																	e?.target
																		?.value
																);
															}}
															onWheel={(e) =>
																e.target.blur()
															}
															error={
																touched.stockQuantity &&
																!!errors.stockQuantity
															}
															errorMsg={
																errors.stockQuantity
															}
														/>
													</Col>
													<Col
														md={6}
														className="mb-4"
													>
														<CustomLabel
															style={{
																color: '#313132EB'
															}}
															id={'stockLimit'}
															tootlTipMessage={`Auto refresh`}
															showRequired
															label={
																'AUTO DAILY REFRESH'
															}
														/>
														<NewCheckBox
															checkBoxProps={{
																name: 'stockLimit',
																onChange: () =>
																	handleStockLimitChange(),
																checked:
																	values?.stockLimit
															}}
														/>
													</Col>
													<Col md={12}>
														<NewFormInput
															showRequired
															label={
																'STOCK LEVEL REMAINING '
															}
															type="number"
															name="stockRemaining"
															placeholder="Stock Remaining"
															value={
																values.stockRemaining
															}
															onChange={
																handleChange
															}
															disabled
														/>
														{values.stockRemaining ==
															0 && (
															<p className="error">
																*currently
																marked as
																unavailable on
																customer app
															</p>
														)}
													</Col>
												</Row>
											)}
										</AccordionBody>
									</AccordionItem>
								</Accordion>

								<Accordion open={false} toggle={() => null}>
									<AccordionItem
										style={{
											border: '2px solid #31313259'
										}}
									>
										<AccordionHeader targetId="4">
											<CustomLabel
												style={{
													color: '#313132EB',
													padding: 0
												}}
												id={'modifierOption'}
												showRequired
												label={'MODIFIER OPTION SETS'}
											/>
										</AccordionHeader>
										<AccordionBody accordionId="4">
											<Row>
												{dietaryRequirements?.length >
												0 ? (
													<>
														{dietaryRequirements?.map(
															(item) => {
																return (
																	<Col
																		xs={6}
																		className="mb-4"
																	>
																		<NewCheckBox
																			label={
																				item?.label
																			}
																			name="dietaryRequirements"
																			value={
																				item?.value
																			}
																			checked={values?.dietaryRequirements?.includes(
																				item?.value?.toString()
																			)}
																			onChange={
																				handleChange
																			}
																		/>
																	</Col>
																);
															}
														)}
													</>
												) : (
													<EmptyData content="No Dietary Requirements Options" />
												)}
											</Row>
										</AccordionBody>
									</AccordionItem>
								</Accordion>
							</div>
							<Row className="pt-100">
								<Col md={6} className="pa-b-20">
									<OutlinedButton
										buttonTitle={
											'Cancel, back to main menu'
										}
										onClick={() => {
											setOpenSideMenu(false);

											dispatch(
												barActions.setCurrentProduct({
													currentProduct: null
												})
											);
										}}
									/>
								</Col>
								<Col md={6} className="pa-b-20">
									<CustomButton
										type="button"
										className="newThemeButton w-100"
										loading={submitFormLoading}
										onClick={handleSave}
									>
										Save menu item
									</CustomButton>
								</Col>
							</Row>
						</Row>
					</form>
				</OffcanvasBody>
			</Offcanvas>

			<PageStructure
				hideScroll
				pageText={
					<>
						{tabMenuData?.length > 0 && (
							<div className="row">
								<div className="col-xl-9 col-md-12 col-12 mb-3 mb-xl-0">
									<NewFormInput
										searchBar
										onChange={(event) => {
											handleSearchInputChange(
												event?.target?.value
											);
										}}
										placeholder="Search your menu here"
									/>
								</div>
								<div className="col-xl-3 col-md-12 col-12">
									<div className="d-flex gap-3">
										<OutlinedButton
											buttonTitle={'Import menu'}
											onClick={() =>
												toast.info(
													'This feature will be coming soon.'
												)
											}
										/>
										<CustomButton
											type="button"
											className="newThemeButton w-100"
											// loading={submitFormLoading}
											onClick={() =>
												setOpenSideMenu(true)
											}
										>
											+ menu item
										</CustomButton>
									</div>
								</div>
							</div>
						)}

						<TabsWrapper {...allThemeData}>
							<>
								<div className="flex-1 customScrollableNavbar position-relative zIndex-1 pa-t-24">
									{tabMenuLoading ? (
										<div className="pa-b-8">
											<TabsSkeleton />
										</div>
									) : (
										<>
											{tabMenuData?.length > 0 ? (
												<Tabs
													activeTab={tabId}
													onTabClick={onTabClick}
													leftBtnIcon={
														<i className="fa fa-angle-left"></i>
													}
													rightBtnIcon={
														<i className="fa fa-angle-right"></i>
													}
													navBtnsIconColor={'#4F4F4F'}
												>
													{tabMenuData?.length > 0 &&
														tabMenuData?.map(
															(item, i) => (
																<Tab
																	key={i}
																	className={`customScrollableNavItem fs-12 regular-text ${
																		tabId ===
																		i
																			? 'active'
																			: ''
																	}`}
																>
																	{
																		item?.categoryName
																	}
																</Tab>
															)
														)}
												</Tabs>
											) : (
												''
											)}
											{tabMenuData?.length > 0 ? (
												<div className="borderBottom position-absolute bottom-0 w-100 zIndex-1" />
											) : (
												''
											)}
											{tabMenuData?.length > 0 && (
												<div className="pt-2 outline-none rearrangeIcon">
													<Rearrange
														width={'18px'}
														height={'18px'}
														onClick={() =>
															setRearrangeMenuModal(
																true
															)
														}
														fill="#F94D73"
														className="cursor-pointer"
													/>
													<Filter
														width={'16px'}
														height={'16px'}
														fill="#F94D73"
														className="cursor-pointer"
														id="manage-menu-filter-popover"
													/>
													<div>
														<FilterPopover
															target="manage-menu-filter-popover"
															isOpen={
																filterPopover
															}
															handleToggle={
																handleFilterPopover
															}
															serviceType={
																serviceType
															}
															setServiceType={
																setServiceType
															}
														/>
													</div>
												</div>
											)}
										</>
									)}
								</div>
							</>
						</TabsWrapper>
					</>
				}
			>
				<div className="h-100">
					<Scrollbars autoHide>
						{tabMenuLoading ? (
							<ProductItemSkeleton />
						) : tabMenuData?.length === 0 ? (
							<div className="w-100 h-100 d-flex justify-content-center align-items-center">
								<EmptyData content="No Data Found" />
							</div>
						) : (
							<TabContent
								activeTab={'' + tabId}
								className="overflow-hidden pb-10"
							>
								{tabMenuData?.length > 0 &&
									tabMenuData?.map((nav, i) => (
										<TabPane key={i} tabId={'' + i}>
											<Menuitems
												isEditFromVenueSetup
												forVenuSetup={() => null}
												isPopularExist={isPopularExist}
												menuItemId={i}
												categoryData={tabMenuData[i]}
												refetch={refetch}
											/>
										</TabPane>
									))}
							</TabContent>
						)}
						<div className="d-flex flex-column gap-4 position-sticky bottom-0 bg-white">
							<div className="pt-4">
								<div className="d-flex flex-xl-row flex-column gap-3 gap-xl-4 justify-content-around px-xl-5 px-3">
									<div className="w-xl-48 w-100">
										<OutlinedButton
											buttonTitle={
												'Skip! MyTab Venue Support is building my menu'
											}
										/>
									</div>
									<div className="w-xl-48 w-100">
										<Button
											className="newThemeButtonFullWidth"
											onClick={() => {}}
										>
											Set Opening Hours
										</Button>
									</div>
								</div>
							</div>
							<Agreement />
						</div>
					</Scrollbars>
				</div>
			</PageStructure>
			{tabMenuData && tabMenuData?.length !== 0 && (
				<RearrangeMenuModal2
					isOpen={rearrangeMenuModal}
					handleModal={handleRearrangeMenuModal}
					tabMenuData={tabMenuData}
					activeTabMenu={tabMenuData[tabId]?.categoryName}
					setTabId={setTabId}
					setMenuItems={setTabMenuData}
					isPopularExist={isPopularExist}
					refetch={refetch}
				/>
			)}
			{operatingHoursModal && (
				<OperatingHoursModal
					isOpen={operatingHoursModal}
					handleModal={handleOperatingHoursModal}
					subCategoryId={subCategoryId}
				/>
			)}
		</>
	);
};

export default ManageMenu;
