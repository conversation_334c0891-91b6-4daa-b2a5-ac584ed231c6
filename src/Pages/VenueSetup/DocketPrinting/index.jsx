import React from 'react';
import <PERSON>Wrapper from './index.style';
import Scrollbars from 'react-custom-scrollbars';
import docketPrintingImage from '../../../Assets/images/docket-printing.png';
import RegisterFormWrapper from '../../../Components/Authentication/registerForm.style';
import AcceptIcon from '../../../Assets/images/green-check.png';
import Agreement from '../../../Components/Authentication/Agreement';
import CustomButton from '../../../Components/Common/CustomButton';
import OutlinedButton from '../../../Components/Common/CustomButton/OutlinedButton';

const DocketPrinting = () => {
	const checkedList = [
		{
			key: 0,
			text: 'Streamlined & simple'
		},
		{
			key: 1,
			text: 'Automated docket printing '
		},
		{
			key: 2,
			text: 'No lock in contract'
		},
		{
			key: 3,
			text: 'Customisable settings to suit your venue'
		}
	];
	return (
		<PageWrapper sectionImage={docketPrintingImage}>
			<div className="formWrapper pl-26">
				<Scrollbars autoHide>
					<RegisterFormWrapper>
						<div className="formCard">
							<h1 className="headingStyle">
								Docket Printing Feature
							</h1>
							<div className="smallInfoWrraper mt-3">
								<p className="smallInfo">
									MyTab's docket printing feature allows a
									seamless, wireless connection to your
									venue's existing Epson POS docket printers *
									<br />
									<br />
									This feature enables all customer orders to
									automatically print to the specific printers
									designated for individual menu sub-headings,
									so you can easily separate your drink and
									food dockets for your chefs and staff.
									Pricing for MyTab's Premium+ Docket Printing
									Fee is 1% per transaction (automatically
									deducted by Stripe).
									<br />
									<br /> Pricing for MyTab's Premium+ Docket
									Printing Fee is 1% per transaction
									(automatically deducted by Stripe).
									<br />
									<br />
								</p>
							</div>
							<div className="d-flex flex-column gap-3">
								{checkedList.map((list) => (
									<div
										key={list}
										className="d-flex  align-items-center gap-1"
									>
										<img
											src={AcceptIcon}
											alt="not-accept"
											className="checkIcon"
										/>
										<div className="smallInfoWrraper">
											<p className="smallInfo m-0">
												{list.text}
											</p>
										</div>
									</div>
								))}
							</div>
						</div>
					</RegisterFormWrapper>
				</Scrollbars>
				<RegisterFormWrapper>
					<div className="gap-2 d-flex flex-column mt-4 mb-3">
						<CustomButton
							type="submit"
							className="newThemeButtonFullWidth"
						>
							Activate Docket Printing Feature
						</CustomButton>
						<OutlinedButton
							buttonTitle={
								'Continue with MyTab Venue Standard Version'
							}
						/>
					</div>
					<Agreement />
				</RegisterFormWrapper>
			</div>
			<div className="imageWrapper">
				<div className="backWrraper">
					<p className="text">
						Send MyTab orders wirelessly
						<br />
						to Epson docket printers
					</p>
					<img
						src={docketPrintingImage}
						alt="side-img"
						className="image"
					/>
				</div>
			</div>
		</PageWrapper>
	);
};

export default DocketPrinting;
