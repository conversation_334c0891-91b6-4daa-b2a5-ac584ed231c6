import { Col, Row } from 'reactstrap';
import CustomButton from '../../../Components/Common/CustomButton';
import NewCustomModal from '../../../Components/Common/Modal/NewCustomModal';
import NewFormInput from '../../../Components/Form/NewFormInput';
import NewVerifyModalWrapper from './pickupLocation';
import Scrollbars from 'react-custom-scrollbars';
import NewFormCheckbox from '../../../Components/Form/NewFormCheckbox/NewFormCheckbox';
import FormSelect from '../../../Components/Form/FormSelect';
import { useState } from 'react';
import { useFormik } from 'formik';
import * as yup from 'yup';
import * as validation from '../../../Helper/YupValidation';
import Api from '../../../Helper/Api';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { createFormData } from '../../../Components/ManageMenu/utils';
import { VenueApiRoutes, VenuePanelRoutes } from '../../../Utils/routes';
import { toast } from 'react-toastify';

const PickupLocationModal = ({
	isOpen,
	handleModal,
	type,
	modalData,
	modalFor,
	closeHandler
}) => {
	// const logindispatch(authActions.venue_login(res.data.data));
	const state = useSelector((state) => ({ ...state }));
	const allThemeData = state?.themeChanger;
	const authData = state?.auth;
	const navigate = useNavigate();
	const { pathname } = useLocation();
	const params = useParams();
	const [subCategoryOptionsLoading, setSubCategoryOptionsLoading] =
		useState(false);
	const [pickupLocationLoading, setPickupLocationLoading] = useState(false);
	const [submitFormLoading, setSubmitFormLoading] = useState(false);
	const [getProductLoading, setGetProductLoading] = useState(false);
	const [subCategoryData, setSubCategoryData] = useState([]);
	const [dietaryRequirements, setDietaryRequirements] = useState([]);
	const [
		initialSelectedDietaryRequirements,
		setInitialSelectedDietaryRequirements
	] = useState([]);
	const [initialFormData, setInitialFormData] = useState(null);
	const [deletedRequiredOptions, setDeletedRequiredOptions] = useState([]);
	const [deletedRequiredOptionsItems, setDeletedRequiredOptionsItems] =
		useState([]);
	const [deletedAdditionalExtras, setDeletedAdditionalExtras] = useState([]);
	const [requiredOptionPopover, setRequiredOptionPopover] = useState(false);
	const [additionalExtrasPopover, setAdditionalExtrasPopover] =
		useState(false);
	const [isError, setIsError] = useState(false);
	const [posStatus, setPosStatus] = useState(false);
	const isEdit = pathname.includes('/edit');
	const handelOnsubmit = async () => {};
	const validationSchemaProduct = yup.object().shape({
		image: validation.YUP_VALIDATION.IMAGE,
		category: validation.YUP_VALIDATION.CATEGORY,
		itemName: validation.YUP_VALIDATION.ITEM_NAME,
		basePrice: validation.YUP_VALIDATION.BASE_PRICE,
		pickupLocation: validation.YUP_VALIDATION.PICKUP_LOCATION,
		description: validation.YUP_VALIDATION.DESCRIPTION,
		serviceType: validation.YUP_VALIDATION.ADD_PRODUCT_SERVICE_TYPE,
		stockQuantity: validation.YUP_VALIDATION.STOCK_QUANTITY,
		requiredOptions: validation.YUP_VALIDATION.REQUIRED_OPTIONS,
		additionalExtras: validation.YUP_VALIDATION.ADDITIONAL_EXTRAS
	});

	const submitFormHandler = async (values) => {
		let formData = createFormData({
			values: values,
			barId: authData?.selectedVenue?.id,
			productId: params?.id,
			initialSelectedDietaryRequirements:
				initialSelectedDietaryRequirements,
			deletedRequiredOptions: deletedRequiredOptions,
			deletedRequiredOptionsItems: deletedRequiredOptionsItems,
			deletedAdditionalExtras: deletedAdditionalExtras,
			isEdit: isEdit,
			initialFormData: initialFormData
		});
		try {
			setSubmitFormLoading(true);
			const res = await Api(
				'POST',
				!isEdit
					? VenueApiRoutes.addProduct
					: VenueApiRoutes.editProduct,
				formData
			);
			if (res?.data?.status) {
				toast.success(res?.data?.message);
				navigate(VenuePanelRoutes.manageMenu);
			} else {
				toast.error(res?.data?.message);
			}
			setSubmitFormLoading(false);
		} catch (err) {
			setSubmitFormLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	const {
		values,
		setFieldValue,
		touched,
		errors,
		handleSubmit,
		handleChange,
		setValues,
		isValid,
		isValidating,
		validateForm,
		isSubmitting
	} = useFormik({
		initialValues: {
			image: null,
			mainCategoryId: '',
			category: '',
			itemName: '',
			basePrice: '',
			pickupLocation: '',
			description: '',
			serviceType: [], //["PICKUP","TABLE"]
			dietaryRequirements: [],
			stockLevel: false,
			stockLimit: false,
			stockQuantity: 0,
			stockRemaining: 0,
			requiredOptions: [], //{ serviceType: null || "PICKUP" || "TABLE" || "BOTH", data: [] }
			additionalExtras: []
		},
		validationSchema: validationSchemaProduct,
		onSubmit: submitFormHandler
	});

	const [pickupLocationData, setPickupLocationData] = useState([]);
	return (
		<NewCustomModal
			onClosed={closeHandler}
			isOpen={isOpen}
			size="md"
			handleModal={handleModal}
			modalClassName="verifyOtp"
			hideCloseButton
		>
			<NewVerifyModalWrapper>
				<p className="verifyOtpHeading">Assign pick up Location</p>
				<form className="mt-3">
					<Row>
						<Col md={12} className="pa-b-20">
							<FormSelect
								name="pickupLocation"
								label="Pickup Location"
								placeholder="Select Pickup Location"
								options={pickupLocationData}
								value={pickupLocationData?.find(
									(item) =>
										item?.value === values.pickupLocation
								)}
								onChange={(item) => {
									setFieldValue(
										'pickupLocation',
										item?.value
									);
								}}
								error={
									touched.pickupLocation &&
									!!errors.pickupLocation
								}
								errorMsg={errors.pickupLocation}
								isLoading={pickupLocationLoading}
								isSearchable={true}
							/>
						</Col>
						<Col md={12} className="pa-b-20">
							<FormSelect
								name="category"
								label="Category"
								placeholder="Select Category"
								options={subCategoryData}
								value={subCategoryData?.find(
									(item) => item?.value == values.category
								)}
								onChange={(item) => {
									setFieldValue('category', item?.value);
									setFieldValue(
										'mainCategoryId',
										item?.categoryId
									);
								}}
								error={touched.category && !!errors.category}
								errorMsg={errors.category}
								isLoading={subCategoryOptionsLoading}
								isSearchable={true}
								disabled={posStatus}
							/>
						</Col>
					</Row>
					{/* <NewFormInput
						autoComplete="off"
						tootlTipMessage="Enter the email used to create your Management Portal"
						type="email"
						name="email"
						label="pick up location"
						placeholder="Enter menu item pick up location"
						// value={loginFormik?.values?.email}
						// onChange={loginFormik.handleChange}
						// onBlur={loginFormik.handleBlur}
						// error={
						// 	loginFormik?.touched?.email &&
						// 	!!loginFormik?.errors?.email
						// }
						// errorMsg={loginFormik?.errors?.email}
						showRequired
					/> */}
				</form>
				<Row className=" mr-1 ml-1">
					{/* <Col xs={12} className="listItemWrraper pt-2">
						<b className="pb-0">Location</b>
						<div className="lineBold mb-2" />
						{[1, 2, 3, 4, 5].map((item) => (
							<div className="d-flex justify-content-between align-items-start">
								<p className="locationText">Item 1</p>
								<NewFormCheckbox
									name="acceptTerms"
									// onChange={registerFormik?.handleChange}
									// checked={
									// 	registerFormik?.values?.acceptTerms
									// }
									// disabled={state ? true : false}
								/>
							</div>
						))}
					</Col> */}
				</Row>
				<CustomButton className={'newThemeButton w-100 mt-100'}>
					Save
				</CustomButton>
			</NewVerifyModalWrapper>
			{/* </ModalWrapper> */}
		</NewCustomModal>
	);
};

export default PickupLocationModal;
