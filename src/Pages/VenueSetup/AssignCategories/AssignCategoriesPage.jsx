import React, { useEffect, useState } from 'react';
import CustomButton from '../../../Components/Common/CustomButton';
import PageWrapper from './pageWrraper';
import Scrollbars from 'react-custom-scrollbars';
import pickupLocationImage from '../../../Assets/images/pickup-location.png';
import OutlinedButton from '../../../Components/Common/CustomButton/OutlinedButton';
import Agreement from '../../../Components/Authentication/Agreement';
import {
	Button,
	Col,
	Popover,
	PopoverBody,
	PopoverHeader,
	Row
} from 'reactstrap';
import { PlusIcon, ThreeDotIcon } from '../../../Components/Icons';
import PickUpModal from '../../../Components/Settings/PickupLocation/PickupLocationModal';
import PickupLocationModal from './PickupLocationModal';
import { VenueApiRoutes } from '../../../Utils/routes';
import { toast } from 'react-toastify';
import { useSelector } from 'react-redux';
import Api from '../../../Helper/Api';
import PickupLocationCard from '../../../Components/Settings/PickupLocation/PickupLocationCard';
import EmptyData from '../../../Components/Common/EmptyData';
import PickupLocationCardSkeleton from '../../../Components/Settings/PickupLocation/Skeleton/PickupLocationCardSkeleton';
import { formatSubCategoryOption } from '../../../Components/ManageMenu/utils';
import PickupLocationPopOver from '../../../Components/Settings/PickupLocation/PickupLocationPopOver';
import NewPickupLocationModal from '../../../Components/Settings/PickupLocation/NewPickupLocationModal';
import AssignCategoriesWrraper from './assignCategories.style';
import DeleteConfirmModal from '../../../Components/Settings/PickupLocation/DeleteConfirmModal';

const AssignCategoriesPage = () => {
	const authData = useSelector((state) => ({ ...state.auth }));
	const [pickupLocationLoading, setPickupLocationLoading] = useState(false);
	const [pickupLocationList, setPickupLocationList] = useState([]);
	const [deleteConfirmModal, setDeleteConfirmModal] = useState(false);
	const [deletePickuplocationId, setDeletePickuplocationId] = useState(null);

	const [openModal, setOpenModal] = useState(false);
	const [modalData, setModalData] = useState({
		type: '',
		data: ''
	});

	const deletePickupLocation = async () => {
		setPickupLocationLoading(true);
		try {
			const res = await Api(
				'DELETE',
				VenueApiRoutes.deletePickupLocation,
				{
					bar_id: authData?.selectedVenue?.id?.toString(),
					id: '' + deletePickuplocationId
				}
			);
			if (res?.data?.status) {
				toast.success(res?.data?.message);
				await getPickupLocationList();
				setDeleteConfirmModal(false);
			} else {
				toast.error(res?.data?.message);
			}
			setPickupLocationLoading(false);
		} catch (err) {
			setPickupLocationLoading(false);
			if (err?.response?.data?.message) {
				toast.error(err?.response?.data?.message);
			}
		}
	};

	const handleDeleteConfirmModal = () => {
		setDeleteConfirmModal((prev) => !prev);
	};
	const handleModal = () => {
		setOpenModal((prev) => !prev);
	};

	const getPickupLocationList = async () => {
		setPickupLocationLoading(true);
		try {
			const res = await Api(
				'POST',
				VenueApiRoutes.getPickupLocationList,
				{
					bar_id: authData?.selectedVenue?.id?.toString()
				}
			);

			if (res?.data?.status) {
				setPickupLocationList(res?.data?.data);
			} else {
				toast.error(res?.data?.message);
			}
			setPickupLocationLoading(false);
		} catch (err) {
			setPickupLocationLoading(false);
			if (err?.response?.data?.message) {
				toast.error(err?.response?.data?.message);
			}
		}
	};

	const [pickupLocationPopover, setPickupLocationPopover] = useState(false);
	const pickupLocationPopoverToggle = () => {
		setPickupLocationPopover((prev) => !prev);
	};

	useEffect(() => {
		getPickupLocationList();
	}, [authData?.selectedVenue?.id]);

	return (
		<PageWrapper>
			<div className="formWrapper pl-26">
				<Scrollbars autoHide>
					<AssignCategoriesWrraper>
						<div className="formCard">
							<h1 className="headingStyle">
								Let’s create your pick up locations
							</h1>
							<p className={`helperLink`}>
								Add your venue’s pick up locations to let
								customers know where to collect menu items on
								their order.
								<br />
								<b>Examples:</b> Collect at counter, collect at
								kiosk, collect inside cafe etc.
								<br />
								<br /> Once a pick up location has been created,
								select which categories you would like it to be
								assigned to.
							</p>
							<Button
								type="button"
								className="themeBorderButton d-flex align-items-center"
								onClick={() => {
									setOpenModal(true);
									setModalData({
										type: 'add',
										data: null
									});
								}}
							>
								<PlusIcon
									height={18}
									width={18}
									className="mr-6"
								/>{' '}
								Add Pick Up Location
							</Button>

							<Row
								className="mt-5"
								style={{
									'--bs-gutter-x': '24px',
									'--bs-gutter-y': '24px'
								}}
							>
								<div className="d-flex flex-column">
									{pickupLocationLoading ? (
										<PickupLocationCardSkeleton />
									) : (
										<div className="picupLocationRow">
											<Row
												className="picupLocationRow  mr-0 "
												style={{
													overflow:
														'hidden !important',
													'--bs-gutter-x': '24px',
													'--bs-gutter-y': '24px'
												}}
											>
												{pickupLocationList?.length >
												0 ? (
													pickupLocationList?.map(
														(item, index) => (
															<Col
																xl={4}
																md={5}
																lg={5}
																key={index}
															>
																<PickupLocationCard
																	standardVersion
																	key={
																		item?.id
																	}
																	cardData={
																		item
																	}
																	setOpenModal={
																		setOpenModal
																	}
																	setModalData={
																		setModalData
																	}
																	deletePickupLocation={
																		deletePickupLocation
																	}
																	setDeleteConfirmModal={
																		setDeleteConfirmModal
																	}
																	setDeletePickuplocationId={
																		setDeletePickuplocationId
																	}
																/>
															</Col>
														)
													)
												) : (
													<div className="pa-t-100">
														<EmptyData content="No pick up locations found" />
													</div>
												)}
											</Row>
										</div>
									)}
								</div>
							</Row>
						</div>
					</AssignCategoriesWrraper>
				</Scrollbars>
				<AssignCategoriesWrraper>
					<div className="ptb-18 mb-1 d-flex gap-2">
						<OutlinedButton
							buttonTitle={
								'Skip! My venue only offers Table Service'
							}
						/>
						<CustomButton className={'newThemeButton w-100'}>
							Done, next step!
						</CustomButton>
					</div>
					<Agreement />
				</AssignCategoriesWrraper>
			</div>

			<div className="imageWrapper">
				<div className="backWrraper">
					<p className="text">
						Pick up locations let
						<br />
						customers know where to
						<br />
						collect their order
					</p>
					<img
						src={pickupLocationImage}
						alt="side-img"
						className="image"
					/>
				</div>
			</div>
			{/* <PickUpModal
				isOpen={openModal}
				handleModal={handleModal}
				modalData={modalData}
				getPickupLocationList={getPickupLocationList}
			/> */}
			<DeleteConfirmModal
				isOpen={deleteConfirmModal}
				handleModal={handleDeleteConfirmModal}
				deleteLoading={pickupLocationLoading}
				handleDeletePickuplocation={deletePickupLocation}
			/>
			<NewPickupLocationModal
				isOpen={openModal}
				handleModal={handleModal}
				closeHandler={() => setOpenModal(false)}
				modalData={modalData}
				getPickupLocationList={getPickupLocationList}
			/>
		</PageWrapper>
	);
};

export default AssignCategoriesPage;
