import React from 'react';
import PageWrapper from './index.style';
import Scrollbars from 'react-custom-scrollbars';
import createAccountImage from '../../../Assets/images/wait-time.png';
import RegisterFormWrapper from '../../../Components/Authentication/registerForm.style';

const WaitTime = () => {
	return (
		<PageWrapper sectionImage={createAccountImage}>
			<div className="formWrapper pl-26">
				<Scrollbars autoHide>
					<RegisterFormWrapper>
						<div className="formCard">
							<h1 className="headingStyle">
								Let’s create your menu wait times
							</h1>
							<div className="smallInfoWrraper mt-3">
								<p className="smallInfo">
									In order to provide customers with an
									accurate estimate of when their Pick
									Up/Table Service order will be ready, you
									are required to enter an estimated wait time
									for each menu category. Upon creating your
									venue account, an automatic wait time of 10
									minutes per category will be set, but you
									have the ability to update these wait times
									at any time.
									<br />
									<br /> Wait times can be scheduled either
									for "All Day" or "Per Hour" during your
									venue's opening hours, taking into account
									that your business may experience
									fluctuations.
									<br />
									<br /> You also have the option to enable
									the wait time automation feature, which will
									automatically notify customers via their app
									when their order (either in part or in full)
									is ready to be collected/served. For
									example, if a customer orders a takeaway
									coffee with a wait time of 10 minutes at
									12:00pm and wait time automation is
									selected, they will receive a notification
									that their coffee is ready at 12:10pm. This
									feature can be enabled for specific service
									types offered by your venue.
									<br />
									<br /> If wait time automation is not
									enabled, customers will still see the
									estimated time of when their order will be
									ready based on the allocated wait time.
									However, you will need to manually mark the
									order as ready for pick up/serving via your
									iPad dashboard to notify the customer.
								</p>
							</div>
						</div>
					</RegisterFormWrapper>
				</Scrollbars>
			</div>
			<div className="imageWrapper">
				<div className="backWrraper">
					<p className="text">
						Wait Times help customers
						<br />
						know when their order will be
						<br />
						ready
					</p>
					<img
						src={createAccountImage}
						alt="side-img"
						className="image"
					/>
				</div>
			</div>
		</PageWrapper>
	);
};

export default WaitTime;
