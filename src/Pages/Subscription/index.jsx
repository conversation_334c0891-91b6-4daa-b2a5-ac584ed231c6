import { useSelector } from 'react-redux';
import 'react-tabs-scrollable/dist/rts.css';

import PageWrapper from './index.style';
import { Col, Row } from 'reactstrap';
import PageStructure from '../../Components/Common/PageStructure';
import SubscriptionCard from '../../Components/Subscription/SubscriptionCard';
import rightImg from '../../Assets/images/right.png';
import crossImg from '../../Assets/images/cross.png';

const currentSubscription = [
	{ title: '1 Connected Venue', listIcon: rightImg },
	{ title: 'Unlimited Staff Profiles', listIcon: rightImg },
	{ title: 'Basic Reports', listIcon: rightImg },
	{ title: 'Live Order Data', listIcon: rightImg },
	{ title: 'Remotely Manage/Update Venue App', listIcon: rightImg },
	{ title: 'Venue Sales/Performance Analytics', listIcon: crossImg },
	{ title: 'Customer Behavioural Analytics', listIcon: crossImg }
];

const essentialSubscription = [
	{ title: '3 Connected Venue', listIcon: rightImg },
	{ title: 'Unlimited Staff Profiles', listIcon: rightImg },
	{
		title: 'Venue Sales/Performance Analytics',
		color: '#FF5F5F',
		listIcon: rightImg
	},
	{ title: 'Live Order Data', listIcon: rightImg },
	{ title: 'Remotely Manage/Update Venue App', listIcon: rightImg }
];

const premiumSubscription = [
	{ title: 'Unlimited Connected Venues', listIcon: rightImg },
	{ title: 'Unlimited Staff Profiles', listIcon: rightImg },
	{
		title: 'Venue Sales/Performance Analytics',
		color: '#FF5F5F',
		listIcon: rightImg
	},
	{
		title: 'Customer Behavioural Analytics',
		color: '#FF5F5F',
		listIcon: rightImg
	},
	{ title: 'Live Order Data', listIcon: rightImg },
	{ title: 'Remotely Manage/Update Venue App', listIcon: rightImg }
];

export const Subscription = () => {
	const allThemeData = useSelector((state) => ({ ...state.themeChanger }));
	const handleUpgradeClick = () => {};
	return (
		<PageStructure
			title="Manage Subscription"
			pageText={
				<p className="pa-t-32 fs-16 regular-text">
					Upgrade your subscription to unlock exclusive data, features
					and much more.
				</p>
			}
		>
			<PageWrapper {...allThemeData}>
				<div className="pa-t-0 overflow-hidden">
					<Row>
						<Col xl="4">
							<SubscriptionCard
								isSubscribed={true}
								title={'Essential'}
								description="Everything you need to manage your MyTab account"
								amount={'0'}
								features={currentSubscription}
								handleUpgradeClick={handleUpgradeClick}
							/>
						</Col>
						<Col xl="4">
							<SubscriptionCard
								title={'Classic'}
								description="Level up your business with access to Professional venue Sales/Performance Analytics and more venue accounts"
								amount={'49'}
								features={essentialSubscription}
								handleUpgradeClick={handleUpgradeClick}
								activeButton={true}
								buttonLabel="Coming Soon"
							/>
						</Col>
						<Col xl="4">
							<SubscriptionCard
								title={'Premium'}
								description="Receive the best of MyTab with Premium reporting features including venue performance and customer behavioural analytics"
								amount={'79'}
								features={premiumSubscription}
								handleUpgradeClick={handleUpgradeClick}
								activeButton={true}
								buttonLabel="Coming Soon"
							/>
						</Col>
					</Row>
				</div>
			</PageWrapper>
		</PageStructure>
	);
};
