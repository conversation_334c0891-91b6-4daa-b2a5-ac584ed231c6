import styled from 'styled-components';

export const CustomerWrraper = styled.div`
	font-family: nunitosans-regular !important;
	padding: 1.7vw;
	min-height: 49.5vw;

	.activeText {
		color: #f95c69 !important;
		text-decoration: underline;
		font-family: nunitosans-regular;
		font-size: 16px;
		cursor: pointer;
	}

	.visit-text{
		font-family: nunitosans-regular;
	}

	.customer-faqs {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.titleWrap {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;

		.mainTitle {
			font-size: 32px;
			font-family: nunitosans-bold;
			color: #202224;
		}

		.mainParagraph {
			font-size: 16px;
			margin: 20px 0px;
			font-family: nunitosans-regular;
			color: #202224;
		}
	}

	.search-icon {
		background-color: #fbfcff !important;
	}

	.input-group {
		.inputGroup,
		formGroupIcon {
			background-color: #fbfcff !important;
		}
		.inputBox {
			background-color: #fbfcff !important;
			font-family: nunitosans-regular !important;
			color: #a4a5a7 !important;
			font-size: 16px !important;
		}
	}

	.searchBoxWrapper {
		width: 198px !important;
		@media only screen and (max-width: 1099px) {
			width: 100% !important;
		}
	}

	.valid-icon-size {
		width: 28px;
		height: 28px;
		cursor: pointer;
	}

	.customer-details {
		background: #fbfcff;
		height: auto;
		border: 0.6px solid rgb(148, 150, 152, 0.5);
		border-radius: 4px;
		margin-bottom: 1em;
		display: flex;
		flex-direction: column;
		padding: 10px;
	}

	.customer-details-two {
		background: #fbfcff;
		height: auto;
		border: 0.6px solid rgb(148, 150, 152, 0.5);
		border-radius: 0px 4px 4px 0px;
		border-left: none;
		display: flex;
		flex-direction: column;
		padding: 10px;
	}

	.boldText{
		font-family: nunitosans-bold !important;
	}

	.details-text {
		font-family: nunitosans-regular;
		color: #202224;
		font-size: 14px;
		display: flex;
		justify-content: space-between;

		div{
			font-family: nunitosans-regular;
		}
	}

	.sagment-table {
		background: #fbfcff;
		border: 0.6px solid rgb(148, 150, 152, 0.5);
		border-radius: 4px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
	}

	.sagment-search {
		height: 3.5vw;
		min-height: 2em;
		align-items: center;
		display: flex;
		gap: 15px;
		width: 100%;
		padding-inline: 20px;
	}

	.sagment-title {
		width: 100%;
		min-height: 2em;
		background-color: #f5f6fa;
		display: flex;
		padding-inline: 3rem;
		align-items: center;
		justify-content: space-between;
		height: 3vw;
	}

	.sagment-title-text {
		font-family: nunitosans-bold;
		color: #202224;
		font-size: 14px;
	}

	.sagment-data-text {
		font-family: nunitosans-semi-bold;
		color: #202224;
		font-size: 16px;
	}

	.sagment-data {
		display: flex;
		width: 100%;
		height: 3.5vw;
		min-height: 2em;
		align-items: center;
		justify-content: space-between;
		padding-inline: 3rem;
	}

	.table-data {
	}

	.table-count {
		background: #fbfcff;
		height: 3.5vw;
		min-height: 2em;
		border: 0.6px solid rgb(148, 150, 152, 0.5);
		border-radius: 4px;
		margin: 1em 0em;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 1em 1.5em;
	}

	.table-count-text-one {
		font-family: nunitosans-bold;
		color: #202224;
		font-size: 16px;
	}
	.table-count-text-two {
		font-family: nunitosans-regular;
		color: #a4a5a7;
		font-size: 16px;
	}

	@media (max-width: 600px) {
		padding: 5vw;

		.titleWrap {
			gap: 0.8rem;
			.mainTitle {
				font-size: 21px;
				font-family: nunitosans-bold;
			}

			.sagment-title-text {
				font-size: 10px;
			}

			.sagment-data-text {
				font-size: 10px;
			}

			.mainParagraph {
				font-size: 13px;
				color: #202224;
				font-family: nunitosans-regular;
			}
		}

		.table-count {
			height: 4.1vw;
			border-radius: 4px;
			margin: 0.8em 0em;
			display: flex;
			justify-content: space-between;
			padding: 0.8em 1.2em;
		}

		.table-count-text-one {
			font-family: nunitosans-bold;
			font-size: 12px;
		}

		.table-count-text-two {
			font-family: nunitosans-regular;
			font-size: 12px;
		}
		.activeText,
		.visit-text {
			font-size: 12px;
		}

		.valid-icon-size {
			width: 17px;
			height: 17px;
		}
	}

	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		min-height: 58.8vw;

		.titleWrap {
			gap: 0.8rem;
			.mainTitle {
				font-size: 21px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 13px;
				font-family: nunitosans-regular;
			}

			.sagment-title-text {
				font-size: 11px;
			}

			.sagment-data-text {
				font-size: 11px;
			}
				
			.table-count-text-two {
				font-size: 11px;
			}
		}

		.table-count {
			height: 4.1vw;
			border-radius: 4px;
			margin: 0.8em 0em;
			display: flex;
			justify-content: space-between;
			padding: 0.8em 1.2em;
		}

		.table-count-text-one {
			font-family: nunitosans-bold;
			font-size: 12px;
		}

		.table-count-text-two {
			font-family: nunitosans-regular;
			font-size: 12px;
		}

		.details-text {
			font-size: 11px;
		}

		.activeText,
		.visit-text {
			font-size: 12px;
		}

		.valid-icon-size {
			width: 19px;
			height: 19px;
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		min-height: 47.5vw;

		.titleWrap {
			gap: 0.8rem;
			.mainTitle {
				font-size: 21px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 13px;
				font-family: nunitosans-regular;
			}

			.details-text {
				font-size: 11px;
			}

			.sagment-table {
				background: #fbfcff;
				border: 0.6px solid rgb(148, 150, 152, 0.5);
				border-radius: 4px;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-between;
			}

			.sagment-search {
				height: 3.5vw;
				align-items: center;
				display: flex;
				gap: 15px;
				width: 100%;
				padding-inline: 20px;
			}

			.sagment-title {
				width: 100%;
				background-color: #f5f6fa;
				display: flex;
				padding-inline: 3rem;
				align-items: center;
				justify-content: space-between;
				height: 3vw;
			}

			.sagment-title-text {
				font-family: nunitosans-bold;
				color: #202224;
				font-size: 11px;
			}

			.sagment-data-text {
				font-family: nunitosans-semi-bold;
				color: #202224;
				font-size: 12px;
			}

			.sagment-data {
				display: flex;
				width: 100%;
				height: 3.5vw;
				align-items: center;
				justify-content: space-between;
				padding-inline: 3rem;
			}
		}

		.table-count {
			height: 4.1vw;
			border-radius: 4px;
			margin: 0.8em 0em;
			display: flex;
			justify-content: space-between;
			padding: 0.8em 1.2em;
		}

		.table-count-text-one {
			font-family: nunitosans-bold;
			font-size: 12px;
		}

		.table-count-text-two {
			font-family: nunitosans-regular;
			font-size: 12px;
		}

		.activeText,
		.visit-text {
			font-size: 12px;
		}

		.valid-icon-size {
			width: 20px;
			height: 20px;
		}
	}

	@media only screen and (max-width: 1299px) and (max-height: 900px) {
		min-height: 47.5vw;

		.titleWrap {
			gap: 0.8rem;
			.mainTitle {
				font-size: 21px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 13px;
				font-family: nunitosans-regular;
			}

			.details-text {
				font-size: 11px;
			}

			.sagment-table {
				background: #fbfcff;
				border: 0.6px solid rgb(148, 150, 152, 0.5);
				border-radius: 4px;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-between;
			}

			.sagment-search {
				height: 3.5vw;
				align-items: center;
				display: flex;
				gap: 15px;
				width: 100%;
				padding-inline: 20px;
			}

			.sagment-title {
				width: 100%;
				background-color: #f5f6fa;
				display: flex;
				padding-inline: 3rem;
				align-items: center;
				justify-content: space-between;
				height: 3vw;
			}

			// .sagment-title-text {
			// 	font-family: nunitosans-bold;
			// 	color: #202224;
			// 	font-size: 11px;
			// }

			// .sagment-data-text {
			// 	font-family: nunitosans-semi-bold;
			// 	color: #202224;
			// 	font-size: 12px;
			// }

			.sagment-data {
				display: flex;
				width: 100%;
				height: 3.5vw;
				align-items: center;
				justify-content: space-between;
				padding-inline: 3rem;
			}
		}

		.table-count {
			height: 4.1vw;
			border-radius: 4px;
			margin: 0.8em 0em;
			display: flex;
			justify-content: space-between;
			padding: 0.8em 1.2em;
		}

		.table-count-text-one {
			font-family: nunitosans-bold;
			font-size: 12px;
		}

		.table-count-text-two {
			font-family: nunitosans-regular;
			font-size: 12px;
		}

		.activeText,
		.visit-text {
			font-size: 12px;
		}

		.valid-icon-size {
			width: 20px;
			height: 20px;
		}
	}
`;

export const PageWrapper = styled.div`
	color: ${(props) => props.layoutTheme.textColor} !important;
`;
