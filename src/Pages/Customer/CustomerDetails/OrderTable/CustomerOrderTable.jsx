import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
	DropdownItem,
	DropdownMenu,
	DropdownToggle,
	UncontrolledDropdown
} from 'reactstrap';
import TableSkeleton from './TableSkeleton';

import TableComponent from '../../../../Components/Common/TableComponent';

import DropdownIconWOCircle from '../../../../Assets/images/newDropdownIconWOCircle.svg';
import TableStyle from '../../TableStyle';
import { NewPagination } from '../../../../Components/Common/NewPagination';
import NewSearchBox from '../../../../Components/Form/NewSearchBox/NewSearchBox';
import { SearchIcon } from '../../../../Components/Icons/SearchIcon/SearchIcon';

const CustomerOrderTable = ({
	loading,
	tableData,
	tableColumns,
	handleRowClick,
	handlePageChange,
	handleSearchInputChange,
	handleSortByChange,
	totalRows,
	currentPage,
	sortByData
}) => {
	const navigate = useNavigate()
	const [selectedRows, setSelectedRows] = useState([])
	const [sortByValue, setSortByValue] = useState("newToOld");
	const [sortByName, setSortByName] = useState("Newest - Oldest");

	useEffect(() => {
		handleSortByChange(sortByValue);
	}, [sortByValue]);


	return (
		<div>
			<TableStyle version={6}>
				<div className="search-sort">
						<NewSearchBox
							formGroupClassName="formGroupClassName"
							labelClassName="labelClassName"
							inputGroupTextClassName="inputGroupTextClassName"
							inputClassName="inputClassName table-count-text-two"
							iconBackgroundClass="iconBackgroundClass"
							type="text"
							name="search"
							placeholder="Search orders"
							icon={
								<SearchIcon className="inputIcon" />
							}
							iconPlacement="start"
							onChange={(event) => {
								handleSearchInputChange(event?.target?.value);
							}}
						/>

					<div className="d-flex align-items-center gap-3 ">
						<UncontrolledDropdown>
							<DropdownToggle
								style={{
									paddingInline: 0,
									background: 'transparent',
									border: 'none',
									outline: 'none'
								}}
							>
								<div className="d-flex align-items-center gap-3">
									<p className="table-count-text-one">{sortByName}</p>
									<img
										caret
										src={DropdownIconWOCircle}
										className='dropdown-image'
										alt="dropdownIcon"
									/>
								</div>
							</DropdownToggle>
							<DropdownMenu className="customer-dropdown-menu">
								{sortByData.map((value, index) => (
									<DropdownItem
										key={index}
										onClick={() => {
											setSortByValue(value.value);
											setSortByName(value.name);
										}}
									>
										{value.name}
									</DropdownItem>
								))}
							</DropdownMenu>
						</UncontrolledDropdown>
					</div>
				</div>
				{loading ? (
						<TableSkeleton />
				) : (
					<>
						<TableComponent
							columns={tableColumns}
							data={tableData}
							internalID={"id"}
							selectedRows={selectedRows}
							setSelectedRows={setSelectedRows}
							NoDataText={'No data found'}
							handleRowClick={handleRowClick}
							checkbox
						/>
						{tableData?.length !== 0 && (
							<div className="pa-t-8">
								<NewPagination
									handlePageChange={handlePageChange}
									total={totalRows}
									pageSize={20}
									currentPage={currentPage}
								/>
							</div>
						)}
					</>

				)}
			</TableStyle>
		</div>
	);
};

export default CustomerOrderTable;
