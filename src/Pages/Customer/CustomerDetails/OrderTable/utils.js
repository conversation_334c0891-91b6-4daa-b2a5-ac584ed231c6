import moment from 'moment';

export const formatOrderData = (data) => {
	let tableData = [];
	if (data?.length > 0) {
		tableData = data?.map((item) => {
			return {
				id: item?.id,
				orderNo: item?.orderNo,
				orderDate: moment(item?.orderDate).format('DD/MM/YYYY'),
				orderStatus:
					item?.orderStatus?.toUpperCase() === 'PICKEDUP'
						? item?.orderServiceType === 'TABLE'
							? 'Served'
							: 'Picked Up'
						: item?.orderStatus,
				paymentStatus: item?.paymentStatus,
				total: '$ ' + Number(item?.total)?.toFixed(2)?.toString(),
				refund:
					item?.refundStatus === 'PartialRefunded'
						? 'Partial Refunded'
						: item?.refundStatus,
				rowData: item
			};
		});
	}
	return tableData;
};

export const orderTableColumns = [
	{
		Header: 'Order Date',
		accessor: 'orderDate',
		className: 'justify-content-start',
		// minWidth: 220,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		style: { flex: 1 }
	},
	{
		Header: 'Order ID',
		accessor: 'orderNo',
		className: 'justify-content-start',
		// minWidth: 220,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		style: { flex: 1 }
	},
	{
		Header: 'Order Status',
		accessor: 'orderStatus',
		className: 'justify-content-start',
		// minWidth: 220,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		style: { flex: 1, textTransform: 'capitalize' }
	},
	{
		Header: 'Payment Status',
		accessor: 'paymentStatus',
		className: 'justify-content-start',
		filterable: false,
		sortable: false,
		// minWidth: 220,
		headerClassName: 'justify-content-start',
		style: { flex: 1 , textTransform: 'capitalize'}
	},
	{
		Header: 'Order Total',
		accessor: 'total',
		className: 'justify-content-start',
		minWidth: 50,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		style: { flex: 1 }
	}
];
