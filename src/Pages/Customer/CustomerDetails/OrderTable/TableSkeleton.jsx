import React from 'react';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import TableStyle from '../../TableStyle';
import TableV6 from '../../../../Components/Common/TableV6';

const data = [
	{
		id: 1,
		name: 'Export Tax for State Transport',
		taxAmount: '45%'
	},
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		taxAmount: '80%'
	},
	{
		id: 1,
		name: 'Export Tax for State Transport',
		taxAmount: '45%'
	},
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		taxAmount: '80%'
	},
	{
		id: 1,
		name: 'Export Tax for State Transport',
		taxAmount: '45%'
	},
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		taxAmount: '80%'
	},
	{
		id: 1,
		name: 'Export Tax for State Transport',
		taxAmount: '45%'
	},
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		taxAmount: '80%'
	},
	{
		id: 1,
		name: 'Export Tax for State Transport',
		taxAmount: '45%'
	},
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		taxAmount: '80%'
	}
];

const tableColumns = [
	{
		id: 'select',
		className: 'justify-content-start',
		style: { flex: 1 },
		minWidth: 15,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Order Date',
		accessor: 'orderDate',
		className: 'justify-content-start',
		style: { flex: 1 },
		// minWidth: 220,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Order ID',
		accessor: 'orderNo',
		className: 'justify-content-start',
		style: { flex: 1 },
		// minWidth: 220,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Order Status',
		accessor: 'orderStatus',
		className: 'justify-content-start',
		style: { flex: 1 },
		// minWidth: 220,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Payment Status',
		accessor: 'paymentStatus',
		className: 'justify-content-start',
		filterable: false,
		sortable: false,
		style: { flex: 1 },
		// minWidth: 220,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Order Total',
		accessor: 'total',
		className: 'justify-content-start',
		style: { flex: 1 },
		minWidth: 50,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	}
];

const TableSkeleton = () => {
	return (
		<TableStyle version={6}>
			<TableV6 columns={tableColumns} data={data} key={'master-todo-table'}/>
		</TableStyle>
	);
};

export default TableSkeleton;
