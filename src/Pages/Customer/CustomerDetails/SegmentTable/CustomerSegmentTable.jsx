import { useState } from 'react';
import TableSkeleton from './TableSkeleton';

import TableComponent from '../../../../Components/Common/TableComponent';

import TableStyle from '../../TableStyle';
import { NewPagination } from '../../../../Components/Common/NewPagination';
import NewSearchBox from '../../../../Components/Form/NewSearchBox/NewSearchBox';
import { SearchIcon } from '../../../../Components/Icons/SearchIcon/SearchIcon';

const CustomerSegmentTable = ({
	loading,
	tableData,
	tableColumns,
	handleRowClick,
	handlePageChange,
	handleSearchInputChange,
	totalRows,
	currentPage,
}) => {
	const [selectedRows, setSelectedRows] = useState([])

	return (
		<div>
			<TableStyle version={6}>
				<div className="search-sort">
						<NewSearchBox
							formGroupClassName="formGroupClassName"
							labelClassName="labelClassName"
							inputGroupTextClassName="inputGroupTextClassName"
							inputClassName="inputClassName table-count-text-two"
							iconBackgroundClass="iconBackgroundClass"
							type="text"
							name="search"
							placeholder="Search the segments this customer belongs to"
							icon={
								<SearchIcon className="inputIcon" />
							}
							iconPlacement="start"
							onChange={(event) => {
								handleSearchInputChange(event?.target?.value);
							}}
						/>

				</div>
				{loading ? (
						<TableSkeleton />
				) : (
					<>
						<TableComponent
							columns={tableColumns}
							data={tableData}
							internalID={"id"}
							selectedRows={selectedRows}
							setSelectedRows={setSelectedRows}
							NoDataText={'No data found'}
							handleRowClick={handleRowClick}
						/>
						{tableData?.length !== 0 && (
							<div className="pa-t-8">
								<NewPagination
									handlePageChange={handlePageChange}
									total={totalRows}
									pageSize={20}
									currentPage={currentPage}
								/>
							</div>
						)}
					</>

				)}
			</TableStyle>
		</div>
	);
};

export default CustomerSegmentTable;
