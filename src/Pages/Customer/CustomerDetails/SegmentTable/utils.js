import moment from 'moment';
import { FilledButton } from '../../../../Components/Layout/Buttons';
import { formatUTCToLocalDate } from '../../../../Utils/dateTimeUtils';

export const formatSegmentData = (data, timezone) => {

    const userTimezone = timezone || 'Australia/Perth';

    let tableData = [];
    if (data?.length > 0) {
        tableData = data?.map((item) => {
            return {
                id: item?.id,
                name: item?.name,
                createdAt: formatUTCToLocalDate(item?.createdAt, userTimezone),
                // createdAt: item?.createdAt,
                rowData: item
            };
        });
    }
    return tableData;
};

export const segmentTableColumns = [
	{
		Header: 'Segment Name',
		accessor: 'name',
		className: 'justify-content-start',
		minWidth: 170,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
	},
	{
		Header: 'Date Added',
		accessor: 'createdAt',
		className: 'justify-content-start',
		minWidth: 120,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		style: { flex: 1 }
	},
	{
		className: 'justify-content-start',
		minWidth: 30,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		style: { flex: 1 },
		Cell: ({ row }) => {
			return ( <u>Manage</u>
			//   <FilledButton
			// 	buttonText={'Subscribed'}
			// 	background={'#def1d9'}
			// 	color={'#6BC242'}
			// 	style={{ height: '2.2em', width: '8.7em'}}
			//   />
			)
			//  : (
			//   <FilledButton
			// 	buttonText={'Not subscribed'}
			// 	background={'#e7e8ea'}
			// 	color={'#979797'}
			// 	style={{ height: '2.2em', width: '8.7em'}}
			//   />
			// );
		  }
	}
];
