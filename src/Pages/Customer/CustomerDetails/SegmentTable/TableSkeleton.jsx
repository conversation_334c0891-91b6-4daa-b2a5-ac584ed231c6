import React from 'react';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import TableStyle from '../../TableStyle';
import TableV6 from '../../../../Components/Common/TableV6';

const data = [
	{
		id: 1,
		name: 'Export Tax for State Transport',
		taxAmount: '45%'
	},
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		taxAmount: '80%'
	},
	{
		id: 1,
		name: 'Export Tax for State Transport',
		taxAmount: '45%'
	},
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		taxAmount: '80%'
	},
	{
		id: 1,
		name: 'Export Tax for State Transport',
		taxAmount: '45%'
	},
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		taxAmount: '80%'
	},
	{
		id: 1,
		name: 'Export Tax for State Transport',
		taxAmount: '45%'
	},
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		taxAmount: '80%'
	},
	{
		id: 1,
		name: 'Export Tax for State Transport',
		taxAmount: '45%'
	},
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		taxAmount: '80%'
	}
];

const tableColumns = [
	{
		Header: 'Segment Name',
		accessor: 'name',
		className: 'justify-content-start',
		style: { flex: 1 },
		minWidth: 170,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Date Added',
		accessor: 'convertedDateTime',
		className: 'justify-content-start',
		style: { flex: 1 },
		minWidth: 100,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		id: 'select',
		className: 'justify-content-start',
		style: { flex: 1 },
		minWidth: 30,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
];

const TableSkeleton = () => {
	return (
		<TableStyle version={6}>
			<TableV6 columns={tableColumns} data={data} key={'master-todo-table'}/>
		</TableStyle>
	);
};

export default TableSkeleton;
