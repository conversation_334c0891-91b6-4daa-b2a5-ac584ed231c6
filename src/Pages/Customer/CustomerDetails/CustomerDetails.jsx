import React, { useEffect, useState } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { useFormik } from 'formik';

import { formatOrderData, orderTableColumns } from './OrderTable/utils';
import CustomerOrderTable from './OrderTable/CustomerOrderTable';
import { formatSegmentData, segmentTableColumns } from './SegmentTable/utils';
import CustomerSegmentTable from './SegmentTable/CustomerSegmentTable';
import { useDebounce } from '../../../Hooks/useDebounce';
import { VenueApiRoutes, VenuePanelRoutes } from '../../../Utils/routes';
import { CustomerWrraper } from './customer.details.style';
import Api from '../../../Helper/Api';
import BackIcon from '../../../Assets/images/back-icon.svg';
import { FilledButton } from '../../../Components/Layout/Buttons';
import OrderViewModal from '../OrderViewModal';
import moment from 'moment';
import CustomerDetailsSkeleton from './CustomerDetailsSkeleton';

const CustomerDetails = () => {
	const state = useSelector((state) => ({ ...state }));
	const navigate = useNavigate();
	const location = useLocation();
	const queryParams = new URLSearchParams(location.state);
	const initialSortOrder = queryParams.get('sortOrder');
	const params = useParams();
	const [customerData, setCustomerData] = useState();
	const [customerUserData, setCustomerUserData] = useState(true);
	const [orderViewModal, setOrderViewModal] = useState(false);
	const [singleRowData, setSingleRowData] = useState(null);
	const [statisticsLoading, setStatisticsLoading] = useState(true);
	const [statisticsData, setStatisticsData] = useState(null);
	const [loading, setLoading] = useState(false);
	const [detailsLoading, setDetailsLoading] = useState(true);
	const [exportLoading, setExportLoading] = useState(false);
	const [currentPage, setCurrentPage] = useState(1);
	const [sortBy, setSortBy] = useState('newToOld');
	const [tableData, setTableData] = useState([]);
	const [tableDataCount, setTableDataCount] = useState(null);
	const [searchTerm, setSearchTerm] = useState('');
	const debounceSearchTerm = useDebounce(searchTerm, 500);
	const [segmentCurrentPage, setSegmentCurrentPage] = useState(1);
	const [segmentTableData, setSegmentTableData] = useState([]);
	const [segmentTableDataCount, setSegmentTableDataCount] = useState(null);
	const [segmentSearchTerm, setSegmentSearchTerm] = useState('');
	const segmentDebounceSearchTerm = useDebounce(segmentSearchTerm, 500);

	const sortByData = [
		{
			id: 1,
			name: 'Newest - Oldest',
			value: 'newToOld'
		},
		{
			id: 2,
			name: 'Oldest - Newest',
			value: 'oldToNew'
		},
		{
			id: 3,
			name: 'Spend: Highest - Lowest',
			value: 'highestSpent'
		},
		{
			id: 4,
			name: 'Spend: Lowest - Highest',
			value: 'lowestSpent'
		}
	];

	const authDetails = state?.auth;
	const selectedVenue = authDetails?.selectedVenue;
	const authData = useSelector((state) => ({ ...state.auth }));
	const timeZone = authData?.timezone_value || 'Australia/Perth';

	useEffect(() => {
		getCustomerDetails();
	}, [selectedVenue?.id]);

	useEffect(() => {
		(async () => {
			await getTableData({
				bar_id: selectedVenue?.id,
				search: debounceSearchTerm,
				page: currentPage,
				sortBy: sortBy,
				user_id: params?.id
			});
		})();
	}, [currentPage, debounceSearchTerm, selectedVenue?.id, sortBy]);

	useEffect(() => {
		(async () => {
			await getSegmentTableData({
				bar_id: selectedVenue?.id,
				search: segmentDebounceSearchTerm,
				page: segmentCurrentPage,
				user_id: params?.id
			});
		})();
	}, [segmentCurrentPage, segmentDebounceSearchTerm, selectedVenue?.id]);

	const getCustomerDetails = async () => {
		try {
			setDetailsLoading(true);
			const res = await Api('POST', VenueApiRoutes.getCustomerDetails, {
				user_id: params?.id,
				bar_id: selectedVenue?.id
			});
			if (res?.data?.status) {
				setCustomerData(res?.data?.data);
				setCustomerUserData(res?.data?.data?.user);
			} else {
				toast.error(res?.data?.message);
			}
			setDetailsLoading(false);
		} catch (err) {
			setDetailsLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	const getSegmentTableData = async (payload) => {
		try {
			setLoading(true);
			const res = await Api(
				'POST',
				VenueApiRoutes.getCustomerSegmentList,
				payload
			);
			if (res?.data?.status) {
				// Pass the timezone to formatSegmentData
				const tableDetails = formatSegmentData(res?.data?.data?.rows, timeZone);
				setSegmentTableData(tableDetails);
				setSegmentTableDataCount(res?.data?.data?.count);
			} else {
				toast.error(res?.data?.message);
			}
			setLoading(false);
		} catch (err) {
			setLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	const handleSegmentPageChange = ({ selected }) => {
		let pageNo = selected + 1;
		setSegmentCurrentPage(pageNo);
	};
	const handleSegmentSearchInputChange = async (value) => {
		setSegmentSearchTerm(value);
		setSegmentCurrentPage(1);
	};

	const getTableData = async (payload) => {
		try {
			setLoading(true);
			const res = await Api(
				'POST',
				VenueApiRoutes.getCustomerOrderList,
				payload
			);
			if (res?.data?.status) {
				const tableDetails = formatOrderData(res?.data?.data?.rows);
				setTableData(tableDetails);
				setTableDataCount(res?.data?.data?.count);
			} else {
				toast.error(res?.data?.message);
			}
			setLoading(false);
		} catch (err) {
			setLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	const handlePageChange = ({ selected }) => {
		let pageNo = selected + 1;
		setCurrentPage(pageNo);
	};
	const handleSearchInputChange = async (value) => {
		setSearchTerm(value);
		setCurrentPage(1);
	};
	const handleOrderViewModal = () => {
		setOrderViewModal((prev) => !prev);
	};
	const handleRowClick = (rowData) => {
		// setSingleRowData(rowData?._original?.rowData);
		// setOrderViewModal(true);
		navigate(VenuePanelRoutes.orderHistory, {
			state: { orderId: rowData?._original?.rowData?.orderNo }
		});
	};
	const handleSegmentRowClick = (rowData) => {
		navigate(VenuePanelRoutes.segments);
	};

	const handleSortByChange = (sortBy) => {
		setSortBy(sortBy);
	};

	return (
		<>
			<CustomerWrraper>
				{detailsLoading ? (
					<CustomerDetailsSkeleton />
				) : (
					<div className="titleWrap">
						<div className="d-flex align-items-center justify-content-between gap-1 ">
							<div className="d-flex align-items-baseline gap-3">
								<div>
									<div className="d-flex gap-3 align-items-center">
										<img
											src={BackIcon}
											alt="not-accept"
											className="valid-icon-size align-items-center"
											onClick={() =>
												navigate(
													VenuePanelRoutes.customers,
													{
														state: {
															sortOrder:
																initialSortOrder
														}
													}
												)
											}
										/>
										<h1 className="mainTitle">
											{customerUserData?.fullName
												? customerUserData?.fullName
												: 'Anonymous User'}
										</h1>
									</div>
									<div className="ml-36">
										<p className="mainParagraph mt-10">
											{customerData
												? `Customer since ${customerData?.customerSince}`
												: 'No data found'}
										</p>
									</div>
								</div>
							</div>

							{/* <div className="d-flex justify-content-end flex-wrap gap-3">
								<FilledButton
									buttonText={'Advertise to customer'}
									background={'#d5ebff'}
									style={{
										width: '160px',
										border: '1px solid rgb(44, 154, 255)'
									}}
									color={'#2c9aff'}
									onClick={() =>
										toast.success(
											'This feature is coming soon!'
										)
									}
								/>

								<FilledButton
									buttonText={'Give discount to customer'}
									style={{
										width: '160px',
										border: '1px solid rgb(107, 194, 66)'
									}}
									background={'#e1f3da'}
									color={'#6bc242'}
									onClick={() =>
										navigate(
											VenuePanelRoutes.createNewDiscount,
											{
												state: {
													type: 'customer',
													selectedItem: {
														...customerData,
														...customerData?.user
													}
												}
											}
										)
									}
								/>
							</div> */}
						</div>
						<div>
							<div>
								<div className="customer-details">
									<div className="table-count-text-one mb-2">
										All time
									</div>
									{customerData ? (
										<div className="details-text d-flex  flex-wrap w-100 gap-1">
											<div className="flex-1">
												<span className="boldText">
													{customerData?.orderCount}{' '}
												</span>
												total orders
											</div>
											<div className="flex-1">
												<span className="boldText">
													$
													{Number(
														customerData?.totalOrderSpent
													).toFixed(2)}{' '}
												</span>
												total spend
											</div>
											<div className="flex-1">
												<span className="boldText">
													$
													{Number(
														customerData?.avgOrderTotal
													).toFixed(2)}{' '}
												</span>
												average spend per order
											</div>
											<div className="flex-1">
												<span className="boldText">
													$
													{Number(
														customerData?.hightestSpent
													).toFixed(2)}{' '}
												</span>
												highest individual purchase
											</div>
										</div>
									) : (
										'No data found'
									)}
								</div>
								<div className="customer-details">
									<div className="table-count-text-one mb-2">
										Details
									</div>
									{customerUserData ? (
										<div className="details-text  d-flex flex-wrap align-items-start gap-1">
											<div className="flex-1">
												<span className="boldText">
													Email:{' '}
												</span>
												{customerUserData?.email}
											</div>
											<div className="flex-1">
												<span className="boldText">
													Mobile:{' '}
												</span>
												{customerUserData?.countryCode}{' '}
												{customerUserData?.mobile}
											</div>
											<div className="flex-1">
												<span className="boldText">
													Birthday:{' '}
												</span>
												{customerUserData?.birthday &&
													moment(
														customerUserData?.birthday
													).format('Do MMMM')}
											</div>
											<div className="flex-1">
												<span className="boldText">
													Age:{' '}
												</span>
												{customerUserData?.age_range}
											</div>
										</div>
									) : (
										'No data found'
									)}
								</div>
							</div>
						</div>
					</div>
				)}

				<div className="table-count">
					<div className="table-count-text-one">Segments</div>
					<div className="table-count-text-two">
						Showing{' '}
						{segmentTableDataCount < 20
							? segmentTableDataCount
							: 20}{' '}
						of {segmentTableDataCount} segments
					</div>
				</div>

				<CustomerSegmentTable
					className="table-data"
					loading={loading}
					tableColumns={segmentTableColumns}
					tableData={segmentTableData}
					handlePageChange={handleSegmentPageChange}
					handleSearchInputChange={handleSegmentSearchInputChange}
					totalRows={segmentTableDataCount}
					currentPage={segmentCurrentPage}
					handleRowClick={handleSegmentRowClick}
				/>

				<div className="table-count">
					<div className="table-count-text-one">Orders</div>
					<div className="table-count-text-two">
						Showing {tableDataCount < 20 ? tableDataCount : 20} of{' '}
						{tableDataCount} orders
					</div>
				</div>

				<CustomerOrderTable
					className="table-data"
					loading={loading}
					tableColumns={orderTableColumns}
					tableData={tableData}
					handlePageChange={handlePageChange}
					handleSearchInputChange={handleSearchInputChange}
					totalRows={tableDataCount}
					currentPage={currentPage}
					sortByData={sortByData}
					handleRowClick={handleRowClick}
					handleSortByChange={handleSortByChange}
				/>
				<div className="customer-faqs mt-3">
					<p className="visit-text">Visit</p>&nbsp;
					<p
						className="activeText"
						onClick={() => navigate(VenuePanelRoutes.support)}
					>
						Customers FAQ Forum
					</p>
				</div>
			</CustomerWrraper>
			{singleRowData && orderViewModal && (
				<OrderViewModal
					isOpen={orderViewModal}
					handleModal={handleOrderViewModal}
					singleRowData={singleRowData}
				/>
			)}
		</>
	);
};

export default CustomerDetails;
