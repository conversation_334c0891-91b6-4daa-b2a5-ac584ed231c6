import React from 'react';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

const CustomerDetailsSkeleton = () => {
  return (
    <div className="titleWrap gap-0">
      <div className="d-flex align-items-center justify-content-between gap-1 ">
        <div className="d-flex align-items-baseline gap-3">
          <div>
            <div className="d-flex gap-3 align-items-center">
              <Skeleton circle={true} height={32} width={32} />
              <Skeleton width={200} height={32} />
            </div>
            <div className="ml-40 mt-10">
              <Skeleton width={150} height={20} />
            </div>
          </div>
        </div>

        <div className="d-flex justify-content-end flex-wrap gap-3">
          <Skeleton width={160} height={40} />
          <Skeleton width={160} height={40} />
        </div>
      </div>

      <div className="customer-details mt-4">
        <Skeleton width={80} height={20} className="mb-2" />
        <div className="details-text d-flex flex-wrap w-100 gap-1">
          <Skeleton width="25%" height={20} />
          <Skeleton width="25%" height={20} />
          <Skeleton width="25%" height={20} />
          <Skeleton width="25%" height={20} />
        </div>
      </div>

      <div className="customer-details">
        <Skeleton width={80} height={20} className="mb-2" />
        <div className="details-text d-flex flex-wrap align-items-start gap-1">
          <Skeleton width="45%" height={20} />
          <Skeleton width="45%" height={20} />
          <Skeleton width="45%" height={20} />
          <Skeleton width="45%" height={20} />
        </div>
      </div>

    </div>
  );
};

export default CustomerDetailsSkeleton;
