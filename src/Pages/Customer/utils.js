import { FilledButton } from '../../Components/Layout/Buttons';

export const formatResponseData = (data) => {
	let tableData = [];
	if (data?.length > 0) {
		tableData = data.map((customer) => {
			return {
				id: customer?.userID,
				birthday: customer?.birthday,
				fullName: customer?.fullName,
				email: customer?.email,
				mobile: customer?.mobile,
				orderCount: customer?.orderCount,
				hightestSpent: customer?.hightestSpent,
				totalOrderSpent: `$${Number(customer?.totalOrderSpent).toFixed(2)}`,
				avgOrderTotal: customer?.avgOrderTotal,
				userSubscription: customer?.userSubscription,
				countryCode: customer?.countryCode,
				mobileNumber: `${customer?.countryCode !== undefined ? customer?.countryCode : ''} ${customer?.mobile}`
			};
		});
	}
	return tableData;
};

export const tableColumns = [
	{
		Header: 'Customer name',
		accessor: 'fullName',
		className: 'justify-content-start',
		minWidth: 120,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		style: { textTransform: 'capitalize', flex: 1 },
	},
	{
		Header: 'Email',
		accessor: 'email',
		className: 'justify-content-start',
		minWidth: 200,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		style: { flex: 1 },
	},
	{
		Header: 'Mobile',
		accessor: 'mobileNumber',
		className: 'justify-content-start',
		minWidth: 110,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		style: { flex: 1 }
	},
	{
		Header: 'Subscription',
		accessor: 'userSubscription',
		className: 'justify-content-center',
		filterable: false,
		sortable: false,
		minWidth: 140,
		headerClassName: 'justify-content-center',
		style: { flex: 1 },
		Cell: ({ row }) => {
			return row.userSubscription === 1 ? (
			  <FilledButton
				buttonText={'Subscribed'}
				background={'#def1d9'}
				color={'#6BC242'}
				style={{ height: '2.2em', width: '8.7em'}}
			  />
			) : (
			  <FilledButton
				buttonText={'Not subscribed'}
				background={'#e7e8ea'}
				color={'#979797'}
				style={{ height: '2.2em', width: '8.7em'}}
			  />
			);
		  }
	},
	{
		Header: 'Orders',
		accessor: 'orderCount',
		className: 'justify-content-start',
		filterable: false,
		sortable: false,
		minWidth: 80,
		headerClassName: 'justify-content-start',
		// style: { flex: 1 },
	},
	{
		Header: 'Total Spend',
		accessor: 'totalOrderSpent',
		className: 'justify-content-start',
		minWidth: 90,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		// style: { flex: 1 },
	}
];