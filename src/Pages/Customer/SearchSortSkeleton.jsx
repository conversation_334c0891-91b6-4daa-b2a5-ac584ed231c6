// SearchSortSkeleton.js
import React from 'react';
import styled from 'styled-components';

const SkeletonWrapper = styled.div`
    .skeleton-search-sort {
        background: #fbfcff;
		height: 3vw;
		border: 0.6px solid rgb(148, 150, 152, 0.5);
		border-bottom: none;
		border-radius: 4px 4px 0px 0px;
		margin-top: 1em;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 1em;
    }

    .skeleton-input,
    .skeleton-sort {
        background: #fbfcff;
        border-radius: 4px;
        height: 2.5em;
    }

    .skeleton-input {
        width: 60%;
    }

    .skeleton-sort {
        width: 15%;
        height: 2.5em;
    }
`;

const SearchSortSkeleton = () => {
    return (
        <SkeletonWrapper>
            {/* <div className="skeleton-search-sort">
                <div className="skeleton-input" />
                <div className="skeleton-sort" />
            </div> */}
        </SkeletonWrapper>
    );
};

export default SearchSortSkeleton;
