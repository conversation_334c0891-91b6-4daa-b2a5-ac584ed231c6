import React from 'react';
import styled from 'styled-components';

const StylesV6 = styled.div`
	width: 100%;
	border: none;
	font-family: nunitosans-regular !important;

	.table-count-text-two {
		display: flex;
		align-items: center;
		font-family: nunitosans-regular;
		color: #a4a5a7;
		font-size: 16px;
	}

	.valid-icon-size {
		margin: 1em;
		height: 19px;
		width: auto;
		color: #7d7e7f;
	}

	.dropdown {
		.customer-dropdown-menu {
			min-width: 14.5vw !important;
			text-align: center;
		}
		.dropdown-item {
			height: 2.24vw;
		}
	}

	.inputClassName::placeholder {
		color: #a4a5a7;
		font-size: 16px;
		font-family: nunitosans-regular !important;
	}

	.inputClassName:focus {
		outline: none;
		box-shadow: none;
		border-color: #a4a5a7;
	}

	.inputGroupTextClassName,
	.inputClassName {
		border: none;
		padding: 0.375rem 0.375rem;
		background: none !important;
		font-family: nunitosans-regular !important;
		color: #a4a5a7 !important;
		font-size: 16px !important;
		transition: color 0.3s ease, border-color 0.3s ease;
	}

	.inputClassName {
		width: 280px;
	}

	.inputClassName:focus {
		color: #000000 !important; /* Change text color when focused */
		border-color: #a4a5a7; /* Optional: Change border color when focused */
		outline: none;
	}

	.inputIcon {
		height: 17px;
		width: 19px;
		font-family: nunitosans-regular !important;
		color: #a4a5a7 !important;
		font-size: 16px !important;
	}

	.formGroupClassName {
		margin: 0px !important;
	}

	.search-sort {
		background: #fbfcff;
		height: 3vw;
		border: 0.6px solid rgb(148, 150, 152, 0.5);
		border-bottom: none;
		border-radius: 4px 4px 0px 0px;
		margin-top: 1em;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 1em;
	}

	.ReactTable {
		border-top: none;
		border-inline: 0.6px solid rgb(148, 150, 152, 0.5);
		border-radius: 0px 0px 4px 4px;
		font-family: nunitosans-regular !important;
		height: 100%;
		width: 100%;
		overflow: hidden;
		.rt-table {
			font-family: nunitosans-regular !important;
			overflow: auto;
			::-webkit-scrollbar {
				width: 5px;
				height: 5px;
			}
		}
		.rt-th {
			div {
				font-family: 'nunitosans-bold' !important;
			}
		}
		.rt-th,
		.rt-td {
			min-width: 50px;
			// max-width: 300px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;

			white-space: pre-wrap !important;
			&.react-table-header-class {
				&.justify-content-start {
					div {
						justify-content: start !important;
					}
				}
				div {
					font-family: nunitosans-bold !important;
				}
			}
		}
		.rt-thead.-header {
			background-color: #fff;
			box-shadow: none !important;
			position: sticky;
			top: 0;
			display: ${(props) => (props.hideHeader ? 'none' : '')};
		}
		.rt-thead .rt-th,
		.rt-thead .rt-td {
			min-width: 50px;
			// max-width: 300px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;

			height: 3.29em;
			padding: 8px 18px !important;
			text-align: center;
			justify-content: center;
			display: flex;
			align-items: center;
			border: none;
			color: #2e2e2e;
			text-transform: capitalize;
			font-size: 14px;
			background-color: #f5f6fa;
			div {
				display: flex;
				align-items: center;
				justify-content: center;
			}
			&.-cursor-pointer {
				box-shadow: none !important;
				display: flex;
				justify-content: center;
				align-items: center;
				&:before {
					content: ' ';
					position: absolute;
					right: 15px;
					font-size: 4rem !important;
					color: #fd6461 !important;
					background-color: transparent !important;
					background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE0IDIyTDEwIDI2TDYgMjIiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTEwIDZWMjYiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTE4IDEwTDIyIDZMMjYgMTAiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTIyIDI2VjYiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);
					background-repeat: no-repeat;
					background-size: 100% 100%;
				}
			}
			&.-sort-desc.-cursor-pointer {
				&:before {
					content: ' ' !important;
					font-size: 4rem !important;
					font-family: 'nunitosans-regular' !important;
					color: #fd6461 !important;
					background-color: transparent !important;
					background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE0IDIyTDEwIDI2TDYgMjIiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTEwIDZWMjYiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTE4IDEwTDIyIDZMMjYgMTAiIHN0cm9rZT0iIzYxNjE2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTIyIDI2VjYiIHN0cm9rZT0iIzYxNjE2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);
					background-repeat: no-repeat;
					background-size: 100% 100%;
				}
			}

			&.-sort-asc.-cursor-pointer {
				&:before {
					content: ' ' !important;
					font-size: 4rem !important;
					font-family: 'nunitosans-regular' !important;
					color: #fd6461 !important;
					background-color: transparent !important;
					background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE0IDIyTDEwIDI2TDYgMjIiIHN0cm9rZT0iIzYxNjE2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTEwIDZWMjYiIHN0cm9rZT0iIzYxNjE2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTE4IDEwTDIyIDZMMjYgMTAiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTIyIDI2VjYiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);
					background-repeat: no-repeat;
					background-size: 100% 100%;
				}
			}
		}
		.rt-tbody {
			overflow-y: initial;
			overflow-x: hidden;
			border-top: none;
			border-bottom: none;
			::-webkit-scrollbar {
				width: 5px;
				height: 5px;
			}
			::-webkit-scrollbar-track {
				border-radius: 50px;
				margin-bottom: 10px;
			}
			::-webkit-scrollbar-thumb {
				border-radius: 50px;
			}
			.rt-td {
				min-width: 50px;
				// max-width: 300px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;

				cursor: pointer;
				border: none;
				border-bottom: 0.6px solid rgb(148, 150, 152, 0.5);
				display: flex;
				font-family: 'nunitosans-semi-bold' !important;
				background-color: #fbfcff;
				height: 3.31em;
				justify-content: center;
				align-items: center;
				padding: 6px 18px !important;
				border-radius: 0px;
			}
			.rt-tr-group {
				box-shadow: 0px 5px 15px 10px rgba(187, 193, 200, 0.05);
				border: none;
				font-size: 16px;
				flex: none;
				color: #000;
			}
			.rt-tr {
				/* align-items: center;
				justify-content: space-evenly; */
			}
		}
	}
	ul {
		list-style: none;
		display: flex;
		justify-content: center;
		align-items: center;
		li {
			margin: 0 5px;
			font-size: 12px;
			border-radius: 4px;
			padding: 0;
			height: 30px;
			text-align: center;
			&.page-break {
				a {
					color: #fd6461 !important;
				}
			}
			a {
				display: inline-block;
				padding: 6px 10px;
				border: none;
				outline: none;
				svg: {
					padding: 0;
				}
			}
			&.page-item,
			.page-break {
				transition: all 0.25s ease-in-out;
				a {
					color: #fd6461 !important;
					outline: none !important;
					&:active {
						outline: none !important;
					}
					&:focus {
						box-shadow: none !important;
					}
				}
			}
			&.page-item.disabled {
				/* visibility: hidden; */
				cursor: not-allowed;
			}
			&.page-item.active {
				a {
					background-color: #fd6461 !important;
					color: #fff !important;
				}
			}
			:first-child {
				a {
					padding: 7px 5px;
					svg {
						font-size: 14px !important;
					}
				}
			}
			:last-child {
				a {
					padding: 7px 5px;
					svg {
						font-size: 14px !important;
					}
				}
			}
		}
	}
	.customPagination {
		height: auto;

		.page-item,
		.page-break {
			height: auto;
			margin: 0 2px !important;
			gap: 2px;
		}

		.page-link {
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 12px !important;
			width: 2em;
			height: 2em;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 4px;
			color: #000000 !important;
			background-color: #fbfcff;
			border: 0.5px solid #d5d5d5 !important; /* Border for the link */
			text-decoration: none; /* Remove underline from links */
		}

		.pagination {
			height: auto;
			margin-bottom: 0 !important;
		}
	}
	.boxBorderRight {
		border-right: 1px solid #bababa;
	}
	.rtNoDataFound {
		text-align: center;
		padding: 28px;
		font-size: 12px;
		color: rgb(1, 1, 1);
		border: 0.6px solid rgb(148, 150, 152, 0.5);
		border-top: none;
		border-radius: 0 0 4px 4px;
	}

	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.inputClassName::placeholder {
			font-size: 12px;
			font-family: nunitosans-regular !important;
		}

		.inputGroupTextClassName,
		.inputClassName {
			font-size: 12px !important;
		}

		.inputIcon {
			height: 12px !important;
			width: 14px !important;
		}

		.ReactTable {
			.rt-thead.-header {
			}
			.rt-thead .rt-th,
			.rt-thead .rt-td {
				min-width: 50px;
				// max-width: 300px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;

				border: none;
				padding: 8px 18px !important;
				font-size: 11px;
				&.-cursor-pointer {
					&:before {
						right: 15px;
						font-size: 4rem !important;
					}
				}
				&.-sort-desc.-cursor-pointer {
					&:before {
						font-size: 4rem !important;
					}
				}

				&.-sort-asc.-cursor-pointer {
					&:before {
						font-size: 4rem !important;
					}
				}
			}
			.rt-tbody {
				border-top: none;
				border-bottom: none;
				.rt-td {
					min-width: 50px;
					// max-width: 300px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;

					border-bottom: 0.6px solid rgb(148, 150, 152, 0.5);
					height: 2.9em;
					padding: 6px 18px !important;
				}
				.rt-tr-group {
					box-shadow: 0px 5px 15px 10px rgba(187, 193, 200, 0.05);
					font-size: 12px;
				}
			}
		}
	}

	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.inputClassName::placeholder {
			font-size: 12px;
			font-family: nunitosans-regular !important;
		}

		.inputGroupTextClassName,
		.inputClassName {
			font-size: 12px !important;
		}

		.inputIcon {
			height: 12px !important;
			width: 14px !important;
		}

		.ReactTable {
			.rt-thead.-header {
			}
			.rt-thead .rt-th,
			.rt-thead .rt-td {
				min-width: 50px;
				// max-width: 300px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;

				padding: 8px 18px !important;
				font-size: 11px;
				&.-cursor-pointer {
					&:before {
						right: 15px;
						font-size: 4rem !important;
					}
				}
				&.-sort-desc.-cursor-pointer {
					&:before {
						font-size: 4rem !important;
					}
				}

				&.-sort-asc.-cursor-pointer {
					&:before {
						font-size: 4rem !important;
					}
				}
			}
			.rt-tbody {
				border-top: none;
				border-bottom: none;
				.rt-td {
					min-width: 50px;
					// max-width: 300px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;

					border-bottom: 0.6px solid rgb(148, 150, 152, 0.5);
					height: 2.9em;
					padding: 6px 18px !important;
				}
				.rt-tr-group {
					box-shadow: 0px 5px 15px 10px rgba(187, 193, 200, 0.05);
					font-size: 12px;
				}
			}
		}
	}
`;

const TableStyle = ({ children, ...props }) => {
	return <StylesV6 {...props}>{children}</StylesV6>;
};

export default TableStyle;
