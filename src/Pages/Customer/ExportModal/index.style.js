import styled from 'styled-components';

export const ExportWrapper = styled.div`
	color: ${(props) => props.layoutTheme.textColor} !important;
	font-family: nunitosans-regular !important;

	h6 {
		font-family: nunitosans-semi-bold !important;
		font-size: 13px !important;
	}

	p {
		font-family: nunitosans-regular !important;
		font-size: 13px !important;
	}
		
	.newThemeRadio {
		border: 2px solid rgba(0, 0, 0, 0.25) !important;
	}

	.newThemeRadio:checked {
		background-image: none !important;
		background-color: ${(props) =>
			props.layoutTheme.headingColor} !important;
		border: 0px !important;
		box-shadow: none !important;
		&:active,
		&:focus {
			border: 0px !important;
		}
	}
	.newThemeRadio:active {
		box-shadow: none !important;
		outline: none !important;
		border-color: #00000040 !important;
	}
`;
