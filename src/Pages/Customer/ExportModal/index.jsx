import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useSelector } from 'react-redux';
import CustomModal from '../../../Components/Common/Modal/CustomModal';
import CustomButton from '../../../Components/Common/CustomButton';
import FormRadioButton from '../../../Components/Form/FormRadioButton';

import { Button, Dropdown, DropdownItem, DropdownMenu, DropdownToggle } from 'reactstrap';
import { defineds } from '../../../Helper/DateHelper';
import useDevice from '../../../Hooks/useDevice';
import CustomDateRangePicker from '../../../Components/Common/DateRangePicker/CustomDateRangePicker';
import { DownArrowIcon, UpArrowIcon } from '../../../Components/Icons';
import { HeaderWrapper } from './dropdown.style';
import CustomerCustomModal from '../../../Components/Common/Modal/CustomerCustomModal';
import { ExportWrapper } from './index.style';

const ExportModal = ({
  handleModal,
  isOpen,
  loading,
  handleExport,
  handleDateRangeChange,
  handleExportUserTypeFilter,
  selectedRowsCount,
}) => {
  const state = useSelector((state) => ({ ...state }));
  const allThemeData = state.themeChanger;
  const authDetails = state.auth;

  const [exportType, setExportType] = useState(selectedRowsCount !== 0 ? 3 : 1); // Export type: 1, 2, or 3
  const [userTypeFilter, setUserTypeFilter] = useState(0); // User filter: 0 (All Customers), 1 (Subscribed Customers)
  const [dateRangePickerLabel, setDateRangePickerLabel] = useState('Today');
  const [dateRange, setDateRange] = useState([defineds.startOfToday, defineds.endOfToday]);

  const dateLabels = useMemo(
    () => ['Today', 'Yesterday', 'This Week', 'This Month', 'This Quarter', 'This Calendar Year', 'All Time'],
    []
  );

  const radioOptions = useMemo(
    () => [
      { label: 'All customers', exportType: 1, userTypeFilter: 0 },
      { label: 'Subscribed customers', exportType: 1, userTypeFilter: 1 },
      { label: 'Current page', exportType: 2, userTypeFilter: 0 },
      { label: `Selected customers: ${selectedRowsCount} selected`, exportType: 3, userTypeFilter: 0 },
    ],
    [selectedRowsCount]
  );

  const handleDateRange = (ranges) => {
    if (ranges[0] && ranges[1]) {
      setDateRangePickerLabel(
        ranges[0].toLocaleDateString() + ' - ' + ranges[1].toLocaleDateString()
      );
    }
    setDateRange(ranges);
  };

  const handleDateRangeLabel = useCallback(
    (label) => {
      const dateRanges = {
        Today: [defineds.startOfToday, defineds.endOfToday],
        Yesterday: [defineds.startOfYesterday, defineds.endOfYesterday],
        'This Week': [defineds.startOfWeek, defineds.endOfToday],
        'This Month': [defineds.startOfMonth, defineds.endOfToday],
        'This Quarter': [defineds.startOfQuarter, defineds.endOfToday],
        'This Calendar Year': [defineds.startOfYear, defineds.endOfToday],
        'All Time': [new Date(authDetails?.selectedVenue?.createdAt), defineds.endOfToday],
      };
      setDateRange(dateRanges[label] || [null, null]);
      setDateRangePickerLabel(label);
    },
    []
  );

  useEffect(() => {
    setExportType(selectedRowsCount !== 0 ? 3 : 1);
    setUserTypeFilter(0);
  }, [isOpen]);

  useEffect(() => {
    handleDateRangeChange(dateRange);
  }, [dateRange, handleDateRangeChange]);

  const handleRadioChange = (option) => {
    setExportType(option.exportType);
    setUserTypeFilter(option.userTypeFilter);
    handleExportUserTypeFilter(option.userTypeFilter)
  };

  return (
    <CustomerCustomModal
      isOpen={isOpen}
      handleModal={handleModal}
      title="Export customers"
      width="464px"
      // height="38rem"
      titleTextSize="14"
    >
      <div className="w-100 p-3">
        <ExportWrapper {...allThemeData}>
          <h6 className="nunito-font mb-2">Customers selected</h6>
          {radioOptions.map((option, index) => (
            <div key={index} className="d-flex align-items-start">
              <div style={{ paddingTop: '2px', height: '26px' }}>
              <FormRadioButton
                style={{ gap: '0px' }}
                name="exportType"
                className="newThemeRadio"
                checked={exportType === option.exportType && userTypeFilter === option.userTypeFilter}
                onChange={() => handleRadioChange(option)}
              /> </div>
              <p className="nunito-font ml-10 mb-1">{option.label}</p>
            </div>
          ))}
        </ExportWrapper>
        <HeaderWrapper {...allThemeData}>
          <div className="filterWrapper">
            <div className="dateDropdownBox d-flex ml-20">
              {/* Date Dropdown */}
              <Dropdown
                isOpen={true} // Always open
                toggle={() => { }}
                className={`dashboard-dropdown dateDropdown mb-2 mr-10 ${exportType === 1 ? 'disabled' : ''}`}
              >
                <DropdownToggle
                  style={{ background: '#f9f9f9', color: '#212529' }}
                  className="dropdownToggle dateDropdownToggle d-flex justify-content-between align-items-center"
                  disabled={exportType !== 1} // Disable for options 1 and 2
                >
                  <span className="dropdown-name fs-13 medium-text flex-1">{dateRangePickerLabel}</span>
                </DropdownToggle>
                <DropdownMenu className="datePickerDropdown pt-8 pl-4 pr-12" style={{ maxHeight: '270px' }}>
                {exportType !== 1 ? <div className='dropdwon-overlay'></div> : <></>}
                  <div className="d-flex flex-column flex-sm-row">
                    <div className="dropdownCalenderParent">
                      <div className="p-0 dropdown-item dropdownCalender">
                        <CustomDateRangePicker dateRange={dateRange} handleDateRange={handleDateRange}  disabled={exportType !== 1} />
                      </div>
                    </div>
                    <div>
                      {dateLabels.map((label) => (
                        <DropdownItem
                          key={label}
                          className={`${dateRangePickerLabel === label ? 'active' : ''
                            } calenderItem`}
                          onClick={() => handleDateRangeLabel(label)}
                          disabled={exportType !== 1}
                        >
                          {label}
                        </DropdownItem>
                      ))}
                    </div>
                  </div>
                </DropdownMenu>
              </Dropdown>
            </div>
          </div>
        </HeaderWrapper>
      </div>
      <hr style={{ margin: '16rem 0 0 0' }} />
      <div className="d-flex align-items-center justify-content-end p-2" style={{ gap: '12px' }}>
        <Button className="borderButtonFullWidth" onClick={handleModal}>
          Cancel
        </Button>
        <CustomButton
          className="themeButtonFullWidth"
          loading={loading}
          onClick={() => handleExport(exportType)}
        >
          Export customers
        </CustomButton>
      </div>
    </CustomerCustomModal>
  );
};

export default ExportModal;