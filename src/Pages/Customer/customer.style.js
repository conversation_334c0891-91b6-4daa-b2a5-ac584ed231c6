import styled from 'styled-components';

export const CustomerWrraper = styled.div`
	font-family: nunitosans-regular !important;

	.activeText {
		color: #f95c69 !important;
		text-decoration: underline;
		font-family: nunitosans-regular;
		font-size: 16px;
		cursor: pointer;
	}

	.visit-text {
		font-family: nunitosans-regular;
	}

	.customer-faqs {
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.saveCancelBtnWrapper {
		display: flex;
		align-items: center;
		gap: 15px;
		padding-top: 24px;
	}

	.titleWrap {
		display: flex;
		flex-direction: column;

		.mainTitle {
			font-size: 32px;
			font-family: nunitosans-bold;
			color: #202224;
		}

		.headerClass {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-bottom: 14px;
		}

		.mainParagraph {
			font-size: 16px;
			font-family: nunitosans-regular;
			color: #202224;
			padding-bottom: 18px;
		}
	}

	.search-icon {
		background-color: #fbfcff !important;
	}

	.input-group {
		.inputGroup,
		formGroupIcon {
			background-color: #fbfcff !important;
		}
		.inputBox {
			background-color: #fbfcff !important;
			font-family: nunitosans-regular !important;
			color: #a4a5a7 !important;
			font-size: 16px !important;
		}
	}

	.searchBoxWrapper {
		width: 198px !important;
		@media only screen and (max-width: 1099px) {
			width: 100% !important;
		}
	}

	.table-data {
	}

	.table-count {
		background: #fbfcff;
		height: 3.81vw;
		min-height: 2em;
		border: 0.6px solid rgb(148, 150, 152, 0.5);
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 1em 1.5em;
	}

	.table-count-text-one {
		font-family: nunitosans-bold;
		color: #202224;
		font-size: 16px;
	}
	.table-count-text-two {
		font-family: nunitosans-regular;
		color: #a4a5a7;
		font-size: 16px;
	}

	@media (max-width: 600px) {
		.titleWrap {
			.mainTitle {
				font-size: 21px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 11px;
				color: #202224;
				font-family: nunitosans-regular;
			}
		}

		.table-count {
			height: 4.1vw;
			border-radius: 4px;
			display: flex;
			justify-content: space-between;
			padding: 0.8em 1.2em;
		}

		.table-count-text-one {
			font-family: nunitosans-bold;
			font-size: 12px;
		}

		.table-count-text-two {
			font-family: nunitosans-regular;
			font-size: 12px;
		}

		.activeText,
		.visit-text {
			font-size: 12px;
		}
	}

	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.titleWrap {
			.mainTitle {
				font-size: 21px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 11px;
				font-family: nunitosans-regular;
			}
		}

		.table-count {
			height: 4.1vw;
			border-radius: 4px;
			display: flex;
			justify-content: space-between;
			padding: 0.8em 1.2em;
		}

		.table-count-text-one {
			font-family: nunitosans-bold;
			font-size: 12px;
		}

		.table-count-text-two {
			font-family: nunitosans-regular;
			font-size: 12px;
		}

		.activeText,
		.visit-text {
			font-size: 12px;
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.titleWrap {
			.mainTitle {
				font-size: 24px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 12px;
				font-family: nunitosans-regular;
			}
		}

		.table-count {
			height: 4.1vw;
			border-radius: 4px;
			display: flex;
			justify-content: space-between;
			padding: 0.8em 1.2em;
		}

		.table-count-text-one {
			font-family: nunitosans-bold;
			font-size: 12px;
		}

		.table-count-text-two {
			font-family: nunitosans-regular;
			font-size: 12px;
		}

		.activeText,
		.visit-text {
			font-size: 12px;
		}
	}
`;

export const PageWrapper = styled.div`
	color: ${(props) => props.layoutTheme.textColor} !important;
`;
