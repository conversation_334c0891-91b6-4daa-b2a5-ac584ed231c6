import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
	DropdownItem,
	DropdownMenu,
	DropdownToggle,
	UncontrolledDropdown
} from 'reactstrap';
import TableSkeleton from './TableSkeleton';

import TableComponent from '../../Components/Common/TableComponent';
import { VenuePanelRoutes } from '../../Utils/routes';

import DropdownIconWOCircle from '../../Assets/images/newDropdownIconWOCircle.svg';
import TableStyle from './TableStyle';
import NewSearchBox from '../../Components/Form/NewSearchBox/NewSearchBox';
import { SearchIcon } from '../../Components/Icons/SearchIcon/SearchIcon';
import { NewPagination } from '../../Components/Common/NewPagination';

const CustomersTable = ({
	loading,
	tableData,
	tableColumns,
	handlePageChange,
	handleSearchInputChange,
	handleSortByChange,
	totalRows,
	currentPage,
	sortByData,
	selectedRows,
	setSelectedRows,
	sortBy
}) => {
	const navigate = useNavigate()
	const [sortByValue, setSortByValue] = useState(sortBy);
	const name = sortByData.find((value) => value.value === sortBy)?.name || 'Newest - Oldest';
	const [sortByName, setSortByName] = useState(name);

	useEffect(() => {
		handleSortByChange(sortByValue);
	}, [sortByValue]);

	const handleRowClick = (rowData) => {
		navigate(VenuePanelRoutes.customerDetails + `/${rowData?._original?.id}`, {state : {sortOrder: sortBy}})
	}

	return (
		<div>
			<TableStyle version={6}>
				<div className="search-sort">
					<NewSearchBox
						formGroupClassName="formGroupClassName"
						labelClassName="labelClassName"
						inputGroupTextClassName="inputGroupTextClassName"
						inputClassName="inputClassName table-count-text-two"
						iconBackgroundClass="iconBackgroundClass"
						type="text"
						name="search"
						placeholder="Search customers"
						icon={
							<SearchIcon className="inputIcon" />
						}
						iconPlacement="start"
						onChange={(event) => {
							handleSearchInputChange(event?.target?.value);
						}}
					/>

					<div className="d-flex align-items-center gap-3 ">
						<UncontrolledDropdown>
							<DropdownToggle
								style={{
									paddingInline: 0,
									background: 'transparent',
									border: 'none',
									outline: 'none'
								}}
							>
								<div className="d-flex align-items-center gap-3">
									<p className="table-count-text-one">{sortByName}</p>
									<img
										caret
										src={DropdownIconWOCircle}
										className='dropdown-image'
										alt="dropdownIcon"
									/>
								</div>
							</DropdownToggle>
							<DropdownMenu className="customer-dropdown-menu">
								{sortByData.map((value, index) => (
									<DropdownItem
										key={index}
										onClick={() => {
											setSortByValue(value.value);
											setSortByName(value.name);
										}}
									>
										{value.name}
									</DropdownItem>
								))}
							</DropdownMenu>
						</UncontrolledDropdown>
					</div>
				</div>
				{loading ? (
					<>
						{/* <SearchSortSkeleton /> */}
						<TableSkeleton />
					</>
				) : (

					<>

						<TableComponent
							columns={tableColumns}
							data={tableData}
							internalID={"id"}
							selectedRows={selectedRows}
							setSelectedRows={setSelectedRows}
							NoDataText={'No data found'}
							handleRowClick={handleRowClick}
							checkbox
						/>
						{tableData?.length !== 0 && (
							<div className="pa-t-8">
								<NewPagination
									handlePageChange={handlePageChange}
									total={totalRows}
									pageSize={50}
									currentPage={currentPage}
								/>
							</div>
						)}
					</>
				)}
			</TableStyle>
		</div>
	);
};

export default CustomersTable;
