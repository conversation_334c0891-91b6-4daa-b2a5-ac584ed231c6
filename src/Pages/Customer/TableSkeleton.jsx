import React from 'react';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import TableStyle from './TableStyle';
import TableV6 from '../../Components/Common/TableV6';

const data = [
	// {
	// 	id: 1,
	// 			birthday: 45/54/52,
	// 			fullName: 'ajdh',
	// 			email: 'yus@fsg.d',
	// 			mobile: '54564546',
	// 			orderCount: 8,
	// 			hightestSpent: 656,
	// 			totalOrderSpent: 154,
	// 			avgOrderTotal: 25654,
	// },
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		taxAmount: '80%'
	},
	{
		id: 1,
		name: 'Export Tax for State Transport',
		taxAmount: '45%'
	},
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		taxAmount: '80%'
	},
	{
		id: 1,
		name: 'Export Tax for State Transport',
		taxAmount: '45%'
	},
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		taxAmount: '80%'
	},
	{
		id: 1,
		name: 'Export Tax for State Transport',
		taxAmount: '45%'
	},
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		taxAmount: '80%'
	},
	{
		id: 1,
		name: 'Export Tax for State Transport',
		taxAmount: '45%'
	},
	{
		id: 2,
		name: 'TD Tax on Food Businesses',
		taxAmount: '80%'
	}
];

const tableColumns = [
	{
		id: 'select',
		className: 'justify-content-start',
		style: { flex: 1 },
		minWidth: 15,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Customer name',
		accessor: 'fullName',
		className: 'justify-content-start',
		style: { flex: 1 },
		minWidth: 120,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Email',
		accessor: 'email',
		className: 'justify-content-start',
		style: { flex: 2 },
		// minWidth: 260,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Mobile',
		accessor: 'mobile',
		className: 'justify-content-start',
		style: { flex: 1 },
		// minWidth: 130,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Subscription',
		accessor: 'userSubscription',
		className: 'justify-content-center',
		filterable: false,
		sortable: false,
		style: { flex: 1 },
		// minWidth: 140,
		headerClassName: 'justify-content-center',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Orders',
		accessor: 'orderCount',
		className: 'justify-content-start',
		filterable: false,
		sortable: false,
		style: { flex: 1 },
		minWidth: 80,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Total Spend',
		accessor: 'totalOrderSpent',
		className: 'justify-content-start',
		style: { flex: 1 },
		minWidth: 70,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	}
];

const TableSkeleton = () => {
	return (
		<TableStyle version={6}>
			<TableV6 columns={tableColumns} data={data} />
		</TableStyle>
	);
};

export default TableSkeleton;
