import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { useFormik } from 'formik';
import fileDownload from 'js-file-download';
import { formatResponseData, tableColumns } from '../Customer/utils';
import { useDebounce } from '../../Hooks/useDebounce';
import { FilledButton } from '../../Components/Layout/Buttons';
import { VenueApiRoutes, VenuePanelRoutes } from '../../Utils/routes';
import { CustomerWrraper } from './customer.style';
import CustomersTable from './CustomersTable';
import Api from '../../Helper/Api';
import moment from 'moment';
import ExportModal from './ExportModal';
import NewPageWrapper from '../../Components/Common/NewPageWrapper';
import NewPageTitle from '../../Components/Common/NewPageTitle';

const Customer = () => {
	const state = useSelector((state) => ({ ...state }));
	const navigate = useNavigate();
	const location = useLocation();
	const queryParams = new URLSearchParams(location.state);
	const initialSortOrder = queryParams.get('sortOrder');
	const [loading, setLoading] = useState(false);
	const [exportLoading, setExportLoading] = useState(false);
	const [currentPage, setCurrentPage] = useState(1);
	const [sortBy, setSortBy] = useState(
		initialSortOrder != null ? initialSortOrder : 'newToOld'
	);
	const [tableData, setTableData] = useState([]);
	const [tableDataCount, setTableDataCount] = useState(null);
	const [searchTerm, setSearchTerm] = useState('');
	const debounceSearchTerm = useDebounce(searchTerm, 500);
	const [exportModal, setExportModal] = useState(false);
	const [selectedRows, setSelectedRows] = useState([]);
	const [exportUserTypeFilter, setExportUserTypeFilter] = useState(0);

	// Memoized handler to avoid re-renders
	const handleExportModal = useCallback(() => {
		setExportModal((prev) => !prev);
	}, []);

	const [dateRange, setDateRange] = useState([
		moment(new Date()).format('YYYY-MM-DD'),
		moment(new Date()).format('YYYY-MM-DD')
	]);

	const sortByData = [
		{
			id: 1,
			name: 'Newest - Oldest',
			value: 'newToOld'
		},
		{
			id: 2,
			name: 'Oldest - Newest',
			value: 'oldToNew'
		},
		{
			id: 3,
			name: 'Order Total: Most - Least',
			value: 'highestOrderTotalCount'
		},
		{
			id: 4,
			name: 'Order Total: Least - Most',
			value: 'lowestOrderTotalCount'
		},
		{
			id: 5,
			name: 'Individual Purchase: Highest- Lowest',
			value: 'highestSpent'
		},
		{
			id: 6,
			name: 'Individual Purchase: Lowest - Highest',
			value: 'lowestSpent'
		},
		{
			id: 7,
			name: 'Total Spend: Highest - Lowest',
			value: 'highestTotalOrderSpent'
		},
		{
			id: 8,
			name: 'Total Spend: Lowest - Highest',
			value: 'lowestTotalOrderSpent'
		}
	];

	const { handleChange, handleBlur, values } = useFormik({
		initialValues: {
			image: null,
			firstName: '',
			lastName: '',
			venueName: '',
			ownerManagerName: '',
			email: '',
			password: '',
			countryCode: '+61',
			mobileNumber: '',
			address: '',
			abnAcn: '',
			isAlcohol: null,
			licenseNumber: '',
			serviceType: [],
			latitude: '',
			longitude: '',
			acceptTerms: true
		}
	});

	const authDetails = state?.auth;
	const selectedVenue = authDetails?.selectedVenue;

	// Fetch table data
	useEffect(() => {
		(async () => {
			await getTableData({
				bar_id: selectedVenue?.id,
				search: debounceSearchTerm,
				page: currentPage,
				sortBy: sortBy,
				isDiscount: false
			});
		})();
	}, [currentPage, debounceSearchTerm, selectedVenue?.id, sortBy]);

	const getTableData = async (payload) => {
		try {
			setLoading(true);
			const res = await Api(
				'POST',
				VenueApiRoutes.getCustomerList,
				payload
			);
			if (res?.data?.status) {
				const tableDetails = formatResponseData(res?.data?.data?.list);
				setTableData(tableDetails);
				setTableDataCount(res?.data?.data?.total);
			} else {
				toast.error(res?.data?.message);
			}
			setLoading(false);
		} catch (err) {
			setLoading(false);
			if (err?.message) toast.error(err?.message);
		}
	};

	const handleDateRangeChange = (data) => {
		const date1 = moment(data[0]).isValid()
			? moment(data[0]).format('YYYY-MM-DD')
			: '';
		const date2 = moment(data[1]).isValid()
			? moment(data[1]).format('YYYY-MM-DD')
			: '';
		setDateRange([date1, date2]);
	};

	const handleExportUserTypeFilter = useCallback((data) => {
		setExportUserTypeFilter(data === 1 ? 1 : 0);
	}, []);

	const handleExport = async (exportType) => {
		try {
			if (exportType === 3 && selectedRows.length === 0) {
				toast.error('No customers selected for export');
			} else {
				setExportLoading(true);
				const res = await Api(
					'POST',
					VenueApiRoutes.exportCustomerTableData,
					{
						bar_id: selectedVenue?.id,
						search: debounceSearchTerm,
						page: currentPage,
						sortBy: sortBy,
						type: exportType,
						customerIds: selectedRows,
						startDate: dateRange[0] || '',
						endDate: dateRange[1] || '',
						userTypeFilter: exportUserTypeFilter
					}
				);
				const blob = new Blob([res?.data?.data], {
					type: 'text/plain;charset=utf-8'
				});
				if (tableData.length) {
					setExportModal(false);
					const fileName = `${
						selectedVenue?.restaurantName
					} MyTab Customers (${moment().format('DD-MM-YYYY')}).csv`;
					fileDownload(blob, fileName);
					toast.success('Report exported successfully.');
				} else {
					toast.error('No data available for export.');
				}
				setExportLoading(false);
			}
		} catch (err) {
			setExportLoading(false);
			if (err?.message) toast.error(err?.message);
		}
	};

	const handleSortByChange = (sortBy) => {
		setSortBy(sortBy);
	};

	const handlePageChange = ({ selected }) => {
		let pageNo = selected + 1;
		setCurrentPage(pageNo);
	};
	const handleSearchInputChange = async (value) => {
		setSearchTerm(value);
		setCurrentPage(1);
	};

	// const handleRowClick = (rowData) => {
	// 	navigate(VenuePanelRoutes.customerDetails + `/${rowData?._original?.id}`)
	// }

	return (
		<NewPageWrapper>
			<CustomerWrraper>
				<div className="titleWrap">
					<div className="headerClass">
						<NewPageTitle>Customers</NewPageTitle>
						<FilledButton
							onClick={handleExportModal}
							buttonText={'Export'}
							background={'#d8d9f9'}
							color={'#3D42DF'}
							style={{
								width: '160px',
								border: '1px solid #3D42DF'
							}}
						/>
					</div>
					<p className="mainParagraph">
						New customer details are added to your database with
						each order. You may use this information for order
						fulfillment, technical support, and marketing campaigns
						via email or text, provided customers have opted in and
						you comply with the Spam Act 2003. In-app advertising
						can be used for all customers regardless of subscription
						status.
					</p>
					<p className="mainParagraph">
						<i>
							Note: An anonymous user refers to a customer who
							previously had a MyTab account but has since deleted
							it. While their total spend is recorded, all
							personal identifying details have been removed.
						</i>
					</p>
				</div>

				<div className="table-count">
					<div className="table-count-text-one">
						{tableDataCount} customers
					</div>
					<div className="table-count-text-two">
						Showing {tableDataCount < 50 ? tableDataCount : 50} of{' '}
						{tableDataCount} customers
					</div>
				</div>

				<CustomersTable
					className="table-data"
					loading={loading}
					tableColumns={tableColumns}
					tableData={tableData}
					handlePageChange={handlePageChange}
					handleSearchInputChange={handleSearchInputChange}
					totalRows={tableDataCount}
					currentPage={currentPage}
					sortByData={sortByData}
					selectedRows={selectedRows}
					setSelectedRows={setSelectedRows}
					sortBy={sortBy}
					// handleRowClick={handleRowClick}
					handleSortByChange={handleSortByChange}
				/>
				<div className="customer-faqs mt-3">
					<span className="visit-text">Visit</span>&nbsp;
					<p
						className="activeText"
						onClick={() => navigate(VenuePanelRoutes.support)}
					>
						Customers FAQ Forum
					</p>
				</div>
				<ExportModal
					isOpen={exportModal}
					handleModal={handleExportModal}
					selectedRowsCount={selectedRows.length}
					handleExport={handleExport}
					handleDateRangeChange={handleDateRangeChange}
					handleExportUserTypeFilter={handleExportUserTypeFilter}
					loading={exportLoading}
				/>
			</CustomerWrraper>
		</NewPageWrapper>
	);
};

export default Customer;
