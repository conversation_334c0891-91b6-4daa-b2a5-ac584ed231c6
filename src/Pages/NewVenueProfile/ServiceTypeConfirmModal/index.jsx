import NewModal from '../../../Components/Common/NewModal';
import { StylesWrapper } from './index.styles';

const ServiceTypeConfirmModal = ({
	handleModal,
	isOpen,
	switchLoading,
	handleConfirm,
	modalData
}) => {
	const handleYes = async () => {
		await handleConfirm(1);
		handleModal();
	};
	return (
		<NewModal
			isOpen={isOpen}
			toggle={handleModal}
			title={'Update service type'}
			handleSubmitButtonClick={handleYes}
			submitButtonLoading={switchLoading}
			submitButtonText="Yes"
			cancelButtonText="No"
		>
			<StylesWrapper>
				{modalData?.content}
				<br />
				Are you sure you want to update service type?
			</StylesWrapper>
		</NewModal>
	);
};

export default ServiceTypeConfirmModal;
