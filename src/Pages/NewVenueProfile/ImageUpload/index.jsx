import React, { useCallback, useEffect, useRef, useState } from 'react';
import { <PERSON>rop<PERSON> } from 'react-cropper';

import { NewCamera } from '../../../Components/Icons';
import { getFileExtension } from '../../../Helper/helper';
import { StyleWrapper } from './index.style';
import NewModal from '../../../Components/Common/NewModal';
import 'cropperjs/dist/cropper.css';

const ImageUpload = ({ value, onChange, ...props }) => {
	const [customFile, setCustomFile] = useState(null);
	const [tempCustomFile, setTempCustomFile] = useState(null);
	// file = {
	//   url: String or null,
	//   originalFile: File object or null
	// }
	const [isOpenCropModal, setIsOpenCropModal] = useState(false);
	const fileInput = useRef(null);
	const cropperRef = useRef(null);

	const handleUploadButtonClick = () => {
		fileInput?.current?.click();
	};

	const handleFileInputChange = (event) => {
		if (event?.target?.files?.length > 0) {
			setTempCustomFile({
				url: URL.createObjectURL(event.target.files[0]),
				originalFile: event.target.files[0]
			});
			setIsOpenCropModal(true);
		}
	};

	const handleCrop = useCallback(() => {
		const imageExt = getFileExtension(tempCustomFile?.originalFile?.name);
		cropperRef?.current?.cropper?.getCroppedCanvas()?.toBlob((blob) => {
			const imageFile = new File([blob], `${Date.now()}.${imageExt}`, {
				type: blob?.type
			});
			const blobUrl = URL.createObjectURL(imageFile);
			if (onChange) {
				onChange({
					url: blobUrl,
					originalFile: imageFile
				});
			} else {
				setCustomFile({
					url: blobUrl,
					originalFile: imageFile
				});
			}
			setIsOpenCropModal(false);
		}, customFile?.originalFile?.type);
	}, [tempCustomFile]);

	useEffect(() => {
		if (value) {
			setCustomFile(value);
		} else {
			setCustomFile(null);
		}
	}, [value]);
	return (
		<StyleWrapper>
			<input
				type="file"
				ref={fileInput}
				accept="image/*"
				hidden
				multiple={false}
				onChange={handleFileInputChange}
			/>
			<div className="uploadedImageWrapper">
				{customFile?.url ? (
					<img
						src={customFile?.url}
						alt="product-image"
						onClick={handleUploadButtonClick}
					/>
				) : (
					<div
						className="uploadButtonWrapper"
						onClick={handleUploadButtonClick}
					>
						<div className="uploadButton">
							<span className="label1">
								Click to select an image
							</span>
							<span className="label2">PNG,JPG up to 2MB</span>
						</div>
					</div>
				)}
				<div className="cameraButton" onClick={handleUploadButtonClick}>
					<div className="cameraIcon">
						<NewCamera
							width="100%"
							height="100%"
							fill={'#414141'}
						/>
					</div>
				</div>
			</div>
			{isOpenCropModal && (
				<NewModal
					isOpen={isOpenCropModal}
					toggle={() => setIsOpenCropModal(false)}
					className="venueProfileImageCropModal"
					submitButtonText="Crop Image"
					handleSubmitButtonClick={handleCrop}
				>
					<div style={{ padding: '16px' }}>
						<Cropper
							ref={cropperRef}
							src={tempCustomFile?.url}
							style={{ height: '100%', width: 600 }}
							aspectRatio={16 / 9} // Set aspect ratio as per requirements
							guides={true}
							viewMode={3}
							minCropBoxHeight={10}
							minCropBoxWidth={10}
							background={false}
							responsive={true}
							autoCropArea={1}
							checkOrientation={false}
						/>
					</div>
				</NewModal>
			)}
		</StyleWrapper>
	);
};

export default ImageUpload;
