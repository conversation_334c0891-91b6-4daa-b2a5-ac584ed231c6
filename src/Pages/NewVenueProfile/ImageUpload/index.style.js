import styled from 'styled-components';

export const StyleWrapper = styled.div`
	.uploadedImageWrapper {
		background-color: #ffffff;
		border: 1px solid rgba(49, 49, 50, 0.35);
		position: relative;
		width: 100%;
		height: 177px;
		padding: 8px;
		img {
			display: block;
			width: 100%;
			height: 100%;
			cursor: pointer;
		}
		.uploadButtonWrapper {
			width: 100%;
			height: 100%;
			cursor: pointer;
			.uploadButton {
				width: 100%;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				gap: 2px;
				.label1 {
					color: rgba(1, 1, 1, 1);
					font-family: 'nunitosans-regular';
					font-size: 12px;
				}
				.label2 {
					color: rgba(1, 1, 1, 0.6);
					font-family: 'nunitosans-regular';
					font-size: 12px;
				}
			}
		}
		.cameraButton {
			position: absolute;
			top: 100%;
			left: 100%;
			transform: translate(-120%, -120%);
			background-color: #ececee;
			width: 52px;
			height: 52px;
			border-radius: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			.cameraIcon {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 17px;
				height: 14px;
			}
		}
	}
	@media (max-width: 600px) {
		.uploadedImageWrapper {
			height: 120px;
			.uploadButtonWrapper {
				.uploadButton {
					gap: 2px;
					.label1 {
						font-size: 10px;
					}
					.label2 {
						font-size: 10px;
					}
				}
			}
			.cameraButton {
				width: 35px;
				height: 35px;
				.cameraIcon {
					width: 12px;
					height: 10px;
				}
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.uploadedImageWrapper {
			height: 133px;
			.uploadButtonWrapper {
				.uploadButton {
					gap: 2px;
					.label1 {
						font-size: 10px;
					}
					.label2 {
						font-size: 10px;
					}
				}
			}
			.cameraButton {
				width: 39px;
				height: 39px;
				.cameraIcon {
					width: 13px;
					height: 11px;
				}
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.uploadedImageWrapper {
			height: 133px;
			.uploadButtonWrapper {
				.uploadButton {
					gap: 2px;
					.label1 {
						font-size: 10px;
					}
					.label2 {
						font-size: 10px;
					}
				}
			}
			.cameraButton {
				width: 39px;
				height: 39px;
				.cameraIcon {
					width: 13px;
					height: 11px;
				}
			}
		}
	}
`;
