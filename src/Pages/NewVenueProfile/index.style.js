import styled from 'styled-components';

export const StylesWrapper = styled.div`
	.subTitleText {
		color: #202224;
		font-family: 'nunitosans-bold';
		font-size: 18px;
		line-height: 1;
		padding-block: 15px;
	}
	.formGridContainer {
		display: grid;
		grid-template-columns: 1fr 1fr;
		grid-gap: 0 27px;
		@media (max-width: 959px) {
			grid-template-columns: 1fr;
			grid-gap: 0 18px;
		}
		@media only screen and (min-width: 960px) and (max-width: 1299px) {
			grid-template-columns: 1fr 1fr;
			grid-gap: 0 20px;
		}
		@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
			grid-template-columns: 1fr 1fr;
			grid-gap: 0 20px;
		}
	}
	.formFieldWrapper {
		padding-bottom: 20px;
		@media (max-width: 959px) {
			padding-bottom: 14px;
		}
		@media only screen and (min-width: 960px) and (max-width: 1299px) {
			padding-bottom: 15px;
		}
		@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
			padding-bottom: 15px;
		}
	}
	.ownerManagerName,
	.venueName {
		@media (max-width: 959px) {
			padding-bottom: 14px;
		}
	}
	.venueAddressField {
		cursor: pointer !important;
		.newCustomInput {
			cursor: pointer !important;
		}
	}
	.saveDeleteBtnWrapper {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		gap: 15px;
		padding-top: 20px;
	}
	@media (max-width: 600px) {
		.subTitleText {
			font-size: 13px;
			padding-block: 10px;
		}
		.saveDeleteBtnWrapper {
			gap: 12px;
			padding-top: 15px;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.subTitleText {
			font-size: 14px;
			padding-block: 11px;
		}
		.saveDeleteBtnWrapper {
			gap: 12px;
			padding-top: 15px;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.subTitleText {
			font-size: 14px;
			padding-block: 11px;
		}
		.saveDeleteBtnWrapper {
			gap: 12px;
			padding-top: 15px;
		}
	}
`;
