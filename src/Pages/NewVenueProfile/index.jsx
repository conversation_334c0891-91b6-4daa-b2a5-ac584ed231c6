import React, { useEffect, useRef, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { PopupButton } from '@typeform/embed-react';

import { OpeningHoursModalDetails } from '../../Components/UserProfile/ConnectVenue/utils';
import NewPageTitle from '../../Components/Common/NewPageTitle';
import NewPageWrapper from '../../Components/Common/NewPageWrapper';
import NewFormCheckbox from '../../Components/NewForm/NewFormCheckbox';
import NewFormErrorMessage from '../../Components/NewForm/NewFormErrorMessage';
import NewFormInput from '../../Components/NewForm/NewFormInput';
import NewFormLabel from '../../Components/NewForm/NewFormLabel';
import NewFormMobileNoInput from '../../Components/NewForm/NewFormMobileNoInput';
import { FilledButton } from '../../Components/Layout/Buttons';
import * as validation from '../../Helper/YupValidation';
import Api from '../../Helper/Api';
import authActions from '../../Redux/auth/actions';
import { AdvertiserApiRoutes, VenueApiRoutes, VenuePanelRoutes } from '../../Utils/routes';
import ImageUpload from './ImageUpload';
import MapModal from './MapModal';
import ServiceTypeConfirmModal from './ServiceTypeConfirmModal';
import { StylesWrapper } from './index.style';
import NewLoader from '../../Components/Common/NewLoader';
import NewFormSelect from '../../Components/NewForm/NewFormSelect';

const allowedFileTypes = ['image/jpg', 'image/jpeg', 'image/png'];
const FILE_SIZE = 20 * 1024 * 1024;

const NewVenueProfile = () => {
    const ref = useRef();
    const openPopup = () => ref.current?.open();
    const state = useSelector((state) => ({ ...state }));
    const authData = state.auth;
    const bars = authData?.bars;
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [openingHoursModalData, setOpeningHoursModalData] = useState(
        OpeningHoursModalDetails
    );
    const [isOpenMapModal, setIsOpenMapModal] = useState(false);
    const [isAlcohol, setIsAlcohol] = useState('No');
    const [loading, setLoading] = useState(false);
    const [getDetailsLoading, setGetDetailsLoading] = useState(false);
    const [typeFormId, setTypeFormId] = useState('');
    const [saveLoading, setSaveLoading] = useState(false);
    const [abnAcnValid, setAbnAcnValid] = useState(null);
    const [serviceTypeConfirmModal, setServiceTypeConfirmModal] =
        useState(false);
    const [serviceTypeModalData, setServiceTypeModalData] = useState({
        type: '',
        content: ''
    });
    const [confirmAction, setConfirmAction] = useState(0);
    const [timezones, setTimezones] = useState([]);

    const validationSchema = yup.object().shape({
        image: yup.mixed().required('Please upload venue profile photo.'),
        venueName: validation.YUP_VALIDATION.VENUE_NAME,
        ownerManagerName: validation.YUP_VALIDATION.OWNER_MANAGER_NAME,
        email: validation.YUP_VALIDATION.EMAIL,
        countryCode: validation.YUP_VALIDATION.COUNTRY_CODE,
        address: validation.YUP_VALIDATION.ADDRESS,
        abnAcn: validation.YUP_VALIDATION.ABN_ACN_NUMBER,
        isAlcohol: validation.YUP_VALIDATION.IS_ALCOHOL,
        licenseNumber:
            isAlcohol === 'Yes' && validation.YUP_VALIDATION.LICENSE_NUMBER
        // serviceType: validation.YUP_VALIDATION.SERVICE_TYPE
    });

    const handleFileUploadChange = (customFileObject) => {
        const isAllowed = allowedFileTypes.includes(
            customFileObject?.originalFile?.type
        );
        if (!isAllowed) {
            toast.error('You can only upload jpg, jpeg or png images.');
            return;
        }
        if (customFileObject?.originalFile?.size > FILE_SIZE) {
            toast.error('Image size must be less than 20Mb.');
            return;
        }
        setFieldValue('image', customFileObject);
    };

    const fetchTimezones = async () => {
        try {
            const res = await Api('GET', AdvertiserApiRoutes?.getTimezones);
            if (res?.data?.status) {
                setTimezones(res?.data?.data || []);

                // If venue has a timezone set, use it
                if (authData?.selectedVenue?.timezone) {
                    setFieldValue('timeZone', authData?.selectedVenue?.timezone);
                    console.log("Setting timezone from venue data:", authData?.selectedVenue?.timezone);
                }
            } else {
                console.error('Failed to fetch timezones:', res?.data?.message);
            }
        } catch (err) {
            console.error('Error fetching timezones:', err?.message);
        }
    };

    useEffect(() => {
        getFormData();
        fetchTimezones();
    }, [authData?.selectedVenue?.id]);

    const handleMapModal = () => {
        setIsOpenMapModal((prev) => !prev);
    };

    const handleServiceTypeConfirmModal = () => {
        setServiceTypeConfirmModal((prev) => !prev);
        getFormData(true);
    };

    const handleDeleteConfirmModal = () => {
        openPopup();
    };

    useEffect(() => {
        if (typeFormId !== '') {
            handleDeleteConfirmModal();
        }
    }, [typeFormId]);

    const handleReadyTypeForm = () => { };

    const handleSubmitTypeForm = (event) => {
        setTypeFormId('');
        const { formId, responseId } = event;
        if (formId) {
            ref.current?.close();
            handleVenueDelete();
        }
    };

    const handleCloseTypeForm = () => {
        setTypeFormId('');
        if (ref.current) {
            ref.current = null;
        }
    };

    const handleErrorTypeForm = (error) => { };

    const handleVenueDelete = async () => {
        try {
            let barId = authData?.selectedVenue?.id;
            const res = await Api('DELETE', VenueApiRoutes.delete, {
                bar_id: authData?.selectedVenue?.id?.toString()
            });
            if (res?.data?.status) {
                navigate(VenuePanelRoutes.connectVenue);
                let updatedBars = [];
                if (bars?.length > 0) {
                    bars?.forEach((item) => {
                        if (item?.id != barId) {
                            updatedBars?.push(item);
                        }
                    });
                }
                if (updatedBars?.length > 0) {
                    dispatch(authActions.set_selected_venue(updatedBars[0]));
                } else {
                    dispatch(authActions.set_selected_venue(null));
                }
                dispatch(authActions.update_venue_list(updatedBars));
                toast.success(res?.data?.message);
            } else {
                toast.error(res?.data?.message);
            }
        } catch (err) {
            setLoading(false);
            if (err?.response?.data?.message) {
                toast.error(err?.response?.data?.message);
            }
        }
    };

    const getDeleteTypeFormId = async () => {
        setLoading(true);
        try {
            const res = await Api(
                'POST',
                VenueApiRoutes.getAccountDeleteTypeForm,
                {
                    bar_id: authData?.selectedVenue?.id
                }
            );
            if (res?.data?.status) {
                const link = res?.data?.data?.link;
                setTypeFormId(link);
            } else {
                toast.error(res?.data?.message);
            }
        } catch (err) {
            setLoading(false);
            if (err?.message) {
                toast.error(err?.message);
            }
        }
    };

    const getFormData = async (updateSelectedVenue = false) => {
        setGetDetailsLoading(true);
        try {
            const res = await Api(
                'POST',
                VenueApiRoutes.getSingleConnectedVenue,
                {
                    bar_id: authData?.selectedVenue?.id
                }
            );
            setGetDetailsLoading(false);
            let venueData = res?.data?.data;
            if (res?.data?.status) {
                setValues({
                    image: venueData?.avatar
                        ? { url: venueData?.avatar, originalFile: null }
                        : null,
                    venueName: venueData?.restaurantName,
                    ownerManagerName: venueData?.managerName,
                    email: venueData?.email,
                    countryCode: venueData?.countryCode,
                    address: venueData?.address,
                    abnAcn: venueData?.businessRegisterId,
                    isAlcohol: venueData?.isVenueServeAlcohol,
                    licenseNumber: venueData?.liquorLicenseNumber,
                    mobile: venueData?.mobile,
                    serviceType:
                        venueData?.serviceType === 'BOTH'
                            ? ['PICKUP', 'TABLE']
                            : [`${venueData?.serviceType}`],
                    latitude: venueData?.latitude,
                    longitude: venueData?.longitude,
                    timeZone: venueData?.timezone || '',
                    timeZoneValue: venueData?.timeZoneValue || ''
                });
                // Log the timezone value from API for debugging
                console.log("Timezone from API:", venueData?.timezone);
                let openingHoursData = venueData?.operating_hours?.map(
                    (item) => {
                        return {
                            id: item?.id,
                            weekDay: item?.weekDay,
                            activeHours: item?.openingHours,
                            inActiveHours: item?.closingHours,
                            isClosed: item?.isClosed
                        };
                    }
                );
                setAbnAcnValid(
                    validateIdentifier(venueData?.businessRegisterId)
                );
                setOpeningHoursModalData(openingHoursData);
                setIsAlcohol(venueData?.isVenueServeAlcohol);
                if (updateSelectedVenue) {
                    dispatch(
                        authActions.update_selected_venue({
                            avatar: venueData?.avatar,
                            restaurantName: venueData?.restaurantName,
                            managerName: venueData?.managerName
                        })
                    );
                }
            } else {
                toast.error(res?.data?.message);
            }
        } catch (err) {
            setGetDetailsLoading(false);
            if (err?.message) {
                toast.error(err?.message);
            }
        }
    };

    const submitFormHandler = async (values) => {
        const payload = {
            venue_name: values?.venueName,
            email: values?.email,
            country_code: values?.countryCode,
            manager_name: values?.ownerManagerName,
            venue_address: values?.address,
            business_register_id: values?.abnAcn,
            is_venue_serve_alcohol: values?.isAlcohol,
            liquor_license_number: values?.licenseNumber,
            image: values?.image?.originalFile
                ? values?.image?.originalFile
                : values?.image?.url,
            mobile: values?.mobile,
            confirm_change_flag: confirmAction,
            service_type:
                values?.serviceType?.includes('PICKUP') &&
                    values?.serviceType?.includes('TABLE')
                    ? 'BOTH'
                    : values?.serviceType,
            bar_id: authData?.selectedVenue?.id,
            latitude: values?.latitude,
            longitude: values?.longitude,
            timezone: values?.timeZone || "Australia/Perth"
        };

        if (isAlcohol === 'No') {
            delete payload.liquor_license_number;
        }

        let formData = new FormData();
        for (let key in payload) {
            formData.append(key, payload[key]);
        }

        for (let i = 0; i < openingHoursModalData?.length; i++) {
            formData.append(
                `operating_hours[${i}][id]`,
                openingHoursModalData[i]?.id
            );
            formData.append(
                `operating_hours[${i}][opening_hours]`,
                openingHoursModalData[i]?.activeHours
            );
            formData.append(
                `operating_hours[${i}][closing_hours]`,
                openingHoursModalData[i]?.inActiveHours
            );
            formData.append(
                `operating_hours[${i}][is_closed]`,
                openingHoursModalData[i]?.isClosed
            );
        }

        try {
            setSaveLoading(true);
            const res = await Api('PUT', VenueApiRoutes.editVenue, formData);
            setSaveLoading(false);
            if (res?.data?.status) {
                if (res?.data?.data?.popUpFlag === 1) {
                    setServiceTypeModalData({
                        type: payload.service_type,
                        content: res?.data?.message
                    });
                    setServiceTypeConfirmModal(true);
                } else {
                    await getFormData(true);
                    setConfirmAction(0);
                    toast.success(res?.data?.message);
                }
            } else {
                toast.error(res?.data?.message);
            }
        } catch (err) {
            setSaveLoading(false);
            if (err?.response?.data?.message) {
                toast.error(err?.response?.data?.message);
            }
        }
    };

    function validateIdentifier(identifier) {
        // Remove spaces
        const cleanIdentifier = identifier.replace(/\s/g, '');

        // Check length for ABN (11 digits) or ACN (9 digits)
        if (cleanIdentifier.length === 11) {
            return validateAbn(cleanIdentifier);
        } else if (cleanIdentifier.length === 9) {
            return validateAcn(cleanIdentifier);
        } else {
            return false; // Invalid length
        }
    }

    function validateAbn(abn) {
        if (abn.length !== 11 || isNaN(abn)) {
            return false;
        }

        const weighting = [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19];
        const firstDigit = parseInt(abn.charAt(0), 10);

        if (isNaN(firstDigit)) {
            return false;
        }

        const firstDigitProcessed = firstDigit - 1;
        let weightedSum = firstDigitProcessed * weighting[0];

        for (let i = 1; i < abn.length; i++) {
            const digit = parseInt(abn.charAt(i), 10);
            if (isNaN(digit)) {
                return false;
            }
            weightedSum += digit * weighting[i];
        }

        return weightedSum % 89 === 0;
    }

    function validateAcn(acn) {
        const cleanAcn = acn.replace(/\s/g, '');

        if (cleanAcn.length !== 9 || isNaN(cleanAcn)) {
            return false;
        }

        const weighting = [8, 7, 6, 5, 4, 3, 2, 1];
        let weightedSum = 0;

        for (let i = 0; i < cleanAcn.length - 1; i++) {
            const digit = parseInt(cleanAcn.charAt(i), 10);
            if (isNaN(digit)) {
                return false;
            }
            weightedSum += digit * weighting[i];
        }

        const checkDigit = parseInt(cleanAcn.charAt(cleanAcn.length - 1), 10);
        if (isNaN(checkDigit)) {
            return false;
        }

        const remainder = (10 - (weightedSum % 10)) % 10;

        return checkDigit === remainder;
    }

    const {
        handleChange,
        handleBlur,
        setFieldValue,
        handleSubmit,
        values,
        touched,
        errors,
        setValues
    } = useFormik({
        initialValues: {
            image: null,
            venueName: '',
            ownerManagerName: '',
            email: '',
            countryCode: '+61',
            mobile: '',
            address: '',
            abnAcn: '',
            isAlcohol: 'No',
            licenseNumber: '',
            serviceType: [],
            latitude: '',
            longitude: '',
            timeZone: '',
            timeZoneValue: ''
        },
        validationSchema,
        onSubmit: submitFormHandler
    });

    return (
        <NewPageWrapper>
            <NewLoader loading={getDetailsLoading}>
                <StylesWrapper>
                    <NewPageTitle>Venue Profile</NewPageTitle>
                    <p className="subTitleText">Owner/Manager Details</p>
                    <form>
                        <div className="formGridContainer">
                            <div className="ownerManagerName">
                                <NewFormLabel
                                    className={'pa-b-5'}
                                    label={'Name'}
                                    tooltip={
                                        "Please enter the owner or manager's name"
                                    }
                                />
                                <NewFormInput
                                    name={'ownerManagerName'}
                                    placeholder="Enter Manager Name"
                                    value={values?.ownerManagerName}
                                    onChange={handleChange}
                                    onBlur={handleBlur}
                                />
                                <NewFormErrorMessage
                                    className={'pa-t-6'}
                                    message={
                                        touched?.ownerManagerName &&
                                            !!errors?.ownerManagerName
                                            ? errors?.ownerManagerName
                                            : ''
                                    }
                                />
                            </div>
                            <div>
                                <NewFormLabel
                                    className={'pa-b-5'}
                                    label={'Mobile Number'}
                                    tooltip={
                                        "Please enter the owner or manager's mobile number"
                                    }
                                />
                                <NewFormMobileNoInput
                                    name={'mobile'}
                                    placeholder="Enter Mobile Number"
                                    mobileNoValue={values?.mobile}
                                    onMobileNoChange={handleChange}
                                    onMobileNoBlur={handleBlur}
                                    countryCodeValue={values?.countryCode}
                                    onSelectCountry={(_, countryDetails) => {
                                        setFieldValue(
                                            'countryCode',
                                            countryDetails.dialCode
                                        );
                                    }}
                                />
                                <NewFormErrorMessage
                                    className={'pa-t-6'}
                                    message={
                                        (touched?.mobile && !!errors?.mobile) ||
                                            (touched?.countryCode &&
                                                !!errors?.countryCode)
                                            ? errors?.mobile ||
                                            errors?.countryCode
                                            : ''
                                    }
                                />
                            </div>
                        </div>
                        <p className="subTitleText">Venue Details</p>
                        <div className="formGridContainer">
                            <div className="formFieldWrapper">
                                <NewFormLabel
                                    className={'pa-b-5'}
                                    label={'Venue Profile Photo'}
                                    tooltip={
                                        'Please add a photo of the front of your venue'
                                    }
                                />
                                <ImageUpload
                                    value={values?.image}
                                    onChange={handleFileUploadChange}
                                />
                                <NewFormErrorMessage
                                    className={'pa-t-6'}
                                    message={
                                        touched?.image && !!errors?.image
                                            ? errors?.image
                                            : ''
                                    }
                                />
                            </div>
                            <div className="formFieldWrapper d-flex flex-column justify-content-between">
                                <div className="venueName">
                                    <NewFormLabel
                                        className={'pa-b-5'}
                                        label={'Venue Name'}
                                        tooltip={
                                            'Please enter the name of your venue'
                                        }
                                    />
                                    <NewFormInput
                                        name="venueName"
                                        placeholder="Enter venue name"
                                        value={values?.venueName}
                                        onChange={handleChange}
                                        onBlur={handleBlur}
                                    />
                                    <NewFormErrorMessage
                                        className={'pa-t-6'}
                                        message={
                                            touched?.venueName &&
                                                !!errors?.venueName
                                                ? errors?.venueName
                                                : ''
                                        }
                                    />
                                </div>
                                <div>
                                    <NewFormLabel
                                        className={'pa-b-5'}
                                        label={'Venue Email Address'}
                                        tooltip={'Please enter email address'}
                                    />
                                    <NewFormInput
                                        name="email"
                                        placeholder="Enter email address"
                                        value={values?.email}
                                        onChange={handleChange}
                                        onBlur={handleBlur}
                                    />
                                    <NewFormErrorMessage
                                        className={'pa-t-6'}
                                        message={
                                            touched?.email && !!errors?.email
                                                ? errors?.email
                                                : ''
                                        }
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="formGridContainer">
                            <div className="formFieldWrapper">
                                <NewFormLabel
                                    className={'pa-b-5'}
                                    label={'Venue Address'}
                                    tooltip={
                                        'Please enter the address of your venue'
                                    }
                                />
                                <NewFormInput
                                    name="address"
                                    placeholder="Enter venue address"
                                    value={values?.address}
                                    onChange={handleChange}
                                    onBlur={handleBlur}
                                    readOnly={true}
                                    onClick={() => setIsOpenMapModal(true)}
                                    wrapperClassName={'venueAddressField'}
                                />
                                <NewFormErrorMessage
                                    className={'pa-t-6'}
                                    message={
                                        touched?.address && !!errors?.address
                                            ? errors?.address
                                            : ''
                                    }
                                />
                            </div>
                            <div className="formFieldWrapper">
                                <NewFormLabel
                                    className={'pa-b-5'}
                                    label={'Select your servive type'}
                                    tooltip={
                                        'Please select the service type/s your venue offers on MyTab'
                                    }
                                />
                                <div className="d-flex" style={{ gap: '15px' }}>
                                    <div className="flex-1">
                                        <NewFormCheckbox
                                            name={'serviceType'}
                                            value={'PICKUP'}
                                            checked={values?.serviceType?.includes(
                                                'PICKUP'
                                            )}
                                            onChange={handleChange}
                                        >
                                            Pick Up (collect order)
                                        </NewFormCheckbox>
                                    </div>
                                    <div className="flex-1">
                                        <NewFormCheckbox
                                            name="serviceType"
                                            value={'TABLE'}
                                            checked={values?.serviceType?.includes(
                                                'TABLE'
                                            )}
                                            onChange={handleChange}
                                        >
                                            Table Service
                                        </NewFormCheckbox>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="formGridContainer">
                            <div className="formFieldWrapper">
                                <NewFormLabel
                                    className={'pa-b-5'}
                                    label={'Venue ABN/ACN Details'}
                                    tooltip={
                                        'Please enter the valid ABN/ACN details of your business'
                                    }
                                />
                                <NewFormInput
                                    name="abnAcn"
                                    placeholder="Enter ABN/ACN details"
                                    value={values?.abnAcn}
                                    onChange={(e) => {
                                        handleChange(e);
                                        let abnOrAcnNumberFlag =
                                            validateIdentifier(e.target.value);
                                        setAbnAcnValid(abnOrAcnNumberFlag);
                                    }}
                                    onBlur={handleBlur}
                                    isShowValidInvalidIcon={true}
                                    isValid={abnAcnValid}
                                />
                                <NewFormErrorMessage
                                    className={'pa-t-6'}
                                    message={
                                        errors?.abnAcn ??
                                        (abnAcnValid !== null
                                            ? !abnAcnValid && 'Invalid ABN/ACN'
                                            : '')
                                    }
                                />
                            </div>
                            <div className="formFieldWrapper">
                                <NewFormLabel
                                    className={'pa-b-5'}
                                    label={'Time Zone'}
                                />
                                <NewFormSelect
                                    name="timeZone"
                                    placeholder="Select Time Zone"
                                    value={values?.timeZone ? {
                                        label: values?.timeZone,
                                        value: values?.timeZone
                                    } : null}
                                    onChange={(selectedOption) => {
                                        setFieldValue('timeZone', selectedOption?.value);
                                        console.log("Selected timezone:", selectedOption?.value);
                                    }}
                                    onBlur={handleBlur}
                                    options={timezones.map(timezone => ({
                                        label: timezone.name,
                                        value: timezone.name
                                    }))}
                                    isSearchable
                                    isLoading={!timezones.length}
                                />
                            </div>
                        </div>
                        <div className="formGridContainer">
                            <div className="formFieldWrapper">
                                <NewFormLabel
                                    className={'pa-b-5'}
                                    label={
                                        'Will your venue be serving alochol?'
                                    }
                                />
                                <div className="d-flex" style={{ gap: '15px' }}>
                                    <div className="flex-1">
                                        <NewFormCheckbox
                                            name="isAlcohol"
                                            value={'Yes'}
                                            checked={values?.isAlcohol?.includes(
                                                'Yes'
                                            )}
                                            onChange={() => {
                                                handleChange({
                                                    target: {
                                                        name: 'isAlcohol',
                                                        value: 'Yes'
                                                    }
                                                });
                                                setIsAlcohol('Yes');
                                            }}
                                        >
                                            Yes
                                        </NewFormCheckbox>
                                    </div>
                                    <div className="flex-1">
                                        <NewFormCheckbox
                                            name="isAlcohol"
                                            value={'No'}
                                            checked={values?.isAlcohol?.includes(
                                                'No'
                                            )}
                                            onChange={() => {
                                                handleChange({
                                                    target: {
                                                        name: 'isAlcohol',
                                                        value: 'No'
                                                    }
                                                });
                                                setIsAlcohol('No');
                                            }}
                                        >
                                            No
                                        </NewFormCheckbox>
                                    </div>
                                </div>
                            </div>
                            {isAlcohol === 'Yes' && (
                                <div className="formFieldWrapper">
                                    <NewFormLabel
                                        className={'pa-b-5'}
                                        label={'Liquor License Number'}
                                        tooltip={
                                            'Please enter the valid Liquor Licence number of your business'
                                        }
                                    />
                                    <NewFormInput
                                        name="licenseNumber"
                                        placeholder="Enter Liquor License Number"
                                        value={values?.licenseNumber}
                                        onChange={handleChange}
                                        onBlur={handleBlur}
                                        disabled={
                                            !values?.isAlcohol ||
                                            values?.isAlcohol === 'No'
                                        }
                                    />
                                    <NewFormErrorMessage
                                        className={'pa-t-6'}
                                        message={
                                            touched?.licenseNumber &&
                                                !!errors?.licenseNumber
                                                ? errors?.licenseNumber
                                                : ''
                                        }
                                    />
                                </div>
                            )}
                        </div>
                        <div className="saveDeleteBtnWrapper">
                            <FilledButton
                                buttonText={'Delete Venue Account'}
                                background={'#ffffff'}
                                color={'rgba(107, 194, 66, 1)'}
                                style={{
                                    width: '160px',
                                    border: '1px solid rgba(107, 194, 66, 1)'
                                }}
                                onClick={() => getDeleteTypeFormId()}
                            />
                            <FilledButton
                                buttonText={'Save'}
                                background={'rgba(107, 194, 66, 0.2)'}
                                color={'rgba(107, 194, 66, 1)'}
                                style={{
                                    width: '160px',
                                    border: '1px solid rgba(107, 194, 66, 1)'
                                }}
                                onClick={handleSubmit}
                                loading={saveLoading}
                            />
                        </div>
                    </form>
                </StylesWrapper>
            </NewLoader>
            {serviceTypeConfirmModal && (
                <ServiceTypeConfirmModal
                    isOpen={serviceTypeConfirmModal}
                    handleModal={handleServiceTypeConfirmModal}
                    handleConfirm={() => {
                        setConfirmAction(1);
                        handleSubmit();
                    }}
                    modalData={serviceTypeModalData}
                />
            )}
            {isOpenMapModal && (
                <MapModal
                    isOpen={isOpenMapModal}
                    handleModal={handleMapModal}
                    setFieldValue={setFieldValue}
                />
            )}
            {typeFormId && (
                <PopupButton
                    id={typeFormId}
                    style={{
                        position: 'absolute',
                        opacity: 0,
                        pointerEvents: 'none'
                    }}
                    embedRef={ref}
                    onReady={handleReadyTypeForm}
                    onSubmit={handleSubmitTypeForm}
                    onClose={handleCloseTypeForm}
                    onError={handleErrorTypeForm}
                ></PopupButton>
            )}
        </NewPageWrapper>
    );
};

export default NewVenueProfile;
