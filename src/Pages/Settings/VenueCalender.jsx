import { useSelector } from 'react-redux';

import PageWrapper from './venueCalender.style';
import PageStructure from '../../Components/Common/PageStructure';
import Calender from '../../Components/Settings/Calender/Calender';
import ComingSoon from '../../Components/Common/ComingSoon';

export const VenueCalender = () => {
	const allThemeData = useSelector((state) => ({ ...state.themeChanger }));

	return (
		<PageStructure title="Your MyTab Venue Calendar" search={false}>
			<PageWrapper {...allThemeData}>
				<p className="mt-3 mb-3 fs-16 regular-text">
					Your MyTab Venue Calendar allows you to schedule opening
					hours, taxes and promo codes 1 month in advance.
				</p>
				<div className="h-100">
					<Calender />
				</div>
				<p className="pt-2"></p>
			</PageWrapper>
		</PageStructure>
	);
};
