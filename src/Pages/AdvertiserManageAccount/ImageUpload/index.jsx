import React, { useCallback, useEffect, useRef, useState } from 'react';
import { <PERSON>ropper } from 'react-cropper';

import { EditIcon } from '../../../Components/Icons';
import NewModal from '../../../Components/Common/NewModal';
import { getFileExtension } from '../../../Helper/helper';
import defaultProfilePic from '../../../Assets/images/phone2.png';
import StyleWrapper from './index.style';
import 'cropperjs/dist/cropper.css';

const ImageUpload = ({ value, onChange, disabled = false }) => {
	const [customFile, setCustomFile] = useState(null);
	const [tempCustomFile, setTempCustomFile] = useState(null);
	// file = {
	//   url: String or null,
	//   originalFile: File object or null
	// }
	const [isOpenCropModal, setIsOpenCropModal] = useState(false);
	const fileInput = useRef(null);
	const cropperRef = useRef(null);

	const handleUploadButtonClick = () => {
		fileInput?.current?.click();
	};

	const handleFileInputChange = (event) => {
		if (event?.target?.files?.length > 0) {
			setTempCustomFile({
				url: URL.createObjectURL(event.target.files[0]),
				originalFile: event.target.files[0]
			});
			setIsOpenCropModal(true);
		}
	};

	const handleCrop = useCallback(() => {
		const imageExt = getFileExtension(tempCustomFile?.originalFile?.name);
		cropperRef?.current?.cropper?.getCroppedCanvas()?.toBlob((blob) => {
			const imageFile = new File([blob], `${Date.now()}.${imageExt}`, {
				type: blob?.type
			});
			const blobUrl = URL.createObjectURL(imageFile);
			if (onChange) {
				onChange({
					url: blobUrl,
					originalFile: imageFile
				});
			} else {
				setCustomFile({
					url: blobUrl,
					originalFile: imageFile
				});
			}
			setIsOpenCropModal(false);
		}, customFile?.originalFile?.type);
	}, [tempCustomFile]);

	useEffect(() => {
		if (value) {
			setCustomFile(value);
		} else {
			setCustomFile(null);
		}
	}, [value]);
	return (
		<StyleWrapper disabled={disabled}>
			<input
				type="file"
				ref={fileInput}
				accept="image/*"
				hidden
				multiple={false}
				onChange={handleFileInputChange}
				disabled={disabled}
			/>
			<div className="imageBox">
				<img
					src={customFile?.url ? customFile?.url : defaultProfilePic}
					alt="product-image"
					onClick={handleUploadButtonClick}
				/>
				<span onClick={handleUploadButtonClick} className="icon">
					<EditIcon height={14} weight={14} stroke="#fff" />
				</span>
			</div>
			{isOpenCropModal && (
				<NewModal
					isOpen={isOpenCropModal}
					toggle={() => setIsOpenCropModal(false)}
					className="advertiserProfileImageCropModal"
					submitButtonText="Crop Image"
					handleSubmitButtonClick={handleCrop}
				>
					<div style={{ padding: '16px' }}>
						<Cropper
							ref={cropperRef}
							src={tempCustomFile?.url}
							viewMode={2}
							aspectRatio={1 / 1}
							autoCropArea={1}
							rotatable={false}
							responsive={true}
							width={400}
							height={400}
						/>
					</div>
				</NewModal>
			)}
		</StyleWrapper>
	);
};

export default ImageUpload;
