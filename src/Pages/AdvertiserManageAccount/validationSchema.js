import * as Yup from 'yup';

export const manageAccountFormSchema = Yup.object().shape({
	image: Yup.mixed().required('Please upload profile image.'),
	business_name: Yup.string().trim().required('Please enter business name.'),
	business_url: Yup.string()
		.trim()
		.url('Please enter a valid url.')
		.required('Please enter business website url.'),
	acn_number: Yup.number()
		.typeError('Please enter a numerical value for your ABN/ACN.')
		.required('Please enter ABN/ACN number.'),
	contact_name: Yup.string().trim().required('Please enter contact name.')
});
