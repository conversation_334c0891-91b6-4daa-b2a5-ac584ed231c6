import { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import { Row, Col, Button } from 'reactstrap';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

import { somethingWentWrongError } from '../../Helper/somethingWentWrongError';
import { AdvertiserApiRoutes } from '../../Utils/routes';
import Api from '../../Helper/Api';
import authActions from '../../Redux/auth/actions';
import FormInput from '../../Components/Form/FormInput';
import CustomButton from '../../Components/Common/CustomButton';
import PageStructure from '../../Components/Common/PageStructure';
import VerifyOtpModal from './VerifyOtpModal';
import { manageAccountFormSchema } from './validationSchema';
import ImageUpload from './ImageUpload';

const allowedFileTypes = ['image/jpg', 'image/jpeg', 'image/png'];
const FILE_SIZE = 20 * 1024 * 1024;

const AdvertiserManageAccount = () => {
	const profileData = useSelector((state) => ({ ...state.auth }));
	const dispatch = useDispatch();
	const [loading, setLoading] = useState(false);
	const [openModal, setOpenModal] = useState(false);
	const [editMode, setEditMode] = useState(false);

	const handleModal = () => {
		setOpenModal((prev) => !prev);
	};
	const submitFormHandler = async () => {
		const payload = {
			image: values?.image?.originalFile
				? values?.image?.originalFile
				: values?.image?.url,
			business_name: values?.business_name,
			business_url: values?.business_url,
			acn_number: values?.acn_number,
			contact_name: values?.contact_name
		};
		let formData = new FormData();
		for (let key in payload) {
			formData.append(key, payload[key]);
		}
		try {
			setLoading(true);
			const res = await Api(
				'PUT',
				AdvertiserApiRoutes?.editProfile,
				formData
			);
			setLoading(false);
			if (res?.data?.status) {
				toast.success(res?.data?.message);
				await dispatch(
					authActions.update_advertiser_profile({
						...profileData,
						...res?.data?.data
					})
				);
				setEditMode(false);
			} else {
				toast.error(res?.data?.message);
			}
		} catch (err) {
			setLoading(false);
			somethingWentWrongError();
		}
	};
	const {
		values,
		handleChange,
		handleBlur,
		setValues,
		touched,
		errors,
		handleSubmit,
		setFieldValue
	} = useFormik({
		initialValues: {
			image: '',
			business_name: '',
			business_url: '',
			acn_number: '',
			contact_name: ''
		},
		validationSchema: manageAccountFormSchema,
		onSubmit: submitFormHandler
	});
	const handleFileUploadChange = (customFileObject) => {
		const isAllowed = allowedFileTypes.includes(
			customFileObject?.originalFile?.type
		);
		if (!isAllowed) {
			toast.error('You can only upload jpg, jpeg or png images.');
			return;
		}
		if (customFileObject?.originalFile?.size > FILE_SIZE) {
			toast.error('Image size must be less than 20Mb.');
			return;
		}
		setFieldValue('image', customFileObject);
	};
	useEffect(() => {
		if (profileData) {
			setValues({
				image: { url: profileData?.profile_image, originalFile: null },
				business_name: profileData?.business_name,
				business_url: profileData?.business_url,
				acn_number: profileData?.acn_number,
				contact_name: profileData?.contact_name
			});
		}
	}, []);
	return (
		<PageStructure title="Manage Account">
			<form onSubmit={handleSubmit}>
				<Row>
					<Col sm={12} className="pa-b-20">
						<ImageUpload
							value={values?.image}
							onChange={handleFileUploadChange}
							disabled={!editMode}
						/>
					</Col>
				</Row>
				<Row style={{ '--bs-gutter-x': '18px' }}>
					<Col md={6} className="pa-b-20">
						<FormInput
							type="text"
							name="business_name"
							label="Business Name (Public)"
							placeholder="Enter your business name"
							value={values?.business_name}
							onChange={handleChange}
							onBlur={handleBlur}
							error={
								touched?.business_name &&
								!!errors?.business_name
							}
							errorMsg={
								touched?.business_name
									? errors?.business_name
									: null
							}
							disabled={!editMode}
						/>
					</Col>
					<Col md={6} className="pa-b-20">
						<FormInput
							type="text"
							name="business_url"
							label="Business Website"
							placeholder="https://yourbusiness.com"
							value={values?.business_url}
							onChange={handleChange}
							onBlur={handleBlur}
							error={
								touched?.business_url && !!errors?.business_url
							}
							errorMsg={
								touched?.business_url
									? errors?.business_url
									: null
							}
							disabled={!editMode}
						/>
					</Col>
				</Row>
				<Row style={{ '--bs-gutter-x': '18px' }}>
					<Col md={6} className="pa-b-20">
						<FormInput
							type="text"
							name="acn_number"
							label="ABN / ACN"
							placeholder={'Enter your ABN or ACN'}
							value={values?.acn_number}
							onChange={handleChange}
							onBlur={handleBlur}
							error={touched?.acn_number && !!errors?.acn_number}
							errorMsg={
								touched?.acn_number ? errors?.acn_number : null
							}
							disabled={!editMode}
						/>
					</Col>
					<Col md={6} className="pa-b-20">
						<FormInput
							type="text"
							name="contact_name"
							label={'Contact Name'}
							placeholder={'Enter your name'}
							value={values?.contact_name}
							onChange={handleChange}
							onBlur={handleBlur}
							error={
								touched?.contact_name && !!errors?.contact_name
							}
							errorMsg={
								touched?.contact_name
									? errors?.contact_name
									: null
							}
							disabled={!editMode}
						/>
					</Col>
				</Row>
				<Row>
					<Col sm={6} className="pa-t-4">
						{!editMode ? (
							<Button
								type="button"
								className="themeButton"
								onClick={() => setOpenModal(true)}
							>
								Edit Account
							</Button>
						) : (
							<CustomButton
								type="submit"
								className="themeButton"
								loading={loading}
							>
								Save Changes
							</CustomButton>
						)}
					</Col>
				</Row>
			</form>
			{openModal && (
				<VerifyOtpModal
					isOpen={openModal}
					handleModal={handleModal}
					setEditMode={setEditMode}
				/>
			)}
		</PageStructure>
	);
};

export default AdvertiserManageAccount;
