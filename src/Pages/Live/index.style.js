import styled from 'styled-components';

const PageWrapper = styled.div`
	color: ${(props) => props.layoutTheme.textColor};
	@media only screen and (max-width: 767px) {
		padding: 0px;
		.border-radius-16 {
			border-radius: 0px !important;
		}
	}
	.dot {
		width: 14px;
		height: 14px;
		background: #242424;
	}
	.defaultBoxShadow {
		box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.08);
	}
	.buttonGroup {
		margin: 10px;
		background: #ffefef;
		padding: 5px;
		border-radius: 25px;
		width: 421px;
		cursor: pointer;
		color: ${(props) => props.layoutTheme.textColor};
		.buttonType {
			margin: 2px;
			padding: 5px;
			@media only screen and (max-height: 1600px) {
				margin: 10px 2px;
			}
			@media only screen and (min-height: 900px) {
				padding: 5px;
			}
			text-align: center;
			span {
				@media only screen and (min-height: 900px) {
					font-size: 16px !important;
				}
				@media only screen and (max-width: 575.98px) {
					font-size: 10px !important;
				}
			}
		}
		.active {
			transition: all 500ms ease-in-out;
			color: #ffffff;
			z-index: 2;
			svg path {
				fill: #fff;
				transition: all 500ms ease-in-out;
			}
		}
		.activeBackground {
			background: #ff6460;
			border-radius: 25px;
			width: 50%;
			height: 90%;
			margin-left: 2px;
			z-index: 1;
			transform: translateX(0%);
			transition: all 500ms ease-in-out;
			&.active {
				transform: translateX(100%);
				margin-left: -0px;
				margin-left: -2px;
			}
		}
	}
	.dashboard-dropdown {
		.activeColor {
			color: ${(props) => props.layoutTheme.buttonColor3} !important;
		}
		&.dropdownCity .dropdownToggle {
			margin-right: 5px;
		}
		.dropdown-item,
		.dropdownToggle {
			outline: 0 !important;
			border: 0px !important;
			padding-left: 8px;
			padding-right: 8px;
			padding-top: 10px;
			background: #f9f9f9;
			font-size: 16px;
			font-family: 'montserrat-medium';
			color: ${(props) => props.layoutTheme.textColor};
			&.active {
				color: ${(props) => props.layoutTheme.buttonColor3};
			}
			@media only screen and (max-width: 575.98px) {
				padding-top: 5px;
				margin-top: 6px;
				margin-bottom: 6px;
			}
			.dropdown-name {
				padding-right: 5px;
				color: ${(props) => props.layoutTheme.textColor};
				@media only screen and (max-width: 575.98px) {
					font-size: 12px !important;
				}
			}
			&:hover {
				outline: 0 !important;
				border: 0px !important;
				color: ${(props) => props.layoutTheme.buttonColor3};
			}
			&:active {
				outline: 0 !important;
				border: 0px !important;
				background-color: #fff !important;
				color: ${(props) => props.layoutTheme.textColor};
			}
		}
		.dropdown-menu {
			border: 0px !important;
			@media only screen and (max-width: 360px) {
				left: -10px !important;
			}
		}
	}
	.dashboard-card {
		border-bottom: 1px solid #f4f4f4;
		padding: 12px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		min-height: 65px;
		gap: 20px;
		@media only screen and (min-height: 1080px) {
			min-height: 105px !important;
		}
		&:last-child {
			border-bottom: 0px;
		}
	}
	.dashboard-card-parent {
		border: 1px solid #f4f4f4;
		border-radius: 12px;
		@media only screen and (min-height: 1080px) {
			margin-top: 28px;
		}
	}
	.dashboard-total-card {
		border: 0px;
		border-radius: 12px;
		padding: 10px;
		background-color: #ffefef;
		color: #ff6460;
		min-height: 70px;
		gap: 20px;
		@media only screen and (min-height: 1080px) {
			min-height: 105px !important;
		}
	}
	.analytics-card {
		background: #ffffff;
		box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.08);
		border-radius: 16px;
	}
	.analytics-card .col-md-6,
	.analytics-card .col-md-12 {
		padding: 0px 5px;
		@media only screen and (max-width: 575.98px) {
			padding: 5px;
		}
	}
	hr {
		color: #eaeaea;
		margin: 0.5rem 0px;
	}
	.dropdown-menu {
		background: #ffffff;
		box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.04);
		border-radius: 8px;
	}
	.dropdownCity {
		.dropdown-menu {
			width: 300px;
			left: -200px !important;
			@media only screen and (max-width: 575.98px) {
				width: 300px;
				left: -10px !important;
			}
		}
		.dropdown-item {
			background-color: #fff !important;
		}
	}
	.css-1xc3v61-indicatorContainer {
		&:hover,
		&:focus,
		&:active {
			color: #000 !important;
		}
		color: #000 !important;
	}

	.dashboard-card {
		.flex-1 {
			white-space: pre;
		}
	}
	.dashboard-total-card {
		p {
			&:last-child {
				white-space: pre;
			}
		}
	}
	.globParent {
		position: relative;
		z-index: 1;
	}
	.globePopup {
		position: absolute;
		z-index: 200;
		background: rgb(255, 255, 255, 1);
		bottom: calc(50% - 40px);
		right: calc(50% - 80px);
		border-radius: 16px;
		border: 1px solid #f87171;
	}
	.globePopupBody {
		z-index: 20;
		color: #f87171;
	}
`;

export default PageWrapper;
