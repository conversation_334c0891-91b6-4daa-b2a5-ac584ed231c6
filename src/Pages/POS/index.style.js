import styled from 'styled-components';
import sectionImage from '../../Assets/images/section-common-back.png';

const PageWrapper = styled.div`
	width: 100%;
	height: 100%;
	/* new design styles */
	display: flex;
	.pageContainer {
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 71px;
	}
	.iconImage {
		width: 51px;
		height: 44px;
	}
	.formSideSpace {
		padding-right: 26px;
	}
	.checkIcon {
		width: 46px;
		height: 41px;
	}

	.smallInfo {
		color: #000000;
		margin: 15px 0 25px;
		font-family: 'poppins-regular';
		font-size: 21px;

		span {
			cursor: pointer;
			color: #f95c69;
		}
	}

	.imageWrapper {
		flex: 1;
		display: flex;

		background: #fff !important;

		.text {
			max-width: 703px;
			font-family: 'montserrat-bold';
			font-size: 55px;
			color: #fff;
			text-shadow: 0px 3px 1px #00000040;
			text-align: center;
		}
		.image {
			width: 938px;
			height: 298px;
		}
		@media only screen and (max-width: 1199px) {
			display: none !important;
		}
	}
	.backWrraper {
		height: 100%;
		width: 100%;
		display: flex;
		gap: 29px;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: url(${sectionImage});

		background-size: 100% 100% !important;
		background-repeat: no-repeat;
	}

	.followSteps {
		display: flex;
		margin-bottom: 23px;
		font-family: 'poppins-regular';
		p {
			font-size: 21px;
			font-family: 'poppins-regular';
		}
		.numberCircle {
			color: #ffffff;
			font-size: 20px;
			font-weight: 800;
			display: flex;
			justify-content: center;
			align-items: center;
			border-radius: 100%;
			height: 51px;
			width: 51px;
			background: #f95c69;
		}
	}

	.verifyOtpSubText {
		font-family: 'poppins-regular';
		font-size: 22px;
		margin-bottom: 0.5%;
	}

	.resendMail {
		font-family: 'poppins-regular';
		color: #000000;
		font-size: 21px;
		text-align: center;
		.spanLink {
			font-family: 'montserrat-light';
			color: #f95c69;
			cursor: pointer;
		}
	}

	@media (max-width: 600px) {
		.checkIcon {
			width: 21.2px;
			height: 18.9px;
		}

		.headingStyle {
			padding-top: 3%;
			font-size: 22px;
			padding-bottom: 5%;
		}

		.iconImage {
			width: 23.5px;
			height: 20.3px;
		}
		.imageWrapper {
			display: none;
		}

		.followSteps {
			p {
				font-size: 9.7px;
				max-width: 618px;
			}
			.numberCircle {
				font-size: 10px;
				font-weight: 800;

				height: 20px;
				width: 20px;
			}
		}
		.resendMail {
			font-size: 9.7px;
		}

		.verifyOtpSubText {
			font-size: 10.1px;
		}

		.smallInfo {
			font-size: 9.7px !important;
		}
		.formWrapper {
			padding: 0 20px !important;
		}
		.formSideSpace {
			padding-right: 0px !important;
		}
	}

	@media only screen and (min-width: 600px) and (max-width: 960px) {
		.checkIcon {
			width: 34.5px;
			height: 30.75px;
		}

		.headingStyle {
			font-size: 31.5px;
			padding-bottom: 7%;
		}

		.iconImage {
			width: 38.2px;
			height: 33px;
		}

		.followSteps {
			p {
				font-size: 15.7px;
				max-width: 618px;
			}
			.numberCircle {
				font-size: 12px;
				font-weight: 800;

				height: 33px;
				width: 33px;
			}
		}

		.smallInfo {
			font-size: 15.7px !important;
		}
		.verifyOtpSubText {
			font-size: 16.5px;
		}
		.resendMail {
			font-size: 15.7px !important;
		}

		.imageWrapper {
			display: none;
		}
		.formWrapper {
			padding: 0 50px !important;
		}
		.formSideSpace {
			padding-right: 0px !important;
		}
	}

	@media only screen and (min-width: 961px) and (max-width: 1824px) and (max-height: 800px) {
		.iconImage {
			width: 28.6px;
			height: 24.7px;
		}
		.checkIcon {
			width: 25.8px;
			height: 23px;
		}
		.imageWrapper {
			.text {
				font-size: 30.9px;
				max-width: 395.4px;
			}
			.image {
				width: 527.6px;
				height: 167.6px;
			}
		}
		.headingStyle {
			padding-top: 30px;
			font-size: 23.6px;
		}

		.followSteps {
			margin-bottom: 12.9px;
			p {
				font-size: 11.8px;
				font-family: 'poppins-regular';
				max-width: 560.5px;
			}
			.numberCircle {
				font-size: 12px;
				font-weight: 800;
				border-radius: 100%;
				height: 30.3px;
				width: 30.3px;
			}
		}

		.smallInfo {
			font-size: 11.8px;
		}
		.verifyOtpSubText {
			font-size: 12.3px;
		}
		.resendMail {
			font-size: 11.8px;
		}
	}
`;

export default PageWrapper;
