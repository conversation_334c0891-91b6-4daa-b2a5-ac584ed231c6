import Scrollbars from 'react-custom-scrollbars';
import { useSelector } from 'react-redux';
import { Col, Row } from 'reactstrap';
import PageWrapper from './index.style';
import {
	abandonedCartData,
	ageDemographicData,
	customerDeviceTypeData,
	exposureRatioData,
	orderByLocationData,
	popularMenuItemsData,
	removedItemData,
	reOrderingData,
	returnRateData
} from './utils';
import {
	CircleDollarIcon,
	Clock,
	Cross,
	Customers,
	EmailCircle,
	Spends
} from '../../../Components/Icons';
import AnalyticsCard from '../../../Components/Reports/CustomerAnalytics/AnalyticsCard';
import ReportsDropDown from '../../../Components/Reports/ReportsDropDown';
import HorizontalBarChart from '../../../Components/Common/Charts/HorizontalBarChart';
import { PieChart } from '../../../Components/Common/Charts/PieChart';
import VerticleStackedBarChart from '../../../Components/Common/Charts/VerticleStackedBarChart';
import VerticleBarChart from '../../../Components/Common/Charts/VerticleBarChart';
import HorizontalStackedBarChart from '../../../Components/Common/Charts/HorizontalStackedBarChart';
import ComingSoon from '../../../Components/Common/ComingSoon';

export const CustomerAnalytics = () => {
	const allThemeData = useSelector((state) => ({ ...state.themeChanger }));
	const pieChartLabelFormatter = (value) => {
		return value + '%';
	};
	return (
		<Scrollbars autoHide>
			<PageWrapper {...allThemeData}>
				<div className="fs-24 medium-text pa-b-32">
					MyTab Customer Analytics
				</div>
				<div className="pa-b-32">
					<ReportsDropDown />
				</div>
				<Row className="g-4 pa-b-32">
					<Col sm={12} lg={6} xl={4}>
						<AnalyticsCard
							count={
								<span className="fs-30 medium-text">
									<span className="fs-16 medium-text">
										{''}$
									</span>
									{''}
									12,00,450.50
								</span>
							}
							heading={'Total Customer Spend'}
							icon={<CircleDollarIcon height={35} width={35} />}
						/>
					</Col>
					<Col sm={12} lg={6} xl={4}>
						<AnalyticsCard
							count={
								<span className="fs-30 medium-text">
									<span className="fs-16 medium-text">
										{''}$
									</span>
									{''}
									30,000
								</span>
							}
							heading={'Avg Customer Spend'}
							icon={
								<Spends
									height={35}
									width={35}
									fill={'#FF5F5F'}
								/>
							}
						/>
					</Col>
					<Col sm={12} lg={6} xl={4}>
						<AnalyticsCard
							count={'2,520'}
							heading={'Total Customers'}
							icon={
								<Customers
									height={35}
									width={35}
									fill={'#FF5F5F'}
								/>
							}
						/>
					</Col>
					<Col sm={12} lg={6} xl={4}>
						<AnalyticsCard
							count={
								<span className="fs-30 medium-text">
									3
									<span className="fs-16 medium-text">
										{' '}
										Min
									</span>{' '}
									26
									<span className="fs-16 medium-text">
										{' '}
										Sec
									</span>
								</span>
							}
							heading={'Avg Menu Browsing Duration'}
							icon={<Clock height={35} width={35} />}
						/>
					</Col>
					<Col sm={12} lg={6} xl={4}>
						<AnalyticsCard
							count={'18'}
							heading={'No. of Customer Issue Emails'}
							icon={
								<EmailCircle
									height={35}
									width={35}
									fill={'#FF5F5F'}
								/>
							}
						/>
					</Col>
					<Col sm={12} lg={6} xl={4}>
						<AnalyticsCard
							count={'8'}
							heading={
								'Customers who viewed menu without ordering'
							}
							icon={
								<Cross
									height={35}
									width={35}
									fill={'#FF5F5F'}
								/>
							}
						/>
					</Col>
				</Row>
				{/* Charts */}
				<Row className="g-3">
					<Col xl="7">
						<HorizontalBarChart
							heading={'Orders By Location'}
							chartData={orderByLocationData}
							yAxisWidth={40}
						/>
					</Col>
					<Col xl="5">
						<PieChart
							heading={'Venue Exposure Ratio'}
							chartData={exposureRatioData}
							labelFormatter={pieChartLabelFormatter}
						/>
					</Col>
					<Col xl="7">
						<HorizontalBarChart
							heading={'Customer Same Day Re-Ordering'}
							chartData={reOrderingData}
							yAxisWidth={92}
						/>
					</Col>
					<Col xl="5">
						<PieChart
							heading={'Customer Device Type'}
							chartData={customerDeviceTypeData}
							labelFormatter={pieChartLabelFormatter}
						/>
					</Col>
					<Col xl="6">
						<VerticleStackedBarChart
							heading={'Customer Return Rate'}
							data={returnRateData}
							yAxisWidth={56}
						/>
					</Col>
					<Col xl="6">
						<VerticleStackedBarChart
							heading={'Customer Spend Rate'}
							data={returnRateData}
							yAxisWidth={56}
						/>
					</Col>
					<Col xl="6">
						<VerticleBarChart
							heading={'Customer Age Demographic'}
							data={ageDemographicData}
							showLegend
							yAxisWidth={56}
						/>
					</Col>
					<Col xl="6">
						<HorizontalStackedBarChart
							heading={
								'Most Popular Menu Items by Age Demographic'
							}
							data={popularMenuItemsData}
							xAxisCount="4"
							yAxisWidth={112}
						/>
					</Col>
					<Col xl="6">
						<VerticleBarChart
							heading={'No. of Abandoned Carts'}
							data={abandonedCartData}
							showLegend
							yAxisWidth={56}
						/>
					</Col>
					<Col xl="6">
						<HorizontalBarChart
							heading={'Most Removed Menu Items From Cart'}
							chartData={removedItemData}
							yAxisWidth={84}
						/>
					</Col>
				</Row>
			</PageWrapper>
		</Scrollbars>
	);
};
