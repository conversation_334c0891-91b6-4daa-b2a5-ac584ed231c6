import Scrollbars from 'react-custom-scrollbars';
import { useSelector } from 'react-redux';
import { Col, Row } from 'reactstrap';
import PageWrapper from './index.style';
import {
	overTimeChartData,
	refundedOrdersData,
	revenueLocationData,
	serviceTypeData,
	topDrinkData,
	topFoodData,
	topMenuData,
	TotalPerformingPromoCodesData
} from './utils';
import { PercentageCircle, TaxCircle } from '../../../Components/Icons';
import DataCard from '../../../Components/Reports/VenueSalesAnalytics/DataCard';
import TotalPerformingCard from '../../../Components/Reports/VenueSalesAnalytics/TotalPerformingCard';
import ReportsDropDown from '../../../Components/Reports/ReportsDropDown';
import MultiSeriesLineChart from '../../../Components/Common/Charts/MultiSeriesLineChart';
import { Pie<PERSON><PERSON> } from '../../../Components/Common/Charts/PieChart';
import HorizontalBarChart from '../../../Components/Common/Charts/HorizontalBarChart';
import VerticleStackedBarChart from '../../../Components/Common/Charts/VerticleStackedBarChart';
import ComingSoon from '../../../Components/Common/ComingSoon';

export const VenueSalesAnalytics = () => {
	const allThemeData = useSelector((state) => ({ ...state.themeChanger }));
	const multiSeriesYAxisData = [
		{ label: '0', value: '0' },
		{ label: 'A$1k', value: '1000' },
		{ label: 'A$2k', value: '2000' }
	];
	const multiSeriesXAxisData = [
		{ label: 'JAN 2022', value: 'JAN 2022' },
		{ label: 'FEB 2022', value: 'FEB 2022' },
		{ label: 'MAR 2022', value: 'MAR 2022' },
		{ label: 'APR 2022', value: 'APR 2022' },
		{ label: 'MAY 2022', value: 'MAY 2022' },
		{ label: 'JUN 2022', value: 'JUN 2022' },
		{ label: 'JUL 2022', value: 'JUL 2022' },
		{ label: 'AUG 2022', value: 'AUG 2022' },
		{ label: 'SEP 2022', value: 'SEP 2022' },
		{ label: 'OCT 2022', value: 'OCT 2022' },
		{ label: 'NOV 2022', value: 'NOV 2022' },
		{ label: 'DEC 2022', value: 'DEC 2022' }
	];
	const pieChartLabelFormatter = (value) => {
		return value + '%';
	};
	return (
		<Scrollbars autoHide>
			<PageWrapper {...allThemeData}>
				<div className="fs-24 medium-text pa-b-32">
					MyTab Venue Sales Analytics
				</div>
				<div className="pa-b-32">
					<ReportsDropDown />
				</div>
				<p className="pa-b-24">
					<span className="fs-24 semi-bold-text">Total Revenue:</span>
					<span className="fs-24 regular-text"> $118,000.50</span>
				</p>
				<div className="pa-b-32">
					<Row className="g-3">
						<Col xl={4}>
							<MultiSeriesLineChart
								id={'sales-over-time'}
								heading={'Sales over time'}
								chartData={overTimeChartData}
								className={'defaultBoxShadow'}
								yAxisData={multiSeriesYAxisData}
								xAxisData={multiSeriesXAxisData}
								showPopover
								popOverContent={'This is Sales Over Time Chart'}
								yAxisWidth={74}
							/>
						</Col>
						<Col xl={4}>
							<PieChart
								heading={'Revenue by Service Type'}
								chartData={serviceTypeData}
								labelFormatter={pieChartLabelFormatter}
							/>
						</Col>
						<Col xl={4}>
							<HorizontalBarChart
								heading={'Revenue by Location'}
								chartData={revenueLocationData}
								yAxisWidth={100}
							/>
						</Col>
					</Row>
				</div>
				<p className="pa-b-24">
					<span className="fs-24 semi-bold-text">Total Orders:</span>
					<span className="fs-24 regular-text"> $560</span>
				</p>
				<div className="pa-b-32">
					<Row className="g-3">
						<Col xl={4}>
							<MultiSeriesLineChart
								id={'orders-over-time'}
								heading={'Orders over time'}
								chartData={overTimeChartData}
								className={'defaultBoxShadow'}
								yAxisData={multiSeriesYAxisData}
								xAxisData={multiSeriesXAxisData}
								showPopover
								popOverContent={
									'This is Orders Over Time Chart'
								}
								yAxisWidth={74}
							/>
						</Col>
						<Col xl={4}>
							<PieChart
								heading={'Order by Service Type'}
								chartData={serviceTypeData}
								labelFormatter={pieChartLabelFormatter}
							/>
						</Col>
						<Col xl={4}>
							<HorizontalBarChart
								heading={'Order by Location'}
								chartData={revenueLocationData}
								yAxisWidth={100}
							/>
						</Col>
					</Row>
				</div>
				<div className="pa-b-32">
					<Row className="g-3">
						<Col xl={4}>
							<HorizontalBarChart
								heading={'Top Menu Subheadings by Units Sold'}
								chartData={topMenuData}
								yAxisWidth={100}
							/>
						</Col>
						<Col xl={4}>
							<HorizontalBarChart
								heading={'Top Food Items by Units Sold'}
								chartData={topFoodData}
								yAxisWidth={100}
							/>
						</Col>
						<Col xl={4}>
							<HorizontalBarChart
								heading={'Top Drink Items by Units Sold'}
								chartData={topDrinkData}
								yAxisWidth={70}
							/>
						</Col>
					</Row>
				</div>
				<div className="pa-b-32">
					<VerticleStackedBarChart
						heading={'Total No. of Refunded Orders'}
						data={refundedOrdersData}
						yAxisWidth={56}
					/>
				</div>
				<div className="pa-b-32">
					<Row className="g-3">
						<Col xl={4}>
							<DataCard
								count={10}
								heading={'Total No. of Orders Not Picked Up'}
							/>
						</Col>
						<Col xl={4}>
							<DataCard
								count={5}
								heading={'Total No. of Orders Not Served'}
							/>
						</Col>
						<Col xl={4}>
							<DataCard
								count={45}
								heading={'Total No. of Too Intoxicated Orders'}
							/>
						</Col>
					</Row>
				</div>
				<div>
					<Row className="g-3">
						<Col xl={6}>
							<TotalPerformingCard
								icon={<PercentageCircle />}
								heading={'Total Performing Promo Codes'}
								data={TotalPerformingPromoCodesData}
							/>
						</Col>
						<Col xl={6}>
							<TotalPerformingCard
								icon={<TaxCircle />}
								heading={'Total Performing Taxes'}
								data={TotalPerformingPromoCodesData}
							/>
						</Col>
					</Row>
				</div>
			</PageWrapper>
		</Scrollbars>
	);
};
