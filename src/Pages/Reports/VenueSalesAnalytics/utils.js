import { DownArrowIcon2, UpArrowIcon2 } from '../../../Components/Icons';

export const revenueLocationData = [
	{
		name: 'Jetty Bar',
		value: 110
	},
	{
		name: 'Riviera Bar',
		value: 220
	},
	{
		name: 'Reception',
		value: 220
	},
	{
		name: 'Pizza Bar',
		value: 75
	},
	{
		name: 'Table Service',
		value: 180
	}
];
export const topMenuData = [
	{
		name: 'All Day Menu',
		value: 110
	},
	{
		name: 'Food',
		value: 220
	},
	{
		name: 'Beer',
		value: 220
	},
	{
		name: 'Ciders',
		value: 75
	},
	{
		name: 'Spirits',
		value: 180
	}
];
export const topFoodData = [
	{
		name: 'All Day Menu',
		value: 110
	},
	{
		name: 'Pizza',
		value: 220
	},
	{
		name: 'Sandwich',
		value: 220
	},
	{
		name: 'Italian',
		value: 75
	},
	{
		name: 'Pasta',
		value: 180
	}
];
export const topDrinkData = [
	{
		name: 'Jetty Bar',
		value: 110
	},
	{
		name: 'Beer',
		value: 220
	},
	{
		name: 'Wine',
		value: 220
	},
	{
		name: 'Crushes',
		value: 75
	},
	{
		name: 'Rum',
		value: 180
	}
];

export const TotalPerformingPromoCodesData = [
	{
		country: 'Australia',
		count: '1',
		percentage: '50%',
		icon: <UpArrowIcon2 />
	},
	{
		country: 'China',
		count: '2',
		percentage: '64%',
		icon: <DownArrowIcon2 />
	},
	{ country: 'Newzealand', count: '1', percentage: '-', icon: null }
];

export const overTimeChartData = [
	{
		name: '1 Jan-31 Dec 2022',
		data: [
			{ category: 'JAN 2022', value: 0 },
			{ category: 'FEB 2022', value: 100 },
			{ category: 'MAR 2022', value: 400 },
			{ category: 'APR 2022', value: 700 },
			{ category: 'MAY 2022', value: 1000 },
			{ category: 'JUN 2022', value: 600 },
			{ category: 'JUL 2022', value: 800 },
			{ category: 'AUG 2022', value: 1200 },
			{ category: 'SEP 2022', value: 700 },
			{ category: 'OCT 2022', value: 200 },
			{ category: 'NOV 2022', value: 100 },
			{ category: 'DEC 2022', value: 0 }
		],
		color: '#FFC3C3'
	},
	{
		name: '1 Jan-31 Dec 2021',
		data: [
			{ category: 'JAN 2022', value: 200 },
			{ category: 'FEB 2022', value: 200 },
			{ category: 'MAR 2022', value: 800 },
			{ category: 'APR 2022', value: 900 },
			{ category: 'MAY 2022', value: 1300 },
			{ category: 'JUN 2022', value: 2000 },
			{ category: 'JUL 2022', value: 1700 },
			{ category: 'AUG 2022', value: 1800 },
			{ category: 'SEP 2022', value: 1100 },
			{ category: 'OCT 2022', value: 800 },
			{ category: 'NOV 2022', value: 900 },
			{ category: 'DEC 2022', value: 0 }
		],
		color: '#FF5F5F'
	}
];
export const serviceTypeData = [
	{
		name: 'Table Service',
		value: 55,
		fill: '#F94D73'
	},
	{
		name: 'Takeaway',
		value: 45,
		fill: '#FF9568'
	}
];

export const refundedOrdersData = {
	chartConfig: [
		{
			name: 'Total No. of Refunds Table Service Orders',
			dataKey: 'tableServiceOrders',
			fill: '#FF5F5F'
		},
		{
			name: 'Total No. of Refunds Takeaway Orders',
			dataKey: 'takeawayOrders',
			fill: '#FFC3C3'
		}
	],
	chartData: [
		{
			name: '9 AM',
			tableServiceOrders: 5,
			takeawayOrders: 7
		},
		{
			name: '10 AM',
			tableServiceOrders: 22,
			takeawayOrders: 10
		},
		{
			name: '11 AM',
			tableServiceOrders: 30,
			takeawayOrders: 10
		},
		{
			name: '12 AM',
			tableServiceOrders: 9,
			takeawayOrders: 1
		},
		{
			name: '1 PM',
			tableServiceOrders: 39,
			takeawayOrders: 3
		},
		{
			name: '2 PM',
			tableServiceOrders: 19,
			takeawayOrders: 7
		},
		{
			name: '3 PM',
			tableServiceOrders: 32,
			takeawayOrders: 16
		},
		{
			name: '4 PM',
			tableServiceOrders: 19,
			takeawayOrders: 7
		},
		{
			name: '5 PM',
			tableServiceOrders: 19,
			takeawayOrders: 7
		}
	]
};
