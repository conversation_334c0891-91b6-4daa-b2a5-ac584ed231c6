export const byServiceTypeChartData = [
	{
		name: 'Takeaway',
		data: [
			{ category: 9, value: 10 },
			{ category: 10, value: 10 },
			{ category: 11, value: 20 },
			{ category: 12, value: 22 },
			{ category: 13, value: 38 },
			{ category: 14, value: 50 },
			{ category: 15, value: 42 },
			{ category: 16, value: 43 },
			{ category: 17, value: 4 }
		],
		color: '#FFC3C3'
	},
	{
		name: 'Table Service',
		data: [
			{ category: 9, value: 0 },
			{ category: 10, value: 5 },
			{ category: 11, value: 10 },
			{ category: 12, value: 12 },
			{ category: 13, value: 28 },
			{ category: 14, value: 40 },
			{ category: 15, value: 32 },
			{ category: 16, value: 33 },
			{ category: 17, value: 3 }
		],
		color: '#FF5F5F'
	}
];
export const byPickupChartData = [
	{
		name: 'Riviera Bar',
		data: [
			{ category: 9, value: 11 },
			{ category: 10, value: 10 },
			{ category: 11, value: 25 },
			{ category: 12, value: 22 },
			{ category: 13, value: 50 },
			{ category: 14, value: 40 },
			{ category: 15, value: 35 },
			{ category: 16, value: 18 },
			{ category: 17, value: 9 }
		],
		color: '#FFC3C3'
	},
	{
		name: 'Jetty Bar',
		data: [
			{ category: 9, value: 0 },
			{ category: 10, value: 10 },
			{ category: 11, value: 15 },
			{ category: 12, value: 20 },
			{ category: 13, value: 32 },
			{ category: 14, value: 35 },
			{ category: 15, value: 28 },
			{ category: 16, value: 16 },
			{ category: 17, value: 0 }
		],
		color: '#FF5F5F'
	},
	{
		name: 'Pizza Bar',
		data: [
			{ category: 9, value: 0 },
			{ category: 10, value: 6 },
			{ category: 11, value: 11 },
			{ category: 12, value: 13 },
			{ category: 13, value: 25 },
			{ category: 14, value: 27 },
			{ category: 15, value: 23 },
			{ category: 16, value: 11 },
			{ category: 17, value: 0 }
		],
		color: '#B11212'
	}
];
export const markedAsReadyData = [
	{
		name: 'Beers',
		data: [
			{ category: 9, value: 11 },
			{ category: 10, value: 10 },
			{ category: 11, value: 25 },
			{ category: 12, value: 22 },
			{ category: 13, value: 50 },
			{ category: 14, value: 40 },
			{ category: 15, value: 35 },
			{ category: 16, value: 18 },
			{ category: 17, value: 9 }
		],
		color: '#FFC3C3'
	},
	{
		name: 'Coffee',
		data: [
			{ category: 9, value: 0 },
			{ category: 10, value: 10 },
			{ category: 11, value: 15 },
			{ category: 12, value: 20 },
			{ category: 13, value: 32 },
			{ category: 14, value: 35 },
			{ category: 15, value: 28 },
			{ category: 16, value: 16 },
			{ category: 17, value: 0 }
		],
		color: '#FF5F5F'
	},
	{
		name: 'Wine',
		data: [
			{ category: 9, value: 0 },
			{ category: 10, value: 6 },
			{ category: 11, value: 11 },
			{ category: 12, value: 13 },
			{ category: 13, value: 25 },
			{ category: 14, value: 27 },
			{ category: 15, value: 23 },
			{ category: 16, value: 11 },
			{ category: 17, value: 0 }
		],
		color: '#B11212'
	}
];
