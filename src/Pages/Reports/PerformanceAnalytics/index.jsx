import Scrollbars from 'react-custom-scrollbars';
import { useSelector } from 'react-redux';
import { Col, Row } from 'reactstrap';
import PageWrapper from './index.style';
import {
	byPickupChartData,
	byServiceTypeChartData,
	markedAsReadyData
} from './utils';
import { CircleDollarIcon, Clock, Orders } from '../../../Components/Icons';
import SubPerformanceCard from '../../../Components/Reports/PerformanceAnalytics/SubPerformanceCard';
import ReportsDropDown from '../../../Components/Reports/ReportsDropDown';
import MultiSeriesLineChart from '../../../Components/Common/Charts/MultiSeriesLineChart';
import ComingSoon from '../../../Components/Common/ComingSoon';

export const PerformanceAnalytics = () => {
	const allThemeData = useSelector((state) => ({ ...state.themeChanger }));
	const multiSeriesYAxisData = [
		{ label: '0', value: '0' },
		{ label: '10', value: '10' },
		{ label: '20', value: '20' },
		{ label: '30', value: '30' },
		{ label: '40', value: '40' },
		{ label: '50', value: '50' },
		{ label: '60', value: '60' }
	];
	const multiSeriesXAxisData = [
		{ label: '9 AM', value: 9 },
		{ label: '10 AM', value: 10 },
		{ label: '11 AM', value: 11 },
		{ label: '12 AM', value: 12 },
		{ label: '1 PM', value: 13 },
		{ label: '2 PM', value: 14 },
		{ label: '3 PM', value: 15 },
		{ label: '4 PM', value: 16 },
		{ label: '5 PM', value: 17 }
	];
	return (
		<Scrollbars autoHide>
			<PageWrapper {...allThemeData}>
				<div className="fs-24 medium-text pa-b-32">
					MyTab Venue Performance Analytics
				</div>
				<div className="performanceCard defaultBoxShadow">
					<h2 className="fs-24 medium-text pa-b-24">
						Your current performance compared to venues within 15km
						of your location
					</h2>
					<Row className="g-4">
						<Col sm={12} xl={4}>
							<SubPerformanceCard
								count={'2'}
								heading={'Total Revenue'}
								icon={
									<CircleDollarIcon height={35} width={35} />
								}
							/>
						</Col>
						<Col sm={12} xl={4}>
							<SubPerformanceCard
								count={'1'}
								heading={'Total Orders'}
								icon={
									<Orders
										height={35}
										width={35}
										fill={'#FF5F5F'}
									/>
								}
							/>
						</Col>
						<Col sm={12} xl={4}>
							<SubPerformanceCard
								count={'2'}
								heading={
									'Avg Time Between Order Received & Order Marked As Ready'
								}
								icon={
									<Clock
										height={35}
										width={35}
										fill={'#FF5F5F'}
									/>
								}
							/>
						</Col>
					</Row>
				</div>
				<div className="ptb-32">
					<ReportsDropDown />
				</div>
				<div className="bg-white border-radius-16 defaultBoxShadow ">
					<h2
						className="plr-24 pa-t-24 fs-24 medium-text"
						style={{ textAlign: 'justify' }}
					>
						Avg Time Between Order Received & Order Marked As Ready
					</h2>
					<Row className="">
						<Col xl={6}>
							<MultiSeriesLineChart
								heading={'By Service Type'}
								chartData={byServiceTypeChartData}
								yAxisData={multiSeriesYAxisData}
								xAxisData={multiSeriesXAxisData}
								yAxisWidth={56}
							/>
						</Col>
						<Col xl={6}>
							<MultiSeriesLineChart
								heading={'By Pick Up Location'}
								chartData={byPickupChartData}
								yAxisData={multiSeriesYAxisData}
								xAxisData={multiSeriesXAxisData}
								yAxisWidth={56}
							/>
						</Col>
					</Row>
				</div>
				<div className="bg-white border-radius-16 defaultBoxShadow ma-t-32">
					<h2 className="plr-24 pa-t-24 fs-24 medium-text">
						Avg Time Between Order Received & Order Marked As Ready
					</h2>
					<Row className="">
						<Col xl={6}>
							<MultiSeriesLineChart
								heading={'By Service Type'}
								chartData={byServiceTypeChartData}
								yAxisData={multiSeriesYAxisData}
								xAxisData={multiSeriesXAxisData}
								yAxisWidth={56}
							/>
						</Col>
						<Col xl={6}>
							<MultiSeriesLineChart
								heading={'By Pick Up Location'}
								chartData={byPickupChartData}
								yAxisData={multiSeriesYAxisData}
								xAxisData={multiSeriesXAxisData}
								yAxisWidth={56}
							/>
						</Col>
					</Row>
				</div>
				<div className="bg-white border-radius-16 defaultBoxShadow ma-t-32 ma-b-48">
					<h2 className="plr-24 pa-t-24 fs-24 medium-text">
						Avg Time Between Order Received & Order Marked As Ready
					</h2>
					<MultiSeriesLineChart
						chartData={markedAsReadyData}
						yAxisData={multiSeriesYAxisData}
						xAxisData={multiSeriesXAxisData}
						yAxisWidth={56}
					/>
				</div>
			</PageWrapper>
		</Scrollbars>
	);
};
