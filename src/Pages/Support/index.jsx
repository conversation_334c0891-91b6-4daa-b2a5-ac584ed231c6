import { useSelector } from 'react-redux';
import { Tab, Tabs } from 'react-tabs-scrollable';
import { useEffect, useState } from 'react';
import { TabContent, TabPane } from 'reactstrap';
import { toast } from 'react-toastify';

import ContactUs from '../../Components/Support/ContactUs';
import PageStructure from '../../Components/Common/PageStructure';
import { TabsWrapper, PageWrapper } from './index.style';
import Api from '../../Helper/Api';
import { VenueApiRoutes } from '../../Utils/routes';
import Loader from '../../Components/Common/Loader';

export const Support = ({ reqTabId }) => {
	const allThemeData = useSelector((state) => ({ ...state.themeChanger }));
	const [faqsLoading, setFaqsLoading] = useState(false);
	const [faqsData, setFaqsData] = useState(null);
	// eslint-disable-next-line no-unused-vars

	const getFaqs = async () => {
		setFaqsLoading(true);
		try {
			const res = await Api('GET', VenueApiRoutes.faqs);
			if (res?.data?.status) {
				setFaqsData(res?.data?.data?.content);
			} else {
				toast.error(res?.data?.message);
			}
			setFaqsLoading(false);
		} catch (err) {
			setFaqsLoading(false);
			if (err?.response?.data?.message) {
				toast.error(err?.response?.data?.message);
			}
		}
	};

	useEffect(() => {
		(async () => {
			await getFaqs();
		})();
	}, []);

	return (
		<PageStructure
			title="FAQ Support Forum"
		>
			<PageWrapper {...allThemeData}>
				<div
					dangerouslySetInnerHTML={{
						__html: faqsData
					}}
					className="fs-12"
				/>
			</PageWrapper>
		</PageStructure>
	);
};
