import styled from 'styled-components';

export const ProductTaxWrraper = styled.div`
	font-family: nunitosans-regular !important;

	.customeHrClass {
		border: 0.6px solid #949596;
		margin-block: 10px;
	}

	ul {
		margin: 0px;
		padding-bottom: 18px;
	}

	.boldText{
		font-family: nunitosans-bold !important;
	}

	.titleWrap {
		display: flex;
		flex-direction: column;

		.headerClass {
			display: flex;
			align-items: center;
			height: 38px !important;
			margin-bottom: 14px;
		}

		.subMainTitle {
			font-size: 16px;
			font-family: nunitosans-bold;
			color: #2e2e2e;
		}

		.mainParagraph {
			font-size: 16px;
			font-family: nunitosans-regular;
			color: #2e2e2e;
			padding-bottom: 18px;
		}

		.listElement {
			font-size: 16px;
			font-family: nunitosans-regular;
			color: #2e2e2e;
		}
	}

	.input-group {
		.inputGroup,
		formGroupIcon {
			background-color: #fbfcff !important;
		}
		.inputBox {
			background-color: #fbfcff !important;
			font-family: nunitosans-regular !important;
			color: #a4a5a7 !important;
			font-size: 16px !important;
		}
	}

	.table-count {
		background: #fbfcff;
		height: 3.81vw;
		min-height: 2em;
		border: 0.6px solid rgb(148, 150, 152, 0.5);
		border-radius: 4px;
		margin: 2.25em 0em 1.25em 0em;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 1em 1.5em;
	}

	.customeLabelClass  {
		font-family: nunitosans-bold;
		color: #202224;
		font-size: 18px !important;
		padding-bottom: 0px;
		text-transform: none;
	}
	.customeInputClass {
		font-family: nunitosans-semi-bold !important;
		color: #2e2e2e !important;
		border: 0.6px solid #d5d5d5 !important;
		width: 90% !important;
	}

	.customeRateInputClass {
		font-family: nunitosans-semi-bold !important;
		color: #2e2e2e !important;
		border: 0.6px solid #d5d5d5 !important;
		width: 77% !important;
	}

	@media (max-width: 600px) {
		.titleWrap {

			.subMainTitle {
				font-size: 11px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 11px;
				color: #202224;
				font-family: nunitosans-regular;
				padding-bottom: 18px;
			}

			.listElement {
				font-size: 11px;
				font-family: nunitosans-regular;
				color: #2e2e2e;
			}
		}

		.table-count {
			height: 4.1vw;
			border-radius: 4px;
			margin: 0.8em 0em;
			display: flex;
			justify-content: space-between;
			padding: 0.8em 1.2em;
		}

		.customeLabelClass  {
			font-family: nunitosans-bold;
			font-size: 12.15px;
		}
	}

	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.titleWrap {
			.subMainTitle {
				font-size: 11px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 11px;
				font-family: nunitosans-regular;
				padding-bottom: 18px;
			}

			.listElement {
				font-size: 11px;
				font-family: nunitosans-regular;
				color: #2e2e2e;
			}
		}

		.table-count {
			height: 4.1vw;
			border-radius: 4px;
			margin: 0.8em 0em;
			display: flex;
			justify-content: space-between;
			padding: 0.8em 1.2em;
		}

		.customeLabelClass  {
			font-family: nunitosans-bold;
			font-size: 13.5px !important;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.titleWrap {
			.subMainTitle {
				font-size: 12px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 12px;
				font-family: nunitosans-regular;
				padding-bottom: 18px;
			}

			.listElement {
				font-size: 12px;
				font-family: nunitosans-regular;
				color: #2e2e2e;
			}
		}

		.table-count {
			height: 4.1vw;
			border-radius: 4px;
			margin: 0.8em 0em;
			display: flex;
			justify-content: space-between;
			padding: 0.8em 1.2em;
		}

		.customeLabelClass  {
			font-family: nunitosans-bold;
			font-size: 13.5px !important;
		}
	}
`;

export const PageWrapper = styled.div`
	color: ${(props) => props.layoutTheme.textColor} !important;
`;