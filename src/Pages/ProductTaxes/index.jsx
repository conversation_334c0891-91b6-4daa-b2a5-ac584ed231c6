import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { VenueApiRoutes } from '../../Utils/routes';
import { ProductTaxWrraper } from './index.style';
import Api from '../../Helper/Api';
import { toast } from 'react-toastify';
import NewDesignCustomLabel from '../../Components/Form/NewDesignCustomLabel';
import NewDesignFormInput from '../../Components/Form/NewDesignFormInput';
import { Col, Row } from 'reactstrap';
import NewPageTitle from '../../Components/Common/NewPageTitle';
import NewPageWrapper from '../../Components/Common/NewPageWrapper';

const ProductTaxes = () => {
    const state = useSelector((state) => ({ ...state })); 
    const authData = state.auth;
    const [loading, setLoading] = useState(false);
    const [dataReady, setDataReady] = useState(false);
    const [taxRate, setTaxRate] = useState('');

    useEffect(() => {
        getProductTaxRate();
    }, [authData?.selectedVenue?.id]);

    const getProductTaxRate = async () => {
        setLoading(true);
        setDataReady(false);
        try {
            const res = await Api(
                'GET',
                VenueApiRoutes.getProductTax,
                {
                    bar_id: authData?.selectedVenue?.id
                }
            );
            setLoading(false);
            if (res?.data?.status) {
                let rateValue = res?.data?.data?.value;
                setTaxRate(Number(rateValue).toFixed(2));
                setDataReady(true);
            } else {
                setLoading(false);
                toast.error(res?.data?.message);
            }
        } catch (err) {
            setLoading(false);
            if (err?.message) {
                toast.error(err?.message);
            }
        }
    };

    return (
        <NewPageWrapper>
            <ProductTaxWrraper>
            <div className="titleWrap">
                <div className="headerClass">
					<NewPageTitle>Product Taxes</NewPageTitle>
                </div>
                <p className="mainParagraph">
                    This page outlines the tax settings linked to your products from the Manage Menu screen. Choose from options like GST (10%) or Tax-Free to ensure proper accounting and compliance with Australian tax regulations.
                </p>
                <ul className=''>
                    <li className="listElement">
                        <span className='boldText'>GST:</span> The product is subject to Goods and Services Tax (GST).
                    </li>
                    <li className="listElement">
                        <span className='boldText'>No Tax:</span> The product is exempt from tax.
                    </li>
                </ul>
                <p className="mainParagraph">
                    These settings help maintain accurate records and ensure compliance with Australian tax requirements.
                </p>
            </div>

            <div>
                <div>
                    <Row>
                        <Col className="pa-b-20 pb-0">
                            <NewDesignCustomLabel
                                id={'taxName'}
                                label={'Tax Name'}
                                className={'customeLabelClass'}
                            />
                        </Col>
                        <Col className="pa-b-20 pb-0">
                            <NewDesignCustomLabel
                                id={'taxRate'}
                                label={'Tax Rate (%)'}
                                className={'customeLabelClass'}
                            />
                        </Col>
                    </Row>
                </div>
                <hr className='customeHrClass' />
                <div>
                    <Row>
                        <Col className="pa-b-20 pb-0">
                            <NewDesignFormInput
                                value={'GST'}
                                className={'customeInputClass'}
                                readOnly={true}
                            />
                        </Col>
                        <Col className="pa-b-20 pb-0">
                            <NewDesignFormInput
                                value={dataReady ? taxRate : ''}
                                className={'customeRateInputClass'}
                                readOnly={true}
                            />
                        </Col>
                    </Row>
                </div>
                <hr className='customeHrClass' />
                <div>
                    <Row>
                        <Col className="pa-b-20 pb-0">
                            <NewDesignFormInput
                                value={'Tax Free'}
                                className={'customeInputClass'}
                                readOnly={true}
                            />
                        </Col>
                        <Col className="pa-b-20 pb-0">
                            <NewDesignFormInput
                                value={'00.00'}
                                className={'customeRateInputClass'}
                                disabled={true}
                            />
                        </Col>
                    </Row>
                </div>
                <hr className='customeHrClass' />
            </div>
            </ProductTaxWrraper>
        </NewPageWrapper>
    );
};

export default ProductTaxes;
