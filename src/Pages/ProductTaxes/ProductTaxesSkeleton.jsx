import React from 'react';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import { Row, Col } from 'reactstrap';

const ProductTaxesSkeleton = () => {
    return (
        <div>
            <div className="">
                <Skeleton height={40} width={200} style={{ marginBottom: '1em' }} />
                <Skeleton height={20} count={1} width={300} style={{ marginBottom: '1em' }} />
                <Skeleton height={20} count={1} style={{ marginBottom: '1em' }} />
                <Skeleton height={30} width={150} style={{ marginBottom: '0.5em' }} />
                <ul>
                    <li><Skeleton height={20} width={300} /></li>
                    <li><Skeleton height={20} width={300} /></li>
                </ul>
                <Skeleton height={30} width={150} style={{ marginBottom: '0.5em' }} />
                <ul>
                    <li><Skeleton height={20} width={300} /></li>
                    <li><Skeleton height={20} width={300} /></li>
                </ul>
                <Skeleton height={20} count={1} style={{ marginBottom: '1em' }} />
            </div>

            <div>
                <Row>
                    <Col className="pa-b-20 pb-0">
                        <Skeleton height={20} width={100} />
                    </Col>
                    <Col className="pa-b-20 pb-0">
                        <Skeleton height={20} width={100} />
                    </Col>
                </Row>
                <hr className='customeHrClass' />

                <Row>
                    <Col className="pa-b-20 pb-0">
                        <Skeleton height={40} width={200} />
                    </Col>
                    <Col className="pa-b-20 pb-0">
                        <Skeleton height={40} width={200} />
                    </Col>
                </Row>
                <hr className='customeHrClass' />

                <Row>
                    <Col className="pa-b-20 pb-0">
                        <Skeleton height={40} width={200} />
                    </Col>
                    <Col className="pa-b-20 pb-0">
                        <Skeleton height={40} width={200} />
                    </Col>
                </Row>
                <hr className='customeHrClass' />
            </div>
        </div>
    );
};

export default ProductTaxesSkeleton;