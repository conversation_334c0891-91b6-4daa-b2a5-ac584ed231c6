import React from 'react';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';

import NewPopover from '../../../Components/Common/NewPopover';
import { StyleWrraper } from './index.style';
import { VenuePanelRoutes } from '../../../Utils/routes';

const ActionPopover = ({ handleExport, segmentData }) => {
	const navigate = useNavigate();
	return (
		<NewPopover
			positions={['bottom', 'left', 'top', 'right']}
			align="end"
			onContentClick={(closePopover) => {
				closePopover();
			}}
			content={
				<StyleWrraper>
					<div
						onClick={() => {
							toast.success('This feature is coming soon.');
						}}
					>
						Advertise to segment
					</div>
					<div
						onClick={() =>
							navigate(VenuePanelRoutes.createNewDiscount, {
								state: {
									type: 'segment',
									selectedItem: segmentData
								}
							})
						}
					>
						Give discount to segment
					</div>
					<div onClick={handleExport}>Export</div>
				</StyleWrraper>
			}
		>
			<span className="actionText">Action</span>
		</NewPopover>
	);
};

export default ActionPopover;
