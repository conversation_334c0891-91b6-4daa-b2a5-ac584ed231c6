import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import fileDownload from 'js-file-download';
import moment from 'moment';

import { VenueApiRoutes, VenuePanelRoutes } from '../../../Utils/routes';
import Api from '../../../Helper/Api';
import BackIcon from '../../../Assets/images/back-icon.svg';
import { FilledButton } from '../../../Components/Layout/Buttons';
import { StyleWrraper } from './index.style';
import MoreActionsPopover from '../MoreActionsPopover';
import SegmentCustomersTable from '../SegmentCustomersTable';
import { segmentCustomersTableColumns } from '../utils';
import { TableCountSkeleton, TitleSkeleton } from './Skeleton';
import NewPageWrapper from '../../../Components/Common/NewPageWrapper';
import NewPageTitle from '../../../Components/Common/NewPageTitle';

let timeoutVar;

const SegmentDetails = () => {
	const state = useSelector((state) => ({ ...state }));
	const navigate = useNavigate();
	const params = useParams();
	const [detailsLoading, setDetailsLoading] = useState(false);
	const [tableLoading, setTableLoading] = useState(false);
	const [segmentDetails, setSegmentDetails] = useState(null);
	const [tableData, setTableData] = useState([]);
	const [tableParams, setTableParams] = useState({
		totalCount: 0,
		currentPage: 1,
		pageSize: 10,
		searchTerm: '',
		sortBy: {
			id: 1,
			name: 'Newest - Oldest Customer',
			value: 'newToOld'
		}
	});
	const authDetails = state?.auth;
	const selectedVenue = authDetails?.selectedVenue;

	const handlePageChange = ({ selected }) => {
		setTableParams({ ...tableParams, currentPage: selected + 1 });
	};

	const handleSearchInputChange = (e) => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
		}
		timeoutVar = setTimeout(() => {
			setTableParams({
				...tableParams,
				currentPage: 1,
				searchTerm: e?.target?.value
			});
		}, 500);
	};

	const handleSortByChange = (item) => {
		setTableParams({
			...tableParams,
			currentPage: 1,
			sortBy: item
		});
	};

	const getTableData = async (data) => {
		try {
			setTableLoading(true);
			let payload = {
				bar_id: data?.barId,
				segment_id: data?.segmentId,
				page: tableParams?.currentPage,
				sortBy: tableParams?.sortBy?.value
					? tableParams?.sortBy?.value
					: 'newToOld',
				search: tableParams?.searchTerm ? tableParams?.searchTerm : ''
			};
			const res = await Api(
				'POST',
				VenueApiRoutes?.getSegmentCustomerList,
				payload
			);
			if (res?.data?.status) {
				setTableData(res?.data?.data?.rows);
				setTableParams((prev) => {
					return {
						...prev,
						totalCount: res?.data?.data?.count
					};
				});
			} else {
				setTableData([]);
				setTableParams((prev) => {
					return {
						...prev,
						currentPage: 1,
						totalCount: 0
					};
				});
				toast.error(res?.data?.message);
			}
			setTableLoading(false);
		} catch (err) {
			setTableLoading(false);
			setTableData([]);
			setTableParams((prev) => {
				return {
					...prev,
					currentPage: 1,
					totalCount: 0
				};
			});
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	const getSegmentDetails = async (data) => {
		try {
			setDetailsLoading(true);
			const res = await Api('POST', VenueApiRoutes?.getSegmentDetails, {
				bar_id: data?.barId,
				segment_id: data?.segmentId
			});
			if (res?.data?.status) {
				setSegmentDetails(res?.data?.data);
			} else {
				toast.error(res?.data?.message);
			}
			setDetailsLoading(false);
		} catch (err) {
			setDetailsLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	const handleExportSegmentCustomerList = async () => {
		try {
			const res = await Api(
				'POST',
				VenueApiRoutes?.exportSegmentCustomerTableData,
				{
					bar_id: selectedVenue?.id,
					segment_id: params?.id
				}
			);
			if (res?.data?.status) {
				const blob = new Blob([res?.data?.data], {
					type: 'text/plain;charset=utf-8'
				});
				const fileName = `${
					selectedVenue?.restaurantName
				} MyTab Segment Customers (${moment().format(
					'DD-MM-YYYY'
				)}).csv`;
				fileDownload(blob, fileName);
				toast.success(res?.data?.message);
			} else {
				toast.error(res?.data?.message);
			}
		} catch (err) {
			if (err?.message) toast.error(err?.message);
		}
	};

	useEffect(() => {
		if (selectedVenue?.id && params?.id) {
			getSegmentDetails({
				barId: selectedVenue?.id,
				segmentId: params?.id
			});
		}
	}, [selectedVenue?.id, params?.id]);

	useEffect(() => {
		if (selectedVenue?.id && params?.id) {
			getTableData({
				barId: selectedVenue?.id,
				segmentId: params?.id
			});
		}
	}, [
		selectedVenue?.id,
		params?.id,
		tableParams?.currentPage,
		tableParams?.pageSize,
		tableParams?.searchTerm,
		tableParams?.sortBy?.value
	]);

	return (
		<NewPageWrapper>
			<StyleWrraper>
				<div className="titleWrapper">
					{detailsLoading ? (
						<TitleSkeleton />
					) : (
						<div className="titleTextWrapper">
							<span className="">
								<img
									src={BackIcon}
									alt="back-icon"
									className="backIcon"
									onClick={() =>
										navigate(VenuePanelRoutes?.segments)
									}
								/>
							</span>
							<NewPageTitle>
								{segmentDetails?.name
									? segmentDetails?.name
									: ''}
							</NewPageTitle>
						</div>
					)}
					{/* <div className="titleButtonWrapper">
						<FilledButton
							buttonText={'Advertise to segment'}
							background={'rgba(44, 154, 255, 0.2)'}
							color={'rgba(44, 154, 255, 1)'}
							style={{
								width: '160px',
								border: '1px solid rgba(44, 154, 255, 1)'
							}}
							onClick={() =>
								toast.success('This feature is coming soon!')
							}
						/>
						<MoreActionsPopover
							handleExport={handleExportSegmentCustomerList}
							segmentData={segmentDetails}
						>
							<FilledButton
								buttonText={'More actions'}
								background={'rgba(107, 194, 66, 0.2)'}
								color={'rgba(107, 194, 66, 1)'}
								style={{
									width: '160px',
									border: '1px solid rgba(107, 194, 66, 1)'
								}}
							/>
						</MoreActionsPopover>
					</div> */}
				</div>
				{detailsLoading ? (
					<TableCountSkeleton />
				) : (
					<div className="tableCount">
						<div className="leftText">
							<span className="leftTextBold">
								{segmentDetails?.segmentCustomerCount
									? segmentDetails?.segmentCustomerCount
									: 0}
							</span>
							{' customers | '}
							<span className="leftTextBold">
								{segmentDetails?.customerPercentage
									? segmentDetails?.customerPercentage
									: 0}
								%
							</span>
							{' of your total customers'}
						</div>
						<div className="rightText">
							Showing {tableData?.length ? tableData?.length : 0}{' '}
							of{' '}
							{tableParams?.totalCount
								? tableParams?.totalCount
								: 0}{' '}
							customers
						</div>
					</div>
				)}
				<div className="pa-t-16">
					<SegmentCustomersTable
						loading={tableLoading}
						tableColumns={segmentCustomersTableColumns}
						tableData={tableData}
						tableDataCount={tableData?.length}
						totalCount={tableParams?.totalCount}
						currentPage={tableParams?.currentPage}
						selectedSortBy={tableParams?.sortBy}
						handleSearchInputChange={handleSearchInputChange}
						handleSortByChange={handleSortByChange}
						handlePageChange={handlePageChange}
					/>
				</div>
				<div className="linkWrapper pa-t-20">
					<div>
						<span className="normalText">Learn more about </span>
						<span
							className="activeText"
							onClick={() => navigate(VenuePanelRoutes.support)}
						>
							segments
						</span>
					</div>
				</div>
			</StyleWrraper>
		</NewPageWrapper>
	);
};

export default SegmentDetails;
