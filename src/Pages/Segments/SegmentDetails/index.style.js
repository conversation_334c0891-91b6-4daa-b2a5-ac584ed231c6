import styled from 'styled-components';

export const StyleWrraper = styled.div`
	.titleWrapper {
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;
		gap: 14px;
		padding-bottom: 14px;
		.titleTextWrapper {
			display: flex;
			align-items: center;
			gap: 10px;
			.backIcon {
				display: block;
				width: 28px;
				height: 28px;
				cursor: pointer;
			}
		}
		.titleButtonWrapper {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: flex-end;
			gap: 22px;
		}
	}

	.tableCount {
		border: 1px solid #d5d5d5;
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-inline: 26px;
		height: 73px;
		background: #fbfcff !important;
		.leftText {
			color: #202224d9;
			font-family: 'nunitosans-regular';
			font-size: 16px;
		}
		.leftTextBold {
			color: #202224d9;
			font-family: 'nunitosans-bold';
			font-size: 16px;
		}
		.rightText {
			color: #20222466;
			font-family: 'nunitosans-regular';
			font-size: 16px;
		}
	}

	.linkWrapper {
		display: flex;
		align-items: center;
		justify-content: center;
		.normalText {
			color: #202224d9;
			font-family: 'nunitosans-regular';
			font-size: 16px;
		}
		.activeText {
			color: #f95c69 !important;
			text-decoration: underline;
			font-family: 'nunitosans-regular';
			font-size: 16px;
			cursor: pointer;
		}
	}

	@media only screen and (max-width: 600px) {
		.titleWrapper {
			padding-bottom: 14px;
		}
		.tableCount {
			padding-inline: 16px;
			height: 54px;
			.leftText {
				font-size: 12px;
			}
			.leftTextBold {
				font-size: 12px;
			}
			.rightText {
				font-size: 12px;
			}
		}
		.linkWrapper {
			.normalText {
				font-size: 12px;
			}
			.activeText {
				font-size: 12px;
			}
		}
	}

	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.titleWrapper {
			padding-bottom: 14px;
			.titleTextWrapper {
				.backIcon {
					width: 21px;
					height: 21px;
				}
			}
		}
		.tableCount {
			padding-inline: 20px;
			height: 54px;
			.leftText {
				font-size: 12px;
			}
			.leftTextBold {
				font-size: 12px;
			}
			.rightText {
				font-size: 12px;
			}
		}
		.linkWrapper {
			.normalText {
				font-size: 12px;
			}
			.activeText {
				font-size: 12px;
			}
		}
	}

	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.titleWrapper {
			padding-bottom: 14px;
			.titleTextWrapper {
				.backIcon {
					width: 21px;
					height: 21px;
				}
			}
		}
		.tableCount {
			padding-inline: 20px;
			height: 54px;
			.leftText {
				font-size: 12px;
			}
			.leftTextBold {
				font-size: 12px;
			}
			.rightText {
				font-size: 12px;
			}
		}
		.linkWrapper {
			.normalText {
				font-size: 12px;
			}
			.activeText {
				font-size: 12px;
			}
		}
	}

	@media only screen and (max-width: 840px) {
		.titleWrapper {
			.titleTextWrapper {
				.backIcon {
					width: 21px;
					height: 21px;
				}
			}
		}
	}
`;
