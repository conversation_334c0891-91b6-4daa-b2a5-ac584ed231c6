import styled from 'styled-components';

export const StyleWrraper = styled.div`
	border-radius: 14px;
	background-color: white;
	box-shadow: 0px 9px 40px 0px #0000001b;
	width: max-content !important;
	div {
		font-family: 'nunitosans-semi-bold' !important;
		font-size: 14px !important;
		color: #404040 !important;
		border-bottom: 1px solid rgba(151, 151, 151, 0.25);
		padding: 15px 20px;
		cursor: pointer;
	}
	div:last-child {
		border-bottom: none;
	}
	@media only screen and (max-width: 1299px) {
		div {
			font-size: 11px !important;
			padding: 11px 15px;
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		div {
			font-size: 11px !important;
			padding: 11px 15px;
		}
	}
`;
