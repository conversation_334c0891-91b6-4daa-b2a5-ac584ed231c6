import React from 'react';
import { useNavigate } from 'react-router-dom';

import NewPopover from '../../../Components/Common/NewPopover';
import { StyleWrraper } from './index.style';
import { VenuePanelRoutes } from '../../../Utils/routes';

const MoreActionsPopover = ({ children, handleExport, segmentData }) => {
	const navigate = useNavigate();
	return (
		<NewPopover
			positions={['bottom', 'left', 'top', 'right']}
			align="end"
			onContentClick={(closePopover) => {
				closePopover();
			}}
			content={
				<StyleWrraper className="ma-t-4 mr-8">
					<div
						onClick={() =>
							navigate(VenuePanelRoutes.createNewDiscount, {
								state: {
									type: 'segment',
									selectedItem: segmentData
								}
							})
						}
					>
						Give discount to segment
					</div>
					<div onClick={handleExport}>Export</div>
				</StyleWrraper>
			}
		>
			{children}
		</NewPopover>
	);
};

export default MoreActionsPopover;
