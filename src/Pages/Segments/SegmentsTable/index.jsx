import ReactTable from 'react-table-6';
import 'react-table-6/react-table.css';

import TableSkeleton from './TableSkeleton';
import NewSearchBox from '../../../Components/Form/NewSearchBox/NewSearchBox';
import { SearchIcon } from '../../../Components/Icons/SearchIcon/SearchIcon';
import StyleWrraper from './index.style';

const SegmentsTable = ({
	loading,
	tableData,
	tableColumns,
	tableDataCount,
	tableDataParentIndexes,
	handleSearchInputChange,
	handleRowClick
}) => {
	return (
		<StyleWrraper>
			<div className="search-sort pa-8">
				<NewSearchBox
					formGroupClassName="formGroupClassName"
					labelClassName="labelClassName"
					inputGroupTextClassName="inputGroupTextClassName"
					inputClassName="inputClassName"
					iconBackgroundClass="iconBackgroundClass"
					type="text"
					placeholder="Search segments"
					icon={<SearchIcon className="inputIcon" />}
					iconPlacement="start"
					onChange={(event) => {
						handleSearchInputChange(event?.target?.value);
					}}
				/>
			</div>
			{loading ? (
				<>
					<TableSkeleton />
				</>
			) : (
				<div className="tableContainer">
					<ReactTable
						columns={tableColumns}
						data={tableData}
						showPagination={false}
						pageSize={tableDataCount}
						NoDataComponent={() => (
							<span className="rtNoDataFound">No data found</span>
						)}
						resizable={false}
						getTrProps={(state, row) => {
							let params = {};
							let style = {};
							// row before parent row, parent row, last row
							if (
								tableDataParentIndexes?.includes(
									row?.index + 1
								) ||
								row?.original?.isParent == 1 ||
								row?.index == Number(tableDataCount) - 1
							) {
								style.borderBottom = 'none';
							}
							// parent row
							if (row?.original?.isParent == 1) {
								style.backgroundColor =
									'rgba(151, 151, 151, 0.31)';
							} else {
								style.cursor = 'pointer';
								params.onClick = () => handleRowClick(row?.row);
							}
							return {
								...params,
								style: { ...style }
							};
						}}
						getTdProps={(state, row, column) => {
							let params = {};
							let style = {};
							if (column?.id === 'action') {
								style.overflow = 'unset';
							}
							// parent row
							if (row?.original?.isParent == 1) {
								params.className = 'parentRowText';
							} else {
								params.className = 'nonParentRowText';
							}
							return {
								...params,
								style: { ...style }
							};
						}}
					/>
				</div>
			)}
		</StyleWrraper>
	);
};

export default SegmentsTable;
