import styled from 'styled-components';

const StyleWrraper = styled.div`
	width: 100%;
	border: 1px solid #d5d5d5;
	border-radius: 4px;
	font-family: 'nunitosans-regular' !important;
	background: #fbfcff !important;

	.inputClassName::placeholder {
		color: #a4a5a7;
		font-size: 16px;
		font-family: 'nunitosans-regular' !important;
	}

	.inputClassName:focus {
		outline: none;
		box-shadow: none;
		border-color: #a4a5a7;
	}

	.inputGroupTextClassName,
	.inputClassName {
		border: none;
		padding: 0.375rem 0.375rem;
		background: none !important;
		font-family: 'nunitosans-regular' !important;
		color: #a4a5a7 !important;
		font-size: 16px !important;
		transition: color 0.3s ease, border-color 0.3s ease;
	}

	.inputClassName:focus {
		color: #000000 !important;
		border-color: #a4a5a7;
		outline: none;
	}

	.inputIcon {
		height: 17px;
		width: 19px;
		font-family: 'nunitosans-regular' !important;
		color: #a4a5a7 !important;
		font-size: 16px !important;
	}

	.formGroupClassName {
		margin: 0px !important;
	}

	.search-sort {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.tableContainer {
		width: 100%;
		overflow: auto;
		&::-webkit-scrollbar {
			width: 4px !important;
			height: 4px !important;
		}
		&::-webkit-scrollbar-thumb {
			border-radius: 4px !important;
			background: rgba(0, 0, 0, 0.2) !important;
		}
	}

	.ReactTable {
		border: none;
		border-radius: 0px 0px 4px 4px;
		font-family: 'nunitosans-regular' !important;
		height: 100%;
		width: 100%;
		min-width: 870px;
		overflow: hidden;
		.rt-table {
			font-family: 'nunitosans-regular' !important;
			overflow: auto;
			::-webkit-scrollbar {
				width: 5px;
				height: 5px;
			}
		}
		.rt-thead.-header {
			background-color: #fff;
			box-shadow: none !important;
			position: sticky;
			top: 0;
			display: ${(props) => (props.hideHeader ? 'none' : '')};
		}
		.rt-thead {
			.rt-tr {
				.rt-th {
					min-width: 50px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					height: 46px;
					padding: 8px 18px !important;
					background-color: #f5f6fa !important;
					border: none;

					font-size: 14px !important;
					font-family: 'nunitosans-bold' !important;
					color: #202224 !important;
					display: flex;
					justify-content: center;
					align-items: center;
					div {
						font-size: 14px !important;
						font-family: 'nunitosans-bold' !important;
						color: #202224 !important;
						display: flex;
						align-items: center;
						justify-content: center;
					}
					&.-cursor-pointer {
						box-shadow: none !important;
						display: flex;
						justify-content: center;
						align-items: center;
						&:before {
							content: ' ';
							position: absolute;
							right: 15px;
							font-size: 4rem !important;
							color: #fd6461 !important;
							background-color: transparent !important;
							background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE0IDIyTDEwIDI2TDYgMjIiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTEwIDZWMjYiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTE4IDEwTDIyIDZMMjYgMTAiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTIyIDI2VjYiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);
							background-repeat: no-repeat;
							background-size: 100% 100%;
						}
					}
					&.-sort-desc.-cursor-pointer {
						&:before {
							content: ' ' !important;
							font-size: 4rem !important;
							font-family: 'nunitosans-regular' !important;
							color: #fd6461 !important;
							background-color: transparent !important;
							background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE0IDIyTDEwIDI2TDYgMjIiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTEwIDZWMjYiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTE4IDEwTDIyIDZMMjYgMTAiIHN0cm9rZT0iIzYxNjE2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTIyIDI2VjYiIHN0cm9rZT0iIzYxNjE2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);
							background-repeat: no-repeat;
							background-size: 100% 100%;
						}
					}
					&.-sort-asc.-cursor-pointer {
						&:before {
							content: ' ' !important;
							font-size: 4rem !important;
							font-family: 'nunitosans-regular' !important;
							color: #fd6461 !important;
							background-color: transparent !important;
							background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE0IDIyTDEwIDI2TDYgMjIiIHN0cm9rZT0iIzYxNjE2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTEwIDZWMjYiIHN0cm9rZT0iIzYxNjE2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTE4IDEwTDIyIDZMMjYgMTAiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTIyIDI2VjYiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);
							background-repeat: no-repeat;
							background-size: 100% 100%;
						}
					}
				}
			}
			.rt-tr > div:first-child {
				padding-left: 69px !important;
			}
			.rt-tr > div:last-child {
				padding-right: 69px !important;
			}
		}

		.rt-tbody {
			overflow-y: initial;
			overflow-x: hidden;
			border-top: none;
			border-bottom: none;
			::-webkit-scrollbar {
				width: 5px;
				height: 5px;
			}
			::-webkit-scrollbar-track {
				border-radius: 50px;
				margin-bottom: 10px;
			}
			::-webkit-scrollbar-thumb {
				border-radius: 50px;
			}
			.rt-tr-group {
				border: none;
				flex: none;
				.rt-tr {
					border-bottom: 1px solid rgba(148, 150, 152, 0.5);
					.rt-td {
						min-width: 50px;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;

						height: 46px;
						padding: 6px 18px !important;
						border: none;
						border-radius: 0px;

						display: flex;
						justify-content: center;
						align-items: center;
					}
					.nonParentRowText {
						font-size: 16px !important;
						font-family: 'nunitosans-semi-bold' !important;
						color: #2e2e2e !important;
					}
					.parentRowText {
						font-size: 14px !important;
						font-family: 'nunitosans-bold' !important;
						color: #202224 !important;
					}
				}
				.rt-tr > div:first-child {
					padding-left: 69px !important;
				}
				.rt-tr > div:last-child {
					padding-right: 69px !important;
				}
			}
		}
	}
	.rtNoDataFound {
		text-align: center;
		padding: 28px;
		font-family: 'nunitosans-regular' !important;
		font-size: 12px;
		color: #2e2e2e;
	}
	.actionText {
		font-family: 'nunitosans-bold';
		text-decoration: underline;
		font-size: 14px;
	}

	@media only screen and (max-width: 1299px) {
		.inputClassName::placeholder {
			font-size: 12px;
		}
		.inputGroupTextClassName,
		.inputClassName {
			font-size: 12px !important;
		}
		.inputIcon {
			height: 12px !important;
			width: 14px !important;
		}
		.ReactTable {
			.rt-thead {
				.rt-tr {
					.rt-th {
						height: 35px !important;
						font-size: 11px !important;
						div {
							font-size: 11px !important;
						}
					}
				}
				.rt-tr > div:first-child {
					padding-left: 52px !important;
				}
				.rt-tr > div:last-child {
					padding-right: 52px !important;
				}
			}
			.rt-tbody {
				.rt-tr-group {
					.rt-tr {
						.rt-td {
							height: 35px !important;
						}
						.nonParentRowText {
							font-size: 12px !important;
						}
						.parentRowText {
							font-size: 11px !important;
						}
					}
					.rt-tr > div:first-child {
						padding-left: 52px !important;
					}
					.rt-tr > div:last-child {
						padding-right: 52px !important;
					}
				}
			}
		}
		.actionText {
			font-size: 11px;
		}
	}

	@media only screen and (max-width: 1199px) {
		.ReactTable {
			.rt-thead {
				.rt-tr > div:first-child {
					padding-left: 30px !important;
				}
				.rt-tr > div:last-child {
					padding-right: 30px !important;
				}
			}
			.rt-tbody {
				.rt-tr-group {
					.rt-tr > div:first-child {
						padding-left: 30px !important;
					}
					.rt-tr > div:last-child {
						padding-right: 30px !important;
					}
				}
			}
		}
	}

	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.inputClassName::placeholder {
			font-size: 12px;
		}
		.inputGroupTextClassName,
		.inputClassName {
			font-size: 12px !important;
		}
		.inputIcon {
			height: 12px !important;
			width: 14px !important;
		}
		.ReactTable {
			.rt-thead {
				.rt-tr {
					.rt-th {
						height: 35px !important;
						font-size: 11px !important;
						div {
							font-size: 11px !important;
						}
					}
				}
				.rt-tr > div:first-child {
					padding-left: 52px !important;
				}
				.rt-tr > div:last-child {
					padding-right: 52px !important;
				}
			}
			.rt-tbody {
				.rt-tr-group {
					.rt-tr {
						.rt-td {
							height: 35px !important;
						}
						.nonParentRowText {
							font-size: 12px !important;
						}
						.parentRowText {
							font-size: 11px !important;
						}
					}
					.rt-tr > div:first-child {
						padding-left: 52px !important;
					}
					.rt-tr > div:last-child {
						padding-right: 52px !important;
					}
				}
			}
		}
		.actionText {
			font-size: 11px;
		}
	}
`;

export default StyleWrraper;
