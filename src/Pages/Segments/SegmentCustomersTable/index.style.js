import styled from 'styled-components';

export const StyleWrraper = styled.div`
	width: 100%;
	font-family: 'nunitosans-regular' !important;

	.borderBox {
		width: 100%;
		border: 1px solid #d5d5d5;
		border-radius: 4px;
		background: #fbfcff !important;
	}

	.filterWrapper {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.inputClassName::placeholder {
		color: #a4a5a7;
		font-size: 16px;
		font-family: 'nunitosans-regular' !important;
	}

	.inputClassName:focus {
		outline: none;
		box-shadow: none;
		border-color: #a4a5a7;
	}

	.inputGroupTextClassName,
	.inputClassName {
		border: none;
		padding: 0.375rem 0.375rem;
		background: none !important;
		font-family: 'nunitosans-regular' !important;
		color: #a4a5a7 !important;
		font-size: 16px !important;
		transition: color 0.3s ease, border-color 0.3s ease;
	}

	.inputClassName:focus {
		color: #000000 !important;
		border-color: #a4a5a7;
		outline: none;
	}

	.inputIcon {
		height: 17px;
		width: 19px;
		font-family: 'nunitosans-regular' !important;
		color: #a4a5a7 !important;
		font-size: 16px !important;
	}

	.formGroupClassName {
		margin: 0px !important;
	}

	.dropdownWrapper {
		display: flex;
		align-items: center;
		gap: 12px;
		cursor: pointer;
		.dropdownText {
			color: #202224 !important;
			font-family: 'nunitosans-bold' !important;
			font-size: 14px !important;
		}
		.dropdownIcon {
			display: block;
			width: 25px;
			height: 25px;
		}
	}

	.tableContainer {
		width: 100%;
		overflow: auto;
		&::-webkit-scrollbar {
			width: 4px !important;
			height: 4px !important;
		}
		&::-webkit-scrollbar-thumb {
			border-radius: 4px !important;
			background: rgba(0, 0, 0, 0.2) !important;
		}
	}

	.ReactTable {
		border: none;
		border-radius: 0px 0px 4px 4px;
		font-family: 'nunitosans-regular' !important;
		height: 100%;
		width: 100%;
		min-width: 600px;
		overflow: hidden;
		position: relative;
		.noDataFoundContainer {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			font-family: 'nunitosans-regular' !important;
			font-size: 12px;
			color: #2e2e2e;
		}
		.rt-table {
			font-family: 'nunitosans-regular' !important;
			overflow: auto;
			::-webkit-scrollbar {
				width: 5px;
				height: 5px;
			}
		}
		.rt-thead.-header {
			background-color: #fff;
			box-shadow: none !important;
			position: sticky;
			top: 0;
			display: ${(props) => (props.hideHeader ? 'none' : '')};
		}
		.rt-thead {
			.rt-tr {
				.rt-th {
					min-width: 50px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					height: 46px;
					padding: 8px 8px !important;
					background-color: #f5f6fa !important;
					border: none;

					font-size: 14px !important;
					font-family: 'nunitosans-bold' !important;
					color: #202224 !important;
					display: flex;
					justify-content: center;
					align-items: center;
					div {
						font-size: 14px !important;
						font-family: 'nunitosans-bold' !important;
						color: #202224 !important;
						display: flex;
						align-items: center;
						justify-content: center;
					}
					&.-cursor-pointer {
						box-shadow: none !important;
						display: flex;
						justify-content: center;
						align-items: center;
						&:before {
							content: ' ';
							position: absolute;
							right: 15px;
							font-size: 4rem !important;
							color: #fd6461 !important;
							background-color: transparent !important;
							background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE0IDIyTDEwIDI2TDYgMjIiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTEwIDZWMjYiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTE4IDEwTDIyIDZMMjYgMTAiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTIyIDI2VjYiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);
							background-repeat: no-repeat;
							background-size: 100% 100%;
						}
					}
					&.-sort-desc.-cursor-pointer {
						&:before {
							content: ' ' !important;
							font-size: 4rem !important;
							font-family: 'nunitosans-regular' !important;
							color: #fd6461 !important;
							background-color: transparent !important;
							background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE0IDIyTDEwIDI2TDYgMjIiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTEwIDZWMjYiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTE4IDEwTDIyIDZMMjYgMTAiIHN0cm9rZT0iIzYxNjE2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTIyIDI2VjYiIHN0cm9rZT0iIzYxNjE2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);
							background-repeat: no-repeat;
							background-size: 100% 100%;
						}
					}
					&.-sort-asc.-cursor-pointer {
						&:before {
							content: ' ' !important;
							font-size: 4rem !important;
							font-family: 'nunitosans-regular' !important;
							color: #fd6461 !important;
							background-color: transparent !important;
							background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE0IDIyTDEwIDI2TDYgMjIiIHN0cm9rZT0iIzYxNjE2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTEwIDZWMjYiIHN0cm9rZT0iIzYxNjE2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTE4IDEwTDIyIDZMMjYgMTAiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTIyIDI2VjYiIHN0cm9rZT0iI0ZENjQ2MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);
							background-repeat: no-repeat;
							background-size: 100% 100%;
						}
					}
				}
				.rt-th:first-child {
					padding: 8px 0px !important;
				}
			}
		}

		.rt-tbody {
			overflow-y: initial;
			overflow-x: hidden;
			border-top: none;
			border-bottom: none;
			::-webkit-scrollbar {
				width: 5px;
				height: 5px;
			}
			::-webkit-scrollbar-track {
				border-radius: 50px;
				margin-bottom: 10px;
			}
			::-webkit-scrollbar-thumb {
				border-radius: 50px;
			}
			.rt-tr-group {
				border: none;
				flex: none;
				.rt-tr {
					border-bottom: 1px solid rgba(148, 150, 152, 0.5);
					.rt-td {
						min-width: 50px;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;

						height: 46px;
						padding: 6px 8px !important;
						border: none;
						border-radius: 0px;

						display: flex;
						justify-content: center;
						align-items: center;

						font-size: 16px !important;
						font-family: 'nunitosans-semi-bold' !important;
						color: #2e2e2e !important;
					}
				}
			}
		}
	}

	.actionText {
		font-family: 'nunitosans-bold';
		text-decoration: underline;
		font-size: 14px;
	}

	.paginationWrapper {
		padding-top: 14px;
		.customPagination {
			height: auto;
			.pagination {
				height: auto;
				margin-bottom: 0 !important;
				display: flex;
				justify-content: center;
				align-items: center;
				gap: 6px;
				.page-item {
					.page-link {
						font-family: 'nunitosans-semi-bold' !important;
						font-size: 12px !important;
						width: 28px;
						height: 28px;
						padding: 0 !important;
						display: flex;
						align-items: center;
						justify-content: center;
						border-radius: 4px;
						color: #000000 !important;
						background: #fbfcff !important;
						border: 1px solid rgba(213, 213, 213, 1) !important;
						text-decoration: none;
						outline: none !important;
						&:active {
							outline: none !important;
						}
						&:focus {
							box-shadow: none !important;
						}
					}
				}
				.page-item.disabled {
					a {
						opacity: 0.5;
						cursor: not-allowed !important;
					}
				}
				.page-item.active {
					a {
						background-color: #fd6461 !important;
						color: #fff !important;
						border: 1px solid #fd6461 !important;
					}
				}
			}
		}
	}

	@media only screen and (max-width: 1299px) {
		.inputClassName::placeholder {
			font-size: 12px;
		}
		.inputGroupTextClassName,
		.inputClassName {
			font-size: 12px !important;
		}
		.inputIcon {
			height: 12px !important;
			width: 14px !important;
		}
		.dropdownWrapper {
			.dropdownText {
				font-size: 12px !important;
			}
		}
		.ReactTable {
			.rt-thead {
				.rt-tr {
					.rt-th {
						height: 35px !important;
						font-size: 11px !important;
						div {
							font-size: 11px !important;
						}
					}
				}
			}
			.rt-tbody {
				.rt-tr-group {
					.rt-tr {
						.rt-td {
							height: 35px !important;
							font-size: 12px !important;
						}
					}
				}
			}
		}
		.actionText {
			font-size: 11px;
		}
	}

	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.inputClassName::placeholder {
			font-size: 12px;
		}
		.inputGroupTextClassName,
		.inputClassName {
			font-size: 12px !important;
		}
		.inputIcon {
			height: 12px !important;
			width: 14px !important;
		}
		.dropdownWrapper {
			.dropdownText {
				font-size: 12px !important;
			}
		}
		.ReactTable {
			.rt-thead {
				.rt-tr {
					.rt-th {
						height: 35px !important;
						font-size: 11px !important;
						div {
							font-size: 11px !important;
						}
					}
				}
			}
			.rt-tbody {
				.rt-tr-group {
					.rt-tr {
						.rt-td {
							height: 35px !important;
							font-size: 12px !important;
						}
					}
				}
			}
		}
		.actionText {
			font-size: 11px;
		}
	}
`;

export const PopoverStyleWrraper = styled.div`
	border-radius: 14px;
	background-color: white;
	box-shadow: 0px 9px 40px 0px #0000001b;
	width: max-content !important;
	div {
		font-family: 'nunitosans-semi-bold' !important;
		font-size: 14px !important;
		color: #404040 !important;
		border-bottom: 1px solid rgba(151, 151, 151, 0.25);
		padding: 15px 20px;
		cursor: pointer;
	}
	div:last-child {
		border-bottom: none;
	}
	@media only screen and (max-width: 1299px) {
		div {
			font-size: 11px !important;
			padding: 11px 15px;
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		div {
			font-size: 11px !important;
			padding: 11px 15px;
		}
	}
`;
