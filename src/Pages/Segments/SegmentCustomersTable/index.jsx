import { useEffect, useMemo, useState } from 'react';
import { Input } from 'reactstrap';
import ReactTable from 'react-table-6';
import 'react-table-6/react-table.css';

import TableSkeleton from './TableSkeleton';
import DropdownIcon from '../../../Assets/images/newDropdownIconWOCircle.svg';
import NewSearchBox from '../../../Components/Form/NewSearchBox/NewSearchBox';
import { SearchIcon } from '../../../Components/Icons/SearchIcon/SearchIcon';
import NewPopover from '../../../Components/Common/NewPopover';
import { NewPagination } from '../../../Components/Common/NewPagination';
import { PopoverStyleWrraper, StyleWrraper } from './index.style';
import { sortByData } from '../utils';
import FormCheckBox from '../../../Components/Form/FormCheckBox';

const SegmentCustomersTable = ({
	loading,
	tableColumns,
	tableData,
	tableDataCount,
	totalCount,
	currentPage,
	selectedSortBy,
	handleSearchInputChange,
	handleSortByChange,
	handlePageChange
}) => {
	const [selectedCustomersIds, setSelectedCustomersIds] = useState([]);
	const [isCheckedHeaderCheckbox, setIsCheckedHeaderCheckbox] =
		useState(false);

	const handleSelectAll = () => {
		if (isCheckedHeaderCheckbox) {
			if (tableData?.length > 0) {
				let tempCustomersIds = [];
				tempCustomersIds = selectedCustomersIds?.filter((id) => {
					return !tableData?.some((item) => item?.userID == id);
				});
				setSelectedCustomersIds(tempCustomersIds);
			}
		} else {
			if (tableData?.length > 0) {
				let tempCustomersIds = [];
				tableData?.forEach((item) => {
					if (!selectedCustomersIds?.includes(item?.userID)) {
						tempCustomersIds?.push(item?.userID);
					}
				});
				setSelectedCustomersIds([
					...selectedCustomersIds,
					...tempCustomersIds
				]);
			}
		}
	};

	const handleCheckboxChange = (customerId) => {
		if (selectedCustomersIds?.includes(customerId)) {
			let updatedSelectedCustomersIds = selectedCustomersIds?.filter(
				(item) => item !== customerId
			);
			setSelectedCustomersIds(updatedSelectedCustomersIds);
		} else {
			setSelectedCustomersIds([...selectedCustomersIds, customerId]);
		}
	};

	const checkboxColumn = useMemo(
		() => ({
			id: 'select',
			Header: () => (
				<FormCheckBox
					style={{ height: '1.4em', width: '1.4em' }}
					checked={isCheckedHeaderCheckbox}
					onClick={(e) => e.stopPropagation()}
					onChange={handleSelectAll}
				/>
			),
			Cell: (row) => {
				return (
					<FormCheckBox
						style={{ height: '1.4em', width: '1.4em' }}
						checked={selectedCustomersIds?.includes(
							row?.original?.userID
						)}
						onClick={(e) => e.stopPropagation()}
						onChange={() =>
							handleCheckboxChange(row?.original?.userID)
						}
					/>
				);
			},
			sortable: false,
			minWidth: 50,
			maxWidth: 50
		}),
		[
			tableData,
			handleCheckboxChange,
			handleSelectAll,
			selectedCustomersIds,
			isCheckedHeaderCheckbox
		]
	);

	useEffect(() => {
		if (tableData?.length > 0) {
			let isCheck = true;
			for (let item of tableData) {
				if (!selectedCustomersIds?.includes(item?.userID)) {
					isCheck = false;
					break;
				}
			}
			setIsCheckedHeaderCheckbox(isCheck);
		}
	}, [tableData, selectedCustomersIds]);

	return (
		<StyleWrraper>
			<div className="borderBox">
				<div className="filterWrapper pa-8">
					<NewSearchBox
						formGroupClassName="formGroupClassName"
						labelClassName="labelClassName"
						inputGroupTextClassName="inputGroupTextClassName"
						inputClassName="inputClassName"
						iconBackgroundClass="iconBackgroundClass"
						type="text"
						placeholder="Search customers"
						icon={<SearchIcon className="inputIcon" />}
						iconPlacement="start"
						onChange={handleSearchInputChange}
					/>
					<NewPopover
						positions={['bottom', 'left', 'top', 'right']}
						align="end"
						onContentClick={(closePopover) => {
							closePopover();
						}}
						content={
							<PopoverStyleWrraper>
								{sortByData?.length > 0 &&
									sortByData?.map((item) => {
										return (
											<div
												key={item?.id}
												onClick={() =>
													handleSortByChange(item)
												}
											>
												{item?.name}
											</div>
										);
									})}
							</PopoverStyleWrraper>
						}
					>
						<div className="dropdownWrapper">
							<span className="dropdownText">
								{selectedSortBy?.name
									? selectedSortBy?.name
									: 'Sort by'}
							</span>
							<img
								className="dropdownIcon"
								src={DropdownIcon}
								alt="dropdown-icon"
							/>
						</div>
					</NewPopover>
				</div>
				{loading ? (
					<>
						<TableSkeleton />
					</>
				) : (
					<div className="tableContainer">
						<ReactTable
							columns={[checkboxColumn, ...tableColumns]}
							data={tableData}
							showPagination={false}
							// pageSize={10}
							minRows={
								totalCount > 10 || totalCount == 0 ? 10 : 0
							}
							resizable={false}
							NoDataComponent={() => (
								<span className="noDataFoundContainer">
									No data found
								</span>
							)}
							getTrProps={(state, row) => {
								let lastIndex =
									totalCount > 10 ? 9 : tableDataCount - 1;
								if (!row || row?.index == lastIndex) {
									return {
										style: { borderBottom: 'none' }
									};
								}
								return {};
							}}
						/>
					</div>
				)}
			</div>
			{totalCount > 0 && (
				<div className="paginationWrapper">
					<NewPagination
						handlePageChange={handlePageChange}
						total={totalCount}
						pageSize={10}
						currentPage={currentPage}
					/>
				</div>
			)}
		</StyleWrraper>
	);
};

export default SegmentCustomersTable;
