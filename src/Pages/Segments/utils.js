import ActionPopover from './ActionPopover';

export const getTableColumns = ({ handleExportSegmentCustomerList }) => {
    return [
        {
            Header: 'Name',
            accessor: 'name',
            className: 'justify-content-start',
            minWidth: 450,
            maxWidth: 600,
            filterable: false,
            sortable: false,
            headerClassName: 'justify-content-start'
        },
        {
            Header: 'Number of customers',
            accessor: 'customerCount',
            className: 'justify-content-start',
            minWidth: 170,
            maxWidth: 300,
            filterable: false,
            sortable: false,
            headerClassName: 'justify-content-start',
            Cell: (row) => {
                if (row?.original?.isParent == 1) {
                    return;
                }
                return row?.value;
            }
        },
        {
            Header: '% of total customers',
            accessor: 'customerPercentage',
            className: 'justify-content-start',
            minWidth: 150,
            filterable: false,
            sortable: false,
            headerClassName: 'justify-content-start',
            Cell: (row) => {
                if (row?.original?.isParent == 1) {
                    return;
                }
                return row?.value + '%';
            }
        },
        {
            Header: '',
            accessor: 'action',
            className: 'justify-content-end',
            maxWidth: 140,
            filterable: false,
            sortable: false,
            headerClassName: 'justify-content-center',
            Cell: (row) => {
                if (row?.original?.isParent == 1) {
                    return;
                }
                return (
                    <div onClick={(e) => e.stopPropagation()}>
                        <ActionPopover
                            handleExport={() => {
                                handleExportSegmentCustomerList(
                                    row?.original?.id
                                );
                            }}
                            segmentData={row?.original}
                        />
                    </div>
                );
            }
        }
    ];
};

export const segmentCustomersTableColumns = [
    {
        Header: 'Customer name',
        accessor: 'user.fullName',
        className: 'justify-content-start',
        minWidth: 250,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'Orders',
        accessor: 'totalOrderCount',
        className: 'justify-content-start',
        minWidth: 150,
        maxWidth: 200,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'Amount spent',
        accessor: 'totalOrderAmount',
        className: 'justify-content-start',
        minWidth: 150,
        maxWidth: 200,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start',
        Cell: (row) => {
            return row?.value ? '$' + row?.value : '0.00';
        }
    }
];

export const sortByData = [
    {
        id: 1,
        name: 'Newest - Oldest Customer',
        value: 'newToOld'
    },
    {
        id: 2,
        name: 'Oldest - Newest Customer',
        value: 'oldToNew'
    },
    {
        id: 3,
        name: 'Alphabetical A-Z',
        value: 'alphabeticAsc'
    },
    {
        id: 4,
        name: 'Alphabetical Z-A',
        value: 'alphabeticDesc'
    },
    {
        id: 5,
        name: 'Highest - Lowest Total Orders',
        value: 'highestTotalOrder'
    },
    {
        id: 6,
        name: 'Lowest - Highest Total Orders',
        value: 'lowestTotalOrder'
    }
];

