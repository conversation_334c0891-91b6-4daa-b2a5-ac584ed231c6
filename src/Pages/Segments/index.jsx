import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import fileDownload from 'js-file-download';
import moment from 'moment';

import { getTableColumns } from './utils';
import { useDebounce } from '../../Hooks/useDebounce';
import { FilledButton } from '../../Components/Layout/Buttons';
import { VenueApiRoutes, VenuePanelRoutes } from '../../Utils/routes';
import { StyleWrraper } from './index.style';
import Api from '../../Helper/Api';
import SegmentsTable from './SegmentsTable';
import TableCountSkeleton from './TableCountSkeleton';
import NewPageTitle from '../../Components/Common/NewPageTitle';
import NewPageWrapper from '../../Components/Common/NewPageWrapper';

const Segments = () => {
	const state = useSelector((state) => ({ ...state }));
	const navigate = useNavigate();
	const [loading, setLoading] = useState(false);
	const [exportLoading, setExportLoading] = useState(false);
	const [tableData, setTableData] = useState([]);
	const [tableDataCount, setTableDataCount] = useState(null);
	const [segmentsCount, setSegmentsCount] = useState(0);
	const [tableDataParentIndexes, setTableDataParentIndexes] = useState([]);
	const [searchTerm, setSearchTerm] = useState('');
	const debounceSearchTerm = useDebounce(searchTerm, 500);

	const authDetails = state?.auth;
	const selectedVenue = authDetails?.selectedVenue;

	const getTableData = async (payload) => {
		try {
			setLoading(true);
			const res = await Api(
				'POST',
				VenueApiRoutes.getSegmentList,
				payload
			);
			if (res?.data?.status) {
				let parentIndexes = [];
				let tempSegmentsCount = 0;
				if (res?.data?.data?.list?.length > 0) {
					res?.data?.data?.list?.forEach((item, index) => {
						if (Number(item?.isParent) === 1) {
							parentIndexes.push(index);
						} else {
							tempSegmentsCount++;
						}
					});
				}
				setTableData(res?.data?.data?.list);
				setTableDataCount(res?.data?.data?.list?.length);
				setTableDataParentIndexes(parentIndexes);
				setSegmentsCount(tempSegmentsCount);
			} else {
				toast.error(res?.data?.message);
			}
			setLoading(false);
		} catch (err) {
			setLoading(false);
			if (err?.message) toast.error(err?.message);
		}
	};

	const handleExportSegmentList = async () => {
		try {
			setExportLoading(true);
			const res = await Api(
				'POST',
				VenueApiRoutes.exportSegmentTableData,
				{
					bar_id: selectedVenue?.id
				}
			);
			if (res?.data?.status) {
				const blob = new Blob([res?.data?.data], {
					type: 'text/plain;charset=utf-8'
				});
				const fileName = `${selectedVenue?.restaurantName
					} MyTab Segments (${moment().format('DD-MM-YYYY')}).csv`;
				fileDownload(blob, fileName);
				toast.success(res?.data?.message);
			} else {
				toast.error(res?.data?.message);
			}
			setExportLoading(false);
		} catch (err) {
			setExportLoading(false);
			if (err?.message) toast.error(err?.message);
		}
	};

	const handleExportSegmentCustomerList = async (segmentId) => {
		try {
			const res = await Api(
				'POST',
				VenueApiRoutes?.exportSegmentCustomerTableData,
				{
					bar_id: selectedVenue?.id,
					segment_id: segmentId
				}
			);
			if (res?.data?.status) {
				const blob = new Blob([res?.data?.data], {
					type: 'text/plain;charset=utf-8'
				});
				const fileName = `${selectedVenue?.restaurantName
					} MyTab Segment Customers (${moment().format(
						'DD-MM-YYYY'
					)}).csv`;
				fileDownload(blob, fileName);
				toast.success(res?.data?.message);
			} else {
				toast.error(res?.data?.message);
			}
		} catch (err) {
			if (err?.message) toast.error(err?.message);
		}
	};

	const handleSearchInputChange = async (value) => {
		setSearchTerm(value);
	};

	const handleRowClick = (rowData) => {
		navigate(
			VenuePanelRoutes.segmentsDetails + `/${rowData?._original?.id}`
		);
	};

	useEffect(() => {
		(async () => {
			await getTableData({
				bar_id: selectedVenue?.id,
				search: debounceSearchTerm
			});
		})();
	}, [debounceSearchTerm, selectedVenue?.id]);

	return (
		<NewPageWrapper>
			<StyleWrraper>
				<div className="titleWrapper">
					<NewPageTitle>Segments</NewPageTitle>
					<FilledButton
						buttonText={'Export'}
						background={'#d8d9f9'}
						color={'#3D42DF'}
						style={{ width: '160px', border: '1px solid #3D42DF' }}
						onClick={handleExportSegmentList}
						loading={exportLoading}
					/>
				</div>
				<p className="mainParagraph">
					MyTab has categorised your customers with similar
					characteristics and purchasing patterns into specific
					segments to enhance your in-app advertising and discount
					strategies to personally engage with your customers more
					effectively.
				</p>
				{loading ? (
					<TableCountSkeleton />
				) : (
					<div className="tableCount">
						<div className="textOne">{segmentsCount} segments</div>
						<div className="textTwo">
							Showing {segmentsCount} of {segmentsCount} segments
						</div>
					</div>
				)}
				<div className="pa-t-16">
					<SegmentsTable
						loading={loading}
						tableColumns={getTableColumns({
							handleExportSegmentCustomerList
						})}
						tableData={tableData}
						tableDataCount={tableDataCount}
						tableDataParentIndexes={tableDataParentIndexes}
						handleSearchInputChange={handleSearchInputChange}
						handleRowClick={handleRowClick}
					/>
				</div>
				<div className="linkWrapper pa-t-20">
					<div>
						<span className="normalText">Visit </span>
						<span
							className="activeText"
							onClick={() => navigate(VenuePanelRoutes.support)}
						>
							Segments FAQ Forum
						</span>
					</div>
				</div>
			</StyleWrraper>
		</NewPageWrapper>
	);
};

export default Segments;
