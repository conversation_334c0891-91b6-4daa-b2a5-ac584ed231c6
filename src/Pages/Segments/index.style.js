import styled from 'styled-components';

export const StyleWrraper = styled.div`
	.titleWrapper {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-bottom: 14px;
	}
    
	.mainParagraph {
		color: #2e2e2e;
		font-family: 'nunitosans-regular';
		font-size: 16px;
		padding-bottom: 24px;
	}

	.tableCount {
		border: 1px solid #d5d5d5;
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-inline: 26px;
		height: 73px;
		background: #fbfcff !important;
		.textOne {
			color: #202224d9;
			font-family: 'nunitosans-bold';
			font-size: 16px;
		}
		.textTwo {
			color: #20222466;
			font-family: 'nunitosans-regular';
			font-size: 16px;
		}
	}

	.linkWrapper {
		display: flex;
		align-items: center;
		justify-content: center;
		.normalText {
			color: #202224d9;
			font-family: 'nunitosans-regular';
			font-size: 16px;
		}
		.activeText {
			color: #f95c69 !important;
			text-decoration: underline;
			font-family: 'nunitosans-regular';
			font-size: 16px;
			cursor: pointer;
		}
	}

	@media (max-width: 600px) {
		.titleWrapper {
			padding-bottom: 14px;
		}
		.mainParagraph {
			font-size: 12px;
			padding-bottom: 18px;
		}
		.tableCount {
			padding-inline: 16px;
			height: 54px;
			.textOne {
				font-size: 12px;
			}
			.textTwo {
				font-size: 12px;
			}
		}
		.linkWrapper {
			.normalText {
				font-size: 12px;
			}
			.activeText {
				font-size: 12px;
			}
		}
	}

	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.titleWrapper {
			padding-bottom: 14px;
		}
		.mainParagraph {
			font-size: 12px;
			padding-bottom: 18px;
		}
		.tableCount {
			padding-inline: 20px;
			height: 54px;
			.textOne {
				font-size: 12px;
			}
			.textTwo {
				font-size: 12px;
			}
		}
		.linkWrapper {
			.normalText {
				font-size: 12px;
			}
			.activeText {
				font-size: 12px;
			}
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.titleWrapper {
			padding-bottom: 14px;
		}
		.mainParagraph {
			font-size: 12px;
			padding-bottom: 18px;
		}
		.tableCount {
			padding-inline: 20px;
			height: 54px;
			.textOne {
				font-size: 12px;
			}
			.textTwo {
				font-size: 12px;
			}
		}
		.linkWrapper {
			.normalText {
				font-size: 12px;
			}
			.activeText {
				font-size: 12px;
			}
		}
	}
`;
