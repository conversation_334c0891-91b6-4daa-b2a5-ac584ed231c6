import styled from 'styled-components';

const PageWrapper = styled.div`
	background-color: #ffffff;
	height: 100%;
	color: ${(props) => props.layoutTheme.textColor} !important;
	@media only screen and (max-width: 767px) {
		padding: 0px;
	}
	.page {
		box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.08);
		border-radius: 16px;
		padding: 20px;
		height: 100%;
		display: flex;
		flex-direction: column;
		@media only screen and (max-width: 767px) {
			border-radius: 0px;
		}
	}
	.page-header {
		padding-bottom: 32px;
	}
	.page-body {
		flex: 1 1 auto;
	}
	.themeHr {
		margin: 0px !important;
		border: 1px solid #dddddd;
	}
	.min-w-52 {
		min-width: 52px;
	}
	.customDivider {
		width: 0.5px;
		background-color: #dddddd;
		margin: 0px 10px;
	}
	.orderCard {
		border: 1px solid #84d565;
		border-radius: 8px;
		&.newOrder {
			.card-header {
				border-radius: 7px 7px 0px 0px;
				background: #84d565;
				.orderWaitTime,
				.orderNo {
					color: #fff;
				}
			}
		}
		&.tableService {
			border: 1px solid #ff482f;
			.card-header {
				border-radius: 7px 7px 0px 0px;
				background: #ff482f;
				.orderWaitTime,
				.orderNo {
					color: #fff;
				}
			}
		}
	}
	.topBox {
		border: 1px solid #eaeaea;
		border-radius: 8px;
		padding: 32px 24px;
	}
`;

export default PageWrapper;
