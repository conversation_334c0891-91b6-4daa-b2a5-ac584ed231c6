import * as Yup from 'yup';

const MIN_PASS_LEN = 8;

// Updated regex to be more permissive with special characters
const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[\d!@#$%^&*(),.?":{}|<>\-])[A-Za-z\d!@#$%^&*(),.?":{}|<>\-]{8,}$/;

export const registerFormSchema = Yup.object().shape({
    image: Yup.mixed().required('Please upload profile image.'),
    business_name: Yup.string().trim().required('Please enter business name.'),
    business_url: Yup.string()
        .trim()
        .url('Please enter a valid url.')
        .required('Please enter business website url.'),
    acn_number: Yup.number()
        .typeError('Please enter a numerical value for your ABN/ACN.')
        .required('Please enter ABN/ACN number.'),
    contact_name: Yup.string().trim().required('Please enter contact name.'),
    timezone: Yup.string().required('Please select timezone.'),
    email: Yup.string()
        .trim()
        .email('Invalid email.')
        .required('Please enter your email.'),
    emailCode: Yup.string()
        .trim()
        .required('Please enter 6-digit email verification code.')
        .matches(/^[0-9]+$/, 'Email verification code must be only digits.')
        .min(6, 'Email verification code must be exactly 6 digits.')
        .max(6, 'Email verification code must be exactly 6 digits.'),
    password: Yup.string()
        .trim()
        .required('Please enter password.')
        .test('password-validation', 
            `Password must be a minimum of ${MIN_PASS_LEN} characters and include at least one letter, one number and one special character.`,
            value => {
                // Simple individual checks instead of regex
                if (!value || value.length < 8) return false;
                if (!/[a-z]/.test(value)) return false;
                if (!/[A-Z]/.test(value)) return false;
                if (!/[\d!@#$%^&*(),.?":{}|<>\-]/.test(value)) return false;
                return true;
            }),
    confirmPassword: Yup.string()
        .trim()
        .oneOf([Yup.ref('password'), null], 'Passwords must match.')
        .required('Please confirm your password.'),
    code: Yup.string()
        .trim()
        .required('Please enter 6-digit authenticator code.')
        .matches(/^[0-9]+$/, 'Authenticator code must be only digits.')
        .min(6, 'Authenticator code must be exactly 6 digits.')
        .max(6, 'Authenticator code must be exactly 6 digits.')
});

export const loginFormSchema = Yup.object().shape({
    email: Yup.string()
        .trim()
        .email('Invalid email.')
        .required('Please enter your email.'),
    password: Yup.string().trim().required('Please enter password.'),
    code: Yup.string()
        .trim()
        .required('Please enter 6-digit authenticator code.')
        .matches(/^[0-9]+$/, 'Authenticator code must be only digits.')
        .min(6, 'Authenticator code must be exactly 6 digits.')
        .max(6, 'Authenticator code must be exactly 6 digits.')
});

export const forgotPasswordFormSchema = Yup.object().shape({
    email: Yup.string()
        .trim()
        .email('Invalid email.')
        .required('Please enter your email.')
});

export const resetPasswordFormSchema = Yup.object().shape({
    password: Yup.string()
        .trim()
        .required('Please enter password.')
        .test('password-validation', 
            `Password must be a minimum of ${MIN_PASS_LEN} characters and include at least one letter, one number and one special character.`,
            value => {
                // Simple individual checks instead of regex
                if (!value || value.length < 8) return false;
                if (!/[a-z]/.test(value)) return false;
                if (!/[A-Z]/.test(value)) return false;
                if (!/[\d!@#$%^&*(),.?":{}|<>\-]/.test(value)) return false;
                return true;
            }),
    confirmPassword: Yup.string()
        .trim()
        .oneOf([Yup.ref('password'), null], 'Passwords must match.')
        .required('Please confirm your password.')
});
