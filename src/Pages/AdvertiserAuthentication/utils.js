const validateAbn = (abn) => {
	if (abn.length !== 11 || isNaN(abn)) {
		return false;
	}

	const weighting = [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19];
	const firstDigit = parseInt(abn.charAt(0), 10);

	if (isNaN(firstDigit)) {
		return false;
	}

	const firstDigitProcessed = firstDigit - 1;
	let weightedSum = firstDigitProcessed * weighting[0];

	for (let i = 1; i < abn.length; i++) {
		const digit = parseInt(abn.charAt(i), 10);
		if (isNaN(digit)) {
			return false;
		}
		weightedSum += digit * weighting[i];
	}

	return weightedSum % 89 === 0;
};

const validateAcn = (acn) => {
	const cleanAcn = acn.replace(/\s/g, '');

	if (cleanAcn.length !== 9 || isNaN(cleanAcn)) {
		return false;
	}

	const weighting = [8, 7, 6, 5, 4, 3, 2, 1];
	let weightedSum = 0;

	for (let i = 0; i < cleanAcn.length - 1; i++) {
		const digit = parseInt(cleanAcn.charAt(i), 10);
		if (isNaN(digit)) {
			return false;
		}
		weightedSum += digit * weighting[i];
	}

	const checkDigit = parseInt(cleanAcn.charAt(cleanAcn.length - 1), 10);
	if (isNaN(checkDigit)) {
		return false;
	}

	const remainder = (10 - (weightedSum % 10)) % 10;

	return checkDigit === remainder;
};

export const validateIdentifier = (identifier) => {
	// Remove spaces
	const cleanIdentifier = identifier.replace(/\s/g, '');

	// Check length for ABN (11 digits) or ACN (9 digits)
	if (cleanIdentifier.length === 11) {
		return validateAbn(cleanIdentifier);
	} else if (cleanIdentifier.length === 9) {
		return validateAcn(cleanIdentifier);
	} else {
		return false; // Invalid length
	}
};

export const validatePassword = (value) => {
	// Check for minimum length of 8
	if (value.length < 8) return false;
	
	// Check for at least one lowercase letter
	if (!/[a-z]/.test(value)) return false;
	
	// Check for at least one uppercase letter
	if (!/[A-Z]/.test(value)) return false;
	
	// Check for at least one number OR special character
	if (!/[0-9!@#$%^&*(),.?":{}|<>\-]/.test(value)) return false;
	
	return true;
};
