import styled from 'styled-components';

const StylesWrapper = styled.div`
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	.newFormWrapper {
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 40px;
	}
	.newFormContentContainer {
		width: 100%;
		max-width: 448px;
		height: fit-content;
	}
	.customCardContentWrapper {
		display: flex;
		flex-direction: column;
		gap: 20px;
	}
	@media (max-height: 860px) {
		.newFormWrapper {
			padding: 8px 30px;
		}
		.customCardContentWrapper {
			gap: 15px;
		}
	}
`;

export default StylesWrapper;
