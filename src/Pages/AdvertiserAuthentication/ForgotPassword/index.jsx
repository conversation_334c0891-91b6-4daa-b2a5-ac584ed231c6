import { useState } from 'react';
import { useFormik } from 'formik';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';

import Layout from '../Layout';
import StylesWrapper from './index.style';
import {
	CustomButton,
	CustomButtonOutlined,
	CustomCard,
	CustomCardTitle,
	MainDescription,
	MainTitle,
	NewFormItemGroup
} from '../index.style';
import Api from '../../../Helper/Api';
import { AdvertiserApiRoutes, CommonRoutes } from '../../../Utils/routes';
import { forgotPasswordFormSchema } from '../validationSchema';
import NewFormInput from '../../../Components/NewForm/NewFormInput';
import NewFormLabel from '../../../Components/NewForm/NewFormLabel';
import NewFormErrorMessage from '../../../Components/NewForm/NewFormErrorMessage';
import NewLoader from '../../../Components/Common/NewLoader';

const ForgotPassword = () => {
	const navigate = useNavigate();
	const [submitButtonLoading, setSubmitButtonLoading] = useState(false);

	const handleFormSubmit = async (values) => {
		try {
			setSubmitButtonLoading(true);
			const res = await Api('POST', AdvertiserApiRoutes?.forgotPassword, {
				email: values?.email
			});
			if (res?.data?.status) {
				toast.success(res?.data?.message);
				navigate(CommonRoutes?.advertiserLogin);
			} else {
				toast.error(res?.data?.message);
			}
			setSubmitButtonLoading(false);
		} catch (err) {
			setSubmitButtonLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	const { values, handleSubmit, handleChange, handleBlur, touched, errors } =
		useFormik({
			initialValues: {
				email: ''
			},
			validationSchema: forgotPasswordFormSchema,
			onSubmit: handleFormSubmit
		});

	return (
		<Layout>
			<StylesWrapper>
				<div className="newFormWrapper">
					<div className="newFormContentContainer">
						<MainTitle>MyTab Ads</MainTitle>
						<MainDescription>
							Connect with real customers and grow your business
							through targeted, powerful ads.
						</MainDescription>
						<CustomCard>
							<form onSubmit={handleSubmit}>
								<div className="customCardContentWrapper">
									<CustomCardTitle>
										Forgot your password?
									</CustomCardTitle>
									<NewFormItemGroup>
										<NewFormLabel
											className={'newFormLabel'}
											label={'Email'}
										/>
										<NewFormInput
											wrapperClassName={
												'newFormInputWrapper'
											}
											placeholder={'Enter your email'}
											name={'email'}
											value={values?.email}
											onChange={handleChange}
											onBlur={handleBlur}
										/>
										<NewFormErrorMessage
											className={'newFormErrorMessage'}
											message={
												touched?.email &&
												!!errors?.email
													? errors?.email
													: ''
											}
										/>
									</NewFormItemGroup>
									<div>
										<CustomButton
											type="submit"
											disabled={submitButtonLoading}
										>
											Send email
											{submitButtonLoading && (
												<NewLoader
													color="#ffffff"
													borderWidth="1.5px"
													size="14px"
												/>
											)}
										</CustomButton>
										<CustomButtonOutlined
											type="button"
											style={{ marginTop: '8px' }}
											onClick={() =>
												navigate(
													CommonRoutes?.advertiserLogin
												)
											}
										>
											Back to Login
										</CustomButtonOutlined>
									</div>
								</div>
							</form>
						</CustomCard>
					</div>
				</div>
			</StylesWrapper>
		</Layout>
	);
};

export default ForgotPassword;
