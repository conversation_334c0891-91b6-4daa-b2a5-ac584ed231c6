import React, { useState } from 'react';
// import { LazyLoadImage } from 'react-lazy-load-image-component';
// import 'react-lazy-load-image-component/src/effects/blur.css';

// import advertiserBanner1 from '../../../Assets/images/advertiser-banner-1.png';
import advertiserBanner2 from '../../../Assets/images/advertiser-banner-2.png';
import NewLoader from '../../../Components/Common/NewLoader';
import StylesWrapper from './index.style';

const Layout = ({ children }) => {
	const [isLoading, setIsLoading] = useState(true);
	return (
		<StylesWrapper>
			<div className="leftSideWrapper">{children}</div>
			<div className="rightSideWrapper">
				{/* <LazyLoadImage
					className="advertiserBanner"
					alt={advertiserBanner1}
					src={advertiserBanner2}
					effect="blur"
					wrapperProps={{
						style: { transitionDelay: '0.5s' }
					}}
				/> */}
				<img
					className="advertiserBanner"
					src={advertiserBanner2}
					alt="advertiser image"
					onLoad={() => setIsLoading(false)}
					onError={() => setIsLoading(false)}
				/>
			</div>
			{isLoading && (
				<div className="pageLoaderWrapper">
					<NewLoader loading={true} />
				</div>
			)}
		</StylesWrapper>
	);
};

export default Layout;
