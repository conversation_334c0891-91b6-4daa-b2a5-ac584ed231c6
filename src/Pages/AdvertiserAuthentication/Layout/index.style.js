import styled from 'styled-components';

const StylesWrapper = styled.div`
	width: 100%;
	height: 100%;
	background-color: #ffffff;
	display: flex;
	position: relative;
	.leftSideWrapper {
		width: 50%;
	}
	.rightSideWrapper {
		width: 50%;
		.advertiserBanner {
			width: 100%;
			height: 100%;
			display: block;
			object-fit: cover;
		}
	}
	.pageLoaderWrapper {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #ffffff;
		z-index: 10;
	}
	@media only screen and (max-width: 960px) {
		.leftSideWrapper {
			width: 100%;
		}
		.rightSideWrapper {
			display: none;
		}
	}
`;

export default StylesWrapper;
