import styled from 'styled-components';

export const MainTitle = styled.h1`
	text-align: center;
	font-family: 'nunitosans-bold' !important;
	font-size: 24px;
	line-height: 32px;
	color: #0f172a;
`;

export const MainDescription = styled.p`
	text-align: center;
	font-family: 'nunitosans-regular' !important;
	font-size: 14px;
	line-height: 20px;
	color: #4b5563;
	padding-top: 4px;
	padding-bottom: 24px;
	@media (max-height: 860px) {
		padding-bottom: 18px;
	}
`;

export const CustomCard = styled.div`
	border: 1px solid #e5e7eb;
	background-color: #ffffff;
	padding: 24px 48px 48px;
	border-radius: 8px;
	box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
	@media (max-height: 860px) {
		padding: 18px 36px 36px;
	}
`;

export const CustomCardTitle = styled.div`
	text-align: center;
	font-family: 'nunitosans-bold' !important;
	font-size: 20px;
	line-height: 28px;
	color: #0f172a;
`;

export const CustomButton = styled.button`
	width: 100%;
	height: 36px;
	border: 1px solid #0f172a;
	background-color: #0f172a;
	color: #f8fafc;
	padding-inline: 16px;
	border-radius: 6px;
	font-family: 'nunitosans-medium' !important;
	font-size: 14px;
	font-weight: 20px;
	box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 10px;
	&:hover {
		opacity: 0.9;
		transition: all 0.2s;
	}
	&:disabled {
		opacity: 0.8;
	}
`;

export const CustomButtonOutlined = styled.button`
	width: 100%;
	height: 36px;
	border: 1px solid #e2e8f0;
	background-color: #ffffff;
	color: #0f172a;
	padding-inline: 16px;
	border-radius: 6px;
	font-family: 'nunitosans-medium' !important;
	font-size: 14px;
	line-height: 20px;
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
	&:hover {
		background-color: #f4f4f5;
		transition: all 0.2s;
	}
`;

export const NewFormItemGroup = styled.div`
	display: flex;
	flex-direction: column;
	gap: 8px;
	.newFormLabel {
		.newLabelContainer {
			.newLabel {
				color: #0f172a !important;
				font-size: 14px !important;
				line-height: 1 !important;
			}
		}
	}
	.newFormInputWrapper {
		.newCustomInputContainer {
			gap: 12px !important;
			border: 1px solid #e2e8f0 !important;
			border-radius: 6px !important;
			height: 40px !important;
			padding-inline: 12px !important;
		}
		.newCustomInput {
			color: #0f172a !important;
			font-size: 14px !important;
			line-height: 1 !important;
			&::placeholder {
				color: #6b7280 !important;
				font-size: 14px !important;
				line-height: 1 !important;
			}
		}
		.prefixContainer,
		.suffixContainer {
			color: #6b7280 !important;
			font-size: 14px !important;
			line-height: 1 !important;
		}
		.validInvalidIconContainer {
			.validInvalidIcon {
				width: 16px !important;
				height: 16px !important;
			}
		}
	}
	.newFormErrorMessage {
	}
	@media (max-height: 860px) {
		gap: 6px;
	}
`;

export const NewFormSelectStylesWrapper = styled.div`
	.newFormSelectWrapper {
		.customContainer {
			height: 34px !important;
		}
		.customControl {
			border: 0.6px solid #d5d5d5 !important;
			padding-inline: 12px !important;
			border-radius: 4px !important;
            height: 40px !important;
            font-size: 14px !important;
		}
		.customSingleValue {
			color: #2e2e2e !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 14px !important;
			line-height: 1 !important;
			cursor: pointer !important;
		}
		.customPlaceholder {
			color: #6b7280 !important;
			font-size: 14px !important;
			line-height: 1 !important;
		}
		.customInput {
			input {
				color: #979797 !important;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 14px !important;
				line-height: 1 !important;
			}
		}
		.customClearIndicator {
			width: 24px !important;
			height: 24px !important;
		}
		.customDropdownIndicator {
			width: 17px !important;
			height: 17px !important;
		}
		.customMenu {
			margin-top: 2px !important;
			margin-bottom: 2px !important;
		}
		.customOption {
			color: #2e2e2e !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 14px !important; /* Updated from 14px to 16px */
			line-height: 1.2 !important; /* Added slightly more line height */
			cursor: pointer !important;
			padding: 10px 12px !important; /* Added more padding for better readability */
		}
		.customOption.isSelected {
			color: #ffffff !important;
		}
		.customNoOptionsMessage {
			color: #979797 !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 14px !important;
		}
		@media (max-width: 600px) {
			.customContainer {
				height: 34px !important;
			}
			.customControl {
				// padding-inline: 8px !important;
				cursor: pointer;
			}
			.customPlaceholder,
			.customSingleValue {
				font-size: 14px !important;
			}
			.customInput {
				input {
					font-size: 12px !important;
				}
			}
			.customClearIndicator {
				width: 24px !important;
				height: 24px !important;
			}
			.customDropdownIndicator {
				width: 17px !important;
				height: 17px !important;
			}
			.customOption {
				font-size: 14px !important; /* Updated from 12px to 14px */
				padding: 8px 10px !important; /* Added padding for mobile */
			}
			.customNoOptionsMessage {
				font-size: 12px !important;
			}
		}
		@media only screen and (min-width: 601px) and (max-width: 1299px) {
			.customContainer {
				height: 34px !important;
			}
			.customControl {
				// padding-inline: 8px !important;			
				cursor: pointer;
			}
			.customPlaceholder,
			.customSingleValue {
				font-size: 14px !important;
			}
			.customInput {
				input {
					font-size: 12px !important;
				}
			}
			.customClearIndicator {
				width: 24px !important;
				height: 24px !important;
			}
			.customDropdownIndicator {
				width: 17px !important;
				height: 17px !important;
			}
			.customOption {
				font-size: 14px !important; /* Updated from 12px to 14px */
				padding: 8px 10px !important; /* Added padding for tablets */
			}
			.customNoOptionsMessage {
				font-size: 12px !important;
			}
		}
		@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
			.customContainer {
				height: 34px !important;
			}
			.customControl {
				// padding-inline: 8px !important;
				cursor: pointer;
			}
			.customPlaceholder,
			.customSingleValue {
				font-size: 14px !important;
			}
			.customInput {
				input {
					font-size: 12px !important;
				}
			}
			.customClearIndicator {
				width: 24px !important;
				height: 24px !important;
			}
			.customDropdownIndicator {
				width: 17px !important;
				height: 17px !important;
			}
			.customOption {
				font-size: 14px !important; /* Updated from 12px to 14px */
				padding: 8px 10px !important; /* Added padding for smaller desktops */
			}
			.customNoOptionsMessage {
				font-size: 12px !important;
			}
		}
	}
`;
