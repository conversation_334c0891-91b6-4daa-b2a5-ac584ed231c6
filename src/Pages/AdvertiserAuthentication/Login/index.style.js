import styled from 'styled-components';

const StylesWrapper = styled.div`
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	.newFormWrapper {
		height: 100%;
		display: flex;
		justify-content: center;
		padding: 40px;
		@media only screen and (min-height: 750px) {
			align-items: center;
		}
	}
	.newFormContentContainer {
		width: 100%;
		max-width: 448px;
		height: fit-content;
	}
	.customCardContentWrapper {
		display: flex;
		flex-direction: column;
		gap: 20px;
	}
	.noteText {
		width: fit-content;
		color: #6b7280;
		font-family: 'nunitosans-regular' !important;
		font-size: 12px;
		line-height: 16px;
	}
	.linkText {
		color: #0f172a;
		font-family: 'nunitosans-semi-bold' !important;
		font-size: 12px;
		line-height: 16px;
		cursor: pointer;
	}
	@media (max-height: 860px) {
		.newFormWrapper {
			padding: 8px 30px;
		}
		.customCardContentWrapper {
			gap: 15px;
		}
	}
`;

export default StylesWrapper;
