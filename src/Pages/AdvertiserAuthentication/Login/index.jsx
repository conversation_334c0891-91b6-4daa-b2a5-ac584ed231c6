import { useState } from 'react';
import { useFormik } from 'formik';
import { toast } from 'react-toastify';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import Scrollbars from 'react-custom-scrollbars';

import Layout from '../Layout';
import StylesWrapper from './index.style';
import {
	CustomButton,
	CustomButtonOutlined,
	CustomCard,
	CustomCardTitle,
	MainDescription,
	MainTitle,
	NewFormItemGroup
} from '../index.style';
import Api from '../../../Helper/Api';
import {
	AdvertiserApiRoutes,
	AdvertiserPanelRoutes,
	CommonRoutes
} from '../../../Utils/routes';
import { loginFormSchema } from '../validationSchema';
import NewLoader from '../../../Components/Common/NewLoader';
import NewFormInput from '../../../Components/NewForm/NewFormInput';
import NewFormPasswordInput from '../../../Components/NewForm/NewFormPasswordInput';
import NewFormLabel from '../../../Components/NewForm/NewFormLabel';
import NewFormErrorMessage from '../../../Components/NewForm/NewFormErrorMessage';
import authActions from '../../../Redux/auth/actions';

const Login = () => {
	const navigate = useNavigate();
	const dispatch = useDispatch();
	const [setupMFALoading, setSetupMFALoading] = useState(false);
	const [submitButtonLoading, setSubmitButtonLoading] = useState(false);

	const handleFormSubmit = async (values) => {
		try {
			setSubmitButtonLoading(true);
			const res = await Api('POST', AdvertiserApiRoutes?.login, {
				email: values?.email,
				password: values?.password,
				code: values?.code
			});
			if (res?.data?.status) {
				toast.success(res?.data?.message);
				dispatch(authActions?.advertiser_login(res?.data?.data));
				navigate(AdvertiserPanelRoutes?.myTabAds);
			} else {
				toast.error(res?.data?.message);
			}
			setSubmitButtonLoading(false);
		} catch (err) {
			setSubmitButtonLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	const { values, handleSubmit, handleChange, handleBlur, touched, errors } =
		useFormik({
			initialValues: {
				email: '',
				password: '',
				code: ''
			},
			validationSchema: loginFormSchema,
			onSubmit: handleFormSubmit
		});

	const handleSetupMFA = async () => {
		if (!values?.email) {
			toast.error('Please enter your email.');
			return;
		}
		try {
			setSetupMFALoading(true);
			const res = await Api('POST', AdvertiserApiRoutes?.setupMfa, {
				email: values?.email
			});
			if (res?.data?.status) {
				toast.success(res?.data?.message);
			} else {
				toast.error(res?.data?.message);
			}
			setSetupMFALoading(false);
		} catch (err) {
			setSetupMFALoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	return (
		<Layout>
			<StylesWrapper>
				<Scrollbars
					renderTrackVertical={({ style, ...props }) => (
						<div
							{...props}
							style={{
								...style,
								width: '4px',
								right: '2px',
								bottom: '2px',
								top: '2px',
								borderRadius: '3px'
							}}
						/>
					)}
					autoHide
				>
					<div className="newFormWrapper">
						<div className="newFormContentContainer">
							<MainTitle>MyTab Ads</MainTitle>
							<MainDescription>
								Connect with real customers and grow your
								business through targeted, powerful ads.
							</MainDescription>
							<CustomCard>
								<form onSubmit={handleSubmit}>
									<div className="customCardContentWrapper">
										<CustomCardTitle>
											Advertiser Portal
										</CustomCardTitle>
										<NewFormItemGroup className="newFormItemGroup">
											<NewFormLabel
												className={'newFormLabel'}
												label={'Email'}
											/>
											<NewFormInput
												wrapperClassName={
													'newFormInputWrapper'
												}
												placeholder={'Enter your email'}
												name={'email'}
												value={values?.email}
												onChange={handleChange}
												onBlur={handleBlur}
											/>
											<NewFormErrorMessage
												className={
													'newFormErrorMessage'
												}
												message={
													touched?.email &&
													!!errors?.email
														? errors?.email
														: ''
												}
											/>
										</NewFormItemGroup>
										<NewFormItemGroup>
											<NewFormLabel
												className={'newFormLabel'}
												label={'Password'}
											/>
											<NewFormPasswordInput
												wrapperClassName={
													'newFormInputWrapper'
												}
												placeholder={
													'Enter your password'
												}
												eyeIconStrokeWidth="1"
												name={'password'}
												value={values?.password}
												onChange={handleChange}
												onBlur={handleBlur}
											/>
											<NewFormErrorMessage
												className={
													'newFormErrorMessage'
												}
												message={
													touched?.password &&
													!!errors?.password
														? errors?.password
														: ''
												}
											/>
											<span
												className="noteText"
												style={{ cursor: 'pointer' }}
												onClick={() =>
													navigate(
														CommonRoutes?.advertiserForgotPassword
													)
												}
											>
												Forgot Password?
											</span>
										</NewFormItemGroup>
										<NewFormItemGroup>
											<NewFormLabel
												className={'newFormLabel'}
												label={'Authenticator Code'}
											/>
											<NewFormInput
												wrapperClassName={
													'newFormInputWrapper'
												}
												placeholder={'6-digit code'}
												name={'code'}
												value={values?.code}
												onChange={handleChange}
												onBlur={handleBlur}
											/>
											<NewFormErrorMessage
												className={
													'newFormErrorMessage'
												}
												message={
													touched?.code &&
													!!errors?.code
														? errors?.code
														: ''
												}
											/>
											<span className="noteText">
												Having trouble? Click{' '}
												<span
													className="linkText"
													onClick={() =>
														!setupMFALoading &&
														handleSetupMFA()
													}
												>
													here
												</span>{' '}
												and we will send you an email to
												reset your 2 factor
												authentication.
											</span>
										</NewFormItemGroup>
										<div>
											<CustomButton
												type="submit"
												disabled={submitButtonLoading}
											>
												Login
												{submitButtonLoading && (
													<NewLoader
														color="#ffffff"
														borderWidth="1.5px"
														size="14px"
													/>
												)}
											</CustomButton>
											<CustomButtonOutlined
												type="button"
												style={{ marginTop: '8px' }}
												onClick={() =>
													navigate(
														CommonRoutes?.advertiserRegister
													)
												}
											>
												Create Account
											</CustomButtonOutlined>
										</div>
									</div>
								</form>
							</CustomCard>
						</div>
					</div>
				</Scrollbars>
			</StylesWrapper>
		</Layout>
	);
};

export default Login;
