import { useEffect, useState } from 'react';
import Scrollbars from 'react-custom-scrollbars';
import { useFormik } from 'formik';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import { deviceDetect, isMobile } from 'react-device-detect';
import axios from 'axios';

import Layout from '../Layout';
import QrCodeBox from './QrCodeBox';
import ImageUpload from './ImageUpload';
import StylesWrapper from './index.style';
import {
    CustomButton,
    CustomButtonOutlined,
    CustomCard,
    CustomCardTitle,
    MainDescription,
    MainTitle,
    NewFormItemGroup
} from '../index.style';
import Api from '../../../Helper/Api';
import { registerFormSchema } from '../validationSchema';
import { validateIdentifier, validatePassword } from '../utils';
import { AdvertiserApiRoutes, CommonRoutes } from '../../../Utils/routes';
import NewLoader from '../../../Components/Common/NewLoader';
import NewFormInput from '../../../Components/NewForm/NewFormInput';
import NewFormLabel from '../../../Components/NewForm/NewFormLabel';
import NewFormPasswordInput from '../../../Components/NewForm/NewFormPasswordInput';
import NewFormErrorMessage from '../../../Components/NewForm/NewFormErrorMessage';
import NewFormSelect from '../../../Components/NewForm/NewFormSelect';
import { NewFormSelectStylesWrapper } from '../index.style';

const allowedFileTypes = ['image/jpg', 'image/jpeg', 'image/png'];
const FILE_SIZE = 20 * 1024 * 1024;

const Register = () => {
    const navigate = useNavigate();
    const [userLocationData, setUserLocationData] = useState({});
    const [timezones, setTimezones] = useState([]);
    const [isAbnAcnValid, setIsAbnAcnValid] = useState(null);
    const [isPasswordValid, setIsPasswordValid] = useState(null);
    const [isConfirmPasswordValid, setIsConfirmPasswordValid] = useState(null);
    const [
        isShowEmailVerificationCodeInput,
        setIsShowEmailVerificationCodeInput
    ] = useState(false);
    const [isEmailVerified, setIsEmailVerified] = useState(false);
    const [sendVerificationLoading, setSendVerificationLoading] =
        useState(false);
    const [qrCodeBoxData, setQrCodeBoxData] = useState(null);
    const [submitButtonLoading, setSubmitButtonLoading] = useState(false);
    const [shouldShowEmailVerificationError, setShouldShowEmailVerificationError] = useState(false);
    const [passwordErrorMessage, setPasswordErrorMessage] = useState('');

    const handleFormSubmit = async (values) => {
        let deviceData = deviceDetect();
        let payload = {
            image: values?.image?.originalFile,
            business_name: values?.business_name,
            business_url: values?.business_url,
            acn_number: values?.acn_number,
            contact_name: values?.contact_name,
            timezone: values?.timezone,
            email: values?.email,
            password: values?.password,
            code: values?.code,
            device_type: 'web',
            device_token: '23',
            device_name: isMobile
                ? (deviceData?.model ?? '') +
                ' ' +
                (deviceData?.os ?? '') +
                ' ' +
                (deviceData?.osVersion ?? '')
                : (deviceData?.browserName ?? '') +
                ' ' +
                (deviceData?.osName ?? '') +
                ' ' +
                (deviceData?.osVersion ?? ''),
            device_location:
                (userLocationData?.city ? userLocationData?.city + ',' : '') +
                ' ' +
                (userLocationData?.region
                    ? userLocationData?.region + ','
                    : '') +
                ' ' +
                (userLocationData?.country_name
                    ? userLocationData?.country_name
                    : '')
        };
        let formData = new FormData();
        for (let key in payload) {
            formData.append(key, payload[key]);
        }
        try {
            setSubmitButtonLoading(true);
            const res = await Api(
                'POST',
                AdvertiserApiRoutes?.register,
                formData
            );
            if (res?.data?.status) {
                navigate(CommonRoutes?.advertiserLogin);
                toast.success(res?.data?.message);
            } else {
                toast.error(res?.data?.message);
            }
            setSubmitButtonLoading(false);
        } catch (err) {
            setSubmitButtonLoading(false);
            if (err?.message) {
                toast.error(err?.message);
            }
        }
    };

    const {
        values,
        handleSubmit,
        handleChange,
        handleBlur,
        touched,
        errors,
        setFieldValue
    } = useFormik({
        initialValues: {
            image: null,
            business_name: '',
            business_url: '',
            acn_number: '',
            contact_name: '',
            timezone: '',
            email: '',
            emailCode: '',
            password: '',
            confirmPassword: '',
            code: ''
        },
        validationSchema: registerFormSchema,
        onSubmit: handleFormSubmit
    });

    useEffect(() => {
        console.log("Password validation state:", {
            password: values?.password,
            isPasswordValid,
            confirmPassword: values?.confirmPassword,
            isConfirmPasswordValid
        });
    }, [values?.password, values?.confirmPassword, isPasswordValid, isConfirmPasswordValid]);

    useEffect(() => {
        setShouldShowEmailVerificationError(errors?.emailCode && !isEmailVerified);
    }, [errors?.emailCode, isEmailVerified]);
    // console.log(errors, "shouldShowEmailVerificationError", shouldShowEmailVerificationError);
    const handleFileUploadChange = (customFileObject) => {
        const isAllowed = allowedFileTypes.includes(
            customFileObject?.originalFile?.type
        );
        if (!isAllowed) {
            toast.error('You can only upload jpg, jpeg or png images.');
            return;
        }
        if (customFileObject?.originalFile?.size > FILE_SIZE) {
            toast.error('Image size must be less than 20Mb.');
            return;
        }
        setFieldValue('image', customFileObject);
    };

    const handlePasswordChange = (e) => {
        handleChange(e);
        if (e?.target?.value) {
            const value = e.target.value;

            // Check each requirement individually
            const hasMinLength = value.length >= 8;
            const hasLowerCase = /[a-z]/.test(value);
            const hasUpperCase = /[A-Z]/.test(value);
            const hasNumber = /[0-9]/.test(value);
            const hasSpecial = /[!@#$%^&*(),.?":{}|<>\-]/.test(value);

            // Overall validation result
            const isValid = hasMinLength && hasLowerCase && hasUpperCase && hasNumber && hasSpecial;

            setIsPasswordValid(isValid);

            // Show error message when password is entered but not valid
            if (!isValid) {
                setPasswordErrorMessage(`Password must be a minimum of 8 characters and include at least one uppercase letter, one lowercase letter, one number and one special character.`);
            } else {
                setPasswordErrorMessage('');
            }
        } else {
            setIsPasswordValid(null);
            setPasswordErrorMessage('');
        }
    };

    const handleConfirmPasswordChange = (e) => {
        handleChange(e);
        if (e?.target?.value) {
            const value = e.target.value;
            // Only check if it matches the password
            const isValid = value === values?.password;
            setIsConfirmPasswordValid(isValid);
        } else {
            setIsConfirmPasswordValid(null);
        }
    };

    const handleSendEmailVerificationCode = async () => {
        if (!values?.contact_name) {
            toast.error('Please enter contact name.');
            return;
        }
        if (!values?.email) {
            toast.error('Please enter email.');
            return;
        }
        try {
            setSendVerificationLoading(true);
            const res = await Api(
                'POST',
                AdvertiserApiRoutes?.sendVerificationCode,
                {
                    email: values?.email,
                    contact_name: values?.contact_name
                }
            );
            if (res?.data?.status) {
                setIsShowEmailVerificationCodeInput(true);
                // Clear the error message when verification code is sent successfully
                setShouldShowEmailVerificationError(false);
                toast.success(res?.data?.message);
            } else {
                toast.error(res?.data?.message);
            }
            setSendVerificationLoading(false);
        } catch (err) {
            setSendVerificationLoading(false);
            if (err?.message) {
                toast.error(err?.message);
            }
        }
    };

    const handleVerifyEmailVerificationCode = async (e) => {
        handleBlur(e);
        if (values?.emailCode?.length != 6) {
            return;
        }
        try {
            const res = await Api('POST', AdvertiserApiRoutes?.verifyEmailOtp, {
                email: values?.email,
                otp: values?.emailCode,
                contact_name: values?.contact_name
            });
            if (res?.data?.status) {
                setIsEmailVerified(true);
                setIsShowEmailVerificationCodeInput(false);
                setQrCodeBoxData(res?.data?.data);
                toast.success(res?.data?.message);
            } else {
                toast.error(res?.data?.message);
            }
        } catch (err) {
            if (err?.message) {
                toast.error(err?.message);
            }
        }
    };

    // Fetch timezones on component mount
    const fetchTimezones = async () => {
        try {
            const res = await Api('GET', AdvertiserApiRoutes?.getTimezones);
            if (res?.data?.status) {
                setTimezones(res?.data?.data || []);
            } else {
                console.error('Failed to fetch timezones:', res?.data?.message);
            }
        } catch (err) {
            console.error('Error fetching timezones:', err?.message);
        }
    };

    useEffect(() => {
        // Fetch user location data
        (async () => {
            try {
                const response = await axios.get('https://ipapi.co/json/');
                let data = response?.data;
                setUserLocationData(data);
            } catch (err) {
                console.log(err);
            }
        })();

        // Fetch timezones
        fetchTimezones();
    }, []);

    return (
        <Layout>
            <StylesWrapper>
                <Scrollbars
                    renderTrackVertical={({ style, ...props }) => (
                        <div
                            {...props}
                            style={{
                                ...style,
                                width: '4px',
                                right: '2px',
                                bottom: '2px',
                                top: '2px',
                                borderRadius: '3px'
                            }}
                        />
                    )}
                    autoHide
                >
                    <div className="newFormWrapper">
                        <div className="newFormContentContainer">
                            <MainTitle>MyTab Ads</MainTitle>
                            <MainDescription>
                                Connect with real customers and grow your
                                business through targeted, powerful ads.
                            </MainDescription>
                            <CustomCard>
                                <form onSubmit={handleSubmit}>
                                    <div className="customCardContentWrapper">
                                        <CustomCardTitle>
                                            Create Account
                                        </CustomCardTitle>
                                        <NewFormItemGroup className="newFormItemGroup">
                                            <ImageUpload
                                                value={values?.image}
                                                onChange={
                                                    handleFileUploadChange
                                                }
                                            />
                                            <div className="d-flex justify-content-center">
                                                <NewFormErrorMessage
                                                    className={
                                                        'newFormErrorMessage'
                                                    }
                                                    message={
                                                        touched?.image &&
                                                            !!errors?.image
                                                            ? errors?.image
                                                            : ''
                                                    }
                                                />
                                            </div>
                                            <p className="detailsText">
                                                Customers will see this
                                            </p>
                                        </NewFormItemGroup>
                                        <NewFormItemGroup className="newFormItemGroup">
                                            <NewFormLabel
                                                className={'newFormLabel'}
                                                label={'Business Name (Public)'}
                                            />
                                            <NewFormInput
                                                wrapperClassName={
                                                    'newFormInputWrapper'
                                                }
                                                placeholder={
                                                    'Enter your business name'
                                                }
                                                name={'business_name'}
                                                value={values?.business_name}
                                                onChange={handleChange}
                                                onBlur={handleBlur}
                                            />
                                            <NewFormErrorMessage
                                                className={
                                                    'newFormErrorMessage'
                                                }
                                                message={
                                                    touched?.business_name &&
                                                        !!errors?.business_name
                                                        ? errors?.business_name
                                                        : ''
                                                }
                                            />
                                        </NewFormItemGroup>
                                        <NewFormItemGroup className="newFormItemGroup">
                                            <NewFormLabel
                                                className={'newFormLabel'}
                                                label={'Business Website'}
                                            />
                                            <NewFormInput
                                                wrapperClassName={
                                                    'newFormInputWrapper'
                                                }
                                                placeholder={
                                                    'https://yourbusiness.com'
                                                }
                                                name={'business_url'}
                                                value={values?.business_url}
                                                onChange={handleChange}
                                                onBlur={handleBlur}
                                            />
                                            <NewFormErrorMessage
                                                className={
                                                    'newFormErrorMessage'
                                                }
                                                message={
                                                    touched?.business_url &&
                                                        !!errors?.business_url
                                                        ? errors?.business_url
                                                        : ''
                                                }
                                            />
                                        </NewFormItemGroup>
                                        <NewFormItemGroup className="newFormItemGroup">
                                            <NewFormLabel
                                                className={'newFormLabel'}
                                                label={'ABN / ACN'}
                                            />
                                            <NewFormInput
                                                wrapperClassName={
                                                    'newFormInputWrapper'
                                                }
                                                placeholder={
                                                    'Enter your ABN or ACN'
                                                }
                                                name={'acn_number'}
                                                value={values?.acn_number}
                                                onChange={(e) => {
                                                    handleChange(e);
                                                    setIsAbnAcnValid(
                                                        validateIdentifier(
                                                            e.target.value
                                                        )
                                                    );
                                                }}
                                                onBlur={handleBlur}
                                                isShowValidInvalidIcon={
                                                    typeof isAbnAcnValid ===
                                                        'boolean'
                                                        ? true
                                                        : false
                                                }
                                                isValid={isAbnAcnValid}
                                            />
                                            <NewFormErrorMessage
                                                className={
                                                    'newFormErrorMessage'
                                                }
                                                message={
                                                    touched?.acn_number &&
                                                        !!errors?.acn_number
                                                        ? errors?.acn_number
                                                        : isAbnAcnValid !== null
                                                            ? !isAbnAcnValid &&
                                                            'Invalid ABN/ACN'
                                                            : ''
                                                }
                                            />
                                        </NewFormItemGroup>
                                        <NewFormItemGroup className="newFormItemGroup">
                                            <NewFormLabel
                                                className={'newFormLabel'}
                                                label={'Contact Name'}
                                            />
                                            <NewFormInput
                                                wrapperClassName={
                                                    'newFormInputWrapper'
                                                }
                                                placeholder={'Enter your name'}
                                                name={'contact_name'}
                                                value={values?.contact_name}
                                                onChange={handleChange}
                                                onBlur={handleBlur}
                                            />
                                            <NewFormErrorMessage
                                                className={
                                                    'newFormErrorMessage'
                                                }
                                                message={
                                                    touched?.contact_name &&
                                                        !!errors?.contact_name
                                                        ? errors?.contact_name
                                                        : ''
                                                }
                                            />
                                        </NewFormItemGroup>
                                        <NewFormItemGroup className="newFormItemGroup">
                                            <NewFormLabel
                                                className={'newFormLabel'}
                                                label={'Timezone'}
                                            />
                                            <NewFormSelectStylesWrapper>
                                                <NewFormSelect
                                                    placeholder="Select Timezone"
                                                    name="timezone"
                                                    wrapperClassName={'newFormSelectWrapper'}
                                                    value={timezones.find(tz => tz.id === values?.timezone) ? {
                                                        label: timezones.find(tz => tz.id === values?.timezone).title,
                                                        value: timezones.find(tz => tz.id === values?.timezone).id
                                                    } : null}
                                                    onChange={(selectedOption) => {
                                                        setFieldValue('timezone', selectedOption?.value);
                                                    }}
                                                    onBlur={handleBlur}
                                                    options={timezones.map(timezone => ({
                                                        label: timezone.title,
                                                        value: timezone.id
                                                    }))}
                                                    isSearchable

                                                    // Override placement only for this instance
                                                    menuPlacement="bottom"

                                                    styles={{
                                                        menu: (provided) => ({
                                                            ...provided,
                                                            zIndex: 9999
                                                        }),
                                                        control: (provided) => ({
                                                            ...provided,
                                                            fontFamily: 'nunitosans-medium',
                                                            fontSize: '12px'
                                                        }),
                                                        option: (provided) => ({
                                                            ...provided,
                                                            fontFamily: 'nunitosans-medium',
                                                            fontSize: '12px'
                                                        }),
                                                        singleValue: (provided) => ({
                                                            ...provided,
                                                            fontFamily: 'nunitosans-medium',
                                                            fontSize: '12px'
                                                        }),
                                                        placeholder: (provided) => ({
                                                            ...provided,
                                                            fontFamily: 'nunitosans-medium',
                                                            fontSize: '12px'
                                                        })
                                                    }}
                                                />
                                            </NewFormSelectStylesWrapper>

                                            <NewFormErrorMessage
                                                className={
                                                    'newFormErrorMessage timezone-error-padding'
                                                }
                                                message={
                                                    touched?.timezone &&
                                                        !!errors?.timezone
                                                        ? errors?.timezone
                                                        : ''
                                                }
                                            />
                                        </NewFormItemGroup>
                                        <NewFormItemGroup className="newFormItemGroup">
                                            <NewFormLabel
                                                className={'newFormLabel'}
                                                label={'Email'}
                                            />
                                            <NewFormInput
                                                wrapperClassName={
                                                    'newFormInputWrapper'
                                                }
                                                placeholder={'Enter your email'}
                                                name={'email'}
                                                value={values?.email}
                                                onChange={handleChange}
                                                onBlur={handleBlur}
                                                disabled={isEmailVerified}
                                                isShowValidInvalidIcon={isEmailVerified}
                                                isValid={true}
                                            />
                                            <NewFormErrorMessage
                                                className={'newFormErrorMessage'}
                                                message={
                                                    errors?.email
                                                        ? errors.email
                                                        : !isEmailVerified && (errors?.emailCode || errors?.code)
                                                            ? 'Please verify your email before proceeding.'
                                                            : ''
                                                }
                                            />
                                            {!isEmailVerified && (
                                                <div style={{
                                                    marginTop: (errors?.email || shouldShowEmailVerificationError) ? '8px' : '0px'
                                                }}>
                                                    <CustomButtonOutlined
                                                        type="button"
                                                        className="sendCodeButton"
                                                        onClick={handleSendEmailVerificationCode}
                                                    >
                                                        Send Verification Code
                                                    </CustomButtonOutlined>
                                                </div>
                                            )}
                                        </NewFormItemGroup>
                                        {isShowEmailVerificationCodeInput && (
                                            <NewFormItemGroup className="newFormItemGroup">
                                                <NewFormLabel
                                                    className={'newFormLabel'}
                                                    label={
                                                        'Email Verification Code'
                                                    }
                                                />
                                                <NewFormInput
                                                    wrapperClassName={
                                                        'newFormInputWrapper'
                                                    }
                                                    placeholder={'6-digit code'}
                                                    name={'emailCode'}
                                                    value={values?.emailCode}
                                                    onChange={handleChange}
                                                    onBlur={
                                                        handleVerifyEmailVerificationCode
                                                    }
                                                />
                                                <NewFormErrorMessage
                                                    className={
                                                        'newFormErrorMessage'
                                                    }
                                                    message={
                                                        touched?.emailCode &&
                                                            !!errors?.emailCode
                                                            ? errors?.emailCode
                                                            : ''
                                                    }
                                                />
                                            </NewFormItemGroup>
                                        )}
                                        <NewFormItemGroup className="newFormItemGroup">
                                            <NewFormLabel
                                                className={'newFormLabel'}
                                                label={'Password'}
                                            />
                                            <NewFormPasswordInput
                                                wrapperClassName={
                                                    'newFormInputWrapper'
                                                }
                                                placeholder={
                                                    'Create a password'
                                                }
                                                eyeIconStrokeWidth="1"
                                                name={'password'}
                                                value={values?.password}
                                                onChange={handlePasswordChange}
                                                onBlur={handleBlur}
                                                isShowValidInvalidIcon={isPasswordValid !== null}
                                                isValid={isPasswordValid}
                                            />
                                            <NewFormErrorMessage
                                                className={
                                                    'newFormErrorMessage'
                                                }
                                                message={
                                                    values?.password && !isPasswordValid
                                                        ? passwordErrorMessage
                                                        : touched?.password && !!errors?.password
                                                            ? errors?.password
                                                            : ''
                                                }
                                            />
                                        </NewFormItemGroup>
                                        <NewFormItemGroup className="newFormItemGroup">
                                            <NewFormLabel
                                                className={'newFormLabel'}
                                                label={'Confirm Password'}
                                            />
                                            <NewFormPasswordInput
                                                wrapperClassName={
                                                    'newFormInputWrapper'
                                                }
                                                placeholder={
                                                    'Confirm your password'
                                                }
                                                eyeIconStrokeWidth="1"
                                                name={'confirmPassword'}
                                                value={values?.confirmPassword}
                                                onChange={handleConfirmPasswordChange}
                                                onBlur={handleBlur}
                                                isShowValidInvalidIcon={isConfirmPasswordValid !== null}
                                                isValid={isConfirmPasswordValid}
                                            />
                                            <NewFormErrorMessage
                                                className={
                                                    'newFormErrorMessage'
                                                }
                                                message={
                                                    touched?.confirmPassword &&
                                                        !!errors?.confirmPassword
                                                        ? errors?.confirmPassword
                                                        : ''
                                                }
                                            />
                                        </NewFormItemGroup>
                                        {qrCodeBoxData && (
                                            <QrCodeBox
                                                qrImage={
                                                    qrCodeBoxData?.mfa_qr_code
                                                }
                                                secretKey={
                                                    qrCodeBoxData?.mfa_code
                                                }
                                            >
                                                <NewFormItemGroup className="newFormItemGroup">
                                                    <NewFormLabel
                                                        className={
                                                            'newFormLabel'
                                                        }
                                                        label={
                                                            'Enter 6-digit code'
                                                        }
                                                    />
                                                    <NewFormInput
                                                        wrapperClassName={
                                                            'newFormInputWrapper'
                                                        }
                                                        placeholder={
                                                            '6-digit code'
                                                        }
                                                        name={'code'}
                                                        value={values?.code}
                                                        onChange={handleChange}
                                                        onBlur={handleBlur}
                                                    />
                                                    <NewFormErrorMessage
                                                        className={
                                                            'newFormErrorMessage'
                                                        }
                                                        message={
                                                            touched?.code &&
                                                                !!errors?.code
                                                                ? errors?.code
                                                                : ''
                                                        }
                                                    />
                                                </NewFormItemGroup>
                                            </QrCodeBox>
                                        )}
                                        <div>
                                            <CustomButton
                                                type="submit"
                                                disabled={submitButtonLoading}
                                            >
                                                Create Account
                                                {submitButtonLoading && (
                                                    <NewLoader
                                                        color="#ffffff"
                                                        borderWidth="1.5px"
                                                        size="14px"
                                                    />
                                                )}
                                            </CustomButton>
                                            <CustomButtonOutlined
                                                type="button"
                                                style={{ marginTop: '8px' }}
                                                onClick={() =>
                                                    navigate(
                                                        CommonRoutes?.advertiserLogin
                                                    )
                                                }
                                            >
                                                Back to Login
                                            </CustomButtonOutlined>
                                        </div>
                                    </div>
                                </form>
                            </CustomCard>
                        </div>
                    </div>
                </Scrollbars>
            </StylesWrapper>
        </Layout>
    );
};

export default Register;
