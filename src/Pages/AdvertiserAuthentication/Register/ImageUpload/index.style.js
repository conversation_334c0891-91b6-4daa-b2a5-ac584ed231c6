import styled from 'styled-components';

const StyleWrapper = styled.div`
	display: flex;
	flex-direction: column;
	align-items: center;
	.imageBox {
		width: 96px;
		height: 96px;
		border-radius: 100%;
		background-color: #f1f5f9;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #0f172a;
		font-family: 'nunitosans-regular' !important;
		font-size: 16px;
		img {
			width: 100%;
			height: 100%;
			display: block;
			border-radius: 100%;
		}
	}
	.uploadButton {
		width: 155px;
		font-size: 12px;
		padding-inline: 0;
		margin-top: 8px;
	}
	@media (max-width: 600px) {
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
	}
`;

export default StyleWrapper;
