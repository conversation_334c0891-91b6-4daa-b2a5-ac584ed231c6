import React, { useState } from 'react';
import StylesWrapper from './index.style';

const QrCodeBox = ({ children, secretKey, qrImage }) => {
	const [isShowSecretKey, setIsShowSecretKey] = useState(false);
	return (
		<StylesWrapper>
			<p className="boxTitle">Set up 2FA with Authenticator App</p>
			<div className="qrImageBox">
				<img src={qrImage} alt="qr-image" className="qrImage" />
			</div>
			{isShowSecretKey && (
				<p className="secretKeyText">Secret key: {secretKey}</p>
			)}
			<div className="secretKeyButtonWrapper">
				<button
					className="secretKeyButton"
					type="button"
					onClick={() => setIsShowSecretKey((prev) => !prev)}
				>
					{isShowSecretKey ? 'Hide secret key' : 'Show secret key'}
				</button>
				<button
					className="secretKeyButton"
					type="button"
					onClick={() => navigator?.clipboard?.writeText(secretKey)}
				>
					Copy secret key
				</button>
			</div>
			{children}
		</StylesWrapper>
	);
};

export default QrCodeBox;
