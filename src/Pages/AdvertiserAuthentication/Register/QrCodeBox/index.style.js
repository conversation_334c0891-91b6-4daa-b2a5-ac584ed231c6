import styled from 'styled-components';

const StylesWrapper = styled.div`
	border: 1px solid rgba(209, 213, 219, 1);
	border-radius: 12px;
	padding: 16px;
	.boxTitle {
		color: #0f172a !important;
		font-family: 'nunitosans-medium' !important;
		font-size: 14px !important;
		line-height: 20px !important;
		text-align: center;
		padding-bottom: 12px;
	}
	.qrImageBox {
		border: 1px solid #e5e7eb;
		background-color: rgba(243, 244, 246, 1);
		padding: 12px;
		border-radius: 8px;
		display: flex;
		justify-content: center;
		.qrImage {
			width: 128px;
			height: 128px;
			display: flex;
			background-color: rgba(0, 0, 0, 0.1);
		}
	}
	.secretKeyText {
		padding-top: 12px;
		font-family: 'nunitosans-regular' !important;
		font-size: 12px;
		line-height: 16px;
		color: rgba(55, 65, 81, 1);
		text-align: center;
		word-break: break-word;
	}
	.secretKeyButtonWrapper {
		display: flex;
		justify-content: center;
		gap: 8px;
		padding-block: 12px;
	}
	.secretKeyButton {
		border: none;
		background-color: transparent;
		height: 36px;
		padding: 8px 16px;
		color: #0f172a;
		border-radius: 6px;
		font-family: 'nunitosans-medium' !important;
		font-size: 12px;
		line-height: 16px;
		&:hover {
			background-color: #f4f4f5;
			transition: all 0.2s;
		}
	}
	@media (max-width: 600px) {
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
	}
`;

export default StylesWrapper;
