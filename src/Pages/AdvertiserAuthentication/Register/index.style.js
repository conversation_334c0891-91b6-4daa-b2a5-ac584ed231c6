import styled from 'styled-components';

const StylesWrapper = styled.div`
	width: 100%;
	height: 100%;
	background-color: #ffffff;
	.newFormWrapper {
		display: flex;
		justify-content: center;
		padding: 40px;
	}
	.newFormContentContainer {
		width: 100%;
		max-width: 448px;
		height: fit-content;
	}
	.customCardContentWrapper {
		display: flex;
		flex-direction: column;
		gap: 16px;
	}
	.detailsText {
		width: fit-content;
		color: #6b7280;
		font-family: 'nunitosans-regular' !important;
		font-size: 12px;
		line-height: 1;
		text-align: center;
		width: 100%;
	}
	.newFormItemGroup {
		gap: 4px;
	}
	.sendCodeButton {
		width: 158px;
		height: 32px;
		font-family: 'nunitosans-medium' !important;
		font-size: 12px;
		padding-inline: 0;
	}
	.timezone-error-padding {
		padding-top: 5px;
	}
	@media (max-height: 860px) {
		.newFormWrapper {
			padding: 8px 30px;
		}
		.customCardContentWrapper {
			gap: 12px;
		}
	}
`;

export default StylesWrapper;
