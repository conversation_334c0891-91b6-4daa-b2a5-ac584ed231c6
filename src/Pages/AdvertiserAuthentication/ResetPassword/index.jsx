import { useState } from 'react';
import { useFormik } from 'formik';
import { toast } from 'react-toastify';
import { useNavigate, useParams } from 'react-router-dom';

import Layout from '../Layout';
import StylesWrapper from './index.style';
import {
	CustomButton,
	CustomCard,
	CustomCardTitle,
	MainDescription,
	MainTitle,
	NewFormItemGroup
} from '../index.style';
import Api from '../../../Helper/Api';
import { AdvertiserApiRoutes, CommonRoutes } from '../../../Utils/routes';
import { resetPasswordFormSchema } from '../validationSchema';
import NewFormLabel from '../../../Components/NewForm/NewFormLabel';
import NewFormPasswordInput from '../../../Components/NewForm/NewFormPasswordInput';
import NewFormErrorMessage from '../../../Components/NewForm/NewFormErrorMessage';
import NewLoader from '../../../Components/Common/NewLoader';
import { validatePassword } from '../utils';

const ResetPassword = () => {
	const navigate = useNavigate();
	const { token } = useParams();
	const [isPasswordValid, setIsPasswordValid] = useState(null);
	const [isConfirmPasswordValid, setIsConfirmPasswordValid] = useState(null);
	const [submitButtonLoading, setSubmitButtonLoading] = useState(false);

	const handleFormSubmit = async (values) => {
		try {
			setSubmitButtonLoading(true);
			const res = await Api('POST', AdvertiserApiRoutes?.resetPassword, {
				password: values?.password,
				otp_token: token?.split('~')[1]
			});
			if (res?.data?.status) {
				toast.success(res?.data?.message);
				navigate(CommonRoutes?.advertiserLogin);
			} else {
				toast.error(res?.data?.message);
			}
			setSubmitButtonLoading(false);
		} catch (err) {
			setSubmitButtonLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	const { values, handleSubmit, handleChange, handleBlur, touched, errors } =
		useFormik({
			initialValues: {
				password: '',
				confirmPassword: ''
			},
			validationSchema: resetPasswordFormSchema,
			onSubmit: handleFormSubmit
		});

	const handlePasswordChange = (e) => {
		handleChange(e);
		if (e?.target?.value) {
			setIsPasswordValid(validatePassword(e?.target?.value));
		} else {
			setIsPasswordValid(null);
		}
	};

	const handleConfirmPasswordChange = (e) => {
		handleChange(e);
		if (e?.target?.value) {
			setIsConfirmPasswordValid(
				e?.target?.value === values?.password &&
					validatePassword(e?.target?.value)
					? true
					: false
			);
		} else {
			setIsConfirmPasswordValid(null);
		}
	};
	return (
		<Layout>
			<StylesWrapper>
				<div className="newFormWrapper">
					<div className="newFormContentContainer">
						<MainTitle>MyTab Ads</MainTitle>
						<MainDescription>
							Connect with real customers and grow your business
							through targeted, powerful ads.
						</MainDescription>
						<CustomCard>
							<form onSubmit={handleSubmit}>
								<div className="customCardContentWrapper">
									<CustomCardTitle>
										Create a new password
									</CustomCardTitle>
									<NewFormItemGroup>
										<NewFormLabel
											className={'newFormLabel'}
											label={'New Password'}
										/>
										<NewFormPasswordInput
											wrapperClassName={
												'newFormInputWrapper'
											}
											placeholder={'Enter new password'}
											eyeIconStrokeWidth="1"
											name={'password'}
											value={values?.password}
											onChange={handlePasswordChange}
											onBlur={handleBlur}
											isShowValidInvalidIcon={
												typeof isPasswordValid ===
												'boolean'
													? true
													: false
											}
											isValid={isPasswordValid}
										/>
										<NewFormErrorMessage
											className={'newFormErrorMessage'}
											message={
												touched?.password &&
												!!errors?.password
													? errors?.password
													: ''
											}
										/>
									</NewFormItemGroup>
									<NewFormItemGroup>
										<NewFormLabel
											className={'newFormLabel'}
											label={'Confirm Password'}
										/>
										<NewFormPasswordInput
											wrapperClassName={
												'newFormInputWrapper'
											}
											placeholder={
												'Confirm your password'
											}
											eyeIconStrokeWidth="1"
											name={'confirmPassword'}
											value={values?.confirmPassword}
											onChange={
												handleConfirmPasswordChange
											}
											onBlur={handleBlur}
											isShowValidInvalidIcon={
												typeof isConfirmPasswordValid ===
												'boolean'
													? true
													: false
											}
											isValid={isConfirmPasswordValid}
										/>
										<NewFormErrorMessage
											className={'newFormErrorMessage'}
											message={
												touched?.confirmPassword &&
												!!errors?.confirmPassword
													? errors?.confirmPassword
													: ''
											}
										/>
									</NewFormItemGroup>
									<div>
										<CustomButton
											type="submit"
											disabled={submitButtonLoading}
										>
											Reset Password
											{submitButtonLoading && (
												<NewLoader
													color="#ffffff"
													borderWidth="1.5px"
													size="14px"
												/>
											)}
										</CustomButton>
									</div>
								</div>
							</form>
						</CustomCard>
					</div>
				</div>
			</StylesWrapper>
		</Layout>
	);
};

export default ResetPassword;
