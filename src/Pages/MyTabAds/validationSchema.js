import * as Yup from 'yup';

export const createAdFormSchema = Yup.object().shape({
	campaign_id: Yup.string().required('Please select campaign.'),
	objective: Yup.string().required('Please select objective.'),
	ad_title: Yup.string().required('Please enter ad title.'),
	ad_description: Yup.string().required('Please enter ad description.'),
	media_url: Yup.mixed().required('Please upload ad image.'),
	call_to_action: Yup.string().required('Please select call to action.'),
	call_to_action_url: Yup.string().when('call_to_action', {
		is: (val) => !!val && val !== 'view_menu',
		then: (schema) =>
			schema
				.trim()
				.url('Please enter a valid url.')
				.required('Please enter call to action url.'),
		otherwise: (schema) => schema.notRequired()
	}),
	state: Yup.string().required('Please select state.'),
	city: Yup.string().required('Please enter city/suburb.'),
	eligibility_type: Yup.string().required('Please select audience.'),
	start_date: Yup.date()
		.typeError('Start date must be a valid date.')
		.required('Please select start date.'),
	end_date: Yup.date()
		.min(Yup.ref('start_date'), 'End date must be after start date')
		.required('Please select end date.'),
});
