import styled from 'styled-components';

export const StylesWrapper = styled.div`
    width: 100%;
    border-radius: 4px;
    overflow: hidden;
    font-family: 'nunitosans-regular';

    .campaignBlock {
        margin-block: 18px;
        // min-height: 100px;
        border: 0.6px solid #d5d5d5;
        background-color: #ffffff;
    }
    .spacerRow {
        height: 10px; /* Adjust height as needed */
        background-color: transparent; /* Or any other color */
    }
  .campaignRow {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-inline: 26px;
    padding-block: 12px;
    cursor: pointer;

    .categoryName {
      font-size: 18px !important;
      font-family: 'nunitosans-bold';
      color: #2e2e2e;
    }

    .adsLinked {
      font-size: 15px;
      color: #565656;
      font-family: 'nunitosans-regular';
      margin-top: 2px;
    }

    .rightSideWrapper {
        display: flex;
        align-items: center;
        gap: 25px;

      .summaryList {
        list-style-type: none;
        margin: 0;
        padding: 0;
        font-size: 15px;
        display: flex;
        flex-direction: column;
        gap: 3px;
        color: #000000;

        li {
          display: flex;
          gap: 8px;
          font-family: nunitosans-regular !important;
          white-space: nowrap;
          color: #000000;
        }

        .label {
          color: #000000;
          text-align: right;
          font-family: nunitosans-regular !important;
          justify-content: flex-end;
          width: -webkit-fill-available;
        }
      }

      .dropdownIconWrapper {
        display: flex;
        justify-content: center;
        align-items: center;

        .dropdownIcon {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 28px;
          height: 28px;
          transition: transform 0.3s ease-in-out;
        }
      }

      &.open .dropdownIcon {
        transform: rotate(180deg);
      }

      .campaignKebabMenu {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;

        .kebabMenuIcon {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 28px;
          height: 28px;
          cursor: pointer;
          border-radius: 4px;
          transition: background-color 0.2s ease;

          &:hover {
            background-color: rgba(0, 0, 0, 0.05);
          }
        }

        .kebabDropdown {
          position: absolute;
          top: 100%;
          right: 0;
          background: white;
          border: 1px solid #e0e0e0;
          border-radius: 4px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          z-index: 1000;
          min-width: 120px;
          margin-top: 4px;

          .kebabDropdownItem {
            padding: 6px 12px;
            cursor: pointer;
            font-size: 14px;
            font-family: 'nunitosans-regular';
            color: #333;
            transition: background-color 0.2s ease;

            &:hover {
              background-color: #f5f5f5;
            }

            &:first-child {
              border-radius: 4px 4px 0 0;
            }

            &:last-child {
              border-radius: 0 0 4px 4px;
            }

            &:only-child {
              border-radius: 4px;
            }
          }
        }
      }
    }
  }

  .toggleContent {
    padding: 16px 24px;
    background-color: #ffffff;

    .campaignTable {
      width: 100%;
      border-collapse: collapse;

      tbody tr.adRow {
        border: 1px solid #eee;
        &:last-child {
          border-bottom: none;
        }
        .categoryName {
            font-size: 18px !important;
            font-family: 'nunitosans-bold';
            color: #2e2e2e;
        }

        td {
            padding: 16px 18px 13px 10px;
            vertical-align: top; 
            font-size: 15px;
            color: #333;
        }

        td:first-child {
          padding-left: 0;
          width: 8%; 
        }

        td:nth-child(2) {
          width: 26%; 
        }

        td:nth-child(3) {
           width: 22.13%; 
        //   width: auto; 
        }

        td:nth-child(4) {
          width: 11%; 
        //   width: auto; 
        }

        td:last-child {
          padding-right: 0;
         width: 6%; 
        //  width: auto; 
        }

        .adImage {
          width: 136px; 
          height: 83px; 
          background: #eee;
          display: block;
          margin-left: 10px;
        }

        .categoryName {
          font-size: 18px !important;
          font-family: 'nunitosans-bold';
          color: #2e2e2e;
          margin-top: 6px;
        }

        .statusRow {
          display: flex;
          gap: 6px;
          margin-block: 22px 9px;
        }

         .infoList,
         .metricsList {
          list-style-type: none;
          padding: 0;
          margin: 0;
          display: flex;
          flex-direction: column; 
          color:#000000;
          gap: 8px;
          font-size:15px;
          font-family: nunitosans-medium !important;
            li {
                display: flex;
                white-space: nowrap;
                font-family: nunitosans-medium !important;
            }
          span {
            font-family: 'nunitosans-bold';
            margin-right: 8px;
          }
           .label {
                width: 90px;
                font-family: 'nunitosans-bold';
                text-align: right;
            }
        }

        .actionButtons {
        //   display: flex;
        //   justify-content: flex-end;

          .actionBtns {
            display: flex;
            flex-direction: column;
            gap: 8px;
          }
        }
      }
    }
  }
    .noDataFound {
        font-size: 12px;
        color: rgb(46, 46, 46);
        font-family: nunitosans-regular !important;
        display: flex;
        justify-content: center;
        align-items: center;
        padding-block: 8%;
    }

  @media (max-width: 600px) {
    .noDataFound {
        padding-block:12%;
    }
    .campaignBlock {
        margin-block: 12px;
    }
    .campaignRow {
      padding-inline: 16px;
      padding-block: 8px;
        .categoryName {
            font-size: 14px !important;
        }
        .adsLinked {
            font-size: 10px;
        }
            
             .rightSideWrapper {
                gap: 19px;
                .summaryList {
                    font-size: 11px !important;
                }
        
                .dropdownIconWrapper {
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .dropdownIcon {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 19px;
                        height: 19px;
                        transition: transform 0.3s ease-in-out;
                    }
                }

                .campaignKebabMenu {
                    .kebabMenuIcon {
                        width: 24px;
                        height: 24px;
                    }

                    .kebabDropdown {
                        min-width: 100px;

                        .kebabDropdownItem {
                            padding: 6px 12px;
                            font-size: 12px;
                        }
                    }
                }
            }
           
    
    }
    
    .toggleContent .campaignTable tbody tr.adRow {
      display: flex; 
      flex-wrap: wrap; 
       .categoryName {
            font-size: 14px !important;
            font-family: 'nunitosans-bold';
            color: #2e2e2e;
        }
        .statusRow {
          display: flex;
          gap: 6px;
          margin-block: 15px 6px;
        }
        .adImage {
            width: 100%;
            height: 56px;
        }
}

    .toggleContent .campaignTable tbody tr.adRow > td {
      flex-basis: auto; 
      width: auto;
      padding: 8px;
      display: block; 
      border-bottom: none !important; 
    }

   
    .toggleContent .campaignTable tbody tr.adRow > td:first-child {
      padding-right: 16px;
      width: 102px;
    }

    .toggleContent .campaignTable tbody tr.adRow > td:nth-child(2) {
      width: calc(100% - 150px); 
    //   margin-bottom: 16px; 
    }

    .toggleContent .campaignTable tbody tr.adRow > td:nth-child(3),
    .toggleContent .campaignTable tbody tr.adRow > td:nth-child(4) {
      width: 50%; 
    }

    .toggleContent .campaignTable tbody tr.adRow > td:last-child {
      width: 100%; 
     // padding-top: 16px;
      display: flex;
      justify-content: flex-start;
      gap: 8px;
    }

    .toggleContent .campaignTable tbody tr.adRow .actionButtons .actionBtns {
     display:flex;
     flex-direction:row;
    }
    
    .toggleContent .campaignTable tbody tr.adRow .statusRow {
      margin-bottom: 0;
    }

    .toggleContent .campaignTable tbody tr.adRow > td:nth-child(3) .infoList,
    .toggleContent .campaignTable tbody tr.adRow > td:nth-child(4) .metricsList {
      display: block; 
      font-size:11px;
      
    }

    .toggleContent .campaignTable tbody tr.adRow .infoList li,
    .toggleContent .campaignTable tbody tr.adRow .metricsList li {
      display: flex;
      margin-bottom: 4px;
      font-size:11px;
    }
  }

  @media (min-width: 601px) and (max-width: 1140px) {
    .noDataFound {
        padding-block:13%;
    }
    .campaignBlock {
        margin-block: 14px;
    }
    .campaignRow {
      padding-inline: 20px;
      padding-block: 9px;
        .categoryName {
            font-size: 14px !important;
        }
        .adsLinked {
            font-size: 10px;
        }
            .rightSideWrapper {
                 gap: 17px;
                .summaryList {
                    font-size: 11px !important;
                }
        
                .dropdownIconWrapper {
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .dropdownIcon {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 19px;
                        height: 19px;
                        transition: transform 0.3s ease-in-out;
                    }
                }

                .campaignKebabMenu {
                    .kebabMenuIcon {
                        width: 24px;
                        height: 24px;
                    }

                    .kebabDropdown {
                        min-width: 100px;

                        .kebabDropdownItem {
                            padding: 6px 12px;
                            font-size: 12px;
                        }
                    }
                }
            }
           
    
    }
    
    .toggleContent .campaignTable tbody tr.adRow {
      display: flex; 
      flex-wrap: wrap; 
       .categoryName {
            font-size: 14px !important;
            font-family: 'nunitosans-bold';
            color: #2e2e2e;
        }
        .adImage {
            width: 100%;
            height: auto;
        }
        .statusRow {
            display: flex;
            gap: 6px;
            margin-block: 17px 7px;
        }
}

    .toggleContent .campaignTable tbody tr.adRow > td {
      flex-basis: auto; 
      width: auto;
      padding: 8px;
      display: block; 
      border-bottom: none !important; 
    }

   
    .toggleContent .campaignTable tbody tr.adRow > td:first-child {
      padding-right: 16px;
      width: 102px;
    }

    .toggleContent .campaignTable tbody tr.adRow > td:nth-child(2) {
      width: calc(100% - 150px); 
    //   margin-bottom: 16px; 
    }

    .toggleContent .campaignTable tbody tr.adRow > td:nth-child(3),
    .toggleContent .campaignTable tbody tr.adRow > td:nth-child(4) {
      width: 50%; 
    }

    .toggleContent .campaignTable tbody tr.adRow > td:last-child {
      width: 100%; 
     // padding-top: 16px;
      display: flex;
      justify-content: flex-start;
      gap: 8px;
    }

    .toggleContent .campaignTable tbody tr.adRow .actionButtons .actionBtns {
     display:flex;
     flex-direction:row;
    }
    
    .toggleContent .campaignTable tbody tr.adRow .statusRow {
       margin-block: 17px 7px;
    }

    .toggleContent .campaignTable tbody tr.adRow > td:nth-child(3) .infoList,
    .toggleContent .campaignTable tbody tr.adRow > td:nth-child(4) .metricsList {
      display: block; 
      font-size:11px;
      
    }

    .toggleContent .campaignTable tbody tr.adRow .infoList li,
    .toggleContent .campaignTable tbody tr.adRow .metricsList li {
      display: flex;
      margin-bottom: 4px;
      font-size:11px;
    }
  }

   @media only screen and (min-width: 1141px) and (max-width: 1824px) and (max-height: 900px) {
    .noDataFound {
        padding-block:13%;
    } 
   .campaignBlock {
         margin-block: 14px;
    }
    .campaignRow {
        padding-inline: 20px;
        padding-block: 9px;
        height: 78px;
      .categoryName {
        font-size: 14px !important;
      }
      .adsLinked {
        font-size: 11px;
      }
      .rightSideWrapper {
            gap: 17px;
            .summaryList {
                font-size: 11px;
                .label {
                    width: 70px !important;
                }
            }
            .dropdownIconWrapper {
                .dropdownIcon {
                    width: 24px;
                    height: 24px;
                }
            }
        }
    }
    .toggleContent {
      .campaignTable thead th {
        font-size: 13px;
        padding: 10px 8px;
      }
      .campaignTable tbody tr.adRow td {
        font-size: 13px;
        color:#000000;
        padding: 12px 14px 10px 8px;
      }
      .campaignTable tbody tr.adRow .adImage {
        width: 102px;
        height: 62px;
      }
    .campaignTable tbody tr.adRow .statusRow {
        margin-block: 17px 7px;
    }
      .campaignTable tbody tr.adRow .categoryName {
        font-size: 14px !important;
      }
      .campaignTable tbody tr.adRow .infoList,
      .campaignTable tbody tr.adRow .metricsList {
        font-size: 11px;
      }
    }
  }
`;