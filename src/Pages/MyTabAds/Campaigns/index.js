
import { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { CircleArrowDown, ThreeDotIcon } from '../../../Components/Icons';
import { StylesWrapper } from './index.style';
import StatusBadge from '../Button/index';
import { objectiveLabels } from '../utils';
import moment from 'moment';
import Api from '../../../Helper/Api';
import { useApiRoutes } from '../../../Hooks/useApiRoutes';
import DeleteConfirmModal from '../../../Components/MyTabAds/DeleteConfirmModal';

const Campaigns = ({ campaignsData, onAdDeleted, onAdRenew, isAdUser}) => {
    const [openIndexes, setOpenIndexes] = useState([]);
    const [deletingAdId, setDeletingAdId] = useState(null);
    const [updatingAdId, setUpdatingAdId] = useState(null);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [adToDelete, setAdToDelete] = useState(null);
    const [showCampaignDeleteModal, setShowCampaignDeleteModal] = useState(false);
    const [campaignToDelete, setCampaignToDelete] = useState(null);
    const [deletingCampaignId, setDeletingCampaignId] = useState(null);
    const [openDropdownIndex, setOpenDropdownIndex] = useState(null);
    const roleData = useSelector((state) => ({ ...state.auth }));
    const apiRoutes = useApiRoutes();
    const dropdownRef = useRef(null);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            // Check if the click is outside any dropdown
            const isOutsideClick = !event.target.closest('.campaignKebabMenu');
            if (isOutsideClick && openDropdownIndex !== null) {
                setOpenDropdownIndex(null);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [openDropdownIndex]);

    const toggleSection = (index) => {
        setOpenIndexes((prev) =>
            prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
        );
    };

    const formatSchedule = (startDateTime, endDateTime) => {
        if (!startDateTime) return 'Schedule not set';

        const timezone = roleData?.timezone_value || 'UTC';
        // Convert UTC datetime strings to local timezone
        const localStartDate = moment.utc(startDateTime).tz(timezone);
        const localEndDate = endDateTime ? moment.utc(endDateTime).tz(timezone) : null;

        // Format dates in "Mar 1, 2025" format
        const start = localStartDate.format('MMM D, YYYY');
        if (!localEndDate) return `${start} - ongoing`;

        const end = localEndDate.format('MMM D, YYYY');
        return `${start} - ${end}`;
    };

    const getActionButtons = (status) => {
        // Handle array of statuses - use the first status to determine actions
        const primaryStatus = Array.isArray(status) ? status[0] : status;
        const statusLower = primaryStatus?.toLowerCase();

        switch (statusLower) {
            case 'active':
                return ['Pause', 'Delete'];
            case 'paused':
                return ['Resume', 'Delete'];
            case 'expired':
                return ['Renew', 'Delete'];
            case 'scheduled':
            case 'under review':
            case 'rejected':
                return ['Delete'];
            default:
                return ['Delete'];
        }
    };

    const handleDeleteAd = async (adId) => {
        try {
            setDeletingAdId(adId);
            const payload = {
                type: roleData?.login_type,
                id: adId.toString()
            };

            const res = await Api('POST', apiRoutes.deleteAd, payload);

            if (res?.data?.status) {
                // toast.success(res?.data?.message || 'Ad deleted successfully');
                // Call the callback to refresh the campaigns data
                if (onAdDeleted) {
                    onAdDeleted();
                }
            } else {
                toast.error(res?.data?.message || 'Failed to delete ad');
            }
        } catch (err) {
            toast.error(err?.message || 'Failed to delete ad');
        } finally {
            setDeletingAdId(null);
        }
    };

    const handlePauseResumeAd = async (adId, action) => {
        try {
            setUpdatingAdId(adId);
            const isPause = action.toLowerCase() === 'pause';
            const payload = {
                type: roleData?.login_type,
                id: adId.toString(),
                flag: isPause // true for pause, false for resume
            };

            const res = await Api('POST', apiRoutes.updatePauseStatus, payload);

            if (res?.data?.status) {
                const actionText = isPause ? 'paused' : 'resumed';
                toast.success(res?.data?.message || `Ad ${actionText} successfully`);
                // Call the callback to refresh the campaigns data
                if (onAdDeleted) {
                    onAdDeleted();
                }
            } else {
                toast.error(res?.data?.message || `Failed to ${action.toLowerCase()} ad`);
            }
        } catch (err) {
            toast.error(err?.message || `Failed to ${action.toLowerCase()} ad`);
        } finally {
            setUpdatingAdId(null);
        }
    };

    const handleActionClick = (action, ad, campaign) => {
        // Prevent resume action if paused by admin
        if (action.toLowerCase() === 'resume' && ad.pause_by_admin?.toLowerCase() === 'yes') {
            return; // Do nothing - click event is blocked
        }

        switch (action.toLowerCase()) {
            case 'delete':
                setAdToDelete(ad);
                setShowDeleteModal(true);
                break;
            case 'pause':
                handlePauseResumeAd(ad.id, 'pause');
                break;
            case 'resume':
                handlePauseResumeAd(ad.id, 'resume');
                break;
            case 'renew':
                // Navigate to Create Ad tab with renewal data including campaign
                if (onAdRenew) {
                    const renewalData = {
                        ...ad,
                        campaign_id: campaign.id, // Add campaign ID
                        campaign_title: campaign.title, // Add campaign title to renewal data
                        start_date: ad.start_date, // Add start date
                        end_date: ad.end_date // Add end date
                    };
                    onAdRenew(renewalData);
                }
                break;
            default:
                break;
        }
    };

    const handleDeleteModal = () => {
        setShowDeleteModal(!showDeleteModal);
        if (showDeleteModal) setAdToDelete(null);
    };

    const confirmDeleteAd = () => {
        if (adToDelete) {
            handleDeleteAd(adToDelete.id);
            setShowDeleteModal(false);
            setAdToDelete(null);
        }
    };

    const handleDeleteCampaign = async (campaignId) => {
        try {
            setDeletingCampaignId(campaignId);
            const payload = {
                type: roleData?.login_type,
                id: campaignId.toString()
            };

            const res = await Api('POST', apiRoutes.deleteCampaign, payload);

            if (res?.data?.status) {
                toast.success(res?.data?.message || 'Campaign deleted successfully');
                // Call the callback to refresh the campaigns data
                if (onAdDeleted) {
                    onAdDeleted();
                }
            } else {
                toast.error(res?.data?.message || 'Failed to delete campaign');
            }
        } catch (err) {
            toast.error(err?.message || 'Failed to delete campaign');
        } finally {
            setDeletingCampaignId(null);
        }
    };

    const handleCampaignDeleteModal = () => {
        setShowCampaignDeleteModal(!showCampaignDeleteModal);
        if (showCampaignDeleteModal) setCampaignToDelete(null);
    };

    const confirmDeleteCampaign = () => {
        if (campaignToDelete) {
            handleDeleteCampaign(campaignToDelete.id);
            setShowCampaignDeleteModal(false);
            setCampaignToDelete(null);
        }
    };

    const toggleDropdown = (index) => {
        setOpenDropdownIndex(openDropdownIndex === index ? null : index);
    };

    const handleCampaignAction = (action, campaign, e) => {
        // Make sure to stop propagation
        if (e) e.stopPropagation();

        switch (action.toLowerCase()) {
            case 'delete':
                setCampaignToDelete(campaign);
                setShowCampaignDeleteModal(true);
                setOpenDropdownIndex(null);
                break;
            default:
                break;
        }
    };

    return (
        <StylesWrapper>
            {campaignsData.map((campaign, index) => {
                const isOpen = openIndexes.includes(index);
                const hasAds = campaign.ads && campaign.ads.length > 0;
                return (
                    <div key={index} className="campaignBlock">
                        <div className="campaignRow" onClick={() => hasAds ? toggleSection(index) : null}>
                            <div>
                                <span className="categoryName">{campaign?.title}</span>
                                <p className="adsLinked">Ads linked: {campaign.ads?.length || 0}</p>
                            </div>

                            <div className={`rightSideWrapper ${isOpen ? 'open' : ''}`}>
                                <ul className="summaryList">
                                    <li><span className="label">Impressions:</span> <span>{campaign.impressions}</span></li>
                                    <li><span className="label">Reach:</span> <span>{campaign.reach}</span></li>
                                    <li><span className="label">Clicks:</span> <span>{campaign.click}</span></li>
                                </ul>

                                {hasAds && (<div className="dropdownIconWrapper">
                                    <div className="dropdownIcon">
                                        <CircleArrowDown width="100%" height="100%" />
                                    </div>
                                </div>
                                )}

                                {!hasAds && (
                                    <div className="campaignKebabMenu">
                                        <div
                                            className="kebabMenuIcon"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                toggleDropdown(index);
                                            }}
                                        >
                                            <ThreeDotIcon width="20" height="20" fill="#666" />
                                        </div>
                                        {openDropdownIndex === index && (
                                            <div className="kebabDropdown">
                                                <div
                                                    className="kebabDropdownItem"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleCampaignAction('delete', campaign, e);
                                                    }}
                                                >
                                                    Delete
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>

                        {isOpen && hasAds && (
                            <div className="toggleContent">
                                <table className="campaignTable">
                                    <tbody>
                                        {campaign.ads.map((ad, i) => (
                                            <>
                                                <tr key={i} className="adRow">
                                                    <td>
                                                        <img src={ad.media_url} alt="Ad Visual" className="adImage" />
                                                    </td>
                                                    <td>
                                                        <p className="categoryName">{ad.ad_title}</p>
                                                        <div className="statusRow">
                                                            {(Array.isArray(ad.status) ? ad.status : [ad.status]).map((s, j) => (
                                                                <StatusBadge key={j} status={s} />
                                                            ))}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <ul className='infoList'>
                                                            <li><span>Objective:</span>{objectiveLabels[ad.objective] || ad.objective}</li>
                                                                {!isAdUser && (
                                                                    <li><span>Budget:</span> ${ad.daily_budget} (lifetime)</li>
                                                                ) }
                                                            <li><span>Schedule:</span> {formatSchedule(ad.start_date_time, ad.end_date_time)}</li>
                                                        </ul>
                                                    </td>
                                                    <td>
                                                        <ul className="metricsList">
                                                            <li><span className="label">Impressions:</span> {ad.impressions}</li>
                                                            <li><span className="label">Reach:</span> {ad.reach}</li>
                                                            <li><span className="label">Clicks:</span> {ad.click}</li>
                                                        </ul>
                                                    </td>
                                                    <td className="actionButtons">
                                                        <div className="actionBtns">
                                                            {getActionButtons(ad.status).map((action, j) => (
                                                                <StatusBadge
                                                                    key={j}
                                                                    status={action}
                                                                    variant="outlined"
                                                                    onClick={() => handleActionClick(action, ad, campaign)}
                                                                    disabled={
                                                                        (deletingAdId === ad.id && action.toLowerCase() === 'delete') ||
                                                                        (updatingAdId === ad.id && ['pause', 'resume'].includes(action.toLowerCase())) ||
                                                                        (ad.pause_by_admin?.toLowerCase() === 'yes' && action.toLowerCase() === 'resume')
                                                                    }
                                                                />
                                                            ))}
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr className="spacerRow">
                                                    <td colSpan="5"></td>
                                                </tr>
                                            </>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}
                    </div>
                );
            })}
            {/* Delete confirmation modal for ads */}
            <DeleteConfirmModal
                isOpen={showDeleteModal}
                handleModal={handleDeleteModal}
                loading={deletingAdId === adToDelete?.id}
                handleDelete={confirmDeleteAd}
            />

            {/* Delete confirmation modal for campaigns */}
            <DeleteConfirmModal
                isOpen={showCampaignDeleteModal}
                handleModal={handleCampaignDeleteModal}
                loading={deletingCampaignId === campaignToDelete?.id}
                handleDelete={confirmDeleteCampaign}
                title="Delete Campaign"
                message="Are you sure you want to delete this campaign?"
            />
        </StylesWrapper>
    );
};

export default Campaigns;
