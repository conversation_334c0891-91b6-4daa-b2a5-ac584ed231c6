import styled from 'styled-components';

export const StyleWrraper = styled.div`
	font-family: nunitosans-regular !important;
	.filterWrapper {
		display: flex;
		flex:1;
		align-items: center;
		justify-content: space-between;
		background:rgba(255, 255, 255, 0.8);
		height:46px !important;
        border-radius: 4px;
        padding-inline:26px;
		border: 0.6px solid #D5D5D5;
	}
    .campaignNameInput {
        flex: 1; 
        padding-inline:23px;
        height: 36px;
        border:none;
        border-radius: 4px;
        font-size: 16px;
        outline: none;
        font-family: nunitosans-regular;
        color: #202224;
		}
        
     .dropdownWrapper {
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px !important;
            .dropdownText {
                font-size: 14px !important;
                color: #202224 !important;
                font-family: 'nunitosans-bold' !important;
            }
            .dropdownIcon {
                display: block;
                width: 25px;
                height: 25px;
            }
	    }
     .newCustomInputContainer{
            border:none !important;
            height: 40px;
            padding-inline: 0px;
    }
	.formGroupClassName {
		margin: 0px !important;
		font-size:16px;
		color: #202224;
	}
   
	.inputSearchIconWrapper {
			display: flex;
			justify-content: center;
			align-items: center;
			.inputSearchIcon {
				width: 19px;
				height: 17px;
				display: flex;
				justify-content: center;
				align-items: center;
			}
	}

	@media (max-width: 600px) {
		.filterWrapper {
            padding-inline:18px;
            .inputSearchIconWrapper {
                    .inputSearchIcon {
                        width: 13px;
                        height: 11px;
                    }
            }
		}
        .newCustomInput {
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
		}
         .campaignNameInput {
                padding-inline:15px;
                flex: 1; 
                font-size: 11px;
                font-family: nunitosans-regular;
                color: #202224;
			}
		.dropdownWrapper {
			gap: 4px;
			.dropdownText {
				color: #202224 !important;
				font-family: 'nunitosans-bold' !important;
				font-size: 9px !important;
			}
		}
        .newCustomInputContainer{
            border:none !important;
            padding-inline: 0px;
        }
	}

	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.filterWrapper {
            padding-inline:20px;
            .dropdownWrapper {
                gap: 5px;
                .dropdownText {
                    color: #202224 !important;
                    font-family: 'nunitosans-bold' !important;
                    font-size: 11px !important;
                }
            }
            .inputSearchIconWrapper {
				.inputSearchIcon {
					width: 14px;
					height: 13px;
				}
			}
		}
        .campaignNameInput {
                padding-inline:17px;
                flex: 1; 
                font-size: 12px;
                font-family: nunitosans-regular;
                color: #202224;
			}
        .newCustomInputContainer{
                border:none !important;
                padding-inline: 0px;
        } 
        .newCustomInput {
			font-size: 12px !important;
            color: rgba(32, 34, 36, 1) !important;
		    font-family: 'nunitosans-regular' !important;
			&::placeholder {
				font-size: 12px !important;
                color: rgba(32, 34, 36, 1) !important;
		        font-family: 'nunitosans-regular' !important;
			}
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.filterWrapper {
            padding-inline:20px;
            .dropdownWrapper {
                gap: 5px;
                .dropdownText {
                    color: #202224 !important;
                    font-family: 'nunitosans-bold' !important;
                    font-size: 11px !important;
                }
            }
            .inputSearchIconWrapper {
				.inputSearchIcon {
					width: 14px;
					height: 13px;
				}
			}
		}
        .campaignNameInput {
                padding-inline:17px;
                flex: 1; 
                font-size: 12px;
                font-family: nunitosans-regular;
                color: #202224;
			}
        .newCustomInputContainer{
            border:none !important;
            padding-inline: 0px;
        }
        .newCustomInput {
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
		}
	}
`;

export const PopoverStyleWrraper = styled.div`
	border-radius: 14px;
	background-color: white;
	box-shadow: 0px 9px 40px 0px #0000001b;
	width: 205px !important;
	div {
		font-family: 'nunitosans-semi-bold' !important;
		font-size: 14px !important;
		color: #404040 !important;
		border-bottom: 1px solid rgba(151, 151, 151, 0.25);
		padding: 15px 20px;
		cursor: pointer;
	}
	div:last-child {
		border-bottom: none;
	}
	@media only screen and (max-width: 1299px) {
		width: 154px !important;
		div {
			font-size: 11px !important;
			padding: 11px 15px;
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		width: 154px !important;
		div {
			font-size: 11px !important;
			padding: 11px 15px;
		}
	}
`;
