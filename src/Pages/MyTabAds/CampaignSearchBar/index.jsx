import React from 'react'
import { StyleWrraper } from './index.style';
import { SearchIcon } from '../../../Components/Icons/SearchIcon/SearchIcon';
import NewPopover from '../../../Components/Common/NewPopover';
import { PopoverStyleWrraper } from './index.style';
import { sortByCampaignData } from '../utils';
import DropdownIcon from '../../../Assets/images/newDropdownIconWOCircle.svg';
import NewFormInput from '../../../Components/NewForm/NewFormInput';

const CampaignSearchBar = ({ onSearchInputChange, onSortByChange, selectedSortBy }) => {

    const handleChange = (e) => {
        onSearchInputChange(e.target.value);
    };

    return (
        <StyleWrraper>
            <div className="filterWrapper">
                <div className="inputSearchIconWrapper">
                    <div className="inputSearchIcon">
                        <SearchIcon width="100%" height="100%" />
                    </div>
                </div>
                <input
                    type="text"
                    placeholder="Campaign name"
                    className="campaignNameInput"
                    name="campaignName"
                    onChange={handleChange}
                />
                <NewPopover
                    positions={['bottom', 'left', 'top', 'right']}
                    align="end"
                    onContentClick={(closePopover) => {
                        closePopover();
                    }}
                    content={
                        <PopoverStyleWrraper>
                            {sortByCampaignData?.length > 0 &&
                                sortByCampaignData?.map((item) => {
                                    return (
                                        <div
                                            key={item?.id}
                                            onClick={() =>
                                                onSortByChange(item)
                                            }
                                        >
                                            {item?.name}
                                        </div>
                                    );
                                })}
                        </PopoverStyleWrraper>
                    }
                >
                    <div className="dropdownWrapper">
                        <span className="dropdownText">
                            {selectedSortBy?.name
                                ? selectedSortBy?.name
                                : 'Sort by'}
                        </span>
                        <img
                            className="dropdownIcon"
                            src={DropdownIcon}
                            alt="dropdown-icon"
                        />
                    </div>
                </NewPopover>
            </div>
        </StyleWrraper>
    );
};

export default CampaignSearchBar;