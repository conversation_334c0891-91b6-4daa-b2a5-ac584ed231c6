import moment from 'moment';
import { convertUTCToLocal } from '../../Utils/dateTimeUtils';

export const tabOptions = [
    { label: 'Campaigns', value: 'campaigns' },
    { label: 'Create Ad', value: 'createAd' },
    { label: 'Reports', value: 'reports' }
];

// Campaigns Tab utils
export const sortByCampaignData = [
    {
        id: 1,
        name: 'Newest - Oldest',
        value: 'newest'
    },
    {
        id: 2,
        name: 'Oldest - Newest',
        value: 'oldest'
    },
    {
        id: 3,
        name: 'Alphabetical A-Z',
        value: 'alphabeticAsc'
    },
    {
        id: 4,
        name: 'Alphabetical Z-A',
        value: 'alphabeticDesc'
    }
    // {
    //     id: 4,
    //     name: 'Active',
    //     value: 'active'
    // },
    // {
    //     id: 5,
    //     name: 'Paused',
    //     value: 'paused'
    // },
    // {
    //     id: 6,
    //     name: 'Scheduled',
    //     value: 'scheduled'
    // },
    // {
    //     id: 6,
    //     name: 'Expired',
    //     value: 'expired'
    // }
];

// Create Ad Tab utils
export const adObjectiveOptions = [
    { label: 'Brand Awareness (CPM)', value: 'CPM', default: true },
    // { label: 'Clicks to Menu or Link (CPC)', value: 'CPC' },
    // { label: 'Drive Orders (CPA)', value: 'CPA' }
];

export const objectiveLabels = {
    CPM: "Brand Awareness (CPM)",
    CPC: "Clicks to Menu or Link (CPC)",
    CPA: "Drive Orders (CPA)"
};


export const callToActionOptions = [
    { label: 'Learn More', value: 'learn_more' },
    { label: 'Order Now', value: 'order_now' },
    { label: 'Buy Tickets', value: 'buy_tickets' },
    // { label: 'View Menu', value: 'view_menu' },
    { label: 'Visit Website', value: 'visit_website' }
];

export const australiaStateOptions = [
    { label: 'New South Wales', value: 'NSW' },
    { label: 'Victoria', value: 'VIC' },
    { label: 'Queensland', value: 'QLD' },
    { label: 'Western Australia', value: 'WA', default: true },
    { label: 'South Australia', value: 'SA' },
    { label: 'Tasmania', value: 'TAS' },
    { label: 'Australian Capital Territory', value: 'ACT' },
    { label: 'Northern Territory', value: 'NT' }
];

// Default city and suburb for Western Australia
export const defaultCity = 'Perth';
export const defaultSuburb = 'Scarborough';

export const advertiserAudienceOptions = [
    { label: 'All MyTab customers', value: 'all_mytab_customers' },
    { label: 'MyTab customer segments', value: 'mytab_customer_segments' }
];

export const venueAudienceOptions = [
    { label: 'All MyTab customers', value: 'all_mytab_customers' },
    { label: 'MyTab customer segments', value: 'mytab_customer_segments' },
    { label: 'Your venue’s customers', value: 'your_venues_customers' },
    {
        label: 'Your venue’s customer segments',
        value: 'your_venues_customers_segment'
    }
];

const CPM = 5;

const objectiveMultiplier = {
    CPM: 1,
    CPC: 1.25,
    CPA: 1.5
};

const audienceMultiplier = {
    all_mytab_customers: 1,
    mytab_customer_segments: 1.2,
    your_venues_customers: 1.5,
    your_venues_customers_segment: 2
};

export const getEstimatedReachedPerDay = (objective, audience, dailyBudget, maxUserCount) => {
    let totalReachedCount = 0;

    if (objective && audience && dailyBudget) {
        totalReachedCount =
            (Number(dailyBudget) /
                (CPM *
                    objectiveMultiplier[objective] *
                    audienceMultiplier[audience])) *
            1000;
    }

    return Math.min(Math.round(totalReachedCount), maxUserCount);
};

export const getMaxBudgetForAllUsers = (objective, audience, maxUserCount) => {
    if (!objective || !audience || !maxUserCount) return 300;

    // Calculate budget needed to reach all users using existing logic
    const budgetForAllUsers = (maxUserCount * CPM * objectiveMultiplier[objective] * audienceMultiplier[audience]) / 1000;

    return Math.max(Math.ceil(budgetForAllUsers), 5); // Minimum $5
};

// export const getMaxBudgetForObjective = (objective, audienceSize, CPM = 5) => {
//     if (!objective || !audienceSize) return 0;

//     switch (objective) {
//         case 'CPM': // Brand Awareness
//             return (audienceSize / 1000) * CPM;
//         case 'CPC': // Clicks to Link
//             return (audienceSize * 3 / 1000) * CPM;
//         case 'CPA': // Drive Orders
//             return (audienceSize * 5 / 1000) * CPM;
//         default:
//             return 0;
//     }
// };

// export const getEstimatedImpressionsPerDay = (
//     objective,
//     audience,
//     dailyBudget,
//     campaignDays,
//     userCount,
//     CPM = 5
// ) => {
//     if (!objective || !audience || !dailyBudget || !campaignDays || !userCount) return 0;

//     const objectiveMultiplier = {
//         CPM: 1,
//         CPC: 1.25,
//         CPA: 1.5
//     };

//     const audienceMultiplier = {
//         all_mytab_customers: 1,
//         mytab_customer_segments: 1.2,
//         your_venues_customers: 1.5,
//         your_venues_customers_segment: 2
//     };

//     const frequencyCap = {
//         CPM: 1,
//         CPC: 3,
//         CPA: 5
//     };

//     const impressionsByBudget = (dailyBudget / (CPM * objectiveMultiplier[objective] * audienceMultiplier[audience])) * 1000;
//     const totalImpressionsFromBudget = impressionsByBudget * campaignDays;

//     const maxTotalImpressions = userCount * frequencyCap[objective];

//     const effectiveTotalImpressions = Math.min(totalImpressionsFromBudget, maxTotalImpressions);

//     return Math.round(effectiveTotalImpressions / campaignDays);
// };


// Reports Tab utils	
export const campaignPerformanceSortByData = [
    {
        id: 1,
        name: 'Newest - Oldest',
        value: 'newest'
    },
    {
        id: 2,
        name: 'Oldest - Newest',
        value: 'oldest'
    },
    {
        id: 3,
        name: 'Alphabetical A-Z',
        value: 'alphabeticAsc'
    },
    {
        id: 4,
        name: 'Alphabetical Z-A',
        value: 'alphabeticDesc'
    },
    {
        id: 5,
        name: 'High - Low Spend',
        value: 'hightolowSpend'
    },
    {
        id: 6,
        name: 'Low - High Spend',
        value: 'lowtohighSpend'
    }
];

export const campaignPerformanceTableColumns = [
    {
        Header: 'Campaign',
        accessor: 'campaign',
        className: 'justify-content-start',
        minWidth: 246,
        maxWidth: 330,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'Ads Linked',
        accessor: 'ads_linked',
        className: 'justify-content-center',
        // minWidth: 166,
        // maxWidth: 224,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-center'
    },
    {
        Header: 'Spend',
        accessor: 'spend',
        className: 'justify-content-center',
        // minWidth: 175,
        // maxWidth: 236,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-center',
        // Cell: ({ row }) => {
        // 	const totalSpend = row.original.ads?.reduce((sum, ad) => sum + parseFloat(ad.daily_budget || 0), 0) || 0;
        // 	return `$${totalSpend.toFixed(2)}`;
        // }
    },
    {
        Header: 'Impressions',
        accessor: 'impressions',
        className: 'justify-content-center',
        // minWidth: 198,
        // maxWidth: 266,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-center'
    },
    {
        Header: 'Reach',
        accessor: 'reach',
        className: 'justify-content-center',
        // minWidth: 159,
        // maxWidth: 215,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-center'
    },
    {
        Header: 'Clicks',
        accessor: 'clicks',
        className: 'justify-content-center',
        // minWidth: 86,
        // maxWidth: 118,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-center'
    }
];

export const adsPerformanceSortByData = [
    {
        id: 1,
        name: 'Newest - Oldest',
        value: 'newest'
    },
    {
        id: 2,
        name: 'Oldest - Newest',
        value: 'oldest'
    },
    {
        id: 3,
        name: 'Alphabetical A-Z',
        value: 'alphabeticAsc'
    },
    {
        id: 4,
        name: 'Alphabetical Z-A',
        value: 'alphabeticDesc'
    },
    // {
    //     id: 5,
    //     name: 'High - Low CTR',
    //     value: ''
    // },
    {
        id: 6,
        name: 'High - Low Spend',
        value: 'hightolowSpend'
    },
    {
        id: 7,
        name: 'Low - High Spend',
        value: 'lowtohighSpend'
    }
];

export const adsPerformanceTableColumns = [
    {
        Header: 'Ad Title',
        accessor: 'title',
        className: 'justify-content-start',
        minWidth: 246,
        maxWidth: 330,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'Campaign',
        accessor: 'campaign',
        className: 'justify-content-start',
        minWidth: 266,
        maxWidth: 355,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'Status',
        accessor: 'status',
        className: 'justify-content-start',
        minWidth: 158,
        maxWidth: 210,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'Schedule',
        accessor: 'schedule',
        className: 'justify-content-start',
        minWidth: 230,
        maxWidth: 310,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'Objective',
        accessor: 'objective',
        className: 'justify-content-start',
        minWidth: 200,
        maxWidth: 268,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'Budget Amount',
        accessor: 'budgetAmount',
        className: 'justify-content-start',
        minWidth: 158,
        maxWidth: 210,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'Audience',
        accessor: 'audience',
        className: 'justify-content-start',
        minWidth: 232,
        maxWidth: 310,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'Audience Details',
        accessor: 'audienceDetails',
        className: 'justify-content-start',
        minWidth: 242,
        maxWidth: 323,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'Total Impressions',
        accessor: 'totalImpressions',
        className: 'justify-content-start',
        minWidth: 170,
        maxWidth: 226,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'Total Reach',
        accessor: 'totalReach',
        className: 'justify-content-start',
        minWidth: 142,
        maxWidth: 190,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'Total Clicks',
        accessor: 'totalClicks',
        className: 'justify-content-start',
        minWidth: 142,
        maxWidth: 190,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'CPM ($)',
        accessor: 'CPM',
        className: 'justify-content-start',
        minWidth: 158,
        maxWidth: 210,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    // {
    //     Header: 'CPC ($)',
    //     accessor: 'CPC',
    //     className: 'justify-content-start',
    //     minWidth: 154,
    //     maxWidth: 206,
    //     filterable: false,
    //     sortable: false,
    //     headerClassName: 'justify-content-start'
    // },
    // {
    //     Header: 'CPA ($)',
    //     accessor: 'CPA',
    //     className: 'justify-content-start',
    //     minWidth: 154,
    //     maxWidth: 206,
    //     filterable: false,
    //     sortable: false,
    //     headerClassName: 'justify-content-start'
    // },
    {
        Header: 'CTR (%)',
        accessor: 'CTR',
        className: 'justify-content-start',
        minWidth: 140,
        maxWidth: 187,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'Invoice Number',
        accessor: 'invoiceNumber',
        className: 'justify-content-start',
        minWidth: 162,
        maxWidth: 216,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start'
    },
    {
        Header: 'Payment Method',
        accessor: 'paymentMethod',
        className: 'justify-content-start truncateCell',
        minWidth: 300,
        maxWidth: 350,
        filterable: false,
        sortable: false,
        headerClassName: 'justify-content-start',
    }
];

export const formatCardExpiry = (value) => {
    // Remove all non-digit characters
    const cleaned = value?.replace(/\D/g, '');
    // Format as MM/YY
    if (cleaned?.length <= 2) {
        return cleaned;
    } else if (cleaned?.length <= 4) {
        return `${cleaned?.slice(0, 2)}/${cleaned?.slice(2)}`;
    } else {
        return `${cleaned?.slice(0, 2)}/${cleaned?.slice(2, 4)}`;
    }
};


export const formatScheduleWithTime = (startDate, endDate, startTime, endTime, timezone) => {
    if (!timezone) {
        console.warn('No timezone provided to formatScheduleWithTime, using UTC');
        timezone = 'UTC';
    }

    // If no start date, return empty string
    if (!startDate) return '';

    // Ensure we have time values, default to start/end of day if not provided
    const startTimeValue = startTime || '00:00:00';
    const endTimeValue = endTime || '23:59:59';

    // Convert UTC dates and times to user's local timezone
    const startLocal = convertUTCToLocal(startDate, startTimeValue, timezone);

    // Format the start date and time - use the exact format shown in the UI
    const start = moment(startLocal.date).format('MMM D, YYYY') + ' ' + startLocal.time;

    // If no end date, show as ongoing
    if (!endDate) return `${start} - ongoing`;

    // Convert end date/time to local
    const endLocal = convertUTCToLocal(endDate, endTimeValue, timezone);

    // Format the end date and time - use the exact format shown in the UI
    const end = moment(endLocal.date).format('MMM D, YYYY') + ' ' + endLocal.time;

    // Return the formatted schedule
    return `${start} - ${end}`;
};
