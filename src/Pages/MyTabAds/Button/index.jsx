// StatusBadge.js
import React from 'react';
import { BadgeWrapper } from './index.style';  // Importing the styles

const StatusBadge = ({ status, variant = 'filled', onClick, disabled = false }) => {
    const statusArray = Array.isArray(status) ? status : [status];
    return (
        <>
            {statusArray.map((s, index) => (
                <BadgeWrapper
                    key={index}
                    status={s}
                    variant={variant}
                    onClick={onClick ? () => onClick(s) : undefined}
                    disabled={disabled}
                    clickable={!!onClick}
                >
                    {s}
                </BadgeWrapper>
            ))}
        </>
    );
};

export default StatusBadge;
