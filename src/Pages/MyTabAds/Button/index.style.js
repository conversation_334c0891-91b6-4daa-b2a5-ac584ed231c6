// statusBadge.style.js
import styled, { css } from 'styled-components';

const statusColors = {
    active: {
        background: 'rgba(107, 194, 66, 0.2)', // lighter background
        text: 'rgba(107, 194, 66, 1)',        // text color
    },
    paused: {
        background: 'rgba(0, 178, 255, 0.2)', // lighter background
        text: 'rgba(0, 178, 255, 1)',         // text color
    },
    expired: {
        background: 'rgba(151, 151, 151, 0.2)', // lighter background
        text: 'rgba(151, 151, 151, 1)',         // text color
    },
    scheduled: {
        background: 'rgba(244, 143, 0, 0.2)',   // lighter background
        text: 'rgba(244, 143, 0, 1)',           // text color
    },
    'under review': {
        background: 'rgba(72, 128, 255, 0.2)',  // lighter background
        text: 'rgba(72, 128, 255, 1)',          // text color
    },
    rejected: {
        background: 'rgba(255, 0, 0, 0.2)',     // lighter background
        text: 'rgba(255, 0, 0, 1)',             // text color
    },
    resume: {
        background: 'rgba(61, 197, 94, 0.2)',   // lighter background
        text: 'rgba(61, 197, 94, 1)',           // text color
    },
    renew: {
        background: 'rgba(61, 66, 223, 0.2)',   // lighter background
        text: 'rgba(61, 66, 223, 1)',           // text color
    },
    pause: {
        background: 'rgba(0, 178, 255, 0.2)',   // lighter background
        text: 'rgba(0, 178, 255, 1)',           // text color
    },
    delete: {
        background: 'rgba(255, 0, 0, 0.2)',     // lighter background
        text: 'rgba(255, 0, 0, 1)',             // text color
    },
    default: {
        background: '#757575',                  // default background
        text: '#fff',                            // default text color
    },
};

export const BadgeWrapper = styled.span`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'nunitosans-medium';
  margin-right: 6px;
  white-space: nowrap;
  border: 1px solid transparent;

  /* Default width and height */
  width: 116px;
  height: 28px;

  /* Center text vertically */
  display: flex;
  justify-content: center;
  align-items: center;

  /* Clickable styles */
  ${({ clickable, disabled }) => clickable && !disabled && css`
    cursor: pointer;
    transition: opacity 0.2s ease, transform 0.1s ease;

    &:hover {
      opacity: 0.8;
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }
  `}

  ${({ disabled }) => disabled && css`
    opacity: 0.8;
    cursor: not-allowed;
    background-color: transparent !important;
    color: #999999 !important;
    border-color: #999999 !important;
  `}

  ${({ status, variant }) => {
        const key = typeof status === 'string' ? status.toLowerCase() : 'default';
        const colors = statusColors[key] || statusColors.default;

        if (variant === 'outlined') {
            return css`
                background-color: white;
                color: ${colors.text};
                border-color: ${colors.text};
            `;
        } else {
            return css`
                background-color: ${colors.background};
                color: ${colors.text};
            `;
        }
    }}

  /* Media Queries for responsiveness */
  @media (max-width: 600px) {
    width: 78px;  
    height: 20px;  
    font-size: 8px; 
  }

  @media only screen and (min-width: 600px) and (max-width: 1299px) {
    width: 87px;   
    height: 21px;  
    font-size: 9px; 
  }

    @media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
    width: 87px;  
    height: 21px;  
    font-size: 9px; 
    }
`;
