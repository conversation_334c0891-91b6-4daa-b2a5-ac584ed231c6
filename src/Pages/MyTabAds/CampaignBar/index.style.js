import styled from 'styled-components';

export const StyleWrraper = styled.div`
	.tableCount {
		border: 1px solid #d5d5d5;
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-inline: 26px;
		height: 73px;
		background: #fbfcff !important;
		.textOne {
			color: #202224d9;
			font-family: 'nunitosans-bold';
			font-size: 16px;
		}
		.textTwo {
			color: #20222466;
			font-family: 'nunitosans-regular';
			font-size: 16px;
		}
	}


	@media (max-width: 600px) {
		.tableCount {
			padding-inline: 16px;
			height: 54px;
			.textOne {
				font-size: 12px;
			}
			.textTwo {
				font-size: 12px;
			}
		}
	}

	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.tableCount {
			padding-inline: 20px;
			height: 54px;
			.textOne {
				font-size: 12px;
			}
			.textTwo {
				font-size: 12px;
			}
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.tableCount {
			padding-inline: 20px;
			height: 54px;
			.textOne {
				font-size: 12px;
			}
			.textTwo {
				font-size: 12px;
			}
		}
	}
`;