import { useCallback, useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';

import NewPageWrapper from '../../Components/Common/NewPageWrapper';
import NewPageTitle from '../../Components/Common/NewPageTitle';
import CampaignSearchBar from './CampaignSearchBar/index';
import NewLoader from '../../Components/Common/NewLoader';
import { MyTabAdsWrraper } from './index.style';
import CreateCampaign from './CreateCampaign';
import CampaignBar from './CampaignBar';
import { tabOptions } from './utils';
import Segmented from './Segmented';
import Campaigns from './Campaigns';
import CreateAds from './CreateAds';
import Api from '../../Helper/Api';
import Reports from './Reports';
import { loadStripe } from '@stripe/stripe-js';
import { stripePublicKey } from '../../Helper/constant';
import { Elements } from '@stripe/react-stripe-js';
import { useApiRoutes } from '../../Hooks/useApiRoutes';
import { FilledButton } from '../../Components/Layout/Buttons';
import { CommonApiRoutes } from '../../Utils/routes';
import fileDownload from 'js-file-download';

let timeoutVar;
const stripePromise = loadStripe(stripePublicKey);

const stripeOptions = {
    fonts: [
        {
            family: 'Nunito Sans',
            src: 'url("https://fonts.gstatic.com/s/nunitosans/v9/pe0qMImSLYBIv1o4X1M8cce9IQ.woff2") format("woff2")',
            weight: '400',
            style: 'normal',
        },
    ],
    appearance: {
        variables: {
            fontFamily: 'Nunito Sans, sans-serif',
            fontWeightNormal: '400',
        },
    },
};


// Create a wrapper component that can force remount
const CreateAdsWrapper = ({ handleCancel, renewalData }) => {
    const [key, setKey] = useState(Date.now());

    const resetForm = () => {
        setKey(Date.now());
    };

    // Reset when renewalData changes (including when it becomes null)
    useEffect(() => {
        setKey(Date.now());
    }, [renewalData]);

    return (
        <Elements stripe={stripePromise} options={stripeOptions}>
            <CreateAds
                key={key}
                handleCancel={handleCancel}
                onSuccessfulSubmit={() => {
                    resetForm();
                    // Call parent's success handler after local reset
                    setTimeout(() => {
                        if (handleCancel) handleCancel();
                    }, 0);
                }}
                renewalData={renewalData}
                isRenewalMode={!!renewalData}
            />
        </Elements>
    );
};

const MyTabAds = () => {
    const [selectedTab, setSelectedTab] = useState('campaigns');
    const [campaignsData, setCampaignsData] = useState([]);
    const [tableLoading, setTableLoading] = useState(false);
    const [renewalAdData, setRenewalAdData] = useState(null);
    const reportsRef = useRef();
    const [exportLoading, setExportLoading] = useState(false);
    const [tableParams, setTableParams] = useState({
        totalCount: 0,
        currentPage: 1,
        pageSize: 10,
        searchTerm: '',
        sortBy: {
            id: 1,
            name: 'Newest - Oldest',
            value: 'newest'
        }
    });
    const roleData = useSelector((state) => ({ ...state.auth }));
    const apiRoutes = useApiRoutes();
    const isAdUser = roleData?.login_type === 'advertiser';
    const handleTabChange = (value) => {
        setSelectedTab(value);

        // Clear renewal data and reset form when switching to create ad tab
        if (value === 'createAd') {
            setRenewalAdData(null);
        }
    };

    const handleSortByChange = (item) => {
        setTableParams({
            ...tableParams,
            currentPage: 1,
            sortBy: item
        });
    };

    const handleSearchInputChange = (value) => {
        if (timeoutVar) clearTimeout(timeoutVar);
        timeoutVar = setTimeout(() => {
            setTableParams((prev) => ({
                ...prev,
                currentPage: 1,
                searchTerm: value
            }));
        }, 500); // Debounce delay
    };

    const getTableData = useCallback(
        async (isShowLoader = true) => {
            try {
                if (isShowLoader) {
                    setTableLoading(true);
                }
                let payload = {
                    type: roleData?.login_type,
                    created_by_id: roleData?.id,
                    limit: tableParams.pageSize,
                    page: tableParams.currentPage,
                    sort_by: tableParams?.sortBy?.value
                        ? tableParams?.sortBy?.value
                        : 'oldest',
                    search: tableParams?.searchTerm
                        ? tableParams?.searchTerm
                        : ''
                };
                const res = await Api(
                    'POST',
                    apiRoutes.campaignList,
                    payload
                );
                if (res?.data?.status) {
                    setCampaignsData(res?.data?.data?.campaigns);
                    setTableParams((prev) => ({
                        ...prev,
                        totalCount: res?.data?.data?.pagination?.total || 0
                    }));
                } else {
                    setCampaignsData([]);
                    setTableParams((prev) => {
                        return {
                            ...prev,
                            currentPage: 1,
                            totalCount: 0
                        };
                    });
                    toast.error(res?.data?.message);
                }
            } catch (err) {
                setCampaignsData([]);
                setTableParams((prev) => {
                    return {
                        ...prev,
                        currentPage: 1,
                        totalCount: 0
                    };
                });
                toast.error(err?.message || 'Failed to fetch Campaign');
            } finally {
                if (isShowLoader) {
                    setTableLoading(false);
                }
            }
        },
        [
            roleData?.login_type,
            roleData?.id,
            tableParams?.pageSize,
            tableParams?.currentPage,
            tableParams?.sortBy?.value,
            tableParams?.searchTerm
        ]
    );

    const handleAdCreated = () => {
        setRenewalAdData(null); // Clear renewal data first
        setSelectedTab('campaigns'); // Then switch tabs
        getTableData();
    };

    const handleAdDeleted = () => {
        getTableData();
    };

    const handleAdRenew = (adData) => {
        setRenewalAdData(adData);
        setSelectedTab('createAd');
    };

    const handleCancelRenewal = () => {
        setRenewalAdData(null);
        setSelectedTab('campaigns');
    };

    useEffect(() => {
        if (selectedTab === 'campaigns') {
            getTableData();
        }
    }, [selectedTab, getTableData]);

    const handleExport = async () => {
        if (reportsRef.current) {
            setExportLoading(true);
            try {
                await reportsRef.current.exportReportData();
            } catch (err) {
                // Error handling is done in Reports component
            } finally {
                setExportLoading(false);
            }
        }
    };
    return (
        <NewPageWrapper>
            <MyTabAdsWrraper>
                <div className="titleWrap">
                    <div className="headerClass">
                        <NewPageTitle>Ad Management Dashboard</NewPageTitle>
                        {selectedTab === 'reports' && (
                            <FilledButton
                                onClick={handleExport}
                                buttonText={'Export'}
                                background={'#d8d9f9'}
                                color={'#3D42DF'}
                                loading={exportLoading}
                               	style={{ width: '160px', border: '1px solid #3D42DF' }}
                            />
                        )}
                    </div>
                    <p className="mainParagraph bottomText">
                        In-app advertising is a powerful way to boost
                        visibility, attract new customers and encourage repeat
                        orders. Whether you're promoting menu specials or
                        looking to connect with engaged food-lovers, our
                        platform offers a direct and effective way to reach your
                        ideal audience.
                    </p>
                    {/* <p className="mainParagraph">
                        Designed to support every stage of the customer
                        journey-from awareness to action-you can choose the
                        objective that aligns with your goals:
                    </p> */}
                    {/* <ul className="pb-0">
                        <li className="listElement">
                            <b>Brand Awareness:</b> Increase visibility and get
                            your venue or brand in front of more people.
                        </li>
                        <li className="listElement">
                            <b>Clicks to Menu or Link:</b> Drive traffic to
                            your menu, website, or event page.
                        </li>
                        <li className="listElement">
                            <b>Drive Orders:</b> Turn ad views into orders by
                            reaching customers ready to purchase.
                        </li>
                    </ul> */}
                    {/* <p className="mainParagraph ">
                        Each objective plays a strategic role in your marketing
                        funnel-from being seen, to being clicked, to driving
                        results.
                    </p> */}
                </div>

                <div className="segmentTab">
                    <Segmented
                        options={tabOptions}
                        value={selectedTab}
                        onChange={handleTabChange}
                    />
                </div>
                {selectedTab === 'campaigns' && (
                    <>
                        <CampaignBar campaignCount={campaignsData?.length} />
                        <CreateCampaign onCampaignCreated={handleAdCreated} />
                        <CampaignSearchBar
                            onSearchInputChange={handleSearchInputChange}
                            onSortByChange={handleSortByChange}
                            selectedSortBy={tableParams?.sortBy}
                        />
                        {tableLoading ? (
                            <div className="loaderWrapper">
                                <NewLoader />
                            </div>
                        ) : (
                            <Campaigns
                                campaignsData={campaignsData}
                                onAdDeleted={handleAdDeleted}
                                onAdRenew={handleAdRenew}
                                isAdUser={isAdUser}
                            />
                        )}
                    </>
                )}
                {selectedTab === 'createAd' && (
                    <Elements stripe={stripePromise}>
                        <CreateAdsWrapper
                            handleCancel={renewalAdData ? handleCancelRenewal : () => setSelectedTab('campaigns')}
                            renewalData={renewalAdData}
                            onSuccessfulSubmit={handleAdCreated}
                        />
                    </Elements>
                )}
                {selectedTab === 'reports' && <Reports ref={reportsRef} />}
            </MyTabAdsWrraper>
        </NewPageWrapper>
    );
};

export default MyTabAds;
