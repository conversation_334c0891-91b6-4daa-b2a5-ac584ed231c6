import React, { useState } from 'react';
import { FilledButton } from '../../../Components/Layout/Buttons';
import { StyleWrraper } from './index.style';
// import { CommonApiRoutes } from '../../../Utils/routes';
import Api from '../../../Helper/Api';
import { toast } from 'react-toastify';
import { useSelector } from 'react-redux';
import * as Yup from 'yup';
import { useApiRoutes } from '../../../Hooks/useApiRoutes';

const validationSchema = Yup.object({
    campaignName: Yup.string().required('Please enter campaign name'),
});

const CreateCampaign = ({ onCampaignCreated }) => {
    const [campaignName, setCampaignName] = useState('');
    const [loading, setLoading] = useState(false);
    const roleData = useSelector((state) => ({ ...state.auth }));
    const apiRoutes = useApiRoutes();

    const handleSubmit = async () => {
        try {
            await validationSchema.validate({ campaignName });

            setLoading(true);

            const payload = {
                name: campaignName,
                created_by_type: roleData?.login_type,
                created_by_id: roleData?.id,
                type: roleData?.login_type,
            };

            const res = await Api('POST', apiRoutes.addCampaign, payload);

            if (res?.data?.status) {
                // toast.success('Campaign created successfully!');
                setCampaignName('');
                onCampaignCreated?.();
            } else {
                toast.error(res?.data?.message || 'Failed to create campaign.');
            }
        } catch (error) {
            if (error.name === 'ValidationError') {
                toast.error(error.message);
            } else {
                console.error('Error creating campaign:', error);
                toast.error('Something went wrong. Please try again.');
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <StyleWrraper>
            <div className="topSection">
                <div className="rowControls">
                    <input
                        type="text"
                        placeholder="Campaign name"
                        className="campaignNameInput"
                        name="campaignName"
                        value={campaignName}
                        onChange={(e) => setCampaignName(e.target.value)}
                    />
                    <FilledButton
                        buttonText={'Create campaign'}
                        loading={loading}
                        className={'filledButtonNewTheme'}
                        color={'rgba(72, 128, 255, 1)'}
                        style={{
                            border: '1px solid #4880FF',
                        }}
                        onClick={handleSubmit}
                        disabled={loading}
                    />
                </div>
            </div>
        </StyleWrraper>
    );
};

export default CreateCampaign;
