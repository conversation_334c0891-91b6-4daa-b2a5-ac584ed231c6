import styled from 'styled-components';

export const StyleWrraper = styled.div`
	.topSection {
		display: flex;
		flex-direction: column;
		gap: 16px; 
        position: relative; 
        padding-block:18px !important;

        
		.rowControls {
			display: flex;
			align-items: center;
			justify-content: space-between; 
			gap: 6px;
		}

		.campaignNameInput {
			flex: 1; 
			padding-inline:26px;
			height: 46px;
			border: 1px solid #ccc;
			border-radius: 4px;
			font-size: 16px;
            outline: none;
			font-family: nunitosans-regular;
            color: #202224;
		}

		.filledButtonNewTheme {
			width: 171px;
			height: 46px;
			font-size:12px;
		}
	}
    .error-message {
        position: absolute;
        left: 2px;
        top: calc(100% + -29px);
        width: 100%;
        color: rgb(255, 95, 95) !important;
        font-family: nunitosans-semi-bold !important;
        font-size: 12px;
    }
    
	@media only screen and (max-width: 600px) {
		.topSection {
            padding-block: 12px !important;
			.campaignNameInput {
                padding-inline:18px;
				flex: 1; 
				height: 31px !important;
				font-size: 11px;
                font-family: nunitosans-regular;
                color: #202224;
			}
            .rowControls {
                gap: 4px;   
            }
		}
        .error-message {
            top: calc(100% + -16px);
        }
        .filledButtonNewTheme {
			width: 115px;
			height: 31px !important;
            width: 115px;
			font-size:8px;
		}
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.topSection {
            padding-block: 14px !important;
			.campaignNameInput {
                padding-inline:20px;
				flex: 1; 
				height: 35px;
				font-size: 12px;
                font-family: nunitosans-regular;
                color: #202224;
			}
            .rowControls {
                gap: 5px;   
            }
		}
        .error-message {
            top: calc(100% + -16px);
        }
        .filledButtonNewTheme {
			width: 128px !important;
			height: 35px !important;
			font-size:9px !important;
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1890px) and (max-height: 900px) {
		.topSection {
            padding-block: 14px !important;
			.campaignNameInput {
				flex: 1; 
				height: 35px;
				font-size: 12px;
                padding-inline:20px;
                font-family: nunitosans-regular;
                color: #202224;
			}
            .rowControls {
                gap: 5px;   
            }
		}
        .error-message {
            top: calc(100% + -16px) !important;
        }
        .filledButtonNewTheme {
			width: 128px !important;
			height: 35px !important;
			font-size:9px !important;
		}
	}
`;
