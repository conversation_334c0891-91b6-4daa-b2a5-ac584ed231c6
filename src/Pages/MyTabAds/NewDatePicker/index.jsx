import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { getMonth, getYear } from 'date-fns';
import { range } from 'd3-array';
import { forwardRef, useEffect, useState } from 'react';
import {
	Dropdown,
	DropdownItem,
	DropdownMenu,
	DropdownToggle
} from 'reactstrap';
import { StylesWrapper } from './index.style';
import useDevice from '../../../Hooks/useDevice';
import {
	CircleArrowDown,
	DownArrowIcon,
	LeftArrowIcon,
	RightArrowIcon,
	UpArrowIcon
} from '../../../Components/Icons';

const NewDatePicker = ({
	name,
	onChange,
	onBlur,
	value,
	minDate,
	startYear = getYear(new Date()),
	suffix = (
		<div className="dropdownIconWrapper">
			<div className="dropdownIcon">
				<CircleArrowDown width="100%" height="100%" />
			</div>
		</div>
	),
	placeholder,
	wrapperClassName,
	disabled
}) => {
	const [date, setDate] = useState(null);
	const [monthDropdown, setMonthDropdown] = useState(false);
	const [yearDropdown, setYearDropdown] = useState(false);
	const { isTablet } = useDevice();
	const years = range(startYear, getYear(new Date()) + 20, 1);
	const months = [
		'January',
		'February',
		'March',
		'April',
		'May',
		'June',
		'July',
		'August',
		'September',
		'October',
		'November',
		'December'
	];
	useEffect(() => {
		setDate(value);
	}, [value]);
	const CustomInput = forwardRef(({ value, onClick, className }, ref) => (
		<button
			type="button"
			className={className}
			onClick={onClick}
			onBlur={onBlur}
			ref={ref}
			disabled={disabled}
		>
			{value ? (
				<span className="valueContainer">{value}</span>
			) : (
				<span className="placeholderContainer">{placeholder}</span>
			)}
			{suffix && <div className="suffixContainer">{suffix}</div>}
		</button>
	));
	return (
		<StylesWrapper className={wrapperClassName}>
			<div className="newCustomDatePickerContainer">
				<div className="newCustomDatePicker">
					<DatePicker
						name={name}
						showPopperArrow={false}
						dateFormat="dd/MM/yyyy"
						placeholderText={placeholder}
						selected={date}
						onChange={(date) => {
							setDate(date);
							if (onChange) {
								onChange(date);
							}
						}}
						minDate={minDate}
						locale="en-GB"
						renderCustomHeader={({
							date,
							changeYear,
							changeMonth,
							decreaseMonth,
							increaseMonth,
							prevMonthButtonDisabled,
							nextMonthButtonDisabled
						}) => (
							<div className="d-flex align-items-center">
								<span
									onClick={decreaseMonth}
									className={`cursor-pointer ${
										prevMonthButtonDisabled
											? 'invisible'
											: 'visible'
									} ml-12`}
								>
									<LeftArrowIcon
										width={16}
										height={16}
										color="#ff5f5f"
									/>
								</span>
								<div className="flex-1 d-flex justify-content-center gap-2">
									{/* Year Dropdown */}
									<Dropdown
										isOpen={yearDropdown}
										toggle={() =>
											setYearDropdown((prev) => !prev)
										}
										direction="down"
										className="customDropdown"
									>
										<DropdownToggle
											color="#fff"
											className="ptb-0 customDropdownToggle yearDropdownToggle"
										>
											<span
												className={`customDropdownToggleText`}
											>
												{getYear(date)}
											</span>
											<span>
												{yearDropdown ? (
													<UpArrowIcon
														height={8}
														width={8}
													/>
												) : (
													<DownArrowIcon
														height={8}
														width={8}
													/>
												)}
											</span>
										</DropdownToggle>
										<DropdownMenu
											className={`${
												isTablet ? 'w-100' : ''
											} customDropdownMenu`}
											end={isTablet}
										>
											{years?.length > 0 &&
												years?.map((year, index) => {
													return (
														<DropdownItem
															key={index}
															className={`${
																getYear(
																	date
																) === year
																	? 'active'
																	: ''
															} customDropdownItem`}
															onClick={() =>
																changeYear(year)
															}
														>
															{year}
														</DropdownItem>
													);
												})}
										</DropdownMenu>
									</Dropdown>
									{/* Month Dropdown */}
									<Dropdown
										isOpen={monthDropdown}
										toggle={() =>
											setMonthDropdown((prev) => !prev)
										}
										direction="down"
										className="customDropdown"
									>
										<DropdownToggle
											color="#fff"
											className="ptb-0 customDropdownToggle monthDropdownToggle"
										>
											<span
												className={`customDropdownToggleText`}
											>
												{months[getMonth(date)]}
											</span>
											<span>
												{monthDropdown ? (
													<UpArrowIcon
														height={8}
														width={8}
													/>
												) : (
													<DownArrowIcon
														height={8}
														width={8}
													/>
												)}
											</span>
										</DropdownToggle>
										<DropdownMenu
											className={`${
												isTablet ? 'w-100' : ''
											} customDropdownMenu monthDropdownMenu`}
											end={isTablet}
										>
											{months?.length > 0 &&
												months?.map((month, index) => {
													return (
														<DropdownItem
															key={index}
															className={`${
																months[
																	getMonth(
																		date
																	)
																] === month
																	? 'active'
																	: ''
															} customDropdownItem`}
															onClick={() => {
																changeMonth(
																	months?.indexOf(
																		month
																	)
																);
															}}
														>
															{month}
														</DropdownItem>
													);
												})}
										</DropdownMenu>
									</Dropdown>
								</div>
								<span
									onClick={increaseMonth}
									className={`cursor-pointer ${
										nextMonthButtonDisabled
											? 'invisible'
											: 'visible'
									} mr-12`}
								>
									<RightArrowIcon
										width={16}
										height={16}
										color="#ff5f5f"
									/>
								</span>
							</div>
						)}
						customInput={
							<CustomInput className="customDateInput" />
						}
					/>
				</div>
			</div>
		</StylesWrapper>
	);
};

export default NewDatePicker;
