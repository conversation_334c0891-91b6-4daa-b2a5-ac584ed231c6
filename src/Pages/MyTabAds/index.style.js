import styled from 'styled-components';

export const MyTabAdsWrraper = styled.div`
	font-family: nunitosans-regular !important;

	.customeHrClass {
		border: 0.6px solid #949596;
		margin-block: 10px;
	}

    .loaderWrapper{
        display: flex;
        justify-content: center;
        margin-block: 80px;
    }

	ul {
		margin: 0px;
		padding-bottom: 18px;
	}

	.segmentTab {
		padding-block: 18px !important;		
	}
	
	.titleWrap {
		display: flex;
		flex-direction: column;

		.headerClass {
			display: flex;
			align-items: center;
            justify-content: space-between;
			height: 38px !important;
		}

		.mainParagraph {
			font-size: 16px;
			font-weight: 400;
			font-family: nunitosans-regular;
			color: #2e2e2e;
		}

		.bottomText {
			padding-top: 14px;
		}

		.listElement {
			font-size: 16px;
			font-weight: 400;
			font-family: nunitosans-regular;
			color: #2e2e2e;
		}
	}
	@media (max-width: 600px) {
		.titleWrap {
			.mainParagraph {
				font-size: 11px;
				color: #202224;
				font-family: nunitosans-regular;
			}
			
			.listElement {
				font-size: 11px;
				font-family: nunitosans-regular;
				color: #2e2e2e;
			}
		}
		.segmentTab {
		    padding-block: 12px !important;		
		}
	}

	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.titleWrap {
			.mainParagraph {
				font-size: 11px;
				font-family: nunitosans-regular;
			}

			.listElement {
				font-size: 11px;
				font-family: nunitosans-regular;
				color: #2e2e2e;
			}
		}
		.segmentTab {
		    padding-block: 14px !important;		
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.titleWrap {
			.mainParagraph {
				font-size: 12px;
				font-family: nunitosans-regular;
			}

			.listElement {
				font-size: 12px;
				font-family: nunitosans-regular;
				color: #2e2e2e;
			}
		}
		.segmentTab {
		    padding-block: 14px !important;		
		}
	}
`;

export const PageWrapper = styled.div`
	color: ${(props) => props.layoutTheme.textColor} !important;
`;