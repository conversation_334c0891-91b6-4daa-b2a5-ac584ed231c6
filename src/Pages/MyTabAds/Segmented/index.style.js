import styled from 'styled-components';

export const StyleWrraper = styled.div`
	.segmentedContainer {
		width: fit-content;
		display: flex;
		/* background-color: #c7c7cc; */
		background-color: #e5e5ea;
		height: 37px;
		padding: 2px;
		border-radius: 9px;
		.segmentedItem {
			width: 172px;
			border-radius: 7px;
			font-family: 'nunitosans-bold';
			font-size: 13px;
			line-height: 18px;
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
		}
		.segmentedItem.active {
			background-color: #ffffff;
			transition: all 0.6s;
		}
	}
	@media only screen and (max-width: 600px) {
		.segmentedContainer {
			height: 28px;
			border-radius: 7px;
			.segmentedItem {
				width: 129px;
				border-radius: 5px;
				font-size: 10px;
				line-height: 14px;
			}
		}
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.segmentedContainer {
			height: 28px;
			border-radius: 7px;
			.segmentedItem {
				width: 129px;
				border-radius: 5px;
				font-size: 10px;
				line-height: 14px;
			}
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.segmentedContainer {
			height: 28px;
			border-radius: 7px;
			.segmentedItem {
				width: 129px;
				border-radius: 5px;
				font-size: 10px;
				line-height: 14px;
			}
		}
	}
`;
