import React, { useMemo, useState, useEffect } from 'react';
import { getTrackBackground, Range } from 'react-range';
import { StyleWrraper } from './index.style';
import { getMaxBudgetForAllUsers } from '../../utils';

const STEP = 1;
const MIN = 5;

const DailyBudget = ({ reachedPerDayCount, value = 5, onChange, maxUserCount, objective, audience }) => {
    // Internal slider position state
    const [sliderValue, setSliderValue] = useState(value);

    // Sync internal state with external value changes
    useEffect(() => {
        setSliderValue(value);
    }, [value]);

    // Calculate dynamic max using existing utils logic
    const dynamicMax = useMemo(() => {
        const calculatedMax = getMaxBudgetForAllUsers(objective, audience, maxUserCount);
        return calculatedMax;
    }, [maxUserCount, objective, audience]);

    // For display - show the constrained value
    const constrainedValue = Math.min(value, dynamicMax || 300);

    // For slider functionality - always ensure it's movable with minimum range
    const sliderMax = Math.max(dynamicMax || 300, MIN + 20); // Ensure at least 20 point range
    
    // For display - show the actual calculated max
    const displayMax = dynamicMax || 300;

    return (
        <StyleWrraper>
            <p className="cardTitle">Daily Budget</p>
            <p className="mutedText">
                Estimated {reachedPerDayCount || 0} impressions per day
            </p>

            <p className="priceText">${Number(constrainedValue)?.toFixed(2)}</p>
            <div className="sliderWrapper">
                <span className="leftSideValue">$5.00</span>
                <div className="flex-1">
                    <Range
                        values={[sliderValue]}
                        step={STEP}
                        min={MIN}
                        max={sliderMax}
                        onChange={(values) => {
                            const newSliderValue = values?.[0];
                            setSliderValue(newSliderValue); // Update internal slider position
                            
                            if (onChange) {
                                // Send constrained value to parent
                                const constrainedNewValue = Math.min(newSliderValue, displayMax);
                                onChange(constrainedNewValue);
                            }
                        }}
                        renderTrack={({ props, children }) => (
                            <div
                                onMouseDown={props?.onMouseDown}
                                onTouchStart={props?.onTouchStart}
                                style={{
                                    ...props?.style,
                                    height: '6px',
                                    display: 'flex',
                                    width: '100%'
                                }}
                            >
                                <div
                                    ref={props?.ref}
                                    style={{
                                        height: '6px',
                                        width: '100%',
                                        borderRadius: '4px',
                                        background: getTrackBackground({
                                            values: [sliderValue],
                                            colors: ['#FF5A5F', '#EEEEEF'],
                                            min: MIN,
                                            max: sliderMax
                                        }),
                                        alignSelf: 'center'
                                    }}
                                >
                                    {children}
                                </div>
                            </div>
                        )}
                        renderThumb={({ props }) => (
                            <div
                                {...props}
                                key={props?.key}
                                style={{
                                    ...props?.style,
                                    height: '12px',
                                    width: '12px',
                                    borderRadius: '100%',
                                    backgroundColor: '#FF5A5F',
                                    outline: 'none',
                                    cursor: 'pointer'
                                }}
                            />
                        )}
                    />
                </div>
                <span className="rightSideValue">${displayMax.toFixed(2)}</span>
            </div>
        </StyleWrraper>
    );
};

export default React.memo(DailyBudget);
