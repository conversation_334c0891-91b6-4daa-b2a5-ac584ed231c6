import styled from 'styled-components';

export const StyleWrraper = styled.div`
	border: 1px solid rgba(213, 213, 213, 1);
	border-radius: 4px;
	background-color: #ffffff;
	padding: 12px 8px;
	.cardTitle {
		color: #2e2e2e;
		font-family: 'nunitosans-bold';
		font-size: 18px;
		line-height: 27px;
	}
	.mutedText {
		color: rgba(32, 34, 36, 0.66);
		font-family: 'nunitosans-semi-bold';
		font-size: 16px;
		line-height: 27px;
		padding-top: 12px;
		text-align: center;
	}
	.priceText {
		color: rgba(255, 90, 95, 1);
		font-family: 'nunitosans-bold';
		font-size: 30px;
		line-height: 1;
		padding-top: 15px;
		padding-bottom: 23px;
		text-align: center;
	}
	.sliderWrapper {
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 13px;
		.leftSideValue,
		.rightSideValue {
			color: rgba(32, 34, 36, 0.66);
			font-family: 'nunitosans-semi-bold';
			font-size: 16px;
			line-height: 27px;
		}
	}
	@media only screen and (max-width: 600px) {
		padding: 8px;
		.cardTitle {
			font-size: 13px;
			line-height: 18px;
		}
		.mutedText {
			font-size: 12px;
			line-height: 20px;
			padding-top: 8px;
		}
		.priceText {
			font-size: 21px;
			padding-top: 10px;
			padding-bottom: 15px;
		}
		.sliderWrapper {
			gap: 9px;
			.leftSideValue,
			.rightSideValue {
				font-size: 12px;
				line-height: 20px;
			}
		}
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		padding: 9px 8px;
		.cardTitle {
			font-size: 14px;
			line-height: 20px;
		}
		.mutedText {
			font-size: 12px;
			line-height: 20px;
			padding-top: 9px;
		}
		.priceText {
			font-size: 23px;
			padding-top: 11px;
			padding-bottom: 17px;
		}
		.sliderWrapper {
			gap: 10px;
			.leftSideValue,
			.rightSideValue {
				font-size: 12px;
				line-height: 20px;
			}
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		padding: 9px 8px;
		.cardTitle {
			font-size: 14px;
			line-height: 20px;
		}
		.mutedText {
			font-size: 12px;
			line-height: 20px;
			padding-top: 9px;
		}
		.priceText {
			font-size: 23px;
			padding-top: 11px;
			padding-bottom: 17px;
		}
		.sliderWrapper {
			gap: 10px;
			.leftSideValue,
			.rightSideValue {
				font-size: 12px;
				line-height: 20px;
			}
		}
	}
`;
