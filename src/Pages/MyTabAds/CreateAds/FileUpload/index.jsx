import { useCallback, useEffect, useRef, useState } from 'react';
import { <PERSON>rop<PERSON> } from 'react-cropper';

import NewModal from '../../../../Components/Common/NewModal';
import { getUniqueFileName } from '../../../../Helper/helper';
import StyleWrapper from './index.style';
import 'cropperjs/dist/cropper.css';

const FileUpload = ({ value, onChange, aspectRatio = NaN, disabled = false, ...props }) => {
    const [customFile, setCustomFile] = useState(null);
    const [tempCustomFile, setTempCustomFile] = useState(null);
    // file = {
    //   url: String or null,
    //   originalFile: File object or null
    // }
    const [isOpenCropModal, setIsOpenCropModal] = useState(false);
    const fileInput = useRef(null);
    const cropperRef = useRef(null);

    const handleUploadButtonClick = () => {
        if (disabled) return;
        fileInput?.current?.click();
    };

    const handleFileInputChange = (event) => {
        if (disabled) return;
        if (event?.target?.files?.length > 0) {
            setTempCustomFile({
                url: URL.createObjectURL(event.target.files[0]),
                originalFile: event.target.files[0]
            });
            setIsOpenCropModal(true);
        }
    };

    const handleCrop = useCallback(() => {
        cropperRef?.current?.cropper?.getCroppedCanvas({
            width: 1080,
            height: 1080,
            imageSmoothingEnabled: true,
            imageSmoothingQuality: 'high'
        })?.toBlob((blob) => {
            const imageFile = new File(
                [blob],
                `${getUniqueFileName(tempCustomFile?.originalFile?.name)}`,
                {
                    type: blob?.type
                }
            );
            const blobUrl = URL.createObjectURL(imageFile);
            if (onChange) {
                onChange({
                    url: blobUrl,
                    originalFile: imageFile,
                    dimensions: { width: 1080, height: 1080 }
                });
            } else {
                setCustomFile({
                    url: blobUrl,
                    originalFile: imageFile,
                    dimensions: { width: 1080, height: 1080 }
                });
            }
            setIsOpenCropModal(false);
        }, tempCustomFile?.originalFile?.type);
    }, [tempCustomFile, onChange]);

    useEffect(() => {
        if (value) {
            setCustomFile(value);
        } else {
            setCustomFile(null);
        }
    }, [value]);
    return (
        <StyleWrapper>
            <input
                type="file"
                ref={fileInput}
                accept="image/*"
                hidden
                multiple={false}
                onChange={handleFileInputChange}
                disabled={disabled}
            />
            <button
                type="button"
                className={`uploadButton ${disabled ? 'cursor-not-allowed' : ''}`}
                onClick={handleUploadButtonClick}
                disabled={disabled}
            >
                <span className="leftSideText">Choose File</span>
                <span className="rightSideText">
                    {customFile?.originalFile?.name
                        ? customFile?.originalFile?.name
                        : 'no file selected'}
                </span>
            </button>
            {isOpenCropModal && (
                <NewModal
                    isOpen={isOpenCropModal}
                    toggle={() => setIsOpenCropModal(false)}
                    className="advertiserProfileImageCropModal"
                    submitButtonText="Crop Image"
                    handleSubmitButtonClick={handleCrop}
                >
                    <div style={{ padding: '16px' }}>
                        <Cropper
                            ref={cropperRef}
                            src={tempCustomFile?.url}
                            autoCropArea={1}
                            rotatable={false}
                            responsive={true}
                            style={{ height: 400, width: '100%' }}
                            aspectRatio={!isNaN(aspectRatio) ? aspectRatio : undefined} // ✅ dynamic constraint
                            guides={true}
                            viewMode={1}
                            checkOrientation={false}
                        />
                    </div>
                </NewModal>
            )}
        </StyleWrapper>
    );
};

export default FileUpload;
