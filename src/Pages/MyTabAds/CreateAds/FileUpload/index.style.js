import styled from 'styled-components';

const StyleWrapper = styled.div`
	display: flex;
	flex-direction: column;
	align-items: center;
	.uploadButton {
		width: 100%;
		background-color: #ffffff;
		border: 0.6px solid #d5d5d5;
		border-radius: 4px;
		height: 34px;
		padding-inline: 8px;
		display: flex;
		align-items: center;
		gap: 16px;
		.leftSideText {
			color: #2e2e2e !important;
			font-family: 'nunitosans-bold' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
		.rightSideText {
			color: #2e2e2e !important;
			font-family: 'nunitosans-medium' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
	}
	@media (max-width: 600px) {
		.uploadButton {
			.leftSideText {
				font-size: 12px !important;
			}
			.rightSideText {
				font-size: 12px !important;
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.uploadButton {
			.leftSideText {
				font-size: 12px !important;
			}
			.rightSideText {
				font-size: 12px !important;
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.uploadButton {
			.leftSideText {
				font-size: 12px !important;
			}
			.rightSideText {
				font-size: 12px !important;
			}
		}
	}
`;

export default StyleWrapper;
