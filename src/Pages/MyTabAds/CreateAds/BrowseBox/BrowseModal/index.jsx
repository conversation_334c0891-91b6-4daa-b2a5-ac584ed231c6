import { useState } from 'react';
import NewModal from '../../../../../Components/Common/NewModal';
import { StylesWrapper } from './index.styles';
import SegmentList from '../SegmentList';
import CustomerList from '../CustomerList';

const BrowseModal = ({
	isOpen,
	closeModal,
	selectedDropdownValue,
	selectedMytabSegments,
	setSelectedMytabSegments,
	selectedVenueCustomers,
	setSelectedVenueCustomers,
	selectedVenueSegments,
	setSelectedVenueSegments
}) => {
	const [currentSelectedMytabSegments, setCurrentSelectedMytabSegments] =
		useState(selectedMytabSegments);
	const [currentSelectedVenueCustomers, setCurrentSelectedVenueCustomers] =
		useState(selectedVenueCustomers);
	const [currentSelectedVenueSegments, setCurrentSelectedVenueSegments] =
		useState(selectedVenueSegments);

	let title = '';
	if (selectedDropdownValue === 'mytab_customer_segments') {
		title = 'MyTab customer segments';
	} else if (selectedDropdownValue === 'your_venues_customers') {
		title = 'Your venue’s customers';
	} else {
		title = 'Your venue’s customer segments';
	}

	const handleSave = () => {
		if (selectedDropdownValue === 'mytab_customer_segments') {
			setSelectedMytabSegments(currentSelectedMytabSegments);
		} else if (selectedDropdownValue === 'your_venues_customers') {
			setSelectedVenueCustomers(currentSelectedVenueCustomers);
		} else {
			setSelectedVenueSegments(currentSelectedVenueSegments);
		}
		closeModal();
	};

	return (
		<NewModal
			isOpen={isOpen}
			toggle={closeModal}
			title={title}
			className={'browseModal'}
			submitButtonText="Save"
			cancelButtonText="Discard"
			handleSubmitButtonClick={handleSave} // Ensure function is executed on Save
		>
			<StylesWrapper>
				{selectedDropdownValue === 'mytab_customer_segments' && (
					<SegmentList
						type="mytab"
						currentSelectedSegments={currentSelectedMytabSegments}
						setCurrentSelectedSegments={
							setCurrentSelectedMytabSegments
						}
					/>
				)}
				{selectedDropdownValue === 'your_venues_customers' && (
					<CustomerList
						currentSelectedCustomers={currentSelectedVenueCustomers}
						setCurrentSelectedCustomers={
							setCurrentSelectedVenueCustomers
						}
					/>
				)}
				{selectedDropdownValue === 'your_venues_customers_segment' && (
					<SegmentList
						type="venue"
						currentSelectedSegments={currentSelectedVenueSegments}
						setCurrentSelectedSegments={
							setCurrentSelectedVenueSegments
						}
					/>
				)}
			</StylesWrapper>
		</NewModal>
	);
};

export default BrowseModal;
