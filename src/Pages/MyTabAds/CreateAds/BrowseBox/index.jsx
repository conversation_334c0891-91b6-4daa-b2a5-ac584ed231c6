import { useState } from 'react';
import NewFormInput from '../../../../Components/NewForm/NewFormInput';
import { SearchIcon } from '../../../../Components/Icons/SearchIcon/SearchIcon';
import { CircleCancelIcon } from '../../../../Components/Icons/Cancel/CircleCancelIcon';
import BrowseModal from './BrowseModal';
import { StyleWrraper } from './index.style';

const BrowseBox = ({
	selectedDropdownValue,
	selectedMytabSegments,
	setSelectedMytabSegments,
	selectedVenueCustomers,
	setSelectedVenueCustomers,
	selectedVenueSegments,
	setSelectedVenueSegments
}) => {
	const [isOpenBrowseModal, setIsOpenBrowseModal] = useState(false);

	const handleRemoveItem = (id) => {
		if (selectedDropdownValue === 'mytab_customer_segments') {
			if (selectedMytabSegments?.length > 0) {
				setSelectedMytabSegments(
					selectedMytabSegments?.filter((item) => item?.id != id)
				);
			}
		}
		if (selectedDropdownValue === 'your_venues_customers') {
			if (selectedVenueCustomers?.length > 0) {
				setSelectedVenueCustomers(
					selectedVenueCustomers?.filter((item) => item?.id != id)
				);
			}
		}
		if (selectedDropdownValue === 'your_venues_customers_segment') {
			if (selectedVenueSegments?.length > 0) {
				setSelectedVenueSegments(
					selectedVenueSegments?.filter((item) => item?.id != id)
				);
			}
		}
	};
	return (
		<>
			<div className="customFormContainer">
				<NewFormInput
					prefix={<SearchIcon width="16px" height="16px" />}
					placeholder={'Search'}
					wrapperClassName={
						'newFormInputWrapper browseBoxSearchInput'
					}
					readOnly
					onClick={() => setIsOpenBrowseModal(true)}
				/>
			</div>
			<StyleWrraper>
				{selectedDropdownValue === 'mytab_customer_segments' &&
					selectedMytabSegments?.length > 0 && (
						<div className="selectedItemWrapper">
							{selectedMytabSegments?.map((item) => {
								return (
									<div
										key={item?.id}
										className="selectedItem"
									>
										<span className="selectedItemLabel">
											{item?.name}
										</span>
										<div
											className="removeIconWrapper"
											onClick={() =>
												handleRemoveItem(item?.id)
											}
										>
											<div className="removeIcon">
												<CircleCancelIcon
													width={'100%'}
													height={'100%'}
												/>
											</div>
										</div>
									</div>
								);
							})}
						</div>
					)}
				{selectedDropdownValue === 'your_venues_customers' &&
					selectedVenueCustomers?.length > 0 && (
						<div className="selectedItemWrapper">
							{selectedVenueCustomers?.map((item) => {
								return (
									<div
										key={item?.userID}
										className="selectedItem"
									>
										<span className="selectedItemLabel">
											{item?.fullName}
										</span>
										<div
											className="removeIconWrapper"
											onClick={() =>
												handleRemoveItem(item?.userID)
											}
										>
											<div className="removeIcon">
												<CircleCancelIcon
													width={'100%'}
													height={'100%'}
												/>
											</div>
										</div>
									</div>
								);
							})}
						</div>
					)}
				{selectedDropdownValue === 'your_venues_customers_segment' &&
					selectedVenueSegments?.length > 0 && (
						<div className="selectedItemWrapper">
							{selectedVenueSegments?.map((item) => {
								return (
									<div
										key={item?.id}
										className="selectedItem"
									>
										<span className="selectedItemLabel">
											{item?.name}
										</span>
										<div
											className="removeIconWrapper"
											onClick={() =>
												handleRemoveItem(item?.id)
											}
										>
											<div className="removeIcon">
												<CircleCancelIcon
													width={'100%'}
													height={'100%'}
												/>
											</div>
										</div>
									</div>
								);
							})}
						</div>
					)}
			</StyleWrraper>
			{isOpenBrowseModal && (
				<BrowseModal
					isOpen={isOpenBrowseModal}
					closeModal={() => setIsOpenBrowseModal(false)}
					selectedDropdownValue={selectedDropdownValue}
					selectedMytabSegments={selectedMytabSegments}
					setSelectedMytabSegments={setSelectedMytabSegments}
					selectedVenueCustomers={selectedVenueCustomers}
					setSelectedVenueCustomers={setSelectedVenueCustomers}
					selectedVenueSegments={selectedVenueSegments}
					setSelectedVenueSegments={setSelectedVenueSegments}
				/>
			)}
		</>
	);
};

export default BrowseBox;
