import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';

import { StyleWrraper } from './index.style';
import Api from '../../../../../Helper/Api';
import { VenueApiRoutes } from '../../../../../Utils/routes';
import { useDebounce } from '../../../../../Hooks/useDebounce';
import { SearchIcon } from '../../../../../Components/Icons/SearchIcon/SearchIcon';
import NewFormInput from '../../../../../Components/NewForm/NewFormInput';
import { TableCountSkeleton } from './Skeleton';
import CustomersTable from './CustomersTable';

const CustomerList = ({
	currentSelectedCustomers,
	setCurrentSelectedCustomers
}) => {
	const state = useSelector((state) => ({ ...state }));
	const [loading, setLoading] = useState(false);
	const [tableData, setTableData] = useState([]);
	const [tableDataCount, setTableDataCount] = useState(null);
	const [searchTerm, setSearchTerm] = useState('');
	const debounceSearchTerm = useDebounce(searchTerm, 500);

	const authDetails = state?.auth;
	const selectedVenue = authDetails?.selectedVenue;

	const getTableData = async (payload) => {
		try {
			setLoading(true);
			const res = await Api(
				'POST',
				VenueApiRoutes.getCustomerList,
				payload
			);
			if (res?.data?.status) {
				setTableData(res?.data?.data?.list);
				setTableDataCount(res?.data?.data?.list?.length);
			} else {
				toast.error(res?.data?.message);
			}
			setLoading(false);
		} catch (err) {
			setLoading(false);
			if (err?.message) toast.error(err?.message);
		}
	};
	const handleSearchInputChange = async (value) => {
		setSearchTerm(value);
	};

	useEffect(() => {
		(async () => {
			await getTableData({
				bar_id: selectedVenue?.id,
				search: debounceSearchTerm,
				page: 1,
				sortBy: 'newToOld',
				isDiscount: true
			});
		})();
	}, [debounceSearchTerm, selectedVenue?.id]);
	return (
		<StyleWrraper>
			{loading ? (
				<TableCountSkeleton />
			) : (
				<div className="tableCount">
					<div className="textOne">{tableDataCount} customers</div>
					<div className="textTwo">
						Showing {tableDataCount} of {tableDataCount} customers
					</div>
				</div>
			)}
			<div className="searchBox ma-t-4">
				<NewFormInput
					prefix={
						<div className="inputSearchIconWrapper">
							<div className="inputSearchIcon">
								<SearchIcon width="100%" height="100%" />
							</div>
						</div>
					}
					placeholder="Search"
					onChange={(event) => {
						handleSearchInputChange(event?.target?.value);
					}}
					wrapperClassName={'newFormInputWrapper'}
				/>
			</div>
			<div className="ma-t-4">
				<CustomersTable
					loading={loading}
					tableData={tableData}
					tableDataCount={tableDataCount}
					currentSelectedCustomers={currentSelectedCustomers}
					setCurrentSelectedCustomers={setCurrentSelectedCustomers}
				/>
			</div>
		</StyleWrraper>
	);
};

export default CustomerList;
