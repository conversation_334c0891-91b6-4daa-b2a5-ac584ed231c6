import styled from 'styled-components';

export const StyleWrraper = styled.div`
	min-height: 463px;
	height: 463px;
	max-height: 463px;
	width: 100%;
	border: 1px solid #d5d5d5;
	border-radius: 4px;
	font-family: 'nunitosans-regular' !important;
	background: #fbfcff !important;
	.tableContainer {
		width: 100%;
		height: 100%;
		overflow: auto;
	}
	.ReactTable {
		border: none;
		border-radius: 0px 0px 4px 4px;
		font-family: 'nunitosans-regular' !important;
		height: 100%;
		width: 100%;
		/* min-width: 650px; */
		min-width: 100%;
		overflow: hidden;
		position: relative !important;
		.rt-table {
			font-family: 'nunitosans-regular' !important;
			overflow: auto;
		}
		.rt-thead.-header {
			background-color: #fff;
			box-shadow: none !important;
			position: sticky;
			top: 0;
			display: ${(props) => (props.hideHeader ? 'none' : '')};
		}
		.rt-thead {
			.rt-tr {
				.rt-th {
					min-width: 50px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					height: 33px;
					padding: 8px 10px !important;
					background-color: #f5f6fa !important;
					border: none;

					font-size: 14px !important;
					font-family: 'nunitosans-bold' !important;
					color: #202224 !important;
					display: flex;
					justify-content: center;
					align-items: center;
					div {
						font-size: 14px !important;
						font-family: 'nunitosans-bold' !important;
						color: #202224 !important;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}
			}
			.rt-tr > div:first-child {
				width: 50px !important;
				min-width: 50px !important;
				max-width: 50px !important;
			}
		}
		.rt-tbody {
			overflow-y: initial;
			overflow-x: hidden;
			border-top: none;
			border-bottom: none;
			&::-webkit-scrollbar {
				width: 4px !important;
			}
			&::-webkit-scrollbar-thumb {
				background-color: rgba(151, 151, 151, 0.31) !important;
				border-radius: 4px !important;
			}
			.rt-tr-group {
				border: none;
				flex: none;
				.rt-tr {
					border-bottom: 1px solid rgba(148, 150, 152, 0.5);
					.rt-td {
						min-width: 50px;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;

						height: 33px;
						padding: 6px 10px !important;
						border: none;
						border-radius: 0px;

						display: flex;
						justify-content: center;
						align-items: center;
					}
					.nonParentRowText {
						font-size: 14px !important;
						font-family: 'nunitosans-semi-bold' !important;
						color: #2e2e2e !important;
					}
					.parentRowText {
						font-size: 14px !important;
						font-family: 'nunitosans-bold' !important;
						color: #202224 !important;
					}
				}
				.rt-tr > div:first-child {
					width: 50px !important;
					min-width: 50px !important;
					max-width: 50px !important;
				}
			}
		}
	}
	.rtNoDataFound {
		font-family: 'nunitosans-regular' !important;
		font-size: 12px;
		color: #2e2e2e;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}
	@media (max-width: 600px) {
		.ReactTable {
			min-width: 100%;
			.rt-thead {
				.rt-tr {
					.rt-th {
						height: 29px !important;
						font-size: 11px !important;
						div {
							font-size: 11px !important;
						}
					}
				}
				.rt-tr > div:first-child {
					width: 40px !important;
					min-width: 40px !important;
					max-width: 40px !important;
				}
			}
			.rt-tbody {
				.rt-tr-group {
					.rt-tr {
						.rt-td {
							height: 29px !important;
						}
						.nonParentRowText {
							font-size: 12px !important;
						}
						.parentRowText {
							font-size: 11px !important;
						}
					}
					.rt-tr > div:first-child {
						width: 40px !important;
						min-width: 40px !important;
						max-width: 40px !important;
					}
				}
			}
		}
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.ReactTable {
			min-width: 100%;
			.rt-thead {
				.rt-tr {
					.rt-th {
						height: 29px !important;
						font-size: 11px !important;
						div {
							font-size: 11px !important;
						}
					}
				}
				.rt-tr > div:first-child {
					width: 40px !important;
					min-width: 40px !important;
					max-width: 40px !important;
				}
			}
			.rt-tbody {
				.rt-tr-group {
					.rt-tr {
						.rt-td {
							height: 29px !important;
						}
						.nonParentRowText {
							font-size: 12px !important;
						}
						.parentRowText {
							font-size: 11px !important;
						}
					}
					.rt-tr > div:first-child {
						width: 40px !important;
						min-width: 40px !important;
						max-width: 40px !important;
					}
				}
			}
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.ReactTable {
			min-width: 100%;
			.rt-thead {
				.rt-tr {
					.rt-th {
						height: 29px !important;
						font-size: 11px !important;
						div {
							font-size: 11px !important;
						}
					}
				}
				.rt-tr > div:first-child {
					width: 40px !important;
					min-width: 40px !important;
					max-width: 40px !important;
				}
			}
			.rt-tbody {
				.rt-tr-group {
					.rt-tr {
						.rt-td {
							height: 29px !important;
						}
						.nonParentRowText {
							font-size: 12px !important;
						}
						.parentRowText {
							font-size: 11px !important;
						}
					}
					.rt-tr > div:first-child {
						width: 40px !important;
						min-width: 40px !important;
						max-width: 40px !important;
					}
				}
			}
		}
	}
`;
