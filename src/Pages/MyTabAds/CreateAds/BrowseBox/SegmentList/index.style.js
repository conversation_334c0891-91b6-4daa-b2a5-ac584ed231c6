import styled from 'styled-components';

export const StyleWrraper = styled.div`
	.tableCount {
		border: 1px solid #d5d5d5;
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-inline: 12px;
		height: 42px;
		background: #fbfcff !important;
		.textOne {
			color: #202224d9;
			font-family: 'nunitosans-bold';
			font-size: 14px;
		}
		.textTwo {
			color: #20222466;
			font-family: 'nunitosans-regular';
			font-size: 14px;
		}
	}
	.searchBox {
		.newCustomInputContainer {
			gap: 14px;
			border: 1px solid #d5d5d5;
			border-radius: 4px;
			height: 34px;
			padding-inline: 12px;
			background: #fbfcff !important;
		}
		.newCustomInput {
			background: #fbfcff !important;
			color: #2e2e2e !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 14px !important;
			line-height: 1 !important;
			&::placeholder {
				color: #979797 !important;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 14px !important;
				line-height: 1 !important;
			}
		}
		.inputSearchIconWrapper {
			display: flex;
			justify-content: center;
			align-items: center;
			.inputSearchIcon {
				width: 16px;
				height: 16px;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}
	@media (max-width: 600px) {
		.tableCount {
			height: 37px;
			.textOne {
				font-size: 12px;
			}
			.textTwo {
				font-size: 12px;
			}
		}
		.searchBox {
			.newCustomInputContainer {
				gap: 12px;
				height: 30px;
			}
			.newCustomInput {
				font-size: 12px !important;
				&::placeholder {
					font-size: 12px !important;
				}
			}
			.inputSearchIconWrapper {
				.inputSearchIcon {
					width: 12px;
					height: 12px;
				}
			}
		}
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.tableCount {
			height: 37px;
			.textOne {
				font-size: 12px;
			}
			.textTwo {
				font-size: 12px;
			}
		}
		.searchBox {
			.newCustomInputContainer {
				gap: 12px;
				height: 30px;
			}
			.newCustomInput {
				font-size: 12px !important;
				&::placeholder {
					font-size: 12px !important;
				}
			}
			.inputSearchIconWrapper {
				.inputSearchIcon {
					width: 12px;
					height: 12px;
				}
			}
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.tableCount {
			height: 37px;
			.textOne {
				font-size: 12px;
			}
			.textTwo {
				font-size: 12px;
			}
		}
		.searchBox {
			.newCustomInputContainer {
				gap: 12px;
				height: 30px;
			}
			.newCustomInput {
				font-size: 12px !important;
				&::placeholder {
					font-size: 12px !important;
				}
			}
			.inputSearchIconWrapper {
				.inputSearchIcon {
					width: 12px;
					height: 12px;
				}
			}
		}
	}
`;
