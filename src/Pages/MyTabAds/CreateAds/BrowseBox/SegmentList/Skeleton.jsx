import ReactTable from 'react-table-6';
import 'react-table-6/react-table.css';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

const tableColumns = [
	{
		id: 'select',
		Header: '',
		sortable: false,
		minWidth: 50,
		maxWidth: 50,
		Cell: () => {
			return (
				<div
					style={{ height: '100%', width: '100%' }}
					className="d-flex justify-content-center align-items-center"
				>
					<div
						style={{
							height: '22px',
							width: '22px',
							marginBottom: '5px'
						}}
					>
						<Skeleton height="100%" width={'100%'} />
					</div>
				</div>
			);
		}
	},
	{
		Header: 'Segment Name',
		accessor: 'name',
		className: 'justify-content-start',
		// minWidth: 450,
		// maxWidth: 600,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Number of customers',
		accessor: 'customerCount',
		className: 'justify-content-end',
		minWidth: 150,
		maxWidth: 190,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-end',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	}
];

const data = Array(20).fill({});

export const TableSkeleton = () => {
	return (
		<div className="tableContainer">
			<ReactTable
				columns={tableColumns}
				data={data}
				showPagination={false}
				resizable={false}
				pageSize={20}
				getTrProps={(state, row) => {
					if (row?.index == data?.length - 1) {
						return {
							style: {
								borderBottom: 'none'
							}
						};
					}
					return {};
				}}
			/>
		</div>
	);
};

export const TableCountSkeleton = () => {
	return (
		<div className="tableCount">
			<div className="leftText">
				<Skeleton height="100%" width={'74px'} />
			</div>
			<div className="rightText">
				<Skeleton height="100%" width={'157px'} />
			</div>
		</div>
	);
};
