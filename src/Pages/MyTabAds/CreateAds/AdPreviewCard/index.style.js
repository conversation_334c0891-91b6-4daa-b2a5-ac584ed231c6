import styled from 'styled-components';

export const StyleWrraper = styled.div`
	border: 1px solid #d5d5d5;
	background-color: #ffffff;
	border-radius: 4px;
	.previewCardHeader {
		padding: 16px 24px;
		display: flex;
		align-items: center;
		gap: 11px;
		.profileImage {
			width: 61px;
			height: 61px;
			background-color: #f1f5f9;
			border-radius: 100%;
			img {
				display: block;
				width: 100%;
				height: 100%;
				border-radius: 100%;
				object-fit: cover;
			}
		}
		.advertiserName {
			color: #2e2e2e;
			font-family: 'nunitosans-bold';
			font-size: 18px;
			line-height: 27px;
		}
		.advertiserAddress {
			color: #6b727f;
			font-family: 'nunitosans-regular';
			font-size: 18px;
			line-height: 27px;
		}
	}
	.previewCardBody {
		height: 352px;
		background-color: #f1f5f9;
		img {
			display: block;
			width: 100%;
			height: 100%;
			object-fit: contain; /* Default to contain to prevent stretching */
		}
	}
	.fitToggle {
		text-align: center;
		padding: 5px 0;
		background-color: #f1f5f9;
		border-top: 1px solid #e2e8f0;
		
		button {
			background: transparent;
			border: none;
			padding: 5px 10px;
			font-size: 12px;
			color: #4a5568;
			cursor: pointer;
			border-radius: 4px;
			
			&.active {
				background-color: #e2e8f0;
				font-weight: bold;
			}
			
			&:hover {
				background-color: #e2e8f0;
			}
		}
	}
	.previewCardFooter {
		padding: 24px;
		.adTitle {
			color: #2e2e2e;
			font-family: 'nunitosans-bold';
			font-size: 26px;
			line-height: 27px;
		}
		.adDescription {
			color: #2e2e2e;
			font-family: 'nunitosans-semi-bold';
			font-size: 18px;
			line-height: 27px;
			padding-top: 14px;
            word-break: break-all;
		}
		.actionButton {
			width: 160px;
			height: 45px;
			border: 1px solid rgba(32, 34, 36, 0.2);
			background-color: #ffffff;
			border-radius: 5px;
			color: #202224;
			font-family: 'nunitosans-bold';
			font-size: 16px;
			line-height: 1;
			margin-top: 30px;
		}
	}
	@media only screen and (max-width: 600px) {
		.previewCardHeader {
			padding: 11px 16px;
			gap: 7px;
			.profileImage {
				width: 41px;
				height: 41px;
			}
			.advertiserName {
				font-size: 13px;
				line-height: 18px;
			}
			.advertiserAddress {
				font-size: 13px;
				line-height: 18px;
			}
		}
		.previewCardBody {
			height: 238px;
			img {
				object-fit: contain;
			}
		}
		.previewCardFooter {
			padding: 16px;
			.adTitle {
				font-size: 18px;
				line-height: 18px;
			}
			.adDescription {
				font-size: 13px;
				line-height: 18px;
				padding-top: 10px;
                word-break: break-all;
			}
			.actionButton {
				width: 108px;
				height: 31px;
				font-size: 11px;
				margin-top: 21px;
			}
		}
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.previewCardHeader {
			padding: 12px 18px;
			gap: 8px;
			.profileImage {
				width: 46px;
				height: 46px;
			}
			.advertiserName {
				font-size: 14px;
				line-height: 20px;
			}
			.advertiserAddress {
				font-size: 14px;
				line-height: 20px;
			}
		}
		.previewCardBody {
			height: 264px;
			img {
				object-fit: contain;
			}
		}
		.previewCardFooter {
			padding: 18px;
			.adTitle {
				font-size: 20px;
				line-height: 20px;
			}
			.adDescription {
				font-size: 14px;
				line-height: 20px;
				padding-top: 11px;
                word-break: break-all;
			}
			.actionButton {
				width: 120px;
				height: 34px;
				font-size: 12px;
				margin-top: 23px;
			}
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.previewCardHeader {
			padding: 12px 18px;
			gap: 8px;
			.profileImage {
				width: 46px;
				height: 46px;
			}
			.advertiserName {
				font-size: 14px;
				line-height: 20px;
			}
			.advertiserAddress {
				font-size: 14px;
				line-height: 20px;
			}
		}
		.previewCardBody {
			height: 264px;
			img {
				object-fit: contain;
			}
		}
		.previewCardFooter {
			padding: 18px;
			.adTitle {
				font-size: 20px;
				line-height: 20px;
			}
			.adDescription {
				font-size: 14px;
				line-height: 20px;
				padding-top: 11px;
                word-break: break-all;
			}
			.actionButton {
				width: 120px;
				height: 34px;
				font-size: 12px;
				margin-top: 23px;
			}
		}
	}
`;
