import { useSelector } from 'react-redux';
import { useState } from 'react';
import { StyleWrraper } from './index.style';
import { defaultCity, defaultSuburb, australiaStateOptions } from '../../utils';

const AdPreviewCard = ({
	state,
	city,
	suburb,
	adImage,
	title,
	description,
	actionButtonText
}) => {
	const authData = useSelector((state) => ({ ...state.auth }));
	const [useContainFit, setUseContainFit] = useState(true); // Default to contain

	// Get default state label for WA
	const defaultStateLabel = australiaStateOptions.find(option => option.default)?.value || 'WA';

	return (
		<StyleWrraper>
			<div className="previewCardHeader">
				<div className="profileImage">
					<img
						src={
							authData?.login_type === 'advertiser'
								? authData?.profile_image
								: authData?.selectedVenue?.avatar
						}
						alt="profile-image"
					/>
				</div>
				<div>
					<p className="advertiserName">
						{authData?.login_type === 'advertiser'
							? authData?.business_name
							: authData?.selectedVenue?.restaurantName}{' '}
					</p>
					<p className="advertiserAddress">
						{city && state 
							? `${city}, ${state}`
							: `${defaultSuburb}, ${defaultCity}, ${defaultStateLabel}`
						}
					</p>
				</div>
			</div>
			{adImage && (
				<div className="previewCardBody">
					<img 
						src={adImage} 
						alt="ad-image" 
						style={{ objectFit: 'contain' }} // Always use contain
					/>
				</div>
			)}
			<div className="previewCardFooter">
				{title && <p className="adTitle">{title}</p>}
				{description && <p className="adDescription">{description}</p>}
				{actionButtonText && (
					<button className="actionButton">{actionButtonText}</button>
				)}
			</div>
		</StyleWrraper>
	);
};

export default AdPreviewCard;
