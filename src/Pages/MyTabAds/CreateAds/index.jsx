import { useCallback, useEffect, useMemo, useState } from 'react';
import { useStripe, useElements, CardNumberElement, CardExpiryElement, CardCvcElement } from '@stripe/react-stripe-js';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import moment from 'moment';
import useDevice from '../../../Hooks/useDevice';
import { convertLocalToUTC } from '../../../Utils/dateTimeUtils';

import NewFormLabel from './NewFormLabel/index';
import NewFormInput from '../../../Components/NewForm/NewFormInput';
import NewFormErrorMessage from '../../../Components/NewForm/NewFormErrorMessage';
import NewFormSelect from '../../../Components/NewForm/NewFormSelect';
import NewFormTextarea from '../../../Components/NewForm/NewFormTextarea';
import NewFormCheckbox from '../../../Components/NewForm/NewFormCheckbox';
import NewDatePicker from '../NewDatePicker';
import NewTimePicker from '../../../Components/Common/NewTimePicker';
import { FilledButton } from '../../../Components/Layout/Buttons';
import { createAdFormSchema } from '../validationSchema';
import AdPreviewCard from './AdPreviewCard';
import DailyBudget from './DailyBudget';
import FileUpload from './FileUpload';
import Api from '../../../Helper/Api';
import BrowseBox from './BrowseBox';
import { NewFormSelectStylesWrapper, StyleWrraper } from './index.style';
import {
    adObjectiveOptions,
    advertiserAudienceOptions,
    australiaStateOptions,
    callToActionOptions,
    formatCardExpiry,
    venueAudienceOptions, getEstimatedImpressionsPerDay,
    getEstimatedReachedPerDay
} from '../utils';
// import { CommonApiRoutes } from '../../../Utils/routes';
import { stripePublicKey } from '../../../Helper/constant';
import stripeImg from '../../../Assets/images/stripe_new.svg';
import { useApiRoutes } from '../../../Hooks/useApiRoutes';
import { debounce } from 'lodash';

const allowedFileTypes = ['image/jpg', 'image/jpeg', 'image/png'];
const FILE_SIZE = 20 * 1024 * 1024;

const CreateAds = ({ handleCancel, onSuccessfulSubmit, renewalData, isRenewalMode }) => {
    const authData = useSelector((state) => ({ ...state.auth }));
    const stripe = useStripe();
    const apiRoutes = useApiRoutes();
    const elements = useElements();
    const { isTabletOrMobile } = useDevice();
    const CLIENT_IDS = [25, 14, 15];
    const isClient = CLIENT_IDS.includes(authData?.id);
    const isAdsUser = authData?.login_type === 'advertiser';
    const shouldSkipPayment = isAdsUser || isClient;
    // Add a function to get the appropriate height based on device
    const getStripeElementHeight = () => {
        return isTabletOrMobile ? '34px' : '34px';
    };

    const [isSpecificWidthRange, setIsSpecificWidthRange] = useState(
        window.innerWidth >= 1234 && window.innerWidth <= 1314
    );

    const [submitFormLoading, setSubmitFormLoading] = useState(false);
    const [campaignList, setCampaignList] = useState([]);
    const [campaignListLoading, setCampaignListLoading] = useState([]);
    const [totalAdBudget, setTotalAdBudget] = useState(null);
    const [selectedMytabSegments, setSelectedMytabSegments] = useState([]);
    const [selectedVenueCustomers, setSelectedVenueCustomers] = useState([]);
    const [selectedVenueSegments, setSelectedVenueSegments] = useState([]);
    const [isShowAdPreview, setIsShowAdPreview] = useState(false);
    const [savedCard, setSavedCard] = useState(null);
    const [isEditingCard, setIsEditingCard] = useState(false);
    const [defaultStateValue, setDefaultStateValue] = useState('WA');
    const [defaultCityValue, setDefaultCityValue] = useState('');
    const [defaultSuburbValue, setDefaultSuburbValue] = useState('');
    const defaultObjective = adObjectiveOptions.find(option => option.default)?.value || 'CPM';
    const [defaultAllMytabCustomersCount, setDefaultAllMytabCustomersCount] = useState(22000);


    useEffect(() => {
        const handleResize = () => {
            setIsSpecificWidthRange(window.innerWidth >= 1234 && window.innerWidth <= 1314);
        };

        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);

    useEffect(() => {
        getCampaignList();
    }, []);


    const stripeElementOptions = {
        style: {
            base: {
                '::placeholder': {
                    color: '#979797',
                },
            },
        },
    };

    // For the Name on card input
    const nameOnCardStyle = {
        '::placeholder': {
            color: '#979797',
        }
    };

    // For all Stripe elements with proper placeholder styling
    const ELEMENT_OPTIONS = {
        style: {
            base: {
                fontSize: isTabletOrMobile || isSpecificWidthRange ||
                    (window.innerWidth >= 1299 && window.innerWidth <= 1824 && window.innerHeight <= 900)
                    ? '12px' : '16px',
                color: 'rgba(32, 34, 36, 1)',
                fontFamily: 'Nunito Sans, sans-serif',
                fontWeight: isTabletOrMobile || isSpecificWidthRange ||
                    (window.innerWidth >= 1299 && window.innerWidth <= 1824 && window.innerHeight <= 900)
                    ? '500' : '500',
                '::placeholder': {
                    color: '#979797',
                    fontFamily: 'Nunito Sans, sans-serif ',
                    fontWeight: isTabletOrMobile || isSpecificWidthRange ||
                        (window.innerWidth >= 1299 && window.innerWidth <= 1824 && window.innerHeight <= 900)
                        ? '500' : '500',
                    fontSize: isTabletOrMobile || isSpecificWidthRange ||
                        (window.innerWidth >= 1299 && window.innerWidth <= 1824 && window.innerHeight <= 900)
                        ? '12px' : '15px',
                    fontStyle: 'normal',
                },
                // Add focus styles to override browser defaults
                ':focus': {
                    outline: 'none',
                    boxShadow: 'none',
                    borderColor: 'transparent'
                }
            },
            invalid: {
                color: '#fa755a',
                iconColor: '#fa755a'
            },
            empty: {
                color: '#979797',
                fontFamily: 'Nunito Sans, sans-serif',
                fontWeight: '500',
                fontSize: isTabletOrMobile || isSpecificWidthRange ||
                    (window.innerWidth >= 1299 && window.innerWidth <= 1824 && window.innerHeight <= 900)
                    ? '12px' : '15px',
            }
        }
    };

    // Create specific options for each element if needed
    const cardNumberOptions = {
        ...ELEMENT_OPTIONS,
        placeholder: 'Card details'
    };

    const cardExpiryOptions = {
        ...ELEMENT_OPTIONS,
        placeholder: 'MM / YY'
    };

    const cardCvcOptions = {
        ...ELEMENT_OPTIONS,
        placeholder: 'CVV'
    };

    useEffect(() => {
        const getSavedCards = async () => {
            try {
                const payload = {
                    created_by_id: authData?.id,
                    created_by_type: authData?.login_type,
                };

                const res = await Api('POST', apiRoutes.getCardList, payload);

                if (res?.data?.status && res?.data?.data?.cards?.length > 0) {
                    setSavedCard(res.data.data.cards[0]); // Assuming you want to display the first saved card
                } else {
                    setSavedCard(null);
                }
            } catch (err) {
                setSavedCard(null);
            }
        };

        if (authData?.id && authData?.login_type) {
            getSavedCards();
        }
    }, [authData?.id, authData?.login_type]);


    const submitFormHandler = async (values) => {
        setSubmitFormLoading(true);
        try {
            let paymentMethodId = null;

            // Only process payment if not ads user and not client
            if (!shouldSkipPayment) {
                if (stripe && elements) {
                    if (savedCard) {
                        paymentMethodId = savedCard.id;
                    } else {
                        const cardElement = elements.getElement(CardNumberElement);
                        const result = await stripe.createPaymentMethod({
                            type: 'card',
                            card: cardElement,
                            billing_details: {
                                name: values.card_name
                            }
                        });
                        if (result.error) throw new Error(result.error.message);
                        paymentMethodId = result.paymentMethod.id;
                    }
                }
            }

            const timezone = authData?.timezone_value || authData.statisticsData;

            // Convert local dates to UTC for storage
            const startDateUTC = convertLocalToUTC(values.start_date, '00:00', timezone);
            const endDateUTC = convertLocalToUTC(values.end_date, '23:59', timezone);

            const payload = {
                campaign_id: values?.campaign_id,
                objective: values?.objective,
                ad_title: values?.ad_title,
                ad_description: values?.ad_description,
                media_url: values?.media_url?.originalFile
                    ? values?.media_url?.originalFile
                    : values?.media_url?.url,
                call_to_action: values?.call_to_action,
                call_to_action_url: values?.call_to_action_url,
                state: values?.state,
                city: values?.city,
                eligibility_type: values?.eligibility_type,
                combined_eligibility: values?.combined_eligibility === "0" ? false : true,
                start_date: startDateUTC.date,
                start_time: startDateUTC.time,
                end_date: endDateUTC.date,
                end_time: endDateUTC.time,
                created_by_type: authData?.login_type,
                created_by_id: authData?.id,
            };
            // Only add payment-related fields if the email is not the special one
            if (!shouldSkipPayment) {
                payload.daily_budget = totalAdBudget;
                payload.payment_method_id = paymentMethodId;
                payload.save_card = values.save_card || false;
            } else {
                payload.daily_budget = 0;
            }

            // Add renewal parameters if in renewal mode
            if (isRenewalMode && renewalData) {
                payload.renew_flag = true;
                payload.id = renewalData.id;
            }

            if (authData?.login_type === 'venue') payload.bar_id = authData?.selectedVenue?.id;
            if (values?.eligibility_type === 'mytab_customer_segments' && selectedMytabSegments.length > 0)
                payload.segment_ids = selectedMytabSegments.map((item) => item.id);
            if (values?.eligibility_type === 'your_venues_customers' && selectedVenueCustomers.length > 0)
                payload.user_ids = selectedVenueCustomers.map((item) => item.userID);
            if (values?.eligibility_type === 'your_venues_customers_segment' && selectedVenueSegments.length > 0)
                payload.segment_ids = selectedVenueSegments.map((item) => item.id);

            const formData = new FormData();
            for (let key in payload) {
                if (Array.isArray(payload[key])) {
                    payload[key].forEach(item => formData.append(`${key}[]`, item));
                } else {
                    formData.append(key, payload[key]);
                }
            }
            formData.append('type', authData?.login_type);
            // console.log(payload, "payload")
            const res = await Api('POST', apiRoutes.create_ad_payment, formData);
            if (res?.data?.status) {
                toast.success(res?.data?.message);

                // Call the parent's success handler which will clear renewal data and switch tabs
                if (onSuccessfulSubmit) {
                    onSuccessfulSubmit();
                }
            }
            else toast.error(res?.data?.message);
        } catch (err) {
            toast.error(err?.message || 'Payment failed');
        } finally {
            setSubmitFormLoading(false);
        }
    };
    const getInitialValues = () => {
        if (isRenewalMode && renewalData) {
            return {
                campaign_id: renewalData.campaign_id || '',
                campaign_title: renewalData.campaign_title || '', // Store campaign title
                objective: renewalData.objective || defaultObjective,
                ad_title: renewalData.ad_title || '',
                ad_description: renewalData.ad_description || '',
                media_url: renewalData.media_url ? { url: renewalData.media_url } : null,
                call_to_action: renewalData.call_to_action || '',
                call_to_action_url: renewalData.call_to_action_url || '',
                state: renewalData.state || defaultStateValue,
                city: renewalData.city || defaultCityValue,
                suburb: renewalData.suburb || defaultSuburbValue,
                eligibility_type: renewalData.eligibility_type || '',
                combined_eligibility: renewalData.combined_eligibility || false,
                start_date: renewalData.start_date ? new Date(renewalData.start_date) : '',
                end_date: '',
                daily_budget: 5, // Cap at 300
                card_name: '',
                card_number: '',
                card_expiry: '',
                card_cvv: '',
                save_card: false
            };
        }
        return {
            campaign_id: '',
            campaign_title: '',
            objective: defaultObjective,
            ad_title: '',
            ad_description: '',
            media_url: null,
            call_to_action: '',
            call_to_action_url: '',
            state: defaultStateValue,
            city: defaultCityValue,
            suburb: defaultSuburbValue,
            eligibility_type: '',
            combined_eligibility: false,
            start_date: '',
            start_time: '',
            end_date: '',
            end_time: '',
            daily_budget: 5,
            card_name: '',
            card_number: '',
            card_expiry: '',
            card_cvv: '',
            save_card: false
        };
    };
    const validationSchema = useMemo(() => {
        return createAdFormSchema.shape({
            end_date: Yup.date()
                .test('end-date-validation', 'End date must be same or after start date', function (value) {
                    const { start_date } = this.parent;
                    if (!start_date || !value) return true;

                    // Allow same date or after start date
                    return moment(value).isSameOrAfter(moment(start_date), 'day');
                })
                .required('Please select end date.'),
            eligibility_type: Yup.string()
                .required('Please select audience.')
                .test('audience-selection', function (value) {
                    if (value === 'mytab_customer_segments') {
                        if (!selectedMytabSegments || selectedMytabSegments.length === 0) {
                            return this.createError({ message: 'Please select the Value' });
                        }
                    }
                    if (value === 'your_venues_customers') {
                        if (!selectedVenueCustomers || selectedVenueCustomers.length === 0) {
                            return this.createError({ message: 'Please select the Value' });
                        }
                    }
                    if (value === 'your_venues_customers_segment') {
                        if (!selectedVenueSegments || selectedVenueSegments.length === 0) {
                            return this.createError({ message: 'Please select the Value' });
                        }
                    }
                    return true;
                })
        });
    }, [isRenewalMode, selectedMytabSegments, selectedVenueCustomers, selectedVenueSegments]);

    const {
        handleSubmit,
        validateForm,
        values,
        handleChange,
        handleBlur,
        setFieldValue,
        setFieldTouched,
        errors,
        touched,
        resetForm
    } = useFormik({
        initialValues: getInitialValues(),
        validationSchema: validationSchema,
        onSubmit: submitFormHandler,
        context: {
            selectedMytabSegments,
            selectedVenueCustomers,
            selectedVenueSegments
        }
    });

    useEffect(() => {
        // Reset form when not in renewal mode or when renewal data changes
        if (!isRenewalMode) {
            resetForm({
                values: {
                    campaign_id: '',
                    campaign_title: '',
                    objective: defaultObjective,
                    ad_title: '',
                    ad_description: '',
                    media_url: null,
                    call_to_action: '',
                    call_to_action_url: '',
                    state: defaultStateValue,
                    city: defaultCityValue,
                    suburb: defaultSuburbValue,
                    eligibility_type: '',
                    combined_eligibility: false,
                    start_date: '',
                    start_time: '',
                    end_date: '',
                    end_time: '',
                    daily_budget: 5,
                    card_name: '',
                    card_number: '',
                    card_expiry: '',
                    card_cvv: '',
                    save_card: false
                }
            });
        }
    }, [isRenewalMode, resetForm]);

    const handleSave = async () => {
        await validateForm(values);
        handleSubmit();
    };
    const getCampaignList = async () => {
        setCampaignListLoading(true);
        try {
            let payload = {
                created_by_id: authData?.id,
                limit: 10,
                page: 1,
                type: authData?.login_type,
                sort_by: 'oldest',
                search: ''
            };
            const res = await Api(
                'POST',
                apiRoutes?.campaignList,
                payload
            );
            if (res?.data?.status) {
                setCampaignList(
                    res?.data?.data?.campaigns?.map((item) => ({
                        label: item?.title,
                        value: item?.id
                    }))
                );
                // Set the active user count for all_mytab_customers calculation
                // setDefaultAllMytabCustomersCount(res?.data?.data?.active_user_count || 22000);
                setDefaultAllMytabCustomersCount(22000);

            } else {
                toast.error(res?.data?.message);
            }
        } catch (err) {
            if (err?.message) {
                toast.error(err?.message);
            }
        } finally {
            setCampaignListLoading(false);
        }
    };
    const handleFileUploadChange = (customFileObject) => {
        const isAllowed = allowedFileTypes.includes(
            customFileObject?.originalFile?.type
        );
        if (!isAllowed) {
            toast.error('You can only upload jpg, jpeg or png images.');
            return;
        }
        if (customFileObject?.originalFile?.size > FILE_SIZE) {
            toast.error('Image size must be less than 20Mb.');
            return;
        }
        setFieldValue('media_url', customFileObject);
    };
    const minStartDate = useMemo(() => {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);
        return tomorrow;
    }, []);

    const minEndDate = useMemo(() => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (isRenewalMode) {
            // In renewal mode, end date should be at least today or start date, whichever is later
            if (values?.start_date) {
                const startDate = moment(values?.start_date).toDate();
                startDate.setHours(0, 0, 0, 0);
                return startDate > today ? startDate : today;
            }
            return today;
        } else {
            // In normal mode, end date should be at least the start date
            return values?.start_date ? moment(values?.start_date).toDate() : null;
        }
    }, [values?.start_date, isRenewalMode]);

    const openToDate = useMemo(() => {
        return values?.start_date ? moment(values?.start_date).toDate() : new Date();
    }, [values?.start_date]);

    // Optimize date change handlers
    const handleStartDateChange = useCallback((value) => {
        setFieldValue('start_date', value);
        // Only reset end_date if it's before the new start date
        if (values?.end_date && moment(value).isAfter(moment(values?.end_date))) {
            setFieldValue('end_date', '');
        }
    }, [setFieldValue, values?.end_date]);

    const handleEndDateChange = useCallback((value) => {
        setFieldValue('end_date', value);
    }, [setFieldValue]);

    // Calculate dynamic user count based on audience selection
    const userCount = useMemo(() => {
        if (!values?.eligibility_type) return defaultAllMytabCustomersCount;

        switch (values?.eligibility_type) {
            case 'all_mytab_customers':
                return defaultAllMytabCustomersCount;

            case 'mytab_customer_segments':
                if (selectedMytabSegments && selectedMytabSegments.length > 0) {
                    const count = selectedMytabSegments.reduce((sum, segment) => sum + (segment.customerCount || 0), 0);
                    return Math.max(count, 1); // Ensure minimum of 1
                }
                return 1; // Minimum value instead of 0

            case 'your_venues_customers':
                const venueCount = selectedVenueCustomers ? selectedVenueCustomers.length : 0;
                return Math.max(venueCount, 1); // Ensure minimum of 1

            case 'your_venues_customers_segment':
                if (selectedVenueSegments && selectedVenueSegments.length > 0) {
                    const count = selectedVenueSegments.reduce((sum, segment) => sum + (segment.customerCount || 0), 0);
                    return Math.max(count, 1); // Ensure minimum of 1
                }
                return 1; // Minimum value instead of 0

            default:
                return defaultAllMytabCustomersCount;
        }
    }, [values?.eligibility_type, selectedMytabSegments, selectedVenueCustomers, selectedVenueSegments, defaultAllMytabCustomersCount]);

    const estimatedReachedPerDay = useMemo(() =>
        getEstimatedReachedPerDay(
            values?.objective,
            values?.eligibility_type,
            values?.daily_budget,
            userCount
        ),
        [
            values?.objective,
            values?.eligibility_type,
            values?.daily_budget,
            userCount
        ]
    );

    // const maxBudgetCap = useMemo(() => {
    //     if (!values?.objective || !userCount) return 300;
    //     const totalMaxBudget = getMaxBudgetForObjective(values.objective, userCount);
    //     const duration =
    //         values?.start_date && values?.end_date
    //             ? Math.floor((new Date(values.end_date) - new Date(values.start_date)) / (1000 * 60 * 60 * 24)) + 1
    //             : 1;
    //     return Math.floor(totalMaxBudget / duration); // Cap per day
    // }, [values?.objective, userCount, values?.start_date, values?.end_date]);



    const debouncedBudgetCalculation = useMemo(
        () => debounce((startDate, endDate, dailyBudget) => {
            if (startDate && endDate && dailyBudget) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                const diffTime = end.getTime() - start.getTime();
                const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1;
                setTotalAdBudget(diffDays * dailyBudget);
            } else {
                setTotalAdBudget(null);
            }
        }, 300),
        []
    );
    useEffect(() => {
        debouncedBudgetCalculation(values?.start_date, values?.end_date, values?.daily_budget);

        return () => {
            debouncedBudgetCalculation.cancel();
        };
    }, [values?.start_date, values?.end_date, values?.daily_budget, debouncedBudgetCalculation]);

    useEffect(() => {
        if (
            values?.media_url ||
            values?.ad_title ||
            values?.ad_description ||
            values?.call_to_action
        ) {
            setIsShowAdPreview(true);
        } else {
            setIsShowAdPreview(false);
        }
    }, [
        values?.media_url,
        values?.ad_title,
        values?.ad_description,
        values?.call_to_action
    ]);

    // Add these useEffect hooks to trigger validation when selections change
    useEffect(() => {
        if (values?.eligibility_type === 'mytab_customer_segments') {
            setFieldTouched('eligibility_type', true);
            validateForm();
        }
    }, [selectedMytabSegments, validateForm, setFieldTouched, values?.eligibility_type]);

    useEffect(() => {
        if (values?.eligibility_type === 'your_venues_customers') {
            setFieldTouched('eligibility_type', true);
            validateForm();
        }
    }, [selectedVenueCustomers, validateForm, setFieldTouched, values?.eligibility_type]);

    useEffect(() => {
        if (values?.eligibility_type === 'your_venues_customers_segment') {
            setFieldTouched('eligibility_type', true);
            validateForm();
        }
    }, [selectedVenueSegments, validateForm, setFieldTouched, values?.eligibility_type]);

    const handleUpdateCard = async () => {
        if (!stripe || !elements) {
            toast.error("Stripe has not been properly initialized");
            return;
        }

        try {
            setSubmitFormLoading(true);
            const cardElement = elements.getElement(CardNumberElement);
            const result = await stripe.createPaymentMethod({
                type: 'card',
                card: cardElement,
                billing_details: {
                    name: values.card_name
                }
            });

            if (result.error) throw new Error(result.error.message);

            // Here, result.paymentMethod.id is the new card's payment method ID (token)
            // savedCard.id is the ID of the existing card you want to replace
            const res = await Api('PUT', apiRoutes.updateCard, {
                new_payment_method_id: result.paymentMethod.id, // New card payment method ID
                card_id: savedCard.id, // Existing card ID to be replaced
                created_by_id: authData?.id,
                created_by_type: authData?.login_type
            });

            if (res?.data?.status) {
                // Update the saved card with new details
                if (res.data.data && res.data.data.card) {
                    setSavedCard(res.data.data.card);
                } else if (res.data.data && res.data.data.payment_method_id) {
                    // If the response format is different, construct the card object
                    setSavedCard({
                        id: res.data.data.payment_method_id, // This is the ID of the new card
                        card_last4: res.data.data.card_details?.last4,
                        card_brand: res.data.data.card_details?.brand,
                        exp_month: res.data.data.card_details?.exp_month,
                        exp_year: res.data.data.card_details?.exp_year
                    });
                }
                setIsEditingCard(false);
                toast.success("Card updated successfully");
            } else {
                throw new Error(res?.data?.message || "Failed to update card");
            }
        } catch (err) {
            toast.error(err.message || "Error updating card");
        } finally {
            setSubmitFormLoading(false);
        }
    };

    // Update form fields to be disabled in renewal mode
    const isFieldDisabled = (fieldName) => {
        if (!isRenewalMode) return false;
        // Only  ly_budget and payment fields are editable in renewal mode
        const editableFields = [
            'daily_budget',
            'card_name',
            'card_number',
            'card_expiry',
            'card_cvv',
            'save_card'
        ];
        return !editableFields.includes(fieldName);
    };

    return (
        <StyleWrraper>
            <div className="leftSideBox">
                <form>
                    <div className="customFormContainer">

                        <NewFormLabel
                            label="Campaign Details"
                            className="newFormLabelWrapper campaign-details-label"
                        />
                        <NewFormSelectStylesWrapper >
                            <NewFormSelect
                                wrapperClassName={`newFormSelectWrapper ${isFieldDisabled('campaign_id') ? 'disabled' : ''}`}
                                options={campaignList}
                                value={isRenewalMode && renewalData?.campaign_title
                                    ? { label: renewalData.campaign_title, value: renewalData.campaign_id }
                                    : campaignList?.find((option) => option?.value == values?.campaign_id)
                                }
                                onChange={(item) => {
                                    setFieldValue('campaign_id', item?.value);
                                }}
                                isLoading={campaignListLoading}
                                placeholder="Select Campaign"
                                isDisabled={isFieldDisabled('campaign_id')}
                            />
                        </NewFormSelectStylesWrapper>
                        <NewFormErrorMessage
                            className="newFormErrorMessageWrapper pa-t-5"
                            message={
                                touched?.campaign_id && !!errors?.campaign_id
                                    ? errors?.campaign_id
                                    : ''
                            }
                        />
                        <NewFormLabel
                            label="Ad Objective"
                            className="newFormLabelWrapper"
                        />
                        <NewFormSelect
                            wrapperClassName="newFormSelectWrapper"
                            placeholder="Select Objective"
                            options={adObjectiveOptions}
                            value={adObjectiveOptions?.find((option) => option?.value === values?.objective)}
                            onChange={(item) => {
                                setFieldValue('objective', item?.value);
                            }}
                            isDisabled={isFieldDisabled('objective')}
                        />
                        <NewFormErrorMessage
                            className="newFormErrorMessageWrapper pa-t-5"
                            message={
                                touched?.objective && !!errors?.objective
                                    ? errors?.objective
                                    : ''
                            }
                        />
                        <NewFormLabel
                            label="Ad Details"
                            className="newFormLabelWrapper"
                        />
                        <NewFormInput
                            wrapperClassName={`newFormInputWrapper ${isFieldDisabled('ad_title') ? 'disabled' : ''}`} name="ad_title"
                            placeholder="Ad Title"
                            value={values?.ad_title}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            disabled={isFieldDisabled('ad_title')}
                        />
                        <NewFormErrorMessage
                            className="newFormErrorMessageWrapper pa-t-5"
                            message={
                                touched?.ad_title && !!errors?.ad_title
                                    ? errors?.ad_title
                                    : ''
                            }
                        />
                        <NewFormTextarea
                            wrapperClassName={`newFormTextareaWrapper ${isFieldDisabled('ad_description') ? 'disabled' : ''}`}
                            name="ad_description"
                            placeholder="Ad Description"
                            value={values?.ad_description}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            maxLength={120}
                            disabled={isFieldDisabled('ad_description')}
                        />
                        <NewFormErrorMessage
                            className="newFormErrorMessageWrapper pa-t-3"
                            message={
                                touched?.ad_description &&
                                    !!errors?.ad_description
                                    ? errors?.ad_description
                                    : ''
                            }
                        />
                        <div className="gapTopPadding">
                            <FileUpload
                                value={values?.media_url}
                                onChange={handleFileUploadChange}
                                aspectRatio={1080 / 1080} // 1:1 crop enforced
                                disabled={isFieldDisabled('media_url')}
                            />
                        </div>
                        <NewFormErrorMessage
                            className="newFormErrorMessageWrapper pa-t-5"
                            message={
                                touched?.media_url && !!errors?.media_url
                                    ? errors?.media_url
                                    : ''
                            }
                        />
                        <NewFormSelectStylesWrapper >
                            <div className="gapTopPadding">
                                <NewFormSelect
                                    wrapperClassName="newFormSelectWrapper"
                                    placeholder="Select Call To Action"
                                    options={callToActionOptions}
                                    value={callToActionOptions?.find(
                                        (option) =>
                                            option?.value == values?.call_to_action
                                    )}
                                    onChange={(item) => {
                                        setFieldValue('call_to_action', item?.value);
                                    }}
                                    isDisabled={isFieldDisabled('call_to_action')}
                                />
                            </div>
                        </NewFormSelectStylesWrapper>
                        <NewFormErrorMessage
                            className="newFormErrorMessageWrapper pa-t-5"
                            message={
                                touched?.call_to_action &&
                                    !!errors?.call_to_action
                                    ? errors?.call_to_action
                                    : ''
                            }
                        />
                        {values?.call_to_action &&
                            values?.call_to_action !== 'view_menu' && (
                                <div className='gapTopPadding'>
                                    <NewFormInput
                                        wrapperClassName={`newFormInputWrapper ${isFieldDisabled('call_to_action_url') ? 'disabled' : ''}`}
                                        name="call_to_action_url"
                                        placeholder="Call to Action URL"
                                        value={values?.call_to_action_url}
                                        onChange={handleChange}
                                        onBlur={handleBlur}
                                        disabled={isFieldDisabled('call_to_action_url')}
                                    />
                                    <NewFormErrorMessage
                                        className="newFormErrorMessageWrapper pa-t-5"
                                        message={
                                            touched?.call_to_action_url &&
                                                !!errors?.call_to_action_url
                                                ? errors?.call_to_action_url
                                                : ''
                                        }
                                    />
                                </div>
                            )}
                        <div className="gapTopPadding">
                            <NewFormInput
                                wrapperClassName={`newFormInputWrapper ${isFieldDisabled('city') ? 'disabled' : ''}`}
                                name="city"
                                placeholder="Suburb/City"
                                value={values?.city}
                                onChange={(e) => {
                                    setFieldValue('city', e.target.value);
                                    setFieldValue('suburb', ''); // Set suburb to blank for backend
                                }}
                                onBlur={handleBlur}
                                disabled={isFieldDisabled('city')}
                            />
                        </div>
                        <NewFormErrorMessage
                            className="newFormErrorMessageWrapper pa-t-5"
                            message={
                                touched?.city && !!errors?.city
                                    ? errors?.city
                                    : ''
                            }
                        />
                        {/* Suburb field removed from UI */}
                        <NewFormSelectStylesWrapper >
                            <div className="gapTopPadding">
                                <NewFormSelect
                                    wrapperClassName="newFormSelectWrapper"
                                    placeholder="Select State"
                                    options={australiaStateOptions}
                                    value={australiaStateOptions?.find(
                                        (option) => option?.value == values?.state
                                    )}
                                    onChange={(item) => {
                                        setFieldValue('state', item?.value);
                                    }}
                                    menuPlacement="bottom"
                                    isDisabled={isFieldDisabled('state')}
                                />
                            </div>
                        </NewFormSelectStylesWrapper>
                        <NewFormErrorMessage
                            className="newFormErrorMessageWrapper pa-t-5"
                            message={
                                touched?.state && !!errors?.state
                                    ? errors?.state
                                    : ''
                            }
                        />
                        <NewFormLabel
                            label="Audience"
                            className="newFormLabelWrapper"
                        />
                        <NewFormSelect
                            wrapperClassName="newFormSelectWrapper"
                            placeholder="Choose Your Audience"
                            options={
                                authData?.login_type === 'venue'
                                    ? venueAudienceOptions
                                    : advertiserAudienceOptions
                            }
                            value={
                                authData?.login_type === 'venue'
                                    ? venueAudienceOptions?.find(
                                        (option) => option?.value == values?.eligibility_type
                                    )
                                    : advertiserAudienceOptions?.find(
                                        (option) => option?.value == values?.eligibility_type
                                    )
                            }
                            onChange={(item) => {
                                setFieldValue('eligibility_type', item?.value);
                                setSelectedMytabSegments([]);
                                setSelectedVenueCustomers([]);
                                setSelectedVenueSegments([]);
                            }}
                            isDisabled={isFieldDisabled('eligibility_type')}
                        />
                        {/* Error message after audience dropdown - only for "Please select audience." */}
                        <NewFormErrorMessage
                            className="newFormErrorMessageWrapper pa-t-5"
                            message={
                                touched?.eligibility_type &&
                                    !!errors?.eligibility_type &&
                                    !values?.eligibility_type
                                    ? 'Please select audience.'
                                    : ''
                            }
                        />

                        {/* BrowseBox section */}
                        {[
                            'mytab_customer_segments',
                            'your_venues_customers',
                            'your_venues_customers_segment'
                        ].includes(values?.eligibility_type) && (
                                <div className="pa-t-12">
                                    <BrowseBox
                                        selectedDropdownValue={values?.eligibility_type}
                                        selectedMytabSegments={selectedMytabSegments}
                                        setSelectedMytabSegments={setSelectedMytabSegments}
                                        selectedVenueCustomers={selectedVenueCustomers}
                                        setSelectedVenueCustomers={setSelectedVenueCustomers}
                                        selectedVenueSegments={selectedVenueSegments}
                                        setSelectedVenueSegments={setSelectedVenueSegments}
                                    />
                                    {/* Error message below BrowseBox - only for "Please select the Value" */}
                                    <NewFormErrorMessage
                                        className="newFormErrorMessageWrapper pa-t-5"
                                        message={
                                            touched?.eligibility_type &&
                                                !!errors?.eligibility_type &&
                                                values?.eligibility_type &&
                                                errors?.eligibility_type === 'Please select the Value'
                                                ? 'Please select the Value'
                                                : ''
                                        }
                                    />
                                </div>
                            )}
                    </div>
                    <div className="customFormContainer">
                        <div className="subTitle gapTopPadding">
                            Schedule & Duration
                        </div>
                        <div
                            className="flex align-items-start pa-b-12"
                            style={{ gap: '20px' }}
                        >
                            <div className="flex-1">
                                <NewFormLabel
                                    label={'Start Date:'}
                                    className="newFormLabelWrapper2 "
                                />
                                <NewDatePicker
                                    wrapperClassName={`newDatePickerWrapper ${isFieldDisabled('start_date') ? 'disabled' : ''}`}
                                    placeholder={'DD/MM/YYYY'}
                                    value={values?.start_date}
                                    onChange={handleStartDateChange}
                                    onBlur={() => setFieldTouched('start_date', true)}
                                    minDate={minStartDate}
                                    dateFormat="dd/MM/yyyy"
                                    disabled={isFieldDisabled('start_date')}
                                />
                                <NewFormErrorMessage
                                    className="newFormErrorMessageWrapper pa-t-5"
                                    message={
                                        touched?.start_date &&
                                            !!errors?.start_date
                                            ? errors?.start_date
                                            : ''
                                    }
                                />
                            </div>
                            {/* <div className="flex-1">
                                <NewFormLabel
                                    label={'Start Time:'}
                                    className="newFormLabelWrapper pa-b-2"
                                />
                                <NewTimePicker
                                    wrapperClassName={'newTimePickerWrapper'}
                                    popoverWrapperClassName={
                                        'createAdNewTimePickerPopoverWrapper'
                                    }
                                    placeholder={'HH:MM'}
                                    value={values?.start_time}
                                    onChange={(value) =>
                                        setFieldValue('start_time', value)
                                    }
                                    onBlur={() =>
                                        setFieldTouched('start_time', true)
                                    }
                                />
                                <NewFormErrorMessage
                                    className="newFormErrorMessageWrapper pa-t-5"
                                    message={
                                        touched?.start_time &&
                                            !!errors?.start_time
                                            ? errors?.start_time
                                            : ''
                                    }
                                />
                            </div> */}
                            <div className="flex-1">
                                <NewFormLabel
                                    label={'End Date:'}
                                    className="newFormLabelWrapper2 pa-b-2"
                                />
                                <NewDatePicker
                                    wrapperClassName={'newDatePickerWrapper'}
                                    placeholder={'DD/MM/YYYY'}
                                    value={values?.end_date}
                                    onChange={handleEndDateChange}
                                    onBlur={() => setFieldTouched('end_date', true)}
                                    minDate={minEndDate}
                                    dateFormat="dd/MM/yyyy"
                                    showMonthDropdown
                                    showYearDropdown
                                    dropdownMode="select"
                                    openToDate={openToDate}
                                    disabled={false} // Always editable in renewal mode
                                />
                                <NewFormErrorMessage
                                    className="newFormErrorMessageWrapper pa-t-5"
                                    message={
                                        touched?.end_date && !!errors?.end_date
                                            ? errors?.end_date
                                            : ''
                                    }
                                />
                            </div>
                        </div>
                        {/* <div
                            className="flex align-items-start pa-b-12"
                            style={{ gap: '20px' }}
                        >
                            <div className="flex-1">
                                <NewFormLabel
                                    label={'End Date:'}
                                    className="newFormLabelWrapper pa-b-2"
                                />
                                <NewDatePicker
                                    wrapperClassName={'newDatePickerWrapper'}
                                    placeholder={'DD-MM-YYYY'}
                                    value={values?.end_date}
                                    onChange={(value) =>
                                        setFieldValue('end_date', value)
                                    }
                                    onBlur={() =>
                                        setFieldTouched('end_date', true)
                                    }
                                    minDate={
                                        values?.start_date
                                            ? moment(
                                                values?.start_date
                                            ).toDate()
                                            : ''
                                    }
                                    dateFormat="dd-MM-yyyy"
                                />
                                <NewFormErrorMessage
                                    className="newFormErrorMessageWrapper pa-t-5"
                                    message={
                                        touched?.end_date && !!errors?.end_date
                                            ? errors?.end_date
                                            : ''
                                    }
                                />
                            </div>
                            <div className="flex-1">
                                <NewFormLabel
                                    label={'End Time:'}
                                    className="newFormLabelWrapper pa-b-2"
                                />
                                <NewTimePicker
                                    wrapperClassName={'newTimePickerWrapper'}
                                    popoverWrapperClassName={
                                        'createAdNewTimePickerPopoverWrapper'
                                    }
                                    placeholder={'HH:MM'}
                                    value={values?.end_time}
                                    onChange={(value) =>
                                        setFieldValue('end_time', value)
                                    }
                                    onBlur={() =>
                                        setFieldTouched('end_time', true)
                                    }
                                />
                                <NewFormErrorMessage
                                    className="newFormErrorMessageWrapper pa-t-5"
                                    message={
                                        touched?.end_time && !!errors?.end_time
                                            ? errors?.end_time
                                            : ''
                                    }
                                />
                            </div>
                        </div> */}
                    </div>
                    {values?.objective && values?.eligibility_type && (
                        <div style={{ display: shouldSkipPayment ? 'none' : 'block' }}>
                            <div className="pa-b-12">
                                <DailyBudget
                                    reachedPerDayCount={estimatedReachedPerDay}
                                    value={values?.daily_budget}
                                    onChange={(value) => setFieldValue('daily_budget', value)}
                                    maxUserCount={userCount}
                                    objective={values?.objective}
                                    audience={values?.eligibility_type}
                                />

                            </div>
                            {totalAdBudget && (
                                <>
                                    <div className="subTitle">
                                        Total Ad Budget: ${totalAdBudget}
                                    </div>
                                    <p className="mutedText">
                                        This excludes any Stripe service fees,
                                        taxes and local fees.
                                    </p>
                                    <div className="subTitle pa-b-1" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                        Payment Details
                                        <img src={stripeImg} className='stripeImage' alt="" />
                                    </div>

                                    {/* Conditional Rendering for Payment Details */}
                                    {savedCard ? (
                                        <>
                                            <div className="savedCardDetails">
                                                <p className="savedPaymentMethodLabel">Saved Payment Method</p>
                                                <p className="cardInfo">
                                                    {savedCard.card_brand.charAt(0).toUpperCase() + savedCard.card_brand.slice(1).toLowerCase()} ending in *****{savedCard.card_last4}
                                                </p>
                                                {/* <p className="cardInfo">Expires: {savedCard.exp_month}/{savedCard.exp_year}</p> */}
                                                {!isEditingCard && (
                                                    <div className='updateCard'>
                                                        <FilledButton
                                                            buttonText={'Update Card'}
                                                            background={'#ffffff'}
                                                            color={'#4880FF'}
                                                            style={{
                                                                width: '160px',
                                                                border: '1px solid #4880FF',
                                                            }}
                                                            onClick={() => setIsEditingCard(true)}
                                                        />
                                                    </div>
                                                )}

                                            </div>

                                            {isEditingCard && (
                                                <div>
                                                    <div className="form-row pa-t-8">
                                                        <NewFormInput
                                                            wrapperClassName="newFormInputWrapper"
                                                            name="card_name"
                                                            placeholder="Name on card"
                                                            value={values.card_name}
                                                            onChange={handleChange}
                                                            onBlur={handleBlur}
                                                            disabled={isFieldDisabled('card_name')}
                                                        />
                                                        <NewFormErrorMessage
                                                            className="newFormErrorMessageWrapper pa-t-5"
                                                            message={
                                                                touched?.card_name && !!errors?.card_name
                                                                    ? errors?.card_name
                                                                    : ''
                                                            }
                                                        />
                                                    </div>

                                                    <div className="form-row three-fields cardPadding" style={{ display: "flex", justifyContent: "space-between" }}>
                                                        <div className="form-field card-number" style={{ width: "360px", marginRight: "10px" }}>
                                                            <div className="stripe-element-container" style={{ height: getStripeElementHeight() }}>
                                                                <CardNumberElement options={cardNumberOptions} />
                                                            </div>
                                                        </div>

                                                        <div className="form-field expiry" style={{ width: "210px", marginRight: "10px" }}>
                                                            <div className="stripe-element-container" style={{ height: getStripeElementHeight() }}>
                                                                <CardExpiryElement options={cardExpiryOptions} />
                                                            </div>
                                                        </div>

                                                        <div className="form-field cvv" style={{ width: "193px" }}>
                                                            <div className="stripe-element-container" style={{ height: getStripeElementHeight() }}>
                                                                <CardCvcElement options={cardCvcOptions} />
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div className="updateCard" style={{ display: "flex", gap: "10px" }}>
                                                        <FilledButton
                                                            buttonText={'Cancel'}
                                                            background={'#ffffff'}
                                                            color={'#999999'}
                                                            style={{
                                                                width: '160px',
                                                                border: '1px solid #999999'
                                                            }}
                                                            onClick={() => setIsEditingCard(false)}
                                                        />
                                                        <FilledButton
                                                            buttonText={'Update Card'}
                                                            background={'#ffffff'}
                                                            color={'#4880FF'}
                                                            style={{
                                                                width: '160px',
                                                                border: '1px solid #4880FF',
                                                                marginTop: '10px'
                                                            }}
                                                            onClick={handleUpdateCard}
                                                            loading={submitFormLoading}
                                                        />
                                                    </div>
                                                </div>
                                            )}
                                        </>
                                    ) : (
                                        <div className="payment-form pa-t-8">
                                            <div className="form-row">
                                                <NewFormInput
                                                    wrapperClassName={`newFormInputWrapper ${isFieldDisabled('card_name') ? 'disabled' : ''}`}
                                                    name="card_name"
                                                    placeholder="Name on card"
                                                    value={values.card_name}
                                                    onChange={handleChange}
                                                    onBlur={handleBlur}
                                                    disabled={isFieldDisabled('card_name')}
                                                />
                                                <NewFormErrorMessage
                                                    className="newFormErrorMessageWrapper pa-t-5"
                                                    message={
                                                        touched?.card_name && !!errors?.card_name
                                                            ? errors?.card_name
                                                            : ''
                                                    }
                                                />
                                            </div>

                                            <div className="form-row three-fields cardPadding" style={{ display: "flex", justifyContent: "space-between" }}>
                                                <div className="form-field card-number" style={{ width: "360px", marginRight: "10px" }}>
                                                    <div className="stripe-element-container" style={{ height: getStripeElementHeight() }}>
                                                        <CardNumberElement options={cardNumberOptions} />
                                                    </div>
                                                </div>

                                                <div className="form-field expiry" style={{ width: "210px", marginRight: "10px" }}>
                                                    <div className="stripe-element-container" style={{ height: getStripeElementHeight() }}>
                                                        <CardExpiryElement options={cardExpiryOptions} />
                                                    </div>
                                                </div>

                                                <div className="form-field cvv" style={{ width: "193px" }}>
                                                    <div className="stripe-element-container" style={{ height: getStripeElementHeight() }}>
                                                        <CardCvcElement options={cardCvcOptions} />
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="flex savebtn" style={{ gap: '8px' }}>
                                                <div>
                                                    <NewFormCheckbox
                                                        checked={values?.save_card}
                                                        onChange={(e) => {
                                                            setFieldValue('save_card', e?.target?.checked);
                                                        }}
                                                    />
                                                </div>
                                                <div>
                                                    <p className="checkboxLabel1">
                                                        Save this card for all future MyTab Ad payments.
                                                    </p>
                                                    <p className="checkboxLabel2 pa-t-4">
                                                        Card details are securely stored by Stripe; MyTab does not store your sensitive financial information.
                                                    </p>
                                                </div>
                                            </div>

                                            {savedCard && (
                                                <div className="button-row" style={{ display: "flex", gap: "10px", marginTop: "15px" }}>
                                                    <>
                                                        <FilledButton
                                                            buttonText={'Cancel'}
                                                            background={'#ffffff'}
                                                            color={'#999999'}
                                                            style={{
                                                                width: '120px',
                                                                border: '1px solid #999999'
                                                            }}
                                                            onClick={() => setIsEditingCard(false)}
                                                        />

                                                        <FilledButton
                                                            buttonText={savedCard ? 'Update Card' : 'Save Card'}
                                                            background={'rgba(107, 194, 66, 0.2)'}
                                                            color={'rgba(107, 194, 66, 1)'}
                                                            style={{
                                                                width: '120px',
                                                                border: '1px solid rgba(107, 194, 66, 1)'
                                                            }}
                                                            onClick={handleUpdateCard}
                                                            loading={submitFormLoading}
                                                        />
                                                    </>

                                                </div>
                                            )}
                                        </div>
                                    )}

                                    <div className="importantBox">
                                        <span className="importantWord">
                                            Important:
                                        </span>{' '}
                                        All MyTab Ad purchases are final and
                                        non-refundable. Funds may only be
                                        applied toward active or future
                                        advertising campaigns within the app.
                                        For full terms, please refer to our
                                        Terms & Conditions.
                                    </div>
                                </>
                            )}
                        </div>
                    )}
                    <div className="saveCancelBtnWrapper">
                        <FilledButton
                            buttonText={'Cancel'}
                            background={'#ffffff'}
                            color={'rgba(107, 194, 66, 1)'}
                            style={{
                                width: '160px',
                                border: '1px solid rgba(107, 194, 66, 1)'
                            }}
                            onClick={handleCancel}
                        />
                        <FilledButton
                            buttonText={'Publish Ad'}
                            background={'rgba(107, 194, 66, 0.2)'}
                            color={'rgba(107, 194, 66, 1)'}
                            style={{
                                width: '160px',
                                border: '1px solid rgba(107, 194, 66, 1)'
                            }}
                            onClick={handleSave}
                            loading={submitFormLoading}
                        />
                    </div>
                </form>
            </div>
            {isShowAdPreview && (
                <div className="rightSideBox">
                    <p className="rightSideBoxTitle">Ad Preview</p>
                    <div>
                        <AdPreviewCard
                            state={values?.state}
                            city={values?.city}
                            suburb={values?.suburb}
                            adImage={values?.media_url?.url}
                            title={values?.ad_title}
                            description={values?.ad_description}
                            actionButtonText={
                                callToActionOptions?.find(
                                    (option) =>
                                        option?.value == values?.call_to_action
                                )?.label
                            }
                        />
                    </div>
                </div>
            )}
        </StyleWrraper>
    );
};

export default CreateAds;
