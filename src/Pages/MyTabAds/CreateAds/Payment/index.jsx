import { forwardRef, useImperativeHandle, useRef } from 'react';
import {
	CardElement,
	Elements,
	useElements,
	useStripe
} from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';

import { stripePublicKey } from '../../../../Helper/constant';

const stripePromise = loadStripe(stripePublicKey);

const PaymentForm = forwardRef((props, ref) => {
	const paymentFormElementRef = useRef(null);

	useImperativeHandle(ref, () => ({
		handlePayment: paymentFormElementRef?.current?.handlePayment
	}));
	return (
		<Elements stripe={stripePromise}>
			<PaymentFormElement ref={paymentFormElementRef} />
		</Elements>
	);
});

const PaymentFormElement = forwardRef((props, ref) => {
	const stripe = useStripe();
	const elements = useElements();

	useImperativeHandle(ref, () => ({
		async handlePayment(clientSecret) {
			return await stripe.confirmCardPayment(clientSecret, {
				payment_method: {
					card: elements.getElement(CardElement)
				}
			});
		}
	}));
	return (
		<Elements stripe={stripePromise} options={{ clientSecret: '' }}>
			<CardElement />
		</Elements>
	);
});

export default PaymentForm;
