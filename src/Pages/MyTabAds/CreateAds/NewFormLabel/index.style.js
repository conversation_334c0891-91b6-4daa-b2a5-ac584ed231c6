import styled from 'styled-components';

export const StyleWrapper = styled.div`
	.newLabelContainer {
		display: flex;
		align-items: center;
		gap: 6px;
		.newLabel {
			color: #202224 !important;
			font-family: 'nunitosans-medium' !important;
			font-size: 18px !important;
			line-height: 1 !important;
		}
		.infoIconImage {
			display: block;
			width: 22px;
			height: 19px;
		}
	}
	
	@media (max-width: 600px) {
		.newLabelContainer {
			gap: 4px;
			.newLabel {
				font-size: 14px !important;
			}
			.infoIconImage {
				width: 15px;
				height: 13px;
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1499px) {
		.newLabelContainer {
			gap: 4px;
			.newLabel {
				font-size: 14px !important;
			}
			.infoIconImage {
				width: 17px;
				height: 14px;
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.newLabelContainer {
			gap: 4px;
			.newLabel {
				font-size: 14px !important;
			}
			.infoIconImage {
				width: 17px;
				height: 14px;
			}
		}
	}
`;
