
import styled from 'styled-components';

export const StyleWrraper = styled.div`
	display: flex;
	gap: 36px;
	
	/* Disabled input styles specific to CreateAds component */
	.newFormInputWrapper.disabled {
		.newCustomInputContainer {
			background-color: #f2f2f2 !important;
			cursor: not-allowed;
		}
		
		.newCustomInput:disabled {
			background-color: 1px solid rgba(49, 49, 50, 0.35) !important;
			cursor: not-allowed;
		}
	}
	
	/* Disabled textarea styles specific to CreateAds component */
	.newFormTextareaWrapper.disabled {
		.newCustomTextarea {
			background-color: #f2f2f2 !important;
			cursor: not-allowed;
			&::placeholder {
				color: #999999 !important;
			}
		}
	}
        
    .newDatePickerWrapper.disabled {
    .newCustomDatePickerContainer {
        background-color: #f2f2f2 !important;
        cursor: not-allowed !important;
        border-color: #e0e0e0 !important;
        opacity: 0.6;
    }
    
    .customDateInput {
        cursor: not-allowed !important;
        pointer-events: none !important;
        
        .valueContainer,
        .placeholderContainer {
            color: #999999 !important;
        }
    }
    
    .suffixContainer {
        cursor: not-allowed !important;
        pointer-events: none !important;
    }
}
	
	.leftSideBox,
	.rightSideBox {
		width: 50%;
		border: 1px solid rgba(213, 213, 213, 1);
		border-radius: 4px;
		padding: 14px;
	}
	@media only screen and (max-width: 1024px) {
		flex-direction: column;
		gap: 27px;
		.leftSideBox,
		.rightSideBox {
			width: 100%;
		}
	}
	// @media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
	// 	gap: 27px;
	// }
	.leftSideBox {
		.customFormContainer {
			max-width: 445px;
			width: 445px;
		}
        .gapadcolumn {
            padding-bottom: 14px;
        }
        .cardPadding {
            padding-top: 7px;
        }
        .gapTopPadding {
            padding-top: 14px;
        }
		.newFormLabelWrapper {
			.newLabelContainer {
				.newLabel {
					color: #000000 !important;
					font-family: 'nunitosans-bold' !important;
					font-size: 18px !important;
					line-height: 27px !important;
                    padding-bottom: 11px;
                    padding-top: 11px;
				}
			}
		}
            
        .newFormLabelWrapper2 {
			.newLabelContainer {
				.newLabel {
					color: #000000 !important;
					font-family: 'nunitosans-bold' !important;
					font-size: 15px !important;
					line-height: 27px !important;
                    // padding-bottom: 0px;
                    padding-top: 10px;
				}
			}
		}
		.newFormInputWrapper {
			.newCustomInputContainer {
				gap: 8px;
				border: 0.6px solid #d5d5d5;
				border-radius: 4px;
				height: 34px;
				padding-inline: 8px;
			}
			.newCustomInput {
				color: #2e2e2e !important;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 16px !important;
				line-height: 1 !important;
				&::placeholder {
					color: #979797 !important;
					font-family: 'nunitosans-semi-bold' !important;
					font-size: 16px !important;
					line-height: 1 !important;
				}
				padding: 0 !important;
			}
			.prefixContainer,
			.suffixContainer {
				color: #979797 !important;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 16px !important;
				line-height: 1 !important;
			}
		}
		.newFormSelectWrapper {
			.customContainer {
				height: 34px !important;
			}
			.customControl {
				border: 0.6px solid #d5d5d5 !important;
				padding-inline: 8px !important;
				border-radius: 4px !important;
				cursor: pointer !important;
			}
			.customSingleValue {
				color: #000000 !important;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 15px !important;
				line-height: 1 !important;
			}
			.customPlaceholder {
				color: #979797 !important;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 15px !important;
				line-height: 1 !important;
			}
			.customInput {
				input {
					color: #979797 !important;
					font-family: 'nunitosans-semi-bold' !important;
					font-size: 16px !important;
					line-height: 1 !important;
				}
			}
			.customClearIndicator {
				width: 24px !important;
				height: 24px !important;
			}
			.customDropdownIndicator {
				width: 17px !important;
				height: 17px !important;
			}
			.customMenu {
				margin-top: 2px !important;
				margin-bottom: 2px !important;
			}
			.customOption {
				color: #2e2e2e !important;
				font-family: 'nunitosans-semi-bold';
				font-size: 16px !important;
				line-height: 1 !important;
				cursor: pointer !important;
			}
			.customOption.isSelected {
				color: #ffffff !important;
			}
			.customNoOptionsMessage {
				color: #979797 !important;
				font-family: 'nunitosans-semi-bold';
				font-size: 16px !important;
			}
		}
		.newFormTextareaWrapper {
			line-height: 1;
            // padding-bottom: 12px !important;
            padding-top: 14px  !important;
			.newCustomTextarea {
				border: 0.6px solid #d5d5d5 !important;
				border-radius: 4px !important;
				color: #2e2e2e !important;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 16px !important;
				line-height: 22px !important;
				&::placeholder {
					color: #979797 !important;
					font-family: 'nunitosans-semi-bold' !important;
					font-size: 16px !important;
					line-height: 1 !important;
				}
				padding: 8px !important;
			}
		}
		.newFormErrorMessageWrapper {
			color: rgb(255, 95, 95) !important;
			font-family: 'nunitosans-semi-bold' !important;
		}
		.checkboxLabel1 {
			color: #2e2e2e !important;
			font-family: 'nunitosans-bold' !important;
			font-size: 15px !important;
			line-height: 1 !important;
		}
		.checkboxLabel2 {
			color: #404040 !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 13px !important;
			line-height: 22px !important;
		}
		.newDatePickerWrapper {
			.newCustomDatePickerContainer {
				gap: 8px;
				border: 0.6px solid #d5d5d5;
				border-radius: 4px;
				height: 34px;
				.newCustomDatePicker {
					.react-datepicker {
						border-radius: 4px !important;
					}
					.react-datepicker-wrapper {
						.react-datepicker__input-container {
							input {
								border-radius: 4px !important;
							}
							.customDateInput {
								padding-inline: 8px;
								gap: 8px;
								.suffixContainer {
									.dropdownIconWrapper {
										.dropdownIcon {
											width: 17px !important;
											height: 17px !important;
										}
									}
								}
							}
						}
					}
				}
			}
		}
		.newTimePickerWrapper {
			.newCustomTimePickerContainer {
				gap: 8px;
				border: 0.6px solid #d5d5d5;
				border-radius: 4px;
				height: 34px;
				padding-inline: 8px;
			}
			.newCustomInput {
				color: #2e2e2e !important;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 16px !important;
				line-height: 1 !important;
				&::placeholder {
					color: #979797 !important;
					font-family: 'nunitosans-semi-bold' !important;
					font-size: 16px !important;
					line-height: 1 !important;
				}
				padding: 0 !important;
			}
			.suffixContainer {
				.dropdownIconWrapper {
					.dropdownIcon {
						width: 17px !important;
						height: 17px !important;
					}
				}
			}
		}
		// .StripeInputClone {
		// 	border: none;
		// 	outline: none;
		// 	font-size: 15px;
		// 	width: 100%;
		// 	color: #979797;
		// 	font-family: 'nunitosans-semi-bold';
		// 	height: 34px;
		// 	padding: 0 12px;
		// 	background-color: #ffffff;
		// 	border: 1px solid #d1d5db;
		// 	border-radius: 4px;
		// 	&::placeholder {
		// 		color: #979797 !important;
        //         fontFamily: "nunitosans-semi-bold";
        //         fontSize: '15px',
		// 	}
		// }
		.stripe-input-clone {
			.newCustomInputContainer {
				height: 34px !important;
				border: 1px solid #d1d5db !important;
				border-radius: 4px !important;
				padding-inline: 12px !important;
				background-color: #ffffff !important;
			}
			.newCustomInput {
				font-size: 14px;
				color: #2e2e2e !important;
				font-family: 'nunitosans-semi-bold';
				&::placeholder {
                    font-size: 14px;
					color: #979797 !important;
					font-family: 'nunitosans-semi-bold';
				}
			}
		}

		.stripe-element-container {
			height: 34px;
			background-color: white;
			display: flex;
			align-items: center;
			border: 0.6px solid #d5d5d5;
			border-radius: 4px;
			padding-inline: 8px;
            padding-block: 5px;
			overflow: hidden;
		}
		.payment-form {
			// margin-top: 15px;
		}600
        .card-top {
            margin-top: 7px;
            display: flex;
            justify-content: space-between;
        }
		/* Base styles for Stripe Elements */
		.stripe-element-container .StripeElement {
			width: 100%;
			height: 100%;
			padding: 0;
			border: none;
			background-color: transparent;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 15px !important;
			color: #2e2e2e !important;
		}
		.stripe-element-container .StripeElement--empty {
			color: #979797 !important;
		}
		.campaign-details-label {
			.newLabel {
				padding-top: 0 !important;
				font-size: 14px !important;
			}
		}
		

		/* Tablet responsive styles */
		@media only screen and (min-width: 600px) and (max-width: 1299px) {
			.stripe-element-container {
				height: 34px !important;
                padding-inline: 0px;
                padding-top: 13px;
			}
			.payment-form {
				// margin-top: 10px;
			}
            .card-top {
                margin-top: 7px;
                display: flex;
            }
            .stripe-element-container .StripeElement {
				height: 34px !important;
				font-size: 12px !important;
				padding-inline: 8px;
                padding-block: 5px;
			}
			// StripeInputClone {
            //     &::placeholder {
            //         color: #979797 !important;
            //         fontFamily: '"nunitosans-semi-bold",
            //         fontSize: '12px',
            //     }
            // }
           
		}

		/* Mobile responsive styles */
		@media only screen and (max-width: 600px) {
			.stripe-element-container {
				height: 34px !important;
                padding-inline: 0px;
                padding-top: 10px;
			}
			
			.payment-form {
				// margin-top: 11px;
			}
			.stripe-element-container .StripeElement {
				height: 34px !important;
				font-size: 12px !important;
				padding-inline: 8px;
                padding-block: 5px;
			}
			// StripeInputClone {
            //     &::placeholder {
            //         color: #979797 !important;
            //         fontFamily: '"nunitosans-semi-bold", 
            //         fontSize: '12px',
            //     }
            // }
			// input[name="card_name"] {
			// 	height: 30px !important;
			// 	font-size: 12px !important;
			// 	padding: 8px 10px !important;
            //     color: #2e2e2e !important;
            //      &::placeholder {
            //         color: #c9d2da !important;
            //         font-family: 'nunitosans-semi-bold' !important;
            //         font-size: 12px !important;
            //     }
			// }
		}

		/* Desktop responsive styles for specific resolution */
		@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
			.stripe-element-container {
				height: 34px !important;
                padding-inline: 0px;
                padding-top: 13px;
			}
			.payment-form {
				// margin-top: 10px;
			}
			.card-top {
				margin-top: 7px;
			}
			.stripe-element-container .StripeElement {
				height: 34px !important;
				font-size: 12px !important;
				padding-inline: 8px;
                padding-block: 5px;
			}
			// .StripeInputClone {
			// 	font-size: 12px !important;
			// 	&::placeholder {
			// 		color: #979797 !important;
			// 		font-size: 12px !important;
			// 	}
			// }
			// input[name="card_name"] {
			// 	height: 30px !important;
			// 	font-size: 12px !important;
			// 	padding: 8px 10px !important;
            //     color: #2e2e2e !important;
            //      &::placeholder {
            //         color: #979797 !important;
            //         font-family: 'nunitosans-semi-bold' !important;
            //         font-size: 12px !important;
            //     }
			// }
		}

		// .stripe-element-container .StripeElement--focus {
		// 	border-color: #4880FF;
		// 	box-shadow: 0 0 0 1px rgba(72, 128, 255, 0.5);
		// }

		/* Style for the Name on card input */
		// input[name="card_name"]  input[name="card_name"] {
		// 	width: 100%;
		// 	height: 34px;
		// 	padding: 6px 12px;
		// 	border: 1px solid #d5d5d5;
		// 	border-radius: 4px;
		// 	background-color: white;
		// 	font-family: 'nunitosans-semi-bold' !important;
		// 	font-size: 15px;
		// 	color: #2e2e2e !important;
        //      &::placeholder {
        //             color: #c9d2da !important;
        //             font-family: 'nunitosans-semi-bold' !important;
        //             font-size: 15px !important;
        //         }
		// }

		.subTitle {
			color: #2e2e2e;
			font-family: 'nunitosans-bold';
			font-size: 18px;
			line-height: 27px;
		}
		.stripeImage {
			height:40px;
			width:147px;
		}
		.savedPaymentMethodLabel {
			color:  #202224D9;
			font-family: 'nunitosans-semi-bold';
			font-size: 15px;
			line-height: 27px;
		}
		.cardInfo {
			color:  #000000;
			font-family: 'nunitosans-semi-bold';
			font-size: 15px;
			padding-top: 0px;
		}
		.updateCard {
			padding-top: 12px;
		}
		.savebtn {
			padding-top: 13px;
		}
		
		.mutedText {
			color: rgba(46, 46, 46, 0.6);
			font-family: 'nunitosans-semi-bold';
			font-size: 13px;
			line-height: 1;
			padding-bottom: 9px;
			padding-top: 5px;
		}
		.importantBox {
			border: 1px solid rgba(213, 213, 213, 1);
			margin-top:25px;
			border-radius: 4px;
			padding: 18px 6px;
			color: rgba(46, 46, 46, 0.6);
			font-family: 'nunitosans-regular';
			font-size: 13px;
			line-height: 17px;
			.importantWord {
				color: rgba(46, 46, 46, 0.6);
				font-family: 'nunitosans-bold';
				font-size: 13px;
				line-height: 17px;
			}
		}
		.saveCancelBtnWrapper {
			display: flex;
			align-items: end;
			justify-content: flex-end;
			gap: 15px;
			padding-top: 18px;
		}
	}
	.rightSideBox {
		.rightSideBoxTitle {
			color: #2e2e2e;
			font-family: 'nunitosans-bold';
			font-size: 18px;
			line-height: 27px;
			padding-bottom: 14px;
		}
	}
	@media only screen and (max-width: 600px) {
		.leftSideBox {
			.customFormContainer {
				max-width: 100%;
				width: 100%;
			}
            .gapTopPadding {
                padding-top: 10px;
            }
            .cardPadding {
                padding-top: 7px;
            }
             .gapadcolumn {
                padding-bottom: 10px;
            }
			.newFormLabelWrapper {
				.newLabelContainer {
					.newLabel {
                        padding-bottom: 9px;
                        padding-top: 9px;
						font-size: 14px !important;
						line-height: 20px !important;
					}
				}
			}
           .newFormLabelWrapper2 {
                .newLabelContainer {
                   .newLabel {
                        padding-bottom: 0px;
                        padding-top: 8px;
						font-size: 12px !important;
						line-height: 20px !important;
					}
                }
		    }
            StripeInputClone {
                &::placeholder {
                    color: #979797 !important;
                    fontFamily: "nunitosans-semi-bold";
                    fontSize: '12px',
                }
            }
			.newFormInputWrapper {
				.newCustomInputContainer {
					gap: 8px;
					height: 34px;
					padding-inline: 8px;
				}
				.newCustomInput {
					font-size: 12px !important;
					&::placeholder {
						font-size: 12px !important;
                        color: #979797 !important;
                        font-family: 'nunitosans-semi-bold' !important;
					}
				}
				.prefixContainer,
				.suffixContainer {
					font-size: 12px !important;
				}
			}
			/* Override for stripe-input-clone in mobile */
			.newFormInputWrapper.stripe-input-clone {
				.newCustomInputContainer {
					height: 34px !important;
					border: 1px solid #d1d5db !important;
					border-radius: 4px !important;
					padding: 0 12px !important;
					background-color: #ffffff !important;
					gap: 0px !important;
					box-shadow: none !important;
					outline: none !important;
				}
				.newCustomInput {
					font-size: 15px !important;
					color: #979797 !important;
                    font-family: 'nunitosans-semi-bold' !important;
					border: none !important;
					outline: none !important;
					box-shadow: none !important;
					background: transparent !important;
					&::placeholder {
						color: #979797 !important;
						font-size: 11px !important;
                        font-family: 'nunitosans-semi-bold' !important;
					}
				}
			}
			.newFormSelectWrapper {
				.customContainer {
					height: 34px !important;
				}
				.customControl {
					padding-inline: 8px !important;
				}
				.customPlaceholder,
				.customSingleValue {
					font-size: 12px !important;
				}
				.customInput {
					input {
						font-size: 12px !important;
					}
				}
				.customClearIndicator {
					width: 24px !important;
					height: 24px !important;
				}
				.customDropdownIndicator {
					width: 17px !important;
					height: 17px !important;
				}
				.customOption {
					font-size: 12px !important;
				}
				.customNoOptionsMessage {
					font-size: 12px !important;
				}
			}
			.newFormTextareaWrapper {
                    // padding-bottom: 9px !important;
                    padding-top: 9px !important;
				.newCustomTextarea {
					font-size: 12px !important;
					&::placeholder {
						font-size: 12px !important;
					}
					padding: 8px !important;
				}
			}
			.checkboxLabel1 {
				font-size: 12px !important;
			}
			.checkboxLabel2 {
				font-size: 11px !important;
				line-height: 16px !important;
			}
			.newDatePickerWrapper {
				.newCustomDatePickerContainer {
					gap: 8px;
					height: 34px;
					.newCustomDatePicker {
						.react-datepicker-wrapper {
							.react-datepicker__input-container {
								.customDateInput {
									padding-inline: 8px;
									gap: 8px;
									.suffixContainer {
										.dropdownIconWrapper {
											.dropdownIcon {
												width: 17px !important;
												height: 17px !important;
											}
										}
									}
								}
							}
						}
					}
				}
			}
			.newTimePickerWrapper {
				.newCustomTimePickerContainer {
					gap: 8px;
					height: 34px;
					padding-inline: 8px;
				}
				.newCustomInput {
					font-size: 12px !important;
					&::placeholder {
						font-size: 12px !important;
                        font-family: 'nunitosans-semi-bold';
                        color: #979797 !important;

					}
				}
				.suffixContainer {
					.dropdownIconWrapper {
						.dropdownIcon {
							width: 17px !important;
							height: 17px !important;
						}
					}
				}
			}
			.subTitle {
				font-size: 13px;
				line-height: 18px;
			}
			.stripeImage {
				height:30px;
				width:110px;
			}
			.savedPaymentMethodLabel {
				font-size: 11px;
				line-height: 18px;
			}
			.cardInfo {
				font-size: 11px;
				line-height: 18px;
				margin-top: 3px;
				padding-bottom: 0px;
			}
			.updateCard {
				padding-top: 11px;
			}
			.savebtn {
				padding-top: 13px;
			}
			.mutedText {
				font-size: 10px;
				padding-bottom: 7px;
				padding-top: 4px;
			}
			.importantBox {
				padding: 13px 6px;
				margin-top:17px;
				font-size: 10px;
				line-height: 13px;
				.importantWord {
					font-size: 10px;
					line-height: 13px;
				}
			}
			.saveCancelBtnWrapper {
				gap: 11px;
				padding-top: 13px;
			}
			.stripe-element-container {
				height: 26px;
				font-size: 12px;
                padding-inline: 0px;
                padding-top: 13px;
			}

			.stripe-element-container .StripeElement {
				height: 26px;
				font-size: 12px;
			}
			// input[name="card_name"] {
			// 	height: 26px;
			// 	font-size: 12px;
            //     font-family: 'nunitosans-semi-bold';
            //     color: #2e2e2e !important;
            //      &::placeholder {
            //         color: #979797 !important;
            //         font-family: 'nunitosans-semi-bold';
            //         font-size: 12px !important;
            //     }
			// }	
		}
		
		.rightSideBox {
			.rightSideBoxTitle {
				font-size: 13px;
				line-height: 18px;
				padding-bottom: 11px;
			}
		}
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.leftSideBox {
			.customFormContainer {
				max-width: 334px;
				width: 334px;
			}
            .gapTopPadding {
                padding-top: 11px;
            }
            .gapadcolumn {
                padding-bottom: 11px;
            }
            .cardPadding {
                padding-top: 5px;
            }
			.newFormLabelWrapper {
				.newLabelContainer {
					.newLabel {
						font-size: 14px !important;
						line-height: 20px !important;
                        padding-bottom: 7px;
                        padding-top: 11px;
					}
				}
			}
            .newFormLabelWrapper2 {
                .newLabelContainer {
                   .newLabel {
                        font-size: 12px !important;
						line-height: 20px !important;
                        padding-bottom: 0px;
                        padding-top: 8px;
					}
                }
		    }
            // StripeInputClone {
            //     &::placeholder {
            //         color: #979797 !important;
            //         fontFamily: '"nunitosans-semi-bold", sans-serif',
            //         fontSize: '12px',
            //     }
            // }
			.newFormInputWrapper {
				.newCustomInputContainer {
					gap: 8px;
					height: 34px;
					padding-inline: 8px;
				}
				.newCustomInput {
					font-size: 12px !important;
					&::placeholder {
						font-size: 12px !important;
                        font-family: 'nunitosans-semi-bold' !important;
                        color: #979797 !important;
					}
				}
				.prefixContainer,
				.suffixContainer {
					font-size: 12px !important;
				}
			}
			/* Override for stripe-input-clone in tablet */
			.newFormInputWrapper.stripe-input-clone {
				.newCustomInputContainer {
					height: 34px !important;
					border: 1px solid #d1d5db !important;
					border-radius: 4px !important;
					padding: 0 12px !important;
					background-color: #ffffff !important;
					gap: 0px !important;
					box-shadow: none !important;
					outline: none !important;
				}
				.newCustomInput {
					font-size: 15px !important;
					color: #979797 !important;
					font-family: 'nunitosans-semi-bold' !important;
					border: none !important;
					outline: none !important;
					box-shadow: none !important;
					background: transparent !important;
					&::placeholder {
						color: #979797 !important;
						font-size: 15px !important;
						font-family: 'nunitosans-semi-bold' !important;
					}
				}
			}
			.newFormSelectWrapper {
				.customContainer {
					height: 34px !important;
				}
				.customControl {
					padding-inline: 8px !important;
				}
				.customPlaceholder,
				.customSingleValue {
					font-size: 12px !important;
				}
				.customInput {
					input {
						font-size: 12px !important;
					}
				}
				.customClearIndicator {
					width: 24px !important;
					height: 24px !important;
				}
				.customDropdownIndicator {
					width: 17px !important;
					height: 17px !important;
				}
				.customOption {
					font-size: 12px !important;
				}
				.customNoOptionsMessage {
					font-size: 12px !important;
				}
			}
			.newFormTextareaWrapper {
                // padding-bottom: 11px !important;
                padding-top: 11px !important;
				.newCustomTextarea {
					font-size: 12px !important;
					&::placeholder {
						font-size: 12px !important;
					}
					padding: 8px !important;
				}
			}
			.checkboxLabel1 {
				font-size: 12px !important;
			}
			.checkboxLabel2 {
				font-size: 11px !important;
				line-height: 16px !important;
			}
			.newDatePickerWrapper {
				.newCustomDatePickerContainer {
					gap: 8px;
					height: 34px;
					.newCustomDatePicker {
						.react-datepicker-wrapper {
							.react-datepicker__input-container {
								.customDateInput {
									padding-inline: 8px;
									gap: 8px;
									.suffixContainer {
										.dropdownIconWrapper {
											.dropdownIcon {
												width: 17px !important;
												height: 17px !important;
											}
										}
									}
								}
							}
						}
					}
				}
			}
			.newTimePickerWrapper {
				.newCustomTimePickerContainer {
					gap: 8px;
					height: 34px;
					padding-inline: 8px;
				}
				.newCustomInput {
					font-size: 12px !important;
					&::placeholder {
						font-size: 12px !important;
                        font-family: 'nunitosans-semi-bold' !important;
                        color: #979797 !important;
					}
				}
				.suffixContainer {
					.dropdownIconWrapper {
						.dropdownIcon {
							width: 17px !important;
							height: 17px !important;
						}
					}
				}
			}
			.subTitle {
				font-size: 14px;
				line-height: 20px;
			}
			.stripeImage {
				height:35px;
				width:120px;
			}
			.savedPaymentMethodLabel {
				font-size: 12px;
				line-height: 20px;
			}
			.cardInfo {
				font-size: 12px;
				line-height: 20px;
				margin-top: 3px;
			}
			.updateCard {
				padding-top: 11px;
			}
			.savebtn {
				padding-top: 13px;
			}
			.mutedText {
				font-size: 10px;
				padding-bottom: 7px;
				padding-top: 4px;
			}
			.importantBox {
				padding: 14px 6px;
				margin-top:18px;
				font-size: 10px;
				line-height: 13px;
				.importantWord {
					font-size: 10px;
					line-height: 13px;
				}
			}
			.saveCancelBtnWrapper {
				gap: 12px;
				padding-top: 14px;
			}
			.stripe-element-container {
				height: 26px;
				font-size: 12px;
                padding-inline: 0px;
                padding-top: 13px;
			}
			.payment-form {
				// margin-top: 11px;
			}
			.stripe-element-container .StripeElement {
				height: 26px;
				font-size: 12px;
			}
			// input[name="card_name"] {
			// 	height: 26px;
			// 	font-size: 12px;
            //     font-family: 'nunitosans-semi-bold' !important;
            //     color: #2e2e2e !important;
            //      &::placeholder {
            //         color: #979797 !important;
            //         font-family: 'nunitosans-semi-bold' !important;
            //         font-size: 12px !important;
            //     }
			// }	
			.newCustomInput {
				height: 26px;
				font-size: 12px;
				&::placeholder {
					height: 26px;
					font-size: 12px;
                    font-family: 'nunitosans-semi-bold' !important;
                    color: #979797 !important;
				}
			}
		}
		.rightSideBox {
			.rightSideBoxTitle {
				font-size: 14px;
				line-height: 20px;
				padding-bottom: 11px;
			}
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.leftSideBox {
			.customFormContainer {
				max-width: 334px;
				width: 334px;
			}
            .gapTopPadding {
                padding-top: 11px;
            }
            .gapadcolumn {
                padding-bottom: 11px;
            }
            .cardPadding {
                padding-top: 5px;
            }
			.newFormLabelWrapper {
				.newLabelContainer {
					.newLabel {
						font-size: 14px !important;
						line-height: 20px !important;
                        padding-bottom: 11px;
                        padding-top: 11px;
					}
				}
			}
             .newFormLabelWrapper2 {
                .newLabelContainer {
                   .newLabel {
                        font-size: 12px !important;
						line-height: 20px !important;
                        padding-bottom: 0px;
                        padding-top: 9px;
					}
                }
		    }
			.newFormInputWrapper {
				.newCustomInputContainer {
					gap: 8px;
					height: 34px;
					padding-inline: 8px;
				}
				.newCustomInput {
					font-size: 12px !important;
					&::placeholder {
						font-size: 12px !important;
                        font-family: 'nunitosans-semi-bold' !important;
                        color: #979797 !important;
					}
				}
				.prefixContainer,
				.suffixContainer {
					font-size: 12px !important;
				}
			}
            // StripeInputClone {
            //     &::placeholder {
            //         color: #979797 !important;
            //         fontFamily: '"nunitosans-semi-bold", sans-serif',
            //         fontSize: '12px',
            //     }
            // }
			/* Override for stripe-input-clone in desktop */
			.newFormInputWrapper.stripe-input-clone {
				.newCustomInputContainer {
					height: 34px !important;
					border: 1px solid #d1d5db !important;
					border-radius: 4px !important;
					padding: 0 12px !important;
					background-color: #ffffff !important;
					gap: 0px !important;
					box-shadow: none !important;
					outline: none !important;
				}
				.newCustomInput {
					font-size: 15px !important;
					color: #979797 !important;
					font-family: 'nunitosans-semi-bold' !important;
					border: none !important;
					outline: none !important;
					box-shadow: none !important;
					background: transparent !important;
					&::placeholder {
						color: #979797 !important;
						font-size: 15px !important;
						font-family: 'nunitosans-semi-bold' !important;
					}
				}
			}
			.newFormSelectWrapper {
				.customContainer {
					height: 34px !important;
				}
				.customControl {
					padding-inline: 8px !important;
				}
				.customPlaceholder,
				.customSingleValue {
					font-size: 12px !important;
				}
				.customInput {
					input {
						font-size: 12px !important;
					}
				}
				.customClearIndicator {
					width: 24px !important;
					height: 24px !important;
				}
				.customDropdownIndicator {
					width: 17px !important;
					height: 17px !important;
				}
				.customOption {
					font-size: 12px !important;
				}
				.customNoOptionsMessage {
					font-size: 12px !important;
				}
			}
			.newFormTextareaWrapper {
                    // padding-bottom: 11px !important;
                    padding-top: 11px !important; 
				.newCustomTextarea {
					font-size: 12px !important;
					&::placeholder {
						font-size: 12px !important;
					}
					padding: 8px !important;
				}
			}
			.checkboxLabel1 {
				font-size: 12px !important;
			}
			.checkboxLabel2 {
				font-size: 11px !important;
				line-height: 16px !important;
			}
			.newDatePickerWrapper {
				.newCustomDatePickerContainer {
					gap: 8px;
					height: 34px;
					.newCustomDatePicker {
						.react-datepicker-wrapper {
							.react-datepicker__input-container {
								.customDateInput {
									padding-inline: 8px;
									gap: 8px;
									.suffixContainer {
										.dropdownIconWrapper {
											.dropdownIcon {
												width: 17px !important;
												height: 17px !important;
											}
										}
									}
								}
							}
						}
					}
				}
			}
			.newTimePickerWrapper {
				.newCustomTimePickerContainer {
					gap: 8px;
					height: 34px;
					padding-inline: 8px;
				}
				.newCustomInput {
					font-size: 12px !important;
					&::placeholder {
						font-size: 12px !important;
                        font-family: 'nunitosans-semi-bold' !important;
                        color: #979797 !important;
					}
				}
				.suffixContainer {
					.dropdownIconWrapper {
						.dropdownIcon {
							width: 17px !important;
							height: 17px !important;
						}
					}
				}
			}
			.subTitle {
				font-size: 14px;
				line-height: 20px;
			}
			.stripeImage {
				height:35px;
				width:120px;
			}
			.savedPaymentMethodLabel {
				font-size: 12px;
				line-height: 20px;
			}
			.cardInfo {
				font-size: 12px;
				line-height: 20px;
				margin-top: 4px;
			}
			.updateCard {
				padding-top: 11px;
			}
			.savebtn {
				padding-top: 13px;
			}
			.mutedText {
				font-size: 10px;
				padding-bottom: 7px;
				padding-top: 4px;
			}
			.importantBox {
				padding: 14px 6px;
				margin-top:18px;
				font-size: 10px;
				line-height: 13px;
				.importantWord {
					font-size: 10px;
					line-height: 13px;
				}
			}
			.saveCancelBtnWrapper {
				gap: 12px;
				padding-top: 14px;
			}
		}
		.stripe-element-container {
			height: 26px;
			font-size: 12px;
		}
		.payment-form {
			// margin-top: 11px;
		}

		.stripe-element-container .StripeElement {
			height: 26px;
			font-size: 12px;
		}
		// input[name="card_name"] {
		// 	height: 26px;
		// 	font-size: 12px;
        //     font-family: 'nunitosans-semi-bold' !important;
        //     color: #2e2e2e !important;
        //      &::placeholder {
        //             color: #979797 !important;
        //             font-family: 'nunitosans-semi-bold' !important;
        //             font-size: 12px !important;
        //     }
            
		// }	
		.rightSideBox {
			.rightSideBoxTitle {
				font-size: 14px;
				line-height: 20px;
				padding-bottom: 11px;
			}
		}
	}
`;

export const NewFormSelectStylesWrapper = styled.div`
    .newFormSelectWrapper {
        .customContainer {
            height: 34px !important;
        }
        .customControl {
            border: 0.6px solid #d5d5d5 !important;
            padding-inline: 8px !important;
            border-radius: 4px !important;
        }
        .customSingleValue {
            color: #2e2e2e !important;
            font-family: 'nunitosans-semi-bold' !important;
            font-size: 16px !important;
            line-height: 1 !important;
            cursor: pointer !important;
        }
        .customPlaceholder {
            color: #979797 !important;
            font-family: 'nunitosans-semi-bold' !important;
            font-size: 16px !important;
            line-height: 1 !important;
        }
        
        /* Disabled styles */
        &.disabled {
            .customContainer {
                cursor: not-allowed !important;
                pointer-events: none !important;
            }
            
            .customControl {
                cursor: not-allowed !important;
            }
            
            .customSingleValue,
            .customPlaceholder {
                cursor: not-allowed !important;
            }
            
            .customIndicatorsContainer {
                cursor: not-allowed !important;
            }
        }
    }
`;

export const StripeElementWrapper = styled.div`
  .StripeElement {
    font-family: 'Nunito Sans', sans-serif !important;
    font-weight: 500 !important;
    
    &::placeholder {
      font-weight: 500 !important;
    }
  }
`;

/* Disabled date picker styles specific to CreateAds component */

