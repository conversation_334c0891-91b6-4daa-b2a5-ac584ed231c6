import styled from 'styled-components';

const StylesWrraper = styled.div`
	border: 0.6px solid #d5d5d5;
	border-radius: 4px;
	padding: 26px;
	.titleText {
		color: rgba(32, 34, 36, 0.66);
		font-family: 'nunitosans-semi-bold';
		font-size: 16px;
		line-height: 27px;
		padding-bottom: 9px;
	}
	.countText {
		color: rgba(32, 34, 36, 0.85);
		font-family: 'nunitosans-bold';
		font-size: 28px;
		line-height: 1;
	}
	@media (max-width: 600px) {
		padding: 16px;
		.titleText {
			font-size: 12px;
			line-height: 20px;
			padding-bottom: 7px;
		}
		.countText {
			font-size: 21px;
		}
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		padding: 16px;
		.titleText {
			font-size: 12px;
			line-height: 20px;
			padding-bottom: 7px;
		}
		.countText {
			font-size: 21px;
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		padding: 20px;
		.titleText {
			font-size: 12px;
			line-height: 20px;
			padding-bottom: 7px;
		}
		.countText {
			font-size: 21px;
		}
	}
`;

export default StylesWrraper;
