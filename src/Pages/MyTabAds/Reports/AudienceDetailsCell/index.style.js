import styled from 'styled-components';

export const StyleWrapper = styled.div`
    .audience-details-toggle {
        cursor: pointer;
        width: 100%;
        position: relative;
        
        .toggle-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            min-height: 20px;
        }
        
        .audience-text {
            flex: 1;
            font-size: 12px;
            line-height: 16px;
            color: #202224;
            font-family: 'nunitosans-regular';
            
            &.truncated {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                padding-right: 8px; /* Space for dropdown arrow */
                max-width: calc(100% - 20px); /* Reserve space for arrow */
            }
        }
        
        .dropdown-arrow {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            margin-left: 4px;
            opacity: 0.7;
            
            &:hover {
                opacity: 1;
            }
        }
        
        &:hover {
            .audience-text {
                color: #000;
            }
            
            .dropdown-arrow {
                opacity: 1;
            }
        }
    }
    
    .audience-details-menu {
        border: 1px solid #eaeaea;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        padding: 0;
        margin-top: 4px;
        min-width: 200px;
        max-width: 350px;
        z-index: 1000;
        
        .expanded-content {
            padding: 12px 16px;
            font-size: 12px;
            line-height: 18px;
            color: #202224;
            font-family: 'nunitosans-regular';
            white-space: normal;
            word-wrap: break-word;
            word-break: break-word;
        }
        
        &:hover {
            background-color: transparent;
        }
    }
    
    /* Ensure dropdown doesn't interfere with table layout */
    .dropdown {
        position: static;
    }
    
    .dropdown-menu.show {
        position: absolute;
        will-change: transform;
    }
`;
