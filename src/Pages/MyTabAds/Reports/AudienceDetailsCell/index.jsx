import React, { useState } from 'react';
import { Dropdown, DropdownToggle, DropdownMenu } from 'reactstrap';
import { DownArrowIcon, UpArrowIcon } from '../../../../Components/Icons';
import { StyleWrapper } from './index.style';

const AudienceDetailsCell = ({ value }) => {
    const [isOpen, setIsOpen] = useState(false);

    const toggle = () => setIsOpen(prevState => !prevState);

    // If no value or empty string, show dash
    if (!value || value.trim() === '') {
        return <span>-</span>;
    }

    // Check if text is long enough to need truncation (more than ~50 characters)
    const needsTruncation = value.length > 50;

    return (
        <StyleWrapper>
            <Dropdown isOpen={isOpen} toggle={toggle} direction="down">
                <DropdownToggle
                    tag="div"
                    className="audience-details-toggle"
                    data-toggle="dropdown"
                    aria-expanded={isOpen}
                >
                    <div className="toggle-content">
                        <span className={`audience-text ${needsTruncation ? 'truncated' : ''}`}>
                            {value}
                        </span>
                        {needsTruncation && (
                            <span className="dropdown-arrow">
                                {isOpen ? (
                                    <UpArrowIcon height={7} width={12} />
                                ) : (
                                    <DownArrowIcon height={7} width={12} />
                                )}
                            </span>
                        )}
                    </div>
                </DropdownToggle>
                {needsTruncation && (
                    <DropdownMenu className="audience-details-menu">
                        <div className="expanded-content">
                            {value}
                        </div>
                    </DropdownMenu>
                )}
            </Dropdown>
        </StyleWrapper>
    );
};

export default AudienceDetailsCell;
