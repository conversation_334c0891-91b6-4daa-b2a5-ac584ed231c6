import { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import ReactTable from 'react-table-6';
import 'react-table-6/react-table.css';

import DropdownIcon from '../../../../Assets/images/newDropdownIconWOCircle.svg';
import NewSearchBox from '../../../../Components/Form/NewSearchBox/NewSearchBox';
import { SearchIcon } from '../../../../Components/Icons/SearchIcon/SearchIcon';
import NewPopover from '../../../../Components/Common/NewPopover';
import { NewPagination } from '../../../../Components/Common/NewPagination';
import { TableSkeleton, TableCountSkeleton } from './TableSkeleton';
import { PopoverStyleWrraper, StyleWrraper } from './index.style';

import {
    adsPerformanceSortByData,
    adsPerformanceTableColumns
} from '../../utils';

const AdsPerformanceTable = ({
    tableData = [],
    handleSortByChange,
    isAdUser,
    tableParams
}) => {
    const handlePageChange = ({ selected }) => {
        handleSortByChange({
            ...tableParams,
            currentPage: selected + 1
        });
    };

    const handleSearchInputChange = (e) => {
        const term = e?.target?.value;
        handleSortByChange({
            ...tableParams,
            currentPage: 1,
            searchTerm: term
        });
    };

    const handleSortByChangeLocal = (item) => {
        handleSortByChange({
            ...tableParams,
            currentPage: 1,
            sortBy: item
        });
    };

    // Pagination calculation
    const paginatedData = tableData.slice(
        (tableParams.currentPage - 1) * tableParams.pageSize,
        tableParams.currentPage * tableParams.pageSize
    );

    const filteredColumns = useMemo(() => {
        if (isAdUser) {
            return adsPerformanceTableColumns.filter(
                col => col.accessor !== 'invoiceNumber' && col.accessor !== 'budgetAmount'
            );
        }
        return adsPerformanceTableColumns;
    }, [isAdUser]);

    return (
        <StyleWrraper className="pa-t-16">
            <div className="tableCount">
                <div className="leftText">Ads Performance</div>
                <div className="rightText">
                    Showing {paginatedData?.length} of {tableData?.length} ads
                </div>
            </div>

            <div className="borderBox ma-t-16">
                <div className="filterWrapper pa-8">
                    <NewSearchBox
                        formGroupClassName="formGroupClassName"
                        labelClassName="labelClassName"
                        inputGroupTextClassName="inputGroupTextClassName"
                        inputClassName="inputClassName"
                        iconBackgroundClass="iconBackgroundClass"
                        type="text"
                        placeholder="Search ads"
                        icon={<SearchIcon className="inputIcon" />}
                        iconPlacement="start"
                        onChange={handleSearchInputChange}
                    />

                    <NewPopover
                        positions={['bottom', 'left', 'top', 'right']}
                        align="end"
                        onContentClick={(closePopover) => closePopover()}
                        content={
                            <PopoverStyleWrraper>
                                {adsPerformanceSortByData?.length > 0 &&
                                    adsPerformanceSortByData.map((item) => (
                                        <div
                                            key={item?.id}
                                            onClick={() => handleSortByChangeLocal(item)}
                                        >
                                            {item?.name}
                                        </div>
                                    ))}
                            </PopoverStyleWrraper>
                        }
                    >
                        <div className="dropdownWrapper">
                            <span className="dropdownText">
                                {tableParams?.sortBy?.name || 'Sort by'}
                            </span>
                            <img className="dropdownIcon" src={DropdownIcon} alt="dropdown-icon" />
                        </div>
                    </NewPopover>
                </div>

                <div className="tableContainer">
                    <ReactTable
                        columns={filteredColumns}
                        data={paginatedData}
                        showPagination={false}
                        minRows={
                            tableParams?.totalCount > 10 || tableParams?.totalCount === 0 ? 10 : 0
                        }
                        resizable={false}
                        NoDataComponent={() => (
                            <span className="noDataFoundContainer">No data found</span>
                        )}
                        getTrProps={(state, row) => {
                            const lastIndex =
                                tableParams?.totalCount > 10 ? 9 : paginatedData?.length - 1;
                            if (!row || row?.index === lastIndex) {
                                return { style: { borderBottom: 'none' } };
                            }
                            return {};
                        }}
                    />
                </div>
            </div>

            {tableParams?.totalCount > 0 && (
                <div className="paginationWrapper">
                    <NewPagination
                        handlePageChange={handlePageChange}
                        total={tableParams?.totalCount}
                        pageSize={10}
                        currentPage={tableParams?.currentPage}
                    />
                </div>
            )}
        </StyleWrraper>
    );
};

export default AdsPerformanceTable;
