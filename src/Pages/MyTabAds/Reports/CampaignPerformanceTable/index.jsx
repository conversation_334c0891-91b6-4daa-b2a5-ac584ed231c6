import { useEffect, useState, useMemo } from 'react';
import { useSelector } from 'react-redux';
import ReactTable from 'react-table-6';
import 'react-table-6/react-table.css';

import DropdownIcon from '../../../../Assets/images/newDropdownIconWOCircle.svg';
import NewSearchBox from '../../../../Components/Form/NewSearchBox/NewSearchBox';
import { SearchIcon } from '../../../../Components/Icons/SearchIcon/SearchIcon';
import NewPopover from '../../../../Components/Common/NewPopover';
import { NewPagination } from '../../../../Components/Common/NewPagination';
import { TableSkeleton, TableCountSkeleton } from './TableSkeleton';
import { PopoverStyleWrraper, StyleWrraper } from './index.style';
import {
    campaignPerformanceSortByData,
    campaignPerformanceTableColumns
} from '../../utils';

let timeoutVar;

const CampaignPerformanceTable = ({
    campaignData = [],
    loading = false,
    handleSortByChange,
    tableParams,
    totalCount = 0,
    isAdUser
}) => {
    // Calculate paginated data from campaignData prop
    const handlePageChange = ({ selected }) => {
        handleSortByChange({
            ...tableParams,
            currentPage: selected + 1
        });
    };
    const handleSearchInputChange = (e) => {
        if (timeoutVar) {
            clearTimeout(timeoutVar);
        }
        timeoutVar = setTimeout(() => {
            // Handle both event object and direct value
            const term = e?.target?.value || e || '';
            console.log('Final search term:', term);
            handleSortByChange({
                ...tableParams,
                currentPage: 1,
                searchTerm: term
            });
        }, 500);
    };

    const paginatedData = campaignData.slice(
        (tableParams.currentPage - 1) * tableParams.pageSize,
        tableParams.currentPage * tableParams.pageSize
    );

    const filteredColumns = useMemo(() => {
    return isAdUser
        ? campaignPerformanceTableColumns.filter(col => col.accessor !== 'spend')
        : campaignPerformanceTableColumns;
    }, [isAdUser]);


    return (
        <StyleWrraper className="pa-t-16">
            {loading ? (
                <TableCountSkeleton />
            ) : (
                <div className="tableCount">
                    <div className="leftText">Campaign Performance</div>
                    <div className="rightText">
                        Showing {paginatedData?.length || 0} of {campaignData?.length} campaigns
                    </div>
                </div>
            )}
            <div className="borderBox ma-t-16">
                <div className="filterWrapper pa-8">
                    <NewSearchBox
                        formGroupClassName="formGroupClassName"
                        labelClassName="labelClassName"
                        inputGroupTextClassName="inputGroupTextClassName"
                        inputClassName="inputClassName"
                        iconBackgroundClass="iconBackgroundClass"
                        type="text"
                        placeholder="Search campaigns"
                        icon={<SearchIcon className="inputIcon" />}
                        iconPlacement="start"
                        onChange={handleSearchInputChange}
                    />
                    <NewPopover
                        positions={['bottom', 'left', 'top', 'right']}
                        align="end"
                        onContentClick={(closePopover) => closePopover()}
                        content={
                            <PopoverStyleWrraper>
                                {campaignPerformanceSortByData?.length > 0 &&
                                    campaignPerformanceSortByData.map((item) => (
                                        <div
                                            key={item?.id}
                                            onClick={() => handleSortByChange(item)}
                                        >
                                            {item?.name}
                                        </div>
                                    ))}
                            </PopoverStyleWrraper>
                        }
                    >
                        <div className="dropdownWrapper">
                            <span className="dropdownText">
                                {tableParams?.sortBy?.name || 'Sort by'}
                            </span>
                            <img
                                className="dropdownIcon"
                                src={DropdownIcon}
                                alt="dropdown-icon"
                            />
                        </div>
                    </NewPopover>
                </div>
                {loading ? (
                    <>
                        <TableSkeleton />
                    </>
                ) : (
                    <div className="tableContainer">
                        <ReactTable
                            data={paginatedData}
                            columns={filteredColumns}
                            showPagination={false}
                            minRows={
                                tableParams?.totalCount > 10 || tableParams?.totalCount === 0 ? 10 : 0
                            }
                            resizable={false}
                            NoDataComponent={() => (
                                <span className="noDataFoundContainer">
                                    No data found
                                </span>
                            )}
                            getTrProps={(state, row) => {
                                let lastIndex =
                                    totalCount > 10
                                        ? 9
                                        : paginatedData?.length - 1;
                                if (!row || row?.index == lastIndex) {
                                    return {
                                        style: { borderBottom: 'none' }
                                    };
                                }
                                return {};
                            }}
                        />
                    </div>
                )}
            </div>
            {totalCount > 0 && (
                <div className="paginationWrapper">
                    <NewPagination
                        handlePageChange={handlePageChange}
                        total={tableParams?.totalCount}
                        pageSize={10}
                        currentPage={tableParams?.currentPage}
                    />
                </div>
            )}
        </StyleWrraper>
    );
};

export default CampaignPerformanceTable;
