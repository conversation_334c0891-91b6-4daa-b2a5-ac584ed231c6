import styled from 'styled-components';

const StylesWrraper = styled.div`
	.reportBar {
		border: 0.6px solid #d5d5d5;
		border-radius: 4px;
		padding: 26px;
		.titleText {
			color: rgba(32, 34, 36, 0.85);
			font-family: 'nunitosans-bold';
			font-size: 18px;
			line-height: 1;
			padding-bottom: 9px;
		}
		.subTitleText {
			color: rgba(46, 46, 46, 1);
			font-family: 'nunitosans-semi-bold';
			font-size: 16px;
			line-height: 27px;
		}
	}
	.statisticsCardWrapper {
		display: flex;
		gap: 16px;
		@media (max-width: 860px) {
			flex-direction: column;
		}
	}
	@media (max-width: 600px) {
		.reportBar {
			padding: 16px;
			.titleText {
				font-size: 14px;
				padding-bottom: 7px;
			}
			.subTitleText {
				font-size: 12px;
				line-height: 20px;
			}
		}
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.reportBar {
			padding: 16px;
			.titleText {
				font-size: 14px;
				padding-bottom: 7px;
			}
			.subTitleText {
				font-size: 12px;
				line-height: 20px;
			}
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.reportBar {
			padding: 20px;
			.titleText {
				font-size: 14px;
				padding-bottom: 7px;
			}
			.subTitleText {
				font-size: 12px;
				line-height: 20px;
			}
		}
	}
`;

export default StylesWrraper;
