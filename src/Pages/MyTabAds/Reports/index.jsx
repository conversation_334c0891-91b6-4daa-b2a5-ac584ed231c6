import React, { forwardRef, useImperativeHandle, useEffect, useRef, useState, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import fileDownload from 'js-file-download';
import Api from '../../../Helper/Api';
import { useApiRoutes } from '../../../Hooks/useApiRoutes';
import { CommonApiRoutes } from '../../../Utils/routes';

import AdsPerformanceTable from './AdsPerformanceTable';
import CampaignPerformanceTable from './CampaignPerformanceTable';
import StatisticsCard from './StatisticsCard';
import StylesWrraper from './index.style';

const Reports = forwardRef((props, ref) => {
    const state = useSelector((state) => ({ ...state }));
    const [reportsData, setReportsData] = useState({ campaigns: [] });
    const [exportLoading, setExportLoading] = useState(false);
    const [statisticsData, setStatisticsData] = useState({});
    const [loading, setLoading] = useState(false);
    const [adsPerformanceData, setAdsPerformanceData] = useState([]);
    const apiRoutes = useApiRoutes();
    const authDetails = state?.auth;
    console.log(authDetails,"authDetails")
    const isAdUser = authDetails?.login_type === 'advertiser';

    const hasFetched = useRef(false);

    const [tableParams, setTableParams] = useState({
        currentPage: 1,
        pageSize: 10,
        searchTerm: '',
        sortBy: {
            id: 1,
            name: 'Newest - Oldest',
            value: 'newest'
        }
    });

    const [adsTableParams, setAdsTableParams] = useState({
        currentPage: 1,
        pageSize: 10,
        searchTerm: '',
        sortBy: {
            id: 1,
            name: 'Newest - Oldest',
            value: 'newest'
        }
    });


    useImperativeHandle(ref, () => ({
        exportReportData
    }));

    const getReportsData = useCallback(async (params = tableParams) => {
        try {
            setLoading(true);
            let payload = {
                type: authDetails?.login_type,
                created_by_id: authDetails?.id,
                limit: params.pageSize,
                page: params.currentPage,
                sort_by: params.sortBy.value,
                search: params.searchTerm
            };
            const res = await Api('POST', apiRoutes.reportsCampaignList, payload);
            if (res?.data?.status) {
                const campaigns = res?.data?.data?.campaigns || [];
                const totalCount = res?.data?.data?.totalCount || 0;
                setReportsData({ campaigns, totalCount });
            }
        } catch (err) {
            toast.error(err?.message || 'Failed to fetch reports data');
        } finally {
            setLoading(false);
        }
    }, [authDetails?.login_type, authDetails?.id, apiRoutes.reportsCampaignList]);


    const exportReportData = async () => {
        try {
            const res = await Api(
                'POST',
                CommonApiRoutes.exportAdReportTableData,
                {
                    type: "advertiser",
                    created_by_id: authDetails?.id
                }
            );
            
            // Extract the CSV string from the response data
            const csvData = res?.data?.data || res?.data;
            
            fileDownload(csvData, 'Ad_Performance_Report.csv');
            toast.success('Report exported successfully.');
        } catch (err) {
            if (err?.message) {
                toast.error(err?.message);
            }
            throw err;
        }
    };

    const getCampaignPerformanceCount = useCallback(async () => {
        try {
            let payload = {
                type: authDetails?.login_type,
                created_by_id: authDetails?.id
            };
            const res = await Api('POST', apiRoutes.campaignPerformanceCount, payload);
            if (res?.data?.status) {
                setStatisticsData(res?.data?.data);
            }
        } catch (err) {
            toast.error(err?.message || 'Failed to fetch performance count');
        }
    }, [authDetails?.login_type, authDetails?.id, apiRoutes.campaignPerformanceCount]);

    const getAdvertiserPerformanceData = useCallback(async (params = adsTableParams) => {
        try {
            let payload = {
                type: 'advertiser',
                created_by_id: authDetails?.id,
                search: params.searchTerm,
                sort_by: params.sortBy.value,
                limit: params.pageSize,
                page: params.currentPage
            };
            const res = await Api('POST', apiRoutes.advertiserPerformanceList, payload);
            if (res?.data?.status) {
                const adsList = res?.data?.data?.ads || [];
                const formatted = adsList.map((item) => ({
                    title: item?.ad_title,
                    campaign: item?.campaign,
                    status: item?.status,
                    schedule: item?.schedule,
                    objective: item?.objective,
                    budgetAmount: item?.budget_amount,
                    audience: item?.audience,
                    audienceDetails: item?.audience_details,
                    totalImpressions: item?.total_impressions,
                    totalReach: item?.total_reach,
                    totalClicks: item?.total_clicks,
                    CPM: item?.cpm,
                    CPC: item?.cpc,
                    CPA: item?.cpa,
                    CTR: item?.ctr,
                    invoiceNumber: item?.invoice_number,
                    paymentMethod: item?.payment_method
                }));
                setAdsPerformanceData(formatted);
            }
        } catch (err) {
            toast.error(err?.message || 'Failed to fetch advertiser performance data');
        }
    }, [authDetails?.id, apiRoutes.advertiserPerformanceList]);

    // ✅ FIXED: handleSortByChange now supports searchTerm and pagination changes
    const handleSortByChange = useCallback((params) => {
        const newParams = { ...tableParams, ...params };
        setTableParams(newParams);
        getReportsData(newParams);
    }, [tableParams, getReportsData]);

    const handleAdsTableChange = useCallback((newParams) => {
        setAdsTableParams(newParams);
        getAdvertiserPerformanceData(newParams);
    }, [getAdvertiserPerformanceData]);

    useEffect(() => {
        const fetchData = async () => {
            if (!hasFetched.current && authDetails?.id) {
                hasFetched.current = true;
                await Promise.allSettled([
                    getReportsData(),
                    getCampaignPerformanceCount(),
                    getAdvertiserPerformanceData()
                ]);
            }
        };
        fetchData();
    }, [authDetails?.id]);

    return (
        <StylesWrraper>
            <div className="reportBar ma-b-16">
                <div className="titleText">Ad Performance Summary</div>
                <div className="subTitleText">Overview of your campaigns</div>
            </div>

            <div className="statisticsCardWrapper">
                <StatisticsCard
                    className="flex-1"
                    title="Total Campaigns"
                    count={loading ? '...' : statisticsData.campaign_count}
                />
                <StatisticsCard
                    className="flex-1"
                    title="Total Ads"
                    count={loading ? '...' : statisticsData.ads_count}
                />
               {!isAdUser && (
                <StatisticsCard
                    className="flex-1"
                    title="Total Spend"
                    count={loading ? '...' : (statisticsData.total_daily_budget_formatted || '...')}
                />
            )}
            </div>

            <CampaignPerformanceTable
                campaignData={reportsData.campaigns}
                loading={loading}
                handleSortByChange={handleSortByChange}
                tableParams={tableParams}
                totalCount={reportsData.totalCount || 0}
                isAdUser={isAdUser}

            />

            <AdsPerformanceTable
                tableData={adsPerformanceData}
                handleSortByChange={handleAdsTableChange}
                tableParams={adsTableParams}
                isAdUser={isAdUser}
            />
        </StylesWrraper>
    );
});

export default Reports;
