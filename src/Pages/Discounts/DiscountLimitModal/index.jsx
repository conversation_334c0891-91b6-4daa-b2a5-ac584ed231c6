import NewModal from '../../../Components/Common/NewModal';
import { StylesWrapper } from './index.styles';

const DiscountLimitModal = ({
	isOpen,
	closeModal,
	handleSubmitButtonClick
}) => {
	return (
		<NewModal
			isOpen={isOpen}
			toggle={closeModal}
			title={'Automatic discount limit reached'}
			showCancelButton={false}
			submitButtonText="OK"
			handleSubmitButtonClick={handleSubmitButtonClick}
			className={'discountLimitModal'}
		>
			<StylesWrapper>
				You have reached the maximum number of active
				<br /> automatic discounts allowed (10).
				<br />
				To save this new discount, please delete one of your
				<br /> existing automatic discounts first.
			</StylesWrapper>
		</NewModal>
	);
};

export default DiscountLimitModal;
