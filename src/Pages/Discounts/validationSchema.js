import * as Yup from 'yup';

export const discountSchema = Yup.object().shape({
	code: Yup.string().required('Please enter discount code.'),
	discount_value: Yup.number()
		.when('discount_type', {
			is: 'percentage',
			then: Yup.number()
				.positive('Discount percentage must be greater than 0.')
				.required('Please enter discount percentage.')
				.max(100, 'Discount value cannot be more than 100.')
				.typeError('Discount value must be a valid number.')
		})
		.when('discount_type', {
			is: 'fixed',
			then: Yup.number()
				.positive('Discount value must be greater than 0.')
				.required('Please enter discount value.')
				.typeError('Discount value must be a valid number.')
		}),
	isUsageLimitChecked: Yup.string(),
	total_usage_limit: Yup.string().when('isUsageLimitChecked', {
		is: '1',
		then: Yup.string().required('Please enter total usage limit.'),
		otherwise: Yup.string().notRequired()
	}),
	start_date: Yup.date()
		.typeError('Start date must be a valid date.')
		.required('Please select start date.'),
	end_date: Yup.date()
		.nullable()
		.min(Yup.ref('start_date'), 'End date must be after start date')
});
