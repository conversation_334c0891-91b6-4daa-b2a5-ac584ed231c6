import React, { useEffect, useState } from 'react';
import ReactTable from 'react-table-6';
import 'react-table-6/react-table.css';

import TableSkeleton from './TableSkeleton';
import DropdownIcon from '../../../Assets/images/newDropdownIconWOCircle.svg';
import NewSearchBox from '../../../Components/Form/NewSearchBox/NewSearchBox';
import { SearchIcon } from '../../../Components/Icons/SearchIcon/SearchIcon';
import NewPopover from '../../../Components/Common/NewPopover';
import { NewPagination } from '../../../Components/Common/NewPagination';
import { PopoverStyleWrraper, StyleWrraper } from './index.style';
import { sortByData } from '../utils';

const DiscountsTable = ({
	loading,
	tableColumns,
	tableData,
	tableDataCount,
	totalCount,
	currentPage,
	selectedSortBy,
	handleSearchInputChange,
	handleSortByChange,
	handlePageChange,
	handleViewDiscount
}) => {
	return (
		<StyleWrraper>
			<div className="borderBox">
				<div className="filterWrapper pa-8">
					<NewSearchBox
						formGroupClassName="formGroupClassName"
						labelClassName="labelClassName"
						inputGroupTextClassName="inputGroupTextClassName"
						inputClassName="inputClassName"
						iconBackgroundClass="iconBackgroundClass"
						type="text"
						placeholder="Search discounts"
						icon={<SearchIcon className="inputIcon" />}
						iconPlacement="start"
						onChange={handleSearchInputChange}
					/>
					<NewPopover
						positions={['bottom', 'left', 'top', 'right']}
						align="end"
						onContentClick={(closePopover) => {
							closePopover();
						}}
						content={
							<PopoverStyleWrraper>
								{sortByData?.length > 0 &&
									sortByData?.map((item) => {
										return (
											<div
												key={item?.id}
												onClick={() =>
													handleSortByChange(item)
												}
											>
												{item?.name}
											</div>
										);
									})}
							</PopoverStyleWrraper>
						}
					>
						<div className="dropdownWrapper">
							<span className="dropdownText">
								{selectedSortBy?.name
									? selectedSortBy?.name
									: 'Sort by'}
							</span>
							<img
								className="dropdownIcon"
								src={DropdownIcon}
								alt="dropdown-icon"
							/>
						</div>
					</NewPopover>
				</div>
				{loading ? (
					<>
						<TableSkeleton />
					</>
				) : (
					<div className="tableContainer">
						<ReactTable
							columns={tableColumns}
							data={tableData}
							showPagination={false}
							// pageSize={10}
							minRows={
								totalCount > 10 || totalCount == 0 ? 10 : 0
							}
							resizable={false}
							NoDataComponent={() => (
								<span className="noDataFoundContainer">
									You have no active discounts
								</span>
							)}
							getTrProps={(state, row) => {
								let style = { cursor: 'pointer' };
								let lastIndex =
									totalCount > 10 ? 9 : tableDataCount - 1;
								if (!row || row?.index == lastIndex) {
									style.borderBottom = 'none';
								}
								return {
									onClick: () =>
										handleViewDiscount(row?.row?.id),
									style: { ...style }
								};
							}}
						/>
					</div>
				)}
			</div>
			{totalCount > 0 && (
				<div className="paginationWrapper">
					<NewPagination
						handlePageChange={handlePageChange}
						total={totalCount}
						pageSize={10}
						currentPage={currentPage}
					/>
				</div>
			)}
		</StyleWrraper>
	);
};

export default DiscountsTable;
