import React from 'react';
import ReactTable from 'react-table-6';
import 'react-table-6/react-table.css';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

const tableColumns = [
	{
		Header: 'Discount Title',
		accessor: 'title',
		className: 'justify-content-start',
		minWidth: 200,
		// maxWidth: 300,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Discount Phase',
		accessor: 'phase',
		className: 'justify-content-start',
		minWidth: 140,
		maxWidth: 280,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Discount Method',
		accessor: 'discount_method',
		className: 'justify-content-start',
		minWidth: 140,
		maxWidth: 280,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Discount Type',
		accessor: 'discount_type',
		className: 'justify-content-start',
		minWidth: 140,
		maxWidth: 280,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Discount Status',
		accessor: 'is_active',
		className: 'justify-content-center',
		minWidth: 140,
		maxWidth: 140,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-center',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: 'Used',
		accessor: 'usage_count',
		className: 'justify-content-center',
		minWidth: 70,
		maxWidth: 120,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-center',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	},
	{
		Header: '',
		accessor: 'id',
		className: 'justify-content-center',
		minWidth: 40,
		maxWidth: 60,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-center',
		Cell: () => {
			return (
				<div className="w-100">
					<Skeleton height="100%" width={'100%'} />
				</div>
			);
		}
	}
];

const data = Array(10).fill({});

const TableSkeleton = () => {
	return (
		<div className="tableContainer">
			<ReactTable
				columns={tableColumns}
				data={data}
				showPagination={false}
				resizable={false}
				pageSize={10}
				getTrProps={(state, row) => {
					if (row?.index == 9) {
						return {
							style: {
								borderBottom: 'none'
							}
						};
					}
					return {};
				}}
			/>
		</div>
	);
};

export default TableSkeleton;
