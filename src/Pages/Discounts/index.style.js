import styled from 'styled-components';

export const StyleWrraper = styled.div`
	.titleWrapper {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-bottom: 14px;
		.titleButtonWrapper {
			display: flex;
			align-items: center;
			gap: 15px;
		}
	}
	.description {
		font-family: 'nunitosans-regular';
		font-size: 16px;
		line-height: 27px;
		color: #2e2e2e;
	}
	.createButtonWrapper {
		padding-top: 22px;
		padding-bottom: 27px;
	}
	.tableCount {
		border: 1px solid #d5d5d5;
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-inline: 26px;
		height: 73px;
		background: #fbfcff !important;
		.leftText {
			color: #202224d9;
			font-family: 'nunitosans-bold';
			font-size: 16px;
		}
		.rightText {
			color: #20222466;
			font-family: 'nunitosans-regular';
			font-size: 16px;
		}
	}
	.linkWrapper {
		display: flex;
		align-items: center;
		justify-content: center;
		.normalText {
			color: #202224d9;
			font-family: 'nunitosans-regular';
			font-size: 16px;
		}
		.activeText {
			color: #f95c69 !important;
			text-decoration: underline;
			font-family: 'nunitosans-regular';
			font-size: 16px;
			cursor: pointer;
		}
	}

	@media only screen and (max-width: 600px) {
		.titleWrapper {
			padding-bottom: 14px;
		}
		.description {
			font-size: 12px;
			line-height: 20px;
		}
		.createButtonWrapper {
			padding-top: 14px;
			padding-bottom: 18px;
		}
		.tableCount {
			padding-inline: 16px;
			height: 54px;
			.leftText {
				font-size: 12px;
			}
			.leftTextBold {
				font-size: 12px;
			}
			.rightText {
				font-size: 12px;
			}
		}
		.linkWrapper {
			.normalText {
				font-size: 12px;
			}
			.activeText {
				font-size: 12px;
			}
		}
	}

	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.titleWrapper {
			padding-bottom: 14px;
		}
		.description {
			font-size: 12px;
			line-height: 20px;
		}
		.createButtonWrapper {
			padding-top: 16px;
			padding-bottom: 20px;
		}
		.tableCount {
			padding-inline: 20px;
			height: 54px;
			.leftText {
				font-size: 12px;
			}
			.leftTextBold {
				font-size: 12px;
			}
			.rightText {
				font-size: 12px;
			}
		}
		.linkWrapper {
			.normalText {
				font-size: 12px;
			}
			.activeText {
				font-size: 12px;
			}
		}
	}

	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.titleWrapper {
			padding-bottom: 14px;
		}
		.description {
			font-size: 12px;
			line-height: 20px;
		}
		.createButtonWrapper {
			padding-top: 16px;
			padding-bottom: 20px;
		}
		.tableCount {
			padding-inline: 20px;
			height: 54px;
			.leftText {
				font-size: 12px;
			}
			.leftTextBold {
				font-size: 12px;
			}
			.rightText {
				font-size: 12px;
			}
		}
		.linkWrapper {
			.normalText {
				font-size: 12px;
			}
			.activeText {
				font-size: 12px;
			}
		}
	}
`;
