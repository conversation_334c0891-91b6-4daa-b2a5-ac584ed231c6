import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';

import Api from '../../Helper/Api';
import { VenueApiRoutes, VenuePanelRoutes } from '../../Utils/routes';
import { FilledButton } from '../../Components/Layout/Buttons';
import { StyleWrraper } from './index.style';
import DiscountsTable from './DiscountsTable';
import { getDiscountsTableColumns } from './utils';
import { TableCountSkeleton } from './Skeleton';
import NewPageWrapper from '../../Components/Common/NewPageWrapper';
import NewPageTitle from '../../Components/Common/NewPageTitle';
import DeleteConfirmModal from './DeleteConfirmModal';
import DiscountTypeModal from './DiscountTypeModal';
import NewLoader from '../../Components/Common/NewLoader';

let timeoutVar;

const Discounts = () => {
	const state = useSelector((state) => ({ ...state }));
	const navigate = useNavigate();
	const [tableLoading, setTableLoading] = useState(false);
	const [tableData, setTableData] = useState([]);
	const [tableParams, setTableParams] = useState({
		totalCount: 0,
		currentPage: 1,
		pageSize: 10,
		searchTerm: '',
		sortBy: {
			id: 1,
			name: 'Newest - Oldest',
			value: 'newest'
		}
	});
	const [statusChangeLoading, setStatusChangeLoading] = useState(false);
	const [deleteLoading, setDeleteLoading] = useState(false);
	const [deleteModal, setDeleteModal] = useState(false);
	const [selectedDiscountId, setSelectedDiscountId] = useState(null);
	const [discountTypeModal, setDiscountTypeModal] = useState(false);
	const authDetails = state?.auth;
	const selectedVenue = authDetails?.selectedVenue;

	useEffect(() => {
		if (selectedVenue?.id) {
			getTableData();
		}
	}, [
		selectedVenue?.id,
		tableParams?.currentPage,
		tableParams?.pageSize,
		tableParams?.searchTerm,
		tableParams?.sortBy?.value
	]);

	const handlePageChange = ({ selected }) => {
		setTableParams({ ...tableParams, currentPage: selected + 1 });
	};

	const handleSearchInputChange = (e) => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
		}
		timeoutVar = setTimeout(() => {
			setTableParams({
				...tableParams,
				currentPage: 1,
				searchTerm: e?.target?.value
			});
		}, 500);
	};

	const handleSortByChange = (item) => {
		setTableParams({
			...tableParams,
			currentPage: 1,
			sortBy: item
		});
	};

	// Toggle discount status (active/inactive)
	const handleToggle = async (discountId, isChecked) => {
		try {
			setStatusChangeLoading(true);
			const updatedStatus = isChecked ? '1' : '0';
			const payload = {
				bar_id: selectedVenue?.id,
				id: String(discountId),
				is_active: updatedStatus
			};

			const res = await Api('PUT', VenueApiRoutes.editDiscount, payload);

			if (res?.data?.status) {
				await getTableData(false); // Refresh table
				toast.success(
					res.data.message || 'Status updated successfully.'
				);
			} else {
				toast.error(res?.data?.message || 'Failed to update status.');
			}
			setStatusChangeLoading(false);
		} catch (err) {
			toast.error(
				err?.response?.data?.message ||
					'An error occurred while updating status.'
			);
			setStatusChangeLoading(false);
		}
	};

	// Delete selected discount
	const handleDeleteDiscount = async () => {
		if (!selectedDiscountId) return;
		setDeleteLoading(true);
		try {
			const payload = {
				bar_id: selectedVenue?.id,
				id: String(selectedDiscountId)
			};
			const res = await Api(
				'DELETE',
				VenueApiRoutes.deleteDiscount,
				payload
			);
			if (res?.data?.status) {
				if (tableData?.length > 1 || tableParams?.currentPage == 1) {
					await getTableData(false);
				} else {
					let currentPage = Number(tableParams?.currentPage) - 1;
					setTableParams({
						...tableParams,
						currentPage
					});
				}
				toast.success(
					res?.data?.message || 'Discount deleted successfully.'
				);
				setDeleteModal(false);
			} else {
				toast.error(res?.data?.message || 'Failed to delete discount.');
			}
		} catch (err) {
			toast.error(err?.message || 'An error occurred while deleting.');
		} finally {
			setDeleteLoading(false);
		}
	};

	// Fetch discounts table data
	const getTableData = async (isShowLoader = true) => {
		try {
			if (isShowLoader) {
				setTableLoading(true);
			}
			let payload = {
				bar_id: selectedVenue?.id,
				page: tableParams.currentPage,
				limit: tableParams.pageSize,
				sort_by: tableParams?.sortBy?.value
					? tableParams?.sortBy?.value
					: 'newest',
				search: tableParams?.searchTerm ? tableParams?.searchTerm : ''
			};
			const res = await Api(
				'POST',
				VenueApiRoutes.getDiscountList,
				payload
			);
			if (res?.data?.status) {
				setTableData(res?.data?.data?.discounts);
				setTableParams((prev) => ({
					...prev,
					totalCount: res?.data?.data?.pagination?.total || 0
				}));
			} else {
				setTableData([]);
				setTableParams((prev) => {
					return {
						...prev,
						currentPage: 1,
						totalCount: 0
					};
				});
				toast.error(res?.data?.message);
			}
		} catch (err) {
			setTableData([]);
			setTableParams((prev) => {
				return {
					...prev,
					currentPage: 1,
					totalCount: 0
				};
			});
			toast.error(err?.message || 'Failed to fetch discounts');
		} finally {
			if (isShowLoader) {
				setTableLoading(false);
			}
		}
	};

	const handleViewDiscount = async (discountId) => {
		navigate(`/venue/discounts/${discountId}`);
	};

	return (
		<NewPageWrapper>
			<StyleWrraper>
				<div className="titleWrapper">
					<NewPageTitle>Discounts</NewPageTitle>
				</div>
				<div className="description">
					Utilising discounts can be an effective marketing strategy
					to boost venue order sales by rewarding existing customers
					to repeat order or enticing new customers with an offer. You
					can implement discounts through the creation of discount
					codes or automatic discounts for a customer’s order.
					<br />
					Discount codes can be customised to provide various
					incentives, such as percentage reductions or fixed dollar
					discounts. Customers can either enter these codes during
					checkout, or have them applied automatically.
				</div>
				<div className="createButtonWrapper">
					<FilledButton
						buttonText={'Create Discount'}
						background={'rgba(130, 128, 255, 0.2)'}
						color={'rgba(130, 128, 255, 1)'}
						style={{
							width: '160px',
							border: '1px solid rgba(130, 128, 255, 1)'
						}}
						onClick={() =>
							navigate(VenuePanelRoutes?.createNewDiscount)
						}
					/>
				</div>
				{tableLoading ? (
					<TableCountSkeleton />
				) : (
					<div className="tableCount">
						<div className="leftText">
							{tableData.length} discounts
						</div>
						<div className="rightText">
							Showing {tableData.length} of{' '}
							{tableParams.totalCount} discounts
						</div>
					</div>
				)}
				<NewLoader loading={statusChangeLoading}>
					<div className="pa-t-16">
						<DiscountsTable
							loading={tableLoading}
							tableColumns={getDiscountsTableColumns({
								setDeleteModal,
								handleToggle,
								setSelectedDiscountId,
								navigate
							})}
							tableData={tableData}
							tableDataCount={tableData?.length}
							totalCount={tableParams?.totalCount}
							currentPage={tableParams?.currentPage}
							selectedSortBy={tableParams?.sortBy}
							handleSearchInputChange={handleSearchInputChange}
							handleSortByChange={handleSortByChange}
							handlePageChange={handlePageChange}
							handleViewDiscount={handleViewDiscount}
						/>
					</div>
				</NewLoader>
				<div className="linkWrapper pa-t-20">
					<div>
						<span className="normalText">Learn more about </span>
						<span
							className="activeText"
							onClick={() => navigate(VenuePanelRoutes.support)}
						>
							Discounts
						</span>
					</div>
				</div>
				{discountTypeModal && (
					<DiscountTypeModal
						isOpen={discountTypeModal}
						closeModal={() => setDiscountTypeModal(false)}
					/>
				)}
				{deleteModal && (
					<DeleteConfirmModal
						isOpen={deleteModal}
						closeModal={() => setDeleteModal(false)}
						handleDeleteDiscount={handleDeleteDiscount}
						deleteLoading={deleteLoading}
					/>
				)}
			</StyleWrraper>
		</NewPageWrapper>
	);
};

export default Discounts;
