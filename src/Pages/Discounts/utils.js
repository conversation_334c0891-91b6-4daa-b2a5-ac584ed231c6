import { CircleCancelIcon } from '../../Components/Icons/Cancel/CircleCancelIcon';
import { FilledButton } from '../../Components/Layout/Buttons';
import NewFormSwitch from '../../Components/NewForm/NewFormSwitch';
import Switch from '../../Components/Common/Switch';

export const getDiscountsTableColumns = ({
	setDeleteModal,
	handleToggle,
	setSelectedDiscountId,
	navigate
}) => {
	return [
		{
			Header: 'Discount Title',
			accessor: 'title',
			className: 'justify-content-start',
			minWidth: 200,
			// maxWidth: 300,
			filterable: false,
			sortable: false,
			headerClassName: 'justify-content-start',
			Cell: (row) => {
				return (
					<div className="discountTitle">{row?.original?.title}</div>
				);
			}
		},
		{
			Header: 'Discount Phase',
			accessor: 'phase',
			className: 'justify-content-start',
			minWidth: 140,
			maxWidth: 280,
			filterable: false,
			sortable: false,
			headerClassName: 'justify-content-start',
			Cell: (row) => {
				const phase = row?.original?.phase;
				const phaseConfig = {
					active: {
						text: 'Active',
						background: 'rgba(107, 194, 66, 0.2)',
						color: 'rgba(107, 194, 66, 1)'
					},
					expired: {
						text: 'Expired',
						background: 'rgba(151, 151, 151, 0.2)',
						color: 'rgba(151, 151, 151, 1)'
					},
					scheduled: {
						text: 'Scheduled',
						background: 'rgba(244, 143, 0, 0.2)',
						color: 'rgba(244, 143, 0, 1)'
					}
				};

				const config = phaseConfig[phase?.toLowerCase()];

				return config ? (
					<FilledButton
						buttonText={config.text}
						background={config.background}
						color={config.color}
						style={{ height: '2.2em', width: '8.7em' }}
					/>
				) : null;
			}
		},
		{
			Header: 'Discount Method',
			accessor: 'discount_method',
			className: 'justify-content-start',
			minWidth: 140,
			maxWidth: 280,
			filterable: false,
			sortable: false,
			headerClassName: 'justify-content-start'
		},
		{
			Header: 'Discount Type',
			accessor: 'discount_type',
			className: 'justify-content-start',
			minWidth: 140,
			maxWidth: 280,
			filterable: false,
			sortable: false,
			headerClassName: 'justify-content-start'
		},
		{
			Header: 'Discount Status',
			accessor: 'is_active',
			className: 'justify-content-center',
			minWidth: 140,
			maxWidth: 140,
			filterable: false,
			sortable: false,
			headerClassName: 'justify-content-center',
			Cell: (row) => (
				<div
					onClick={(e) => {
						e.stopPropagation();
					}}
				>
					<NewFormSwitch
						name="is_active"
						value={row?.original?.is_active === '1'}
						checked={row?.original?.is_active === '1'}
						onChange={(checked) =>
							handleToggle(row?.original?.id, checked)
						}
					/>
				</div>
			)
		},
		{
			Header: 'Used',
			accessor: 'usage_count',
			className: 'justify-content-center',
			minWidth: 70,
			maxWidth: 120,
			filterable: false,
			sortable: false,
			headerClassName: 'justify-content-center'
		},
		{
			Header: '',
			accessor: 'id',
			className: 'justify-content-center',
			minWidth: 40,
			maxWidth: 60,
			filterable: false,
			sortable: false,
			headerClassName: 'justify-content-center',
			Cell: (row) => {
				return (
					<div
						className="removeIconWrapper"
						onClick={(e) => {
							e.stopPropagation();
							setSelectedDiscountId(row?.original?.id); // Set discount ID
							setDeleteModal(true);
						}}
					>
						<div className="removeIcon">
							<CircleCancelIcon
								width={'100%'}
								height={'100%'}
								className="cursor-pointer"
								fill="transparent"
							/>
						</div>
					</div>
				);
			}
		}
	];
};

export const sortByData = [
	{
		id: 1,
		name: 'Newest - Oldest',
		value: 'newest'
	},
	{
		id: 2,
		name: 'Oldest - Newest',
		value: 'oldest'
	},
	{
		id: 3,
		name: 'Alphabetical A-Z',
		value: 'alphabeticAsc'
	},
	{
		id: 4,
		name: 'Alphabetical Z-A',
		value: 'alphabeticDesc'
	},
	{
		id: 5,
		name: 'Most - Least Used',
		value: 'most_used'
	},
	{
		id: 6,
		name: 'Least - Most Used',
		value: 'least_used'
	}
];

export const discountTypeModalData = [
	{
		id: '1',
		discountType: 'Discount on order',
		discountDescription:
			'Fixed dollar or percentage discount on order total',
		isComingSoon: false
	},
	{
		id: '2',
		discountType: 'Discount on products',
		discountDescription:
			'Fixed dollar or percentage discount on specific categories or products',
		isComingSoon: false
	},
	{
		id: '3',
		discountType: 'Buy A Get B',
		discountDescription: 'Product discount based on a customer’s purchase',
		isComingSoon: true
	},
	{
		id: '4',
		discountType: 'Buy A and B for discount',
		discountDescription:
			'Combined product discount based on a customer’s purchase',
		isComingSoon: true
	}
];

export const months = [
	'January',
	'February',
	'March',
	'April',
	'May',
	'June',
	'July',
	'August',
	'September',
	'October',
	'November',
	'December'
];
