import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useFormik } from 'formik';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import moment from 'moment';

import { discountSchema } from '../validationSchema';
import Api from '../../../Helper/Api';
import { VenueApiRoutes, VenuePanelRoutes } from '../../../Utils/routes';
import NewPageWrapper from '../../../Components/Common/NewPageWrapper';
import NewPageTitle from '../../../Components/Common/NewPageTitle';
import BackIcon from '../../../Assets/images/back-icon.svg';
import { StyleWrraper } from './index.style';
import DiscountOptionCard from './DiscountOptionCard';
import DiscountTypeCard from './DiscountTypeCard';
import CustomerEligibilityCard from './CustomerEligibilityCard';
import DiscountRestrictionsCard from './DiscountRestrictionsCard';
import ScheduleDiscountCard from './ScheduleDiscountCard';
import { FilledButton } from '../../../Components/Layout/Buttons';
import DiscountLimitModal from '../DiscountLimitModal';
import NewLoader from '../../../Components/Common/NewLoader';
import DiscountCombinationCard from './DiscountCombinationCard';

const CreateDiscount = () => {
	const state = useSelector((state) => ({ ...state }));
	console.log('state: ', state);
	const selectedVenue = state?.auth?.selectedVenue;
	const { id } = useParams();
	const navigate = useNavigate();
	const { state: routeState } = useLocation();

	const [submitFormLoading, setSubmitFormLoading] = useState(false);
	const [selectedSegments, setSelectedSegments] = useState([]);
	const [selectedCustomers, setSelectedCustomers] = useState([]);
	const [viewLoading, setViewLoading] = useState(false);
	const [viewOnly, setViewOnly] = useState(false);
	const [viewDetails, setViewDetails] = useState(null);
	const [openDiscountLimitModal, setOpenDiscountLimitModal] = useState(false);

	const submitFormHandler = async (values) => {
		let payload = {
			bar_id: selectedVenue?.id,
			code: values?.code,
			type: values?.type,
			discount_type: values?.discount_type,
			discount_value: values?.discount_value,
			eligibility_type: values?.eligibility_type,
			per_user_limit: values?.per_user_limit,
			combined_eligibility: values?.combined_eligibility,
			is_combined_discount: values?.is_combined_discount,
			start_date: moment(values?.start_date).format('yyyy-MM-DD')
		};
		if (values?.eligibility_type === 'segment_group') {
			if (selectedSegments?.length > 0) {
				payload.segment_ids = selectedSegments?.map((item) => item?.id);
			} else {
				toast.error('Please select atleast one customer segment.');
				return;
			}
		}
		if (values?.eligibility_type === 'individual_users') {
			if (selectedCustomers?.length > 0) {
				payload.user_ids = selectedCustomers?.map(
					(item) => item?.userID
				);
			} else {
				toast.error('Please select atleast one customer.');
				return;
			}
		}
		if (values?.isUsageLimitChecked === '1') {
			payload.total_usage_limit = values?.total_usage_limit;
		}
		if (values?.end_date) {
			payload.end_date = moment(values?.end_date).format('yyyy-MM-DD');
		}
		setSubmitFormLoading(true);
		try {
			const res = await Api('POST', VenueApiRoutes.addDiscount, payload);
			if (res?.data?.status == -3) {
				setOpenDiscountLimitModal(true);
			} else if (res?.data?.status) {
				toast.success(res?.data?.message);
				navigate(VenuePanelRoutes.newDiscounts);
			} else {
				toast.error(res?.data?.message);
			}
		} catch (err) {
			if (err?.message) {
				toast.error(err?.message);
			}
		} finally {
			setSubmitFormLoading(false);
		}
	};

	const formik = useFormik({
		initialValues: {
			type: 'manual',
			code: '',
			discount_type: 'percentage',
			discount_value: '',
			eligibility_type: 'all_users',
			combined_eligibility: '0',
			isUsageLimitChecked: '0',
			total_usage_limit: '',
			per_user_limit: '0',
			is_combined_discount: '0',
			start_date: '',
			end_date: ''
		},
		validationSchema: discountSchema,
		validateOnChange: false,
		validateOnBlur: false,
		onSubmit: submitFormHandler
	});

	const handleSave = async () => {
		await formik.validateForm(formik?.values);
		formik?.handleSubmit();
	};

	const fetchDiscountDetails = async () => {
		try {
			setViewLoading(true);
			const res = await Api('POST', VenueApiRoutes.viewDiscount, {
				discount_id: id,
				bar_id: selectedVenue?.id
			});
			if (res?.data?.status) {
				let data = res?.data?.data;
				formik.setValues({
					type: data?.type,
					code: data?.code,
					discount_type: data?.discount_type,
					discount_value: data?.discount_value,
					eligibility_type: data?.eligibility_type,
					combined_eligibility: data?.combined_eligibility,
					isUsageLimitChecked: data?.total_usage_limit ? '1' : '0',
					total_usage_limit: data?.total_usage_limit,
					per_user_limit: data?.per_user_limit,
					is_combined_discount: data?.is_combined_discount,
					start_date: moment(data?.start_date)?.toDate(),
					end_date: data?.end_date
						? moment(data?.end_date)?.toDate()
						: ''
				});
				setViewDetails(data);
				setViewOnly(true);
			} else {
				toast.error(
					res?.data?.message || 'Failed to fetch discount details'
				);
			}
		} catch (error) {
			toast.error(
				error?.response?.data?.message ||
					'An error occurred while fetching discount details'
			);
		} finally {
			setViewLoading(false);
		}
	};

	useEffect(() => {
		if (id) {
			fetchDiscountDetails();
		}
	}, [id]);

	useEffect(() => {
		if (routeState?.type === 'segment') {
			formik.setFieldValue('eligibility_type', 'segment_group');
			setSelectedSegments([routeState?.selectedItem]);
		}
		if (routeState?.type === 'customer') {
			formik.setFieldValue('eligibility_type', 'individual_users');
			setSelectedCustomers([routeState?.selectedItem]);
		}
	}, [routeState]);
	return (
		<NewPageWrapper>
			<NewLoader loading={viewLoading}>
				<StyleWrraper>
					<div className="titleWrapper">
						<div className="titleTextWrapper">
							<span className="">
								<img
									src={BackIcon}
									alt="back-icon"
									className="backIcon"
									onClick={() =>
										navigate(VenuePanelRoutes?.newDiscounts)
									}
								/>
							</span>
							<NewPageTitle>
								{viewOnly
									? 'View Your Discount'
									: 'Design Your Discount'}
							</NewPageTitle>
						</div>
						<div className="discountTypeText">
							Discount on Order
						</div>
					</div>
					<form>
						<DiscountOptionCard
							formik={formik}
							viewOnly={viewOnly}
						/>
						<DiscountTypeCard formik={formik} viewOnly={viewOnly} />
						<CustomerEligibilityCard
							formik={formik}
							viewOnly={viewOnly}
							selectedSegments={selectedSegments}
							setSelectedSegments={setSelectedSegments}
							selectedCustomers={selectedCustomers}
							setSelectedCustomers={setSelectedCustomers}
							viewDetails={viewDetails}
						/>
						<DiscountRestrictionsCard
							formik={formik}
							viewOnly={viewOnly}
						/>
						<DiscountCombinationCard
							formik={formik}
							viewOnly={viewOnly}
						/>
						<ScheduleDiscountCard
							formik={formik}
							viewOnly={viewOnly}
						/>
						{!viewOnly && (
							<div
								className="d-flex justify-content-end align-items-center"
								style={{ gap: '15px' }}
							>
								<FilledButton
									buttonText={'Cancel'}
									background={'#ffffff'}
									color={'rgba(107, 194, 66, 1)'}
									style={{
										width: '160px',
										border: '1px solid rgba(107, 194, 66, 1)'
									}}
									onClick={() =>
										navigate(VenuePanelRoutes?.newDiscounts)
									}
								/>
								<FilledButton
									buttonText={'Save Discount'}
									type="button"
									background={'rgba(107, 194, 66, 0.2)'}
									color={'rgba(107, 194, 66, 1)'}
									style={{
										width: '160px',
										border: '1px solid rgba(107, 194, 66, 1)'
									}}
									onClick={handleSave}
									loading={submitFormLoading}
								/>
							</div>
						)}
					</form>
					{openDiscountLimitModal && (
						<DiscountLimitModal
							isOpen={openDiscountLimitModal}
							closeModal={() => setOpenDiscountLimitModal(false)}
							handleSubmitButtonClick={() =>
								navigate(VenuePanelRoutes.newDiscounts)
							}
						/>
					)}
				</StyleWrraper>
			</NewLoader>
		</NewPageWrapper>
	);
};

export default CreateDiscount;
