import React from 'react';
import {
	BorderBox,
	CustomContainer,
	DescriptionText,
	HeadingText,
	NewFormErrorMessageStylesWrapper,
	NewFormInputStylesWrapper
} from '../index.style';
import NewFormInput from '../../../../Components/NewForm/NewFormInput';
import Segmented from '../Segmented';
import NewFormErrorMessage from '../../../../Components/NewForm/NewFormErrorMessage';

const DiscountOptionCard = ({ formik, viewOnly }) => {
	const options = [
		{ label: 'Discount code', value: 'manual' },
		{ label: 'Automatic discount', value: 'automatic' }
	];

	return (
		<BorderBox>
			<HeadingText className="pa-b-5">Discount Option</HeadingText>
			<DescriptionText>
				Choose your preferred discount option.
			</DescriptionText>
			<div className="pa-t-10 pa-b-10">
				<Segmented
					options={options}
					value={formik?.values?.type} // Set the value dynamically
					onChange={(value) => {
						formik?.setFieldValue('type', value);
						formik?.setFieldValue('code', '');
					}}
					disabled={viewOnly}
				/>
			</div>
			<>
				<HeadingText className="pa-b-5">
					{formik?.values?.type === 'manual'
						? 'Discount Code'
						: 'Discount Title'}
				</HeadingText>
				<DescriptionText className="pa-b-5">
					{formik?.values?.type === 'manual'
						? 'Customers must enter this code at checkout.'
						: 'This will be displayed to the customer on their final cart screen at checkout.'}
				</DescriptionText>
				<CustomContainer>
					<NewFormInputStylesWrapper>
						<NewFormInput
							name="code"
							placeholder={
								formik?.values?.type === 'manual'
									? 'Example: HELLO10'
									: 'Example: Welcome Offer'
							}
							value={formik?.values?.code}
							onChange={formik?.handleChange}
							wrapperClassName="newFormInputWrapper"
							disabled={viewOnly}
						/>
					</NewFormInputStylesWrapper>
					<NewFormErrorMessageStylesWrapper>
						<NewFormErrorMessage
							className={'newFormErrorMessageWrapper'}
							message={
								formik?.errors?.code
									? formik.values.type === 'manual'
										? 'Please enter discount code.'
										: 'Please enter discount title.'
									: null
							}
						/>
					</NewFormErrorMessageStylesWrapper>
				</CustomContainer>
			</>
		</BorderBox>
	);
};

export default DiscountOptionCard;
