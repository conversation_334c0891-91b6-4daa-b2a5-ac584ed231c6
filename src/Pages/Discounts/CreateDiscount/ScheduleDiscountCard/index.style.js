import styled from 'styled-components';

export const StyleWrraper = styled.div`
	.fieldLabel {
		color: #404040;
		font-family: 'nunitosans-semi-bold';
		font-size: 15px;
		line-height: 27px;
	}
	.fieldWrapper {
		width: 330px;
	}
	.newFormMultiSelectWrapper {
		.customContainer {
			height: 34px !important;
			width: 543px !important;
		}
		.customControl {
			border: 0.6px solid #d5d5d5 !important;
			padding-inline: 8px !important;
			border-radius: 4px !important;
		}
		.customPlaceholder {
			color: #979797 !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
		.customInput {
			input {
				color: #2e2e2e !important;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 16px !important;
				line-height: 1 !important;
			}
		}
		.customDropdownIndicator {
			width: 17px !important;
			height: 17px !important;
		}
		.customMenu {
			margin-top: 2px !important;
			margin-bottom: 2px !important;
			border: 0.6px solid #d5d5d5 !important;
		}
		.customOption {
			gap: 18px;
			padding-inline: 18px;
			padding-block: 10px;
		}
	}
	@media only screen and (max-width: 600px) {
		.fieldLabel {
			font-size: 11px;
			line-height: 20px;
		}
		.fieldWrapper {
			max-width: 248px !important;
			width: 100% !important;
		}
		.newFormMultiSelectWrapper {
			.customContainer {
				height: 34px !important;
				width: 100% !important;
			}
			.customControl {
				padding-inline: 8px !important;
			}
			.customPlaceholder {
				font-size: 12px !important;
				line-height: 1 !important;
			}
			.customInput {
				input {
					font-size: 12px !important;
					line-height: 1 !important;
				}
			}
			.customOption {
				gap: 8px;
				padding-inline: 8px;
				padding-block: 8px;
			}
		}
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.fieldLabel {
			font-size: 11px;
			line-height: 20px;
		}
		.fieldWrapper {
			max-width: 248px !important;
			width: 100% !important;
		}
		.newFormMultiSelectWrapper {
			.customContainer {
				height: 34px !important;
				max-width: 470px !important;
				width: 100% !important;
			}
			.customControl {
				padding-inline: 8px !important;
			}
			.customPlaceholder {
				font-size: 12px !important;
				line-height: 1 !important;
			}
			.customInput {
				input {
					font-size: 12px !important;
					line-height: 1 !important;
				}
			}
			.customOption {
				gap: 8px;
				padding-inline: 8px;
				padding-block: 8px;
			}
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.fieldLabel {
			font-size: 11px;
			line-height: 20px;
		}
		.fieldWrapper {
			max-width: 248px !important;
			width: 100% !important;
		}
		.newFormMultiSelectWrapper {
			.customContainer {
				height: 34px !important;
				max-width: 543px !important;
				width: 100% !important;
			}
			.customControl {
				padding-inline: 8px !important;
			}
			.customPlaceholder {
				font-size: 12px !important;
				line-height: 1 !important;
			}
			.customInput {
				input {
					font-size: 12px !important;
					line-height: 1 !important;
				}
			}
			.customOption {
				gap: 8px;
				padding-inline: 8px;
				padding-block: 8px;
			}
		}
	}
`;
