import React from 'react';
import moment from 'moment';

import NewDatePicker from '../../../../Components/Common/NewDatePicker';
import NewFormErrorMessage from '../../../../Components/NewForm/NewFormErrorMessage';
import {
	BorderBox,
	CustomContainer,
	HeadingText,
	NewDatePickerStylesWrapper,
	NewFormErrorMessageStylesWrapper
} from '../index.style';
import { StyleWrraper } from './index.style';

const ScheduleDiscountCard = ({ formik, viewOnly }) => {
	const handleStartDateChange = (value) => {
		formik?.setFieldValue('start_date', value);
		formik?.setFieldValue('end_date', '');
	};

	return (
		<BorderBox>
			<StyleWrraper>
				<HeadingText className="pa-b-5">
					Schedule the duration of your discount
				</HeadingText>
				<CustomContainer>
					<div
						className="flex align-items-center"
						style={{ gap: '24px' }}
					>
						<div className="flex-1">
							<div className="fieldLabel pa-b-5">Start Date:</div>
							<NewDatePickerStylesWrapper className="fieldWrapper">
								<NewDatePicker
									placeholder={'YYYY-MM-DD'}
									wrapperClassName={'newDatePickerWrapper'}
									value={formik?.values?.start_date}
									onChange={(value) => {
										handleStartDateChange(value);
									}}
									minDate={new Date(new Date().setHours(0, 0, 0, 0))}
									disabled={viewOnly}
								/>
							</NewDatePickerStylesWrapper>
						</div>
						<div className="flex-1">
							<div className="fieldLabel pa-b-5">
								End Date: (optional)
							</div>
							<NewDatePickerStylesWrapper className="fieldWrapper">
								<NewDatePicker
									placeholder={'YYYY-MM-DD'}
									wrapperClassName={'newDatePickerWrapper'}
									value={formik?.values?.end_date}
									onChange={(value) => {
										formik?.setFieldValue(
											'end_date',
											value
										);
									}}
									minDate={
										formik?.values?.start_date
											? moment(
													formik?.values?.start_date
											  ).toDate()
											: ''
									}
									disabled={viewOnly}
								/>
							</NewDatePickerStylesWrapper>
						</div>
					</div>
					<NewFormErrorMessageStylesWrapper>
						<NewFormErrorMessage
							className={'newFormErrorMessageWrapper'}
							message={
								formik?.errors?.start_date
									? formik.errors.start_date
									: null
							}
						/>
					</NewFormErrorMessageStylesWrapper>
				</CustomContainer>
			</StyleWrraper>
		</BorderBox>
	);
};

export default ScheduleDiscountCard;
