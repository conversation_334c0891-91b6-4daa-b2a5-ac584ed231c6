import React from 'react';
import {
	BorderBox,
	CustomContainer,
	DescriptionText,
	HeadingText,
	NewFormSelectStylesWrapper
} from '../index.style';
import NewFormSelect from '../../../../Components/NewForm/NewFormSelect';
import BrowseBox from '../BrowseBox';
import NewFormCheckbox from '../../../../Components/NewForm/NewFormCheckbox';
import { StyleWrraper } from './index.style';

const CustomerEligibilityCard = ({
	formik,
	viewOnly,
	selectedSegments,
	setSelectedSegments,
	selectedCustomers,
	setSelectedCustomers,
	viewDetails
}) => {
	const dropdownOptions = [
		{ label: 'All MyTab Customers', value: 'all_users' },
		{
			label: 'Specific customer segments',
			value: 'segment_group'
		},
		{ label: 'Your customers', value: 'individual_users' }
	];

	return (
		<BorderBox>
			<StyleWrraper>
				<HeadingText className="pa-b-5">
					Customer Eligibility
				</HeadingText>
				<DescriptionText className="pa-b-5">
					Select which customers qualify for this discount
				</DescriptionText>
				<CustomContainer>
					<NewFormSelectStylesWrapper>
						<NewFormSelect
							placeholder="Select"
							wrapperClassName={'newFormSelectWrapper'}
							options={dropdownOptions}
							value={dropdownOptions?.find(
								(option) =>
									option?.value ==
									formik?.values?.eligibility_type
							)}
							onChange={(item) => {
								formik?.setFieldValue(
									'eligibility_type',
									item?.value
								);
								setSelectedSegments([]);
								setSelectedCustomers([]);
							}}
							disabled={viewOnly}
						/>
					</NewFormSelectStylesWrapper>
				</CustomContainer>
				{/* Show BrowseBox only for segment_group or individual_users */}
				{formik?.values?.eligibility_type !== 'all_users' && (
					<div className="pa-t-9">
						<BrowseBox
							selectedDropdownValue={
								formik?.values?.eligibility_type
							}
							selectedSegments={selectedSegments}
							setSelectedSegments={setSelectedSegments}
							selectedCustomers={selectedCustomers}
							setSelectedCustomers={setSelectedCustomers}
							viewOnly={viewOnly}
							viewDetails={viewDetails}
						/>
					</div>
				)}

				{formik?.values?.eligibility_type === 'segment_group' && (

					<div
						className="pa-t-12 flex align-items-start"
						style={{ gap: '9px' }}
					>
						<div className="pa-t-4">
							<NewFormCheckbox
								checked={
									formik?.values?.combined_eligibility === '0'
										? false
										: true
								}
								onChange={(e) => {
									formik?.setFieldValue(
										'combined_eligibility',
										e?.target?.checked ? '1' : '0'
									);
								}}
								disabled={viewOnly}
							/>
						</div>

						<div>
							<div className="checkboxLabelBold">
								Combine customers from chosen segments to offer
								tailored discount
							</div>
							<div className="checkboxLabelSemiBold">
								If selected, only customers who meet the
								requirements for every selected segment will receive
								the discount
							</div>
						</div>

					</div>
				)}
			</StyleWrraper>
		</BorderBox>
	);
};

export default CustomerEligibilityCard;
