import styled from 'styled-components';

export const StyleWrraper = styled.div`
	.checkboxLabelBold {
		color: #2e2e2e;
		font-family: 'nunitosans-bold';
		font-size: 16px;
		line-height: 27px;
	}
	.checkboxLabelSemiBold {
		color: #2e2e2e;
		font-family: 'nunitosans-semi-bold';
		font-size: 16px;
		line-height: 27px;
	}
	@media only screen and (max-width: 600px) {
		.checkboxLabelBold {
			font-size: 12px;
			line-height: 20px;
		}
		.checkboxLabelSemiBold {
			font-size: 12px;
			line-height: 20px;
		}
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.checkboxLabelBold {
			font-size: 12px;
			line-height: 20px;
		}
		.checkboxLabelSemiBold {
			font-size: 12px;
			line-height: 20px;
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.checkboxLabelBold {
			font-size: 12px;
			line-height: 20px;
		}
		.checkboxLabelSemiBold {
			font-size: 12px;
			line-height: 20px;
		}
	}
`;
