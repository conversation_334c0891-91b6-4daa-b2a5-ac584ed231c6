import styled from 'styled-components';

export const StyleWrraper = styled.div`
	.titleWrapper {
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;
		padding-bottom: 14px;
		.titleTextWrapper {
			display: flex;
			align-items: center;
			gap: 10px;
			padding-right: 24px;
			.backIcon {
				display: block;
				width: 28px;
				height: 28px;
				cursor: pointer;
			}
		}
		.discountTypeText {
			flex: 1;
			display: flex;
			justify-content: flex-end;
			white-space: nowrap;
			color: rgba(151, 151, 151, 1);
			font-family: 'nunitosans-bold';
			font-size: 24px;
			line-height: 27px;
		}
	}
	@media only screen and (max-width: 600px) {
		.titleWrapper {
			padding-bottom: 14px;
			.titleTextWrapper {
				.backIcon {
					width: 21px;
					height: 21px;
				}
			}
			.discountTypeText {
				font-size: 16px;
				line-height: 18px;
			}
		}
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.titleWrapper {
			padding-bottom: 14px;
			.titleTextWrapper {
				.backIcon {
					width: 21px;
					height: 21px;
				}
			}
			.discountTypeText {
				font-size: 18px;
				line-height: 20px;
			}
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.titleWrapper {
			padding-bottom: 14px;
			.titleTextWrapper {
				.backIcon {
					width: 21px;
					height: 21px;
				}
			}
			.discountTypeText {
				font-size: 18px;
				line-height: 20px;
			}
		}
	}
`;

export const BorderBox = styled.div`
	border: 0.6px solid rgba(213, 213, 213, 1);
	border-radius: 4px;
	padding: 16px;
	margin-bottom: 15px;
	@media only screen and (max-width: 600px) {
		padding: 11px;
		margin-bottom: 12px;
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		padding: 12px;
		margin-bottom: 12px;
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		padding: 12px;
		margin-bottom: 12px;
	}
`;

export const HeadingText = styled.div`
	color: #2e2e2e;
	font-family: 'nunitosans-bold';
	font-size: 16px;
	line-height: 27px;
	@media only screen and (max-width: 600px) {
		font-size: 12px;
		line-height: 20px;
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		font-size: 12px;
		line-height: 20px;
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		font-size: 12px;
		line-height: 20px;
	}
`;

export const DescriptionText = styled.div`
	color: #404040;
	font-family: 'nunitosans-semi-bold';
	font-size: 15px;
	line-height: 27px;
	@media only screen and (max-width: 600px) {
		font-size: 11px;
		line-height: 20px;
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		font-size: 11px;
		line-height: 20px;
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		font-size: 11px;
		line-height: 20px;
	}
`;

export const CustomContainer = styled.div`
	max-width: 530px;
	width: 530px;
	@media only screen and (max-width: 600px) {
		width: 100%;
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		max-width: 100%;
		width: 398px;
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		max-width: 398px;
		width: 398px;
	}
`;

export const NewFormInputStylesWrapper = styled.div`
	.newFormInputWrapper {
		.newCustomInputContainer {
			gap: 8px;
			border: 0.6px solid #d5d5d5;
			border-radius: 4px;
			height: 34px;
			padding-inline: 8px;
		}
		.newCustomInput {
			color: #2e2e2e !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 16px !important;
			line-height: 1 !important;
			&::placeholder {
				color: #979797 !important;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 16px !important;
				line-height: 1 !important;
			}
		}
		.prefixContainer,
		.suffixContainer {
			color: #979797 !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
		@media (max-width: 600px) {
			.newCustomInputContainer {
				gap: 8px;
				height: 34px;
				padding-inline: 8px;
			}
			.newCustomInput {
				font-size: 12px !important;
				&::placeholder {
					font-size: 12px !important;
				}
			}
			.prefixContainer,
			.suffixContainer {
				font-size: 12px !important;
			}
		}
		@media only screen and (min-width: 601px) and (max-width: 1299px) {
			.newCustomInputContainer {
				gap: 8px;
				height: 34px;
				padding-inline: 8px;
			}
			.newCustomInput {
				font-size: 12px !important;
				&::placeholder {
					font-size: 12px !important;
				}
			}
			.prefixContainer,
			.suffixContainer {
				font-size: 12px !important;
			}
		}
		@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
			.newCustomInputContainer {
				gap: 8px;
				height: 34px;
				padding-inline: 8px;
			}
			.newCustomInput {
				font-size: 12px !important;
				&::placeholder {
					font-size: 12px !important;
				}
			}
			.prefixContainer,
			.suffixContainer {
				font-size: 12px !important;
			}
		}
	}
	.browseBoxSearchInput {
			cursor: pointer !important;
			.newCustomInput {
				cursor: pointer !important;
			}
	}
`;

export const NewFormSelectStylesWrapper = styled.div`
	.newFormSelectWrapper {
		.customContainer {
			height: 34px !important;
		}
		.customControl {
			border: 0.6px solid #d5d5d5 !important;
			padding-inline: 8px !important;
			border-radius: 4px !important;
		}
		.customSingleValue {
			color: #2e2e2e !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 16px !important;
			line-height: 1 !important;
			cursor: pointer !important;
		}
		.customPlaceholder {
			color: #979797 !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
		.customInput {
			input {
				color: #979797 !important;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 16px !important;
				line-height: 1 !important;
			}
		}
		.customClearIndicator {
			width: 24px !important;
			height: 24px !important;
		}
		.customDropdownIndicator {
			width: 17px !important;
			height: 17px !important;
		}
		.customMenu {
			margin-top: 2px !important;
			margin-bottom: 2px !important;
		}
		.customOption {
			color: #2e2e2e !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 16px !important;
			line-height: 1 !important;
			cursor: pointer !important;
		}
		.customOption.isSelected {
			color: #ffffff !important;
		}
		.customNoOptionsMessage {
			color: #979797 !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 16px !important;
		}
		@media (max-width: 600px) {
			.customContainer {
				height: 34px !important;
			}
			.customControl {
				padding-inline: 8px !important;
				cursor: pointer;
			}
			.customPlaceholder,
			.customSingleValue {
				font-size: 12px !important;
			}
			.customInput {
				input {
					font-size: 12px !important;
				}
			}
			.customClearIndicator {
				width: 24px !important;
				height: 24px !important;
			}
			.customDropdownIndicator {
				width: 17px !important;
				height: 17px !important;
			}
			.customOption {
				font-size: 12px !important;
			}
			.customNoOptionsMessage {
				font-size: 12px !important;
			}
		}
		@media only screen and (min-width: 601px) and (max-width: 1299px) {
			.customContainer {
				height: 34px !important;
			}
			.customControl {
				padding-inline: 8px !important;			
				cursor: pointer;
			}
			.customPlaceholder,
			.customSingleValue {
				font-size: 12px !important;
			}
			.customInput {
				input {
					font-size: 12px !important;
				}
			}
			.customClearIndicator {
				width: 24px !important;
				height: 24px !important;
			}
			.customDropdownIndicator {
				width: 17px !important;
				height: 17px !important;
			}
			.customOption {
				font-size: 12px !important;
			}
			.customNoOptionsMessage {
				font-size: 12px !important;
			}
		}
		@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
			.customContainer {
				height: 34px !important;
			}
			.customControl {
				padding-inline: 8px !important;
				cursor: pointer;
			}
			.customPlaceholder,
			.customSingleValue {
				font-size: 12px !important;
			}
			.customInput {
				input {
					font-size: 12px !important;
				}
			}
			.customClearIndicator {
				width: 24px !important;
				height: 24px !important;
			}
			.customDropdownIndicator {
				width: 17px !important;
				height: 17px !important;
			}
			.customOption {
				font-size: 12px !important;
			}
			.customNoOptionsMessage {
				font-size: 12px !important;
			}
		}
	}
`;

export const NewDatePickerStylesWrapper = styled.div`
	.newDatePickerWrapper {
		.newCustomDatePickerContainer {
			gap: 8px;
			border: 0.6px solid #d5d5d5;
			border-radius: 4px;
			height: 34px;
			.newCustomDatePicker {
				.react-datepicker {
					border-radius: 4px !important;
				}
				.react-datepicker-wrapper {
					.react-datepicker__input-container {
						input {
							border-radius: 4px !important;
						}
						.customDateInput {
							padding-inline: 8px;
							gap: 8px;
							.suffixContainer {
								.dropdownIconWrapper {
									.dropdownIcon {
										width: 17px !important;
										height: 17px !important;
									}
								}
							}
						}
					}
				}
			}
		}
	}
	@media (max-width: 600px) {
		.newDatePickerWrapper {
			.newCustomDatePickerContainer {
				gap: 8px;
				height: 34px;
				.newCustomDatePicker {
					.react-datepicker-wrapper {
						.react-datepicker__input-container {
							.customDateInput {
								padding-inline: 8px;
								gap: 8px;
								.suffixContainer {
									.dropdownIconWrapper {
										.dropdownIcon {
											width: 17px !important;
											height: 17px !important;
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.newDatePickerWrapper {
			.newCustomDatePickerContainer {
				gap: 8px;
				height: 34px;
				.newCustomDatePicker {
					.react-datepicker-wrapper {
						.react-datepicker__input-container {
							.customDateInput {
								padding-inline: 8px;
								gap: 8px;
								.suffixContainer {
									.dropdownIconWrapper {
										.dropdownIcon {
											width: 17px !important;
											height: 17px !important;
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.newDatePickerWrapper {
			.newCustomDatePickerContainer {
				gap: 8px;
				height: 34px;
				.newCustomDatePicker {
					.react-datepicker-wrapper {
						.react-datepicker__input-container {
							.customDateInput {
								padding-inline: 8px;
								gap: 8px;
								.suffixContainer {
									.dropdownIconWrapper {
										.dropdownIcon {
											width: 17px !important;
											height: 17px !important;
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
`;

export const NewFormErrorMessageStylesWrapper = styled.div`
	padding-top: 6px;
	.newFormErrorMessageWrapper {
		color: rgb(255, 95, 95) !important;
		font-family: 'nunitosans-semi-bold' !important;
	}
`;
