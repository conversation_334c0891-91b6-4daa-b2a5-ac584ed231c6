import styled from 'styled-components';

export const StylesWrapper = styled.div`
	padding: 4px;
	.searchResultWrapper {
		padding: 16px;
	}
	.noDataText {
		color: rgba(32, 34, 36, 0.5);
		font-family: 'nunitosans-regular';
		font-size: 14px;
		line-height: 19px;
		width: 100%;
		height: 388px;
	}
	@media (max-width: 600px) {
		.noDataText {
			font-size: 12px;
			line-height: 19px;
			height: 262px;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.noDataText {
			font-size: 12px;
			line-height: 19px;
			height: 291px;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.noDataText {
			font-size: 12px;
			line-height: 19px;
			height: 291px;
		}
	}
`;
