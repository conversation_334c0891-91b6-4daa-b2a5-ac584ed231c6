import React, { useState } from 'react';
import NewModal from '../../../../../Components/Common/NewModal';
import { StylesWrapper } from './index.styles';
import SegmentList from '../SegmentList';
import CustomerList from '../CustomerList';

const BrowseModal = ({
	isOpen,
	closeModal,
	selectedDropdownValue,
	selectedSegments,
	setSelectedSegments,
	selectedCustomers,
	setSelectedCustomers
}) => {
	const [currentSelectedSegments, setCurrentSelectedSegments] =
		useState(selectedSegments);
	const [currentSelectedCustomers, setCurrentSelectedCustomers] =
		useState(selectedCustomers);

	let title =
		selectedDropdownValue === 'segment_group'
			? "Specific Customer Segments"
			: "Your Venue's Customers";

	const handleSave = () => {
		if (selectedDropdownValue === 'segment_group') {
			setSelectedSegments(currentSelectedSegments);
		} else {
			setSelectedCustomers(currentSelectedCustomers);
		}
		closeModal();
	};

	return (
		<NewModal
			isOpen={isOpen}
			toggle={closeModal}
			title={title}
			className={'browseModal'}
			submitButtonText="Save"
			cancelButtonText="Discard"
			handleSubmitButtonClick={handleSave} // Ensure function is executed on Save
		>
			<StylesWrapper>
				{selectedDropdownValue === 'segment_group' ? (
					<SegmentList
						currentSelectedSegments={currentSelectedSegments}
						setCurrentSelectedSegments={setCurrentSelectedSegments}
					/>
				) : (
					<CustomerList
						currentSelectedCustomers={currentSelectedCustomers}
						setCurrentSelectedCustomers={
							setCurrentSelectedCustomers
						}
					/>
				)}
			</StylesWrapper>
		</NewModal>
	);
};

export default BrowseModal;
