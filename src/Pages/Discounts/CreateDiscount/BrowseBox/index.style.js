import styled from 'styled-components';

export const StyleWrraper = styled.div`
	.selectedItemWrapper {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 9px;
		padding-top: 9px;
		.selectedItem {
			border: 0.6px solid rgba(213, 213, 213, 1);
			border-radius: 4px;
			display: flex;
			align-items: center;
			gap: 20px;
			height: 34px;
			padding-inline: 8px;
			.selectedItemLabel {
				color: #2e2e2e;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 16px !important;
				line-height: 27px !important;
			}
			.removeIconWrapper {
				display: flex;
				justify-content: center;
				align-items: center;
				.removeIcon {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 15px;
					height: 15px;
					cursor: pointer;
				}
			}
		}
	}
	@media only screen and (max-width: 600px) {
		.selectedItemWrapper {
			.selectedItem {
				gap: 14px;
				height: 26px;
				padding-inline: 5px;
				.selectedItemLabel {
					font-size: 12px !important;
					line-height: 20px !important;
				}
				.removeIconWrapper {
					.removeIcon {
						width: 14px;
						height: 14px;
					}
				}
			}
		}
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.selectedItemWrapper {
			.selectedItem {
				gap: 15px;
				height: 26px;
				padding-inline: 6px;
				.selectedItemLabel {
					font-size: 12px !important;
					line-height: 20px !important;
				}
				.removeIconWrapper {
					.removeIcon {
						width: 14px;
						height: 14px;
					}
				}
			}
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.selectedItemWrapper {
			.selectedItem {
				gap: 15px;
				height: 26px;
				padding-inline: 6px;
				.selectedItemLabel {
					font-size: 12px !important;
					line-height: 20px !important;
				}
				.removeIconWrapper {
					.removeIcon {
						width: 14px;
						height: 14px;
					}
				}
			}
		}
	}
`;
