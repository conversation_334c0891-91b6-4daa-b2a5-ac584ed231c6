import React, { useState } from 'react';
import { CustomContainer, NewFormInputStylesWrapper } from '../index.style';
import NewFormInput from '../../../../Components/NewForm/NewFormInput';
import { SearchIcon } from '../../../../Components/Icons/SearchIcon/SearchIcon';
import { CircleCancelIcon } from '../../../../Components/Icons/Cancel/CircleCancelIcon';
import BrowseModal from './BrowseModal';
import { StyleWrraper } from './index.style';

const BrowseBox = ({
	selectedDropdownValue,
	selectedSegments,
	setSelectedSegments,
	selectedCustomers,
	setSelectedCustomers,
	viewOnly,
	viewDetails
}) => {
	const [isOpenBrowseModal, setIsOpenBrowseModal] = useState(false);

	const handleRemoveItem = (id) => {
		if (selectedDropdownValue === 'segment_group') {
			if (selectedSegments?.length > 0) {
				setSelectedSegments(
					selectedSegments?.filter((item) => item?.id != id)
				);
			}
		} else {
			if (selectedCustomers?.length > 0) {
				setSelectedCustomers(
					selectedCustomers?.filter((item) => item?.userID != id)
				);
			}
		}
	};

	return (
		<>
			{!viewOnly && (
				<CustomContainer>
					<NewFormInputStylesWrapper
						className="flex-1"
						onClick={() => setIsOpenBrowseModal(true)}
					>
						<NewFormInput
							prefix={<SearchIcon width="16px" height="16px" />}
							placeholder={'Search'}
							wrapperClassName={'newFormInputWrapper browseBoxSearchInput'}
							readOnly
						/>
					</NewFormInputStylesWrapper>
				</CustomContainer>
			)}
			<StyleWrraper>
				{!viewOnly ? (
					<>
						{selectedDropdownValue === 'segment_group' &&
							selectedSegments?.length > 0 && (
								<div className="selectedItemWrapper">
									{selectedSegments?.map((item) => {
										return (
											<div
												key={item?.id}
												className="selectedItem"
											>
												<span className="selectedItemLabel">
													{item?.name}
												</span>
												<div
													className="removeIconWrapper"
													onClick={() =>
														handleRemoveItem(
															item?.id
														)
													}
												>
													<div className="removeIcon">
														<CircleCancelIcon
															width={'100%'}
															height={'100%'}
														/>
													</div>
												</div>
											</div>
										);
									})}
								</div>
							)}
						{selectedDropdownValue === 'individual_users' &&
							selectedCustomers?.length > 0 && (
								<div className="selectedItemWrapper">
									{selectedCustomers?.map((item) => {
										return (
											<div
												key={item?.userID}
												className="selectedItem"
											>
												<span className="selectedItemLabel">
													{item?.fullName}
												</span>
												<div
													className="removeIconWrapper"
													onClick={() =>
														handleRemoveItem(
															item?.userID
														)
													}
												>
													<div className="removeIcon">
														<CircleCancelIcon
															width={'100%'}
															height={'100%'}
														/>
													</div>
												</div>
											</div>
										);
									})}
								</div>
							)}
					</>
				) : (
					<>
						{selectedDropdownValue === 'segment_group' &&
							viewDetails?.segments?.length > 0 && (
								<div className="selectedItemWrapper pa-t-0">
									{viewDetails?.segments?.map((item) => {
										return (
											<div
												key={item?.segment_id}
												className="selectedItem"
											>
												<span className="selectedItemLabel">
													{item?.segment_name}
												</span>
											</div>
										);
									})}
								</div>
							)}
						{selectedDropdownValue === 'individual_users' &&
							viewDetails?.users?.length > 0 && (
								<div className="selectedItemWrapper pa-t-0">
									{viewDetails?.users?.map((item) => {
										return (
											<div
												key={item?.user_id}
												className="selectedItem"
											>
												<span className="selectedItemLabel">
													{item?.user_name}
												</span>
											</div>
										);
									})}
								</div>
							)}
					</>
				)}
			</StyleWrraper>
			{isOpenBrowseModal && (
				<BrowseModal
					isOpen={isOpenBrowseModal}
					closeModal={() => setIsOpenBrowseModal(false)}
					selectedDropdownValue={selectedDropdownValue}
					selectedSegments={selectedSegments}
					setSelectedSegments={setSelectedSegments}
					selectedCustomers={selectedCustomers}
					setSelectedCustomers={setSelectedCustomers}
				/>
			)}
		</>
	);
};

export default BrowseBox;
