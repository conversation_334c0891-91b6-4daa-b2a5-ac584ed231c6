import { useMemo } from 'react';
import ReactTable from 'react-table-6';
import 'react-table-6/react-table.css';

import TableSkeleton from './TableSkeleton';
import { StyleWrraper } from './index.style';
import NewFormCheckbox from '../../../../../../Components/NewForm/NewFormCheckbox';

const SegmentsTable = ({
	loading,
	tableData,
	tableDataParentIndexes,
	tableDataCount,
	currentSelectedSegments,
	setCurrentSelectedSegments
}) => {
	const handleCheckboxChange = (segmentItem) => {
		if (
			currentSelectedSegments?.length > 0 &&
			currentSelectedSegments?.some((item) => item?.id == segmentItem?.id)
		) {
			setCurrentSelectedSegments(
				currentSelectedSegments?.filter(
					(item) => item?.id != segmentItem?.id
				)
			);
		} else {
			setCurrentSelectedSegments([
				...currentSelectedSegments,
				segmentItem
			]);
		}
	};

	const checkboxColumn = useMemo(
		() => ({
			id: 'select',
			Header: () => null,
			Cell: ({ row }) => {
				if (row?._original?.isParent == '1') {
					return;
				}
				return (
					<NewFormCheckbox
						checked={currentSelectedSegments?.some(
							(item) => item?.id == row?._original?.id
						)}
						onClick={(e) => e.stopPropagation()}
						onChange={() => handleCheckboxChange(row?._original)}
					/>
				);
			},
			sortable: false
		}),
		[currentSelectedSegments]
	);

	const tableColumns = [
		{
			Header: 'Segment name',
			accessor: 'name',
			className: 'justify-content-start',
			filterable: false,
			sortable: false,
			headerClassName: 'justify-content-start'
		},
		{
			Header: 'Number of customers',
			accessor: 'customerCount',
			className: 'justify-content-end',
			minWidth: 150,
			maxWidth: 170,
			filterable: false,
			sortable: false,
			headerClassName: 'justify-content-end',
			Cell: (row) => {
				if (row?.original?.isParent == 1) {
					return;
				}
				return row?.value;
			}
		}
	];
	return (
		<StyleWrraper>
			{loading ? (
				<>
					<TableSkeleton />
				</>
			) : (
				<div className="tableContainer">
					<ReactTable
						columns={[checkboxColumn, ...tableColumns]}
						data={tableData}
						showPagination={false}
						pageSize={tableDataCount}
						NoDataComponent={() => (
							<span className="rtNoDataFound">No data found</span>
						)}
						resizable={false}
						getTrProps={(state, row) => {
							let params = {};
							let style = {};
							// row before parent row, parent row, last row
							if (
								tableDataParentIndexes?.includes(
									row?.index + 1
								) ||
								row?.original?.isParent == 1 ||
								row?.index == Number(tableDataCount) - 1
							) {
								style.borderBottom = 'none';
							}
							// parent row
							if (row?.original?.isParent == 1) {
								style.backgroundColor =
									'rgba(151, 151, 151, 0.31)';
							} else {
								style.cursor = 'pointer';
								params.onClick = () =>
									handleCheckboxChange(row?.original);
							}
							return {
								...params,
								style: { ...style }
							};
						}}
						getTdProps={(state, row, column) => {
							let params = {};
							let style = {};
							if (column?.id === 'action') {
								style.overflow = 'unset';
							}
							// parent row
							if (row?.original?.isParent == 1) {
								params.className = 'parentRowText';
							} else {
								params.className = 'nonParentRowText';
							}
							return {
								...params,
								style: { ...style }
							};
						}}
					/>
				</div>
			)}
		</StyleWrraper>
	);
};

export default SegmentsTable;
