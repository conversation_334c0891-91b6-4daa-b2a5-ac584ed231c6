import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';

import { StyleWrraper } from './index.style';
import TableCountSkeleton from './TableCountSkeleton';
import Api from '../../../../../Helper/Api';
import { VenueApiRoutes } from '../../../../../Utils/routes';
import { useDebounce } from '../../../../../Hooks/useDebounce';
import { SearchIcon } from '../../../../../Components/Icons/SearchIcon/SearchIcon';
import SegmentsTable from './SegmentsTable';
import NewFormInput from '../../../../../Components/NewForm/NewFormInput';

const SegmentList = ({
	currentSelectedSegments,
	setCurrentSelectedSegments
}) => {
	const state = useSelector((state) => ({ ...state }));
	const [loading, setLoading] = useState(false);
	const [tableData, setTableData] = useState([]);
	const [tableDataCount, setTableDataCount] = useState(null);
	const [segmentsCount, setSegmentsCount] = useState(0);
	const [tableDataParentIndexes, setTableDataParentIndexes] = useState([]);
	const [searchTerm, setSearchTerm] = useState('');
	const debounceSearchTerm = useDebounce(searchTerm, 500);

	const authDetails = state?.auth;
	const selectedVenue = authDetails?.selectedVenue;

	const getTableData = async (payload) => {
		try {
			setLoading(true);
			const res = await Api(
				'POST',
				VenueApiRoutes.getSegmentList,
				payload
			);
			if (res?.data?.status) {
				let parentIndexes = [];
				let tempSegmentsCount = 0;
				if (res?.data?.data?.list?.length > 0) {
					res?.data?.data?.list?.forEach((item, index) => {
						if (Number(item?.isParent) === 1) {
							parentIndexes.push(index);
						} else {
							tempSegmentsCount++;
						}
					});
				}
				setTableData(res?.data?.data?.list);
				setTableDataCount(res?.data?.data?.list?.length);
				setTableDataParentIndexes(parentIndexes);
				setSegmentsCount(tempSegmentsCount);
			} else {
				toast.error(res?.data?.message);
			}
		} catch (err) {
			if (err?.message) toast.error(err?.message);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		(async () => {
			await getTableData({
				bar_id: selectedVenue?.id,
				search: debounceSearchTerm
			});
		})();
	}, [debounceSearchTerm, selectedVenue?.id]);

	return (
		<StyleWrraper>
			{loading ? (
				<TableCountSkeleton />
			) : (
				<div className="tableCount">
					<div className="textOne">{segmentsCount} segments</div>
					<div className="textTwo">
						Showing {segmentsCount} of {segmentsCount} segments
					</div>
				</div>
			)}
			<div className="searchBox ma-t-4">
				<NewFormInput
					prefix={
						<div className="inputSearchIconWrapper">
							<div className="inputSearchIcon">
								<SearchIcon width="100%" height="100%" />
							</div>
						</div>
					}
					placeholder="Search"
					onChange={(event) => setSearchTerm(event?.target?.value)}
					wrapperClassName={'newFormInputWrapper'}
				/>
			</div>
			<div className="ma-t-4">
				<SegmentsTable
					loading={loading}
					tableData={tableData}
					tableDataCount={tableDataCount}
					tableDataParentIndexes={tableDataParentIndexes}
					currentSelectedSegments={currentSelectedSegments}
					setCurrentSelectedSegments={setCurrentSelectedSegments}
				/>
			</div>
		</StyleWrraper>
	);
};

export default SegmentList;
