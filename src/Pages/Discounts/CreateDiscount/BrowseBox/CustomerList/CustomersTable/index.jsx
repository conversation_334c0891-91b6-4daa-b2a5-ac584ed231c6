import { useMemo } from 'react';
import ReactTable from 'react-table-6';
import 'react-table-6/react-table.css';

import TableSkeleton from './TableSkeleton';
import { StyleWrraper } from './index.style';
import NewFormCheckbox from '../../../../../../Components/NewForm/NewFormCheckbox';

const CustomersTable = ({
	loading,
	tableData,
	tableDataCount,
	currentSelectedCustomers,
	setCurrentSelectedCustomers
}) => {
	const handleCheckboxChange = (customerItem) => {
		if (
			currentSelectedCustomers?.length > 0 &&
			currentSelectedCustomers?.some(
				(item) => item?.userID == customerItem?.userID
			)
		) {
			setCurrentSelectedCustomers(
				currentSelectedCustomers?.filter(
					(item) => item?.userID != customerItem?.userID
				)
			);
		} else {
			setCurrentSelectedCustomers([
				...currentSelectedCustomers,
				customerItem
			]);
		}
	};

	const checkboxColumn = useMemo(
		() => ({
			id: 'select',
			Header: () => null,
			Cell: ({ row }) => {
				return (
					<NewFormCheckbox
						checked={currentSelectedCustomers?.some(
							(item) => item?.userID == row?._original?.userID
						)}
						onClick={(e) => e.stopPropagation()}
						onChange={() => handleCheckboxChange(row?._original)}
					/>
				);
			},
			sortable: false
		}),
		[currentSelectedCustomers]
	);

	// Table column definitions
	const tableColumns = [
		{
			Header: 'Customer Name',
			accessor: 'fullName',
			className: 'justify-content-start',
			filterable: false,
			sortable: false,
			headerClassName: 'justify-content-start'
		},
		{
			Header: 'Total Customer Spend',
			accessor: 'totalOrderSpent',
			className: 'justify-content-end',
			filterable: false,
			sortable: false,
			headerClassName: 'justify-content-end',
			minWidth: 120,
			maxWidth: 180,
			Cell: ({ value }) =>
				`$${Number(Number(value || 0).toFixed(2)).toLocaleString(
					'en-US', { minimumFractionDigits: 2 }
				)}`
		}
	];

	return (
		<StyleWrraper>
			{loading ? (
				<TableSkeleton />
			) : (
				<div className="tableContainer">
					<ReactTable
						columns={[checkboxColumn, ...tableColumns]}
						data={tableData}
						showPagination={false}
						pageSize={tableDataCount}
						NoDataComponent={() => (
							<span className="rtNoDataFound">No data found</span>
						)}
						resizable={false}
						getTrProps={(state, row) => {
							let params = {
								onClick: () =>
									handleCheckboxChange(row?.original)
							};
							let style = {
								cursor: 'pointer'
							};
							let lastIndex = tableDataCount - 1;
							if (!row || row?.index == lastIndex) {
								style.borderBottom = 'none';
							}
							return {
								...params,
								style: { ...style }
							};
						}}
					/>
				</div>
			)}
		</StyleWrraper>
	);
};

export default CustomersTable;
