import React from 'react';
import { BorderBox, DescriptionText, HeadingText } from '../index.style';
import NewFormCheckbox from '../../../../Components/NewForm/NewFormCheckbox';
import { StyleWrraper } from './index.style';

const DiscountCombinationCard = ({ formik, viewOnly }) => {
	return (
		<BorderBox>
			<StyleWrraper>
				<HeadingText className="pa-b-5">Combinations</HeadingText>
				<DescriptionText className="pa-b-5">
					Select if this order discount can be combined with other
					discounts
				</DescriptionText>
				<div
					className="flex align-items-center pa-b-10"
					style={{ gap: '9px' }}
				>
					<NewFormCheckbox
						checked={formik?.values?.is_combined_discount == 0}
						onChange={(event) => {
							formik?.setFieldValue(
								'is_combined_discount',
								event?.target?.checked ? '0' : '1'
							);
						}}
						disabled={viewOnly}
					/>
					<div className="checkboxLabel">No</div>
				</div>
				<div className="flex align-items-center" style={{ gap: '9px' }}>
					<NewFormCheckbox
						checked={formik?.values?.is_combined_discount == 1}
						onChange={(event) => {
							formik?.setFieldValue(
								'is_combined_discount',
								event?.target?.checked ? '1' : '0'
							);
						}}
						disabled={viewOnly}
					/>
					<div className="checkboxLabel">Yes</div>
				</div>
			</StyleWrraper>
		</BorderBox>
	);
};

export default DiscountCombinationCard;
