import React from 'react';
import {
	BorderBox,
	DescriptionText,
	HeadingText,
	NewFormErrorMessageStylesWrapper,
	NewFormInputStylesWrapper
} from '../index.style';
import NewFormInput from '../../../../Components/NewForm/NewFormInput';
import NewFormCheckbox from '../../../../Components/NewForm/NewFormCheckbox';
import NewFormErrorMessage from '../../../../Components/NewForm/NewFormErrorMessage';
import { StyleWrraper } from './index.style';

const DiscountRestrictionsCard = ({ formik, viewOnly }) => {
	return (
		<BorderBox>
			<StyleWrraper>
				<HeadingText className="pa-b-5">
					Discount Restrictions
				</HeadingText>
				<DescriptionText className="pa-b-5">
					Select the restrictions you want to set
				</DescriptionText>
				<div
					className="pa-b-10 flex align-items-start"
					style={{ gap: '9px' }}
				>
					<div className="pa-t-4">
						<NewFormCheckbox
							checked={
								formik?.values?.isUsageLimitChecked === '0'
									? false
									: true
							}
							onChange={(event) => {
								formik?.setFieldValue(
									'isUsageLimitChecked',
									event?.target?.checked ? '1' : '0'
								);
								formik?.setFieldValue('total_usage_limit', '');
							}}
							disabled={viewOnly}
						/>
					</div>
					<div>
						<div className="checkboxLabel pa-b-5">
							Limit total number of times this discount can be
							used
						</div>
						{formik?.values?.isUsageLimitChecked == '1' && (
							<>
								<NewFormInputStylesWrapper className="inputWrapper">
									<NewFormInput
										placeholder={'time(s)'}
										wrapperClassName={'newFormInputWrapper'}
										name={'total_usage_limit'}
										value={
											formik?.values?.total_usage_limit
										}
										onChange={(event) => {
											if (
												/^\d*$/.test(
													event?.target?.value
												)
											) {
												formik?.setFieldValue(
													'total_usage_limit',
													event?.target?.value
												);
											}
										}}
										disabled={viewOnly}
									/>
								</NewFormInputStylesWrapper>
								<NewFormErrorMessageStylesWrapper>
									<NewFormErrorMessage
										className={'newFormErrorMessageWrapper'}
										message={
											formik?.errors?.total_usage_limit
												? formik.errors
													.total_usage_limit
												: null
										}
									/>
								</NewFormErrorMessageStylesWrapper>
							</>
						)}
					</div>
				</div>
				<div className="flex align-items-center" style={{ gap: '9px' }}>
					<NewFormCheckbox
						checked={formik?.values?.per_user_limit == 1}
						onChange={(event) => {
							formik?.setFieldValue(
								'per_user_limit',
								event?.target?.checked ? '1' : '0'
							);
						}}
						disabled={viewOnly}
					/>
					<div className="checkboxLabel">
						Limit to one use per customer
					</div>
				</div>
			</StyleWrraper>
		</BorderBox>
	);
};

export default DiscountRestrictionsCard;
