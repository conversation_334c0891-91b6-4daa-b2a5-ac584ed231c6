import styled from 'styled-components';

export const StyleWrraper = styled.div`
	.checkboxLabel {
		color: #2e2e2e;
		font-family: 'nunitosans-semi-bold';
		font-size: 16px;
		line-height: 27px;
	}
	.inputWrapper {
		width: 179px;
	}
	@media only screen and (max-width: 600px) {
		.checkboxLabel {
			font-size: 12px;
			line-height: 20px;
		}
		.inputWrapper {
			width: 121px;
		}
	}
	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.checkboxLabel {
			font-size: 12px;
			line-height: 20px;
		}
		.inputWrapper {
			width: 134px;
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.checkboxLabel {
			font-size: 12px;
			line-height: 20px;
		}
		.inputWrapper {
			width: 134px;
		}
	}
`;
