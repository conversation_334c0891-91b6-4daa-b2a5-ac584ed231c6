import React, { useState, useEffect } from 'react';
import { StyleWrraper } from './index.style';

const Segmented = ({ options, value, onChange, disabled }) => {
	const [selectedItem, setSelectedItem] = useState(value || null);

	useEffect(() => {
		if (value !== selectedItem) {
			setSelectedItem(value);
		}
	}, [value, selectedItem]);

	const handleSegmentedItemClick = (item) => {
		if (disabled) return;
		setSelectedItem(item?.value);
		if (onChange) {
			onChange(item?.value);
		}
	};

	return (
		<StyleWrraper>
			<div className="segmentedContainer">
				{options?.length &&
					options?.map((item, i) => (
						<div
							key={i}
							className={`segmentedItem ${
								item?.value === selectedItem ? 'active' : ''
							}`}
							onClick={() => handleSegmentedItemClick(item)}
							style={{
								cursor: disabled ? 'not-allowed' : 'pointer'
							}} // Change cursor if disabled
						>
							{item?.label}
						</div>
					))}
			</div>
		</StyleWrraper>
	);
};

export default Segmented;
