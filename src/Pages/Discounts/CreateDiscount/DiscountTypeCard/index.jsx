import React from 'react';
import {
	BorderBox,
	CustomContainer,
	DescriptionText,
	HeadingText,
	NewFormErrorMessageStylesWrapper,
	NewFormInputStylesWrapper,
	NewFormSelectStylesWrapper
} from '../index.style';
import NewFormInput from '../../../../Components/NewForm/NewFormInput';
import NewFormSelect from '../../../../Components/NewForm/NewFormSelect';
import NewFormErrorMessage from '../../../../Components/NewForm/NewFormErrorMessage';

const DiscountTypeCard = ({ formik, viewOnly }) => {
	const dropdownOptions = [
		{ label: 'Fixed dollar discount', value: 'fixed' },
		{ label: 'Percentage discount', value: 'percentage' }
	];

	const handleDiscountChange = (e) => {
		let value = e.target.value;
		if (/^\d*\.?\d*$/.test(value)) {
			if ((value.match(/\./g) || []).length > 1)
				value = value.replace(/\.+$/, '');

			value = value.replace(/^(\d+\.?\d{0,2}).*$/, '$1'); // Allow max two decimal places for percentage

			formik?.setFieldValue(e.target.name, value || undefined);
		}
	};

	return (
		<BorderBox>
			<HeadingText className="pa-b-5">
				Discount Type: Discount on Order
			</HeadingText>
			<DescriptionText className="pa-b-5">
				Select between a percentage-based discount or a fixed dollar
				amount discount.
			</DescriptionText>
			<CustomContainer>
				<div className="flex align-items-center" style={{ gap: '8px' }}>
					<div style={{ flex: '7 7 0' }}>
						<NewFormSelectStylesWrapper>
							<NewFormSelect
								placeholder="Select discount method"
								wrapperClassName={'newFormSelectWrapper'}
								options={dropdownOptions}
								value={dropdownOptions?.find(
									(option) =>
										option?.value ==
										formik?.values?.discount_type
								)}
								onChange={(item) => {
									formik?.setFieldValue(
										'discount_type',
										item?.value
									);
								}}
								disabled={viewOnly}
							/>
						</NewFormSelectStylesWrapper>
					</div>

					{/* Discount Value Input */}
					<div style={{ flex: '3 3 0' }}>
						<NewFormInputStylesWrapper>
							<NewFormInput
								name="discount_value"
								placeholder={'0.00'}
								value={formik?.values?.discount_value}
								disabled={viewOnly}
								onChange={handleDiscountChange}
								onBlur={() => {
									if (formik?.values?.discount_value) {
										formik?.setFieldValue(
											'discount_value',
											Number(
												formik?.values
													?.discount_value == '.'
													? 0
													: formik?.values
															?.discount_value
											).toFixed(2)
										);
									}
								}}
								prefix={
									formik?.values?.discount_type === 'fixed'
										? '$'
										: null
								}
								suffix={
									formik?.values?.discount_type ===
									'percentage'
										? '%'
										: null
								}
								wrapperClassName={'newFormInputWrapper'}
							/>
						</NewFormInputStylesWrapper>
					</div>
				</div>
				<NewFormErrorMessageStylesWrapper>
					<NewFormErrorMessage
						className={'newFormErrorMessageWrapper'}
						message={
							formik?.errors?.discount_value
								? formik.errors.discount_value
								: null
						}
					/>
				</NewFormErrorMessageStylesWrapper>
			</CustomContainer>
		</BorderBox>
	);
};

export default DiscountTypeCard;
