import styled from 'styled-components';

export const StylesWrapper = styled.div`
	.discountItem {
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 16px;
		border-bottom: 1px solid rgba(0, 0, 0, 0.17) !important;
		padding-inline: 16px;
		padding-block: 12px;
		cursor: pointer;
		.discountType {
			font-family: 'nunitosans-bold' !important;
			font-size: 16px !important;
			line-height: 27px !important;
			color: rgba(46, 46, 46, 1) !important;
			.comingSoonText {
				font-family: 'nunitosans-bold' !important;
				font-size: 16px !important;
				line-height: 27px !important;
				color: rgba(151, 151, 151, 1) !important;
			}
		}
		.discountDescription {
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 16px !important;
			line-height: 27px !important;
			color: rgba(151, 151, 151, 1) !important;
		}
		.arrowIconWrapper {
			display: flex;
			justify-content: center;
			align-items: center;
			.arrowIcon {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 24px;
				height: 24px;
			}
		}
		&:hover {
			background-color: #f3f3f3;
		}
	}
	.discountItem:last-child {
		border-bottom: none !important;
		&:hover {
			background-color: #f3f3f3;
			border-bottom-left-radius: 16px;
			border-bottom-right-radius: 16px;
		}
	}
	@media (max-width: 600px) {
		.discountItem {
			gap: 11px;
			padding-block: 8px;
			.discountType {
				font-size: 11px !important;
				line-height: 18px !important;
				.comingSoonText {
					font-size: 11px !important;
					line-height: 18px !important;
				}
			}
			.discountDescription {
				font-size: 11px !important;
				line-height: 18px !important;
			}
			.arrowIconWrapper {
				.arrowIcon {
					width: 16px;
					height: 16px;
				}
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.discountItem {
			gap: 12px;
			padding-block: 9px;
			.discountType {
				font-size: 12px !important;
				line-height: 20px !important;
				.comingSoonText {
					font-size: 12px !important;
					line-height: 20px !important;
				}
			}
			.discountDescription {
				font-size: 12px !important;
				line-height: 20px !important;
			}
			.arrowIconWrapper {
				.arrowIcon {
					width: 18px;
					height: 18px;
				}
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.discountItem {
			gap: 12px;
			padding-block: 9px;
			.discountType {
				font-size: 12px !important;
				line-height: 20px !important;
				.comingSoonText {
					font-size: 12px !important;
					line-height: 20px !important;
				}
			}
			.discountDescription {
				font-size: 12px !important;
				line-height: 20px !important;
			}
			.arrowIconWrapper {
				.arrowIcon {
					width: 18px;
					height: 18px;
				}
			}
		}
	}
`;
