import React from 'react';
import NewModal from '../../../Components/Common/NewModal';
import { StylesWrapper } from './index.styles';
import { discountTypeModalData } from '../utils';
import { CircleArrowRight } from '../../../Components/Icons';

const DiscountTypeModal = ({ isOpen, closeModal }) => {
	return (
		<NewModal
			isOpen={isOpen}
			toggle={closeModal}
			title={'Select Your Discount Type'}
			showFooter={false}
			className={'discountTypeModal'}
		>
			<StylesWrapper>
				{discountTypeModalData?.length > 0 &&
					discountTypeModalData?.map((item, index) => (
						<div key={index + 1} className="discountItem">
							<div>
								<div className="discountType">
									{item?.discountType}
									{item?.isComingSoon && (
										<span className="comingSoonText">
											{' (coming soon)'}
										</span>
									)}
								</div>
								<div className="discountDescription">
									{item?.discountDescription}
								</div>
							</div>
							<div className="arrowIconWrapper">
								<div className="arrowIcon">
									<CircleArrowRight
										width={'100%'}
										height={'100%'}
									/>
								</div>
							</div>
						</div>
					))}
			</StylesWrapper>
		</NewModal>
	);
};

export default DiscountTypeModal;
