import NewModal from '../../../Components/Common/NewModal';
import { StylesWrapper } from './index.styles';

const DeleteConfirmModal = ({
	isOpen,
	closeModal,
	handleDeleteDiscount,
	deleteLoading
}) => {
	return (
		<NewModal
			isOpen={isOpen}
			toggle={closeModal}
			title={'Delete discount'}
			cancelButtonText="Cancel"
			submitButtonText="Delete discount"
			handleSubmitButtonClick={handleDeleteDiscount}
			submitButtonLoading={deleteLoading}
		>
			<StylesWrapper>
				Are you sure you would like to delete this discount? This action
				can not be undone.
			</StylesWrapper>
		</NewModal>
	);
};

export default DeleteConfirmModal;
