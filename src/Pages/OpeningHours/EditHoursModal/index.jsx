import { useSelector } from 'react-redux';
import NewModal from '../../../Components/Common/NewModal';
import { StylesWrapper } from './index.style';
import OpeningHoursCard from './OpeningHoursCard';
import React, { useEffect, useState } from 'react';
import moment from 'moment';

const EditHoursModal = ({
    isOpen,
    closeModal,
    handleEditHoursAPI,
    editModalLoading,
    isVenueEdit,
    openingHoursModalData,
    menucategory
}) => {
    const state = useSelector((state) => ({ ...state }));
    const authData = state.auth;
    const [modalData, setModalData] = useState();
    const [renderKey, setRenderKey] = useState(0);

    const handleModalData = (data) => {
        const updatedModalData = [...modalData];

        let index;
        if (updatedModalData?.length > 0) {
            index = updatedModalData?.findIndex(
                (item) => item.weekDay === data.weekDay
            );
            updatedModalData[index] = data;
            setModalData(updatedModalData);
        }
    };

    const convertTo24HourFormat = (time) => {
        if (!time) return "";

        if (/^\d{2}:\d{2}$/.test(time)) {
            return time;
        }

        if (/^(0?[1-9]|1[0-2]):[0-5][0-9]\s?(AM|PM)$/i.test(time)) {
            return moment(time, ["h:mm A"]).format("HH:mm");
        }

        const momentTime = moment(time);

        return momentTime.isValid() ? momentTime.format("HH:mm") : time;
    };

    const handleDoneClick = () => {
        let payload = {}
        if (isVenueEdit) {
            payload = {
                bar_id: authData?.selectedVenue?.id.toString(),
                venueOpeningHours: modalData?.map((item, index) => ({
                    weekDay: index,
                    isClosed: item?.isClosed ? "0" : "1" || "0",
                    timeSlots: item?.timeSlots
                        .filter(time => time.openingHours && time.closingHours)
                        .map((time) => ({
                            openingHours: convertTo24HourFormat(time.openingHours),
                            closingHours: convertTo24HourFormat(time.closingHours)
                        }))
                })),
            };
        } else {
            payload = {
                bar_id: authData?.selectedVenue?.id.toString(),
                subCategoryID: menucategory?.subCategoryID.toString(),
                subCategoryOpeningHours: modalData?.map((item, index) => ({
                    weekDay: index,
                    isClosed: item?.isClosed ? "0" : "1" || "0",
                    timeSlots: item?.timeSlots
                        .filter(time => time.openingHours && time.closingHours)
                        .map((time) => ({
                            openingHours: convertTo24HourFormat(time.openingHours),
                            closingHours: convertTo24HourFormat(time.closingHours)
                        }))
                })),
            };
        }
        handleEditHoursAPI(payload, false);
    };

    useEffect(() => {
        setModalData(openingHoursModalData);
    }, [openingHoursModalData]);

    useEffect(() => {
        setRenderKey(prevKey => prevKey + 1);
    }, [modalData]);

    const handleApplyToAll = (data) => {
        const mondaySlots = data?.timeSlots || [];
        if (mondaySlots.length === 0) return;
        const updatedData = modalData.map(day => ({
            ...day,
            timeSlots: [...mondaySlots]
        }));
        setModalData(updatedData);
    };

    const maxWeekDay = modalData ? Math.max(...modalData.map(item => item.weekDay)) : null;

    return (
        <NewModal
            isOpen={isOpen}
            toggle={closeModal}
            title={isVenueEdit ? 'Venue Opening Hours' : 'Menu Category Opening Hours: ' + menucategory?.subCategoryName}
            cancelButtonText="Cancel"
            submitButtonText="Save"
            handleSubmitButtonClick={handleDoneClick}
            submitButtonLoading={editModalLoading}
            className={'venueOpeningHoursModal'}
        >
            <StylesWrapper>
                <div className='header'>
                    {/* <div className='headerText'> */}
                    <div className='headerText weekDaytxt'>Week Day</div>
                    <div className='headerText leftContent'>Venue Opening Hours</div>
                    {/* </div> */}
                    {/* <div></div>
                    <div></div> */}
                    <div className='headerText venueHours'>Status</div>
                </div>
                <div key={renderKey} className='openingCardBox'>
                    {modalData?.length > 0 &&
                        modalData.map((item, index) => (
                            <React.Fragment key={item?.weekDay ?? index}>
                                <OpeningHoursCard
                                    weekDay={item?.weekDay}
                                    timeSlots={item.timeSlots}
                                    isVenueEdit={isVenueEdit}
                                    isClosed={item?.isClosed}
                                    maxWeekDay={maxWeekDay}
                                    handleApplyToAll={handleApplyToAll}
                                    handleModalData={handleModalData}
                                />
                            </React.Fragment>
                        ))
                    }
                </div>
            </StylesWrapper>
        </NewModal>
    );
};

export default EditHoursModal;