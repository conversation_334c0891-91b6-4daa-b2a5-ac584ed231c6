import styled from 'styled-components';

const ModuleWrapper = styled.div`
	.react-datepicker {
		width: 70px !important;
		border: 1px solid #eaeaea !important;
		background-color: #fff;
		border-radius: 4px !important;
	}

	.react-datepicker__month-container {
		border: none !important;
		.react-datepicker__header {
			padding-top: 0;
			border: none !important;
			background-color: #fff;
		}
		.react-datepicker__day-names {
			padding-top: 5px;
		}
		.react-datepicker__day-name {
			font-weight: 600;
			color: ${(props) => props.layoutTheme.buttonColor3} !important;
		}
		.react-datepicker__current-month,
		.react-datepicker__current-month--hasMonthDropdown {
			display: none;
		}
		.react-datepicker__year-dropdown-container--scroll {
			margin: 0 25px;
		}
		.react-datepicker__month-read-view--down-arrow,
		.react-datepicker__year-read-view--down-arrow {
			top: 3px !important;
			border-color: #000 !important;
			border-width: 2px 2px 0 0 !important;
		}
		.react-datepicker__day--keyboard-selected,
		.react-datepicker__month-read-view--selected-month,
		.react-datepicker__year-read-view--selected-year,
		.react-datepicker__month-option,
		.react-datepicker__year-option {
			color: ${(props) => props.layoutTheme.textColor};
			background-color: #fff;
			font-weight: 600;
		}
		.react-datepicker__year-select,
		.react-datepicker__month-select {
			color: ${(props) => props.layoutTheme.textColor};
			background-color: #fff;
			font-weight: 600;
			padding: 5px 0 3px;
			border: none;
			outline: none;
		}
		.react-datepicker__month-read-view,
		.react-datepicker__year-read-view {
			visibility: visible !important;
		}
		.react-datepicker__month-dropdown,
		.react-datepicker__year-dropdown {
			background-color: #fff;
		}
		.react-datepicker__day--selected,
		.react-datepicker__day--in-selecting-range,
		.react-datepicker__day--in-range {
			background-color: #fd6461 !important;
			color: #fff !important;
			border-radius: 50%;
		}
		.react-datepicker__day {
			color: ${(props) => props.layoutTheme.textColor};
		}
	}

	.react-datepicker__navigation-icon--next::before,
	.react-datepicker__navigation-icon--previous::before {
		border-color: #000 !important;
		top: 18px;
	}

	.react-datepicker-wrapper input {
		background-color: #fff !important;
		color: #202224 !important;
		font-family: 'nunitosans-medium' !important;
		width: 7em;
		height: 2.4em;
		border-radius: 3px !important;
		font-size: 10px !important;

		&::placeholder {
			color: ${(props) => props.layoutTheme.placeHolderColor} !important;
			font-family: 'nunitosans-medium' !important;
			font-weight: 500 !important;
			font-size: 10px !important;
			opacity: 1;
		}
		&:focus {
			border-radius: 4px !important;
			border: 1px solid #eaeaea !important;
			outline: none !important;
			box-shadow: none !important;
		}
	}

	.react-datepicker__header--time,
	.react-datepicker__triangle {
		display: none;
	}

	.react-datepicker-popper {
		padding-top: 0;
	}

	.react-datepicker__time-list {
		font-family: 'nunitosans-medium' !important;
		font-size: 10px !important;
		scrollbar-width: thin;
		scrollbar-color: rgba(0, 0, 0, 0.2) #fff;

		::-webkit-scrollbar {
			width: 2px;
		}
		::-webkit-scrollbar-thumb {
			background-color: rgba(0, 0, 0, 0.2);
			border-radius: 3px !important;
		}
		::-webkit-scrollbar-track {
			background-color: #fff;
			border-radius: 18px !important;
		}
	}

	.react-datepicker__time-container {
		width: 105px !important;
		border-radius: 4px !important;
		margin-right: -14px;
		background-color: #fff;
		overflow: hidden;

		.react-datepicker__time {
			width: 70px !important;
			border-radius: 4px !important;

			.react-datepicker__time-box {
				width: 98px !important;
				background-color: #fff;
				overflow: hidden;

				ul.react-datepicker__time-list {
					width: 100px !important;
					min-width: 100px !important;
					max-width: 100px !important;
					border: 1px solid #eaeaea !important;
					border-radius: 4px !important;
					overflow-x: hidden;
					background-color: #fff;

					li.react-datepicker__time-list-item {
						padding: 4px;
						height: 24px !important;
						font-family: 'nunitosans-medium' !important;
						color: #202224 !important;
						display:flex !important;
						justify-content:center !important;
						align-items: center !important;
						
						&--selected {
							background-color: #fd6461 !important;
							color: white !important;
							border-radius: 3px !important;
						}
						&:hover {
							background-color: #e5e5e5 !important;
							border-radius: 3px !important;
							color: white !important;
							cursor: pointer;
						}
					}
				}
			}
		}
	}

	.circleCancelIconWraper {
		display: flex;
		align-items: center;
		justify-content: center;
		top: 6px !important;
		bottom: 6px !important;
		right: 2px !important;
		.circleCancelIcon {
			height: 19px !important;
			width: 19px !important;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	span,
	.customeButton {
		font-size: 14px !important;
		font-family: 'nunitosans-medium' !important;
	}

	.customeButton {
		border: 0.6px solid #d5d5d5;
		border-radius: 4px;
		// padding: 3px;
		background: #ffffff;
		text-align: start;
		width: 7em;
		height: 2.4em;
		// padding: 1px 0px 0px 1px !important;
		color: #202224 !important;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	@media (max-width: 600px) {
		.circleCancelIconWraper {
			top: 6px !important;
			bottom: 6px !important;
			right: 2px !important;
			.circleCancelIcon {
				width: 11px !important;
				height: 11px !important;
			}
		}
		.react-datepicker__time-container {
			width: 86px !important;
			.react-datepicker__time {
				.react-datepicker__time-box {
					width: 66px !important;
					ul.react-datepicker__time-list {
						width: 68px !important;
						min-width: 68px !important;
						max-width: 68px !important;
					}
				}
			}
		}
		span,
		.customeButton {
			font-size: 9px !important;
			font-family: 'nunitosans-medium' !important;
		}
	}

	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.circleCancelIconWraper {
		  bottom: 6px !important;
		  top: 6px !important;
		  right: 2px !important;
			.circleCancelIcon {
				width: 15px !important;
				height: 15px !important;
			}
		}
		.react-datepicker__time-container {
			width: 86px !important;
			.react-datepicker__time {
				.react-datepicker__time-box {
					width: 76px !important;
					ul.react-datepicker__time-list {
						width: 79px !important;
						min-width: 79px !important;
						max-width: 79px !important;
					}
				}
			}
		}
		span,
		.customeButton {
			font-size: 10px !important;
			font-family: 'nunitosans-medium' !important;
		}
	}

	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.circleCancelIconWraper {
			top: 6px !important;
			bottom: 6px !important;
			right: 2px !important;
			.circleCancelIcon {
				width: 15px !important;
				height: 15px !important;
			}
		}

		.react-datepicker__time-container {
			width: 86px !important;
			.react-datepicker__time {
				.react-datepicker__time-box {
					width: 76px !important;
						ul.react-datepicker__time-list {
							width: 79px !important;
							min-width: 79px !important;
							max-width: 79px !important;
						}
				}
			}
		}
		span,
		.customeButton {
			font-size: 10px !important;
			font-family: 'nunitosans-medium' !important;
		}
	}

`;

export default ModuleWrapper;
