import { forwardRef, useRef } from 'react';
import { useSelector } from 'react-redux';
import DatePicker from 'react-datepicker';
import ModuleWrapper from './customTimePicker.style';
import { CircleArrowDown } from '../../../Components/Icons';
import 'react-datepicker/dist/react-datepicker.css';

const CustomTimePicker = ({
	date,
	handleDateChange,
	customClassName,
	icon,
	iconClassName,
	valueContainer,
	portalId,
	timeCaption,
	placeholderText = 'HH:MM A',
	...props
}) => {
	const timePicker = useRef(null);
	const allThemeData = useSelector((state) => ({ ...state.themeChanger }));

	const CustomInput = forwardRef(({ value, onClick, className }, ref) => (
		<button type="button" className='customeButton' onClick={onClick} ref={ref}>
			{value ? (
				<span className="valueContainer">{value}</span>
			) : (
				<span className="placeholderContainer">{placeholderText}</span>
			)}
			{/* {suffix && <div className="suffixContainer">{suffix}</div>} */}

			{icon && (
				<div className="circleCancelIconWraper">
					<div className='circleCancelIcon'>
						<CircleArrowDown
							width="100%"
							height="100%"
						/>
					</div>
				</div>
			)}
		</button>
	));

	return (
		<ModuleWrapper {...allThemeData}>
			<div className="position-relative">
				<DatePicker
					selected={date}
					onChange={handleDateChange}
					placeholderText={placeholderText}
					ref={timePicker}
					popperPlacement="bottom"
					popperProps={{ strategy: 'fixed' }}
					portalId={portalId ?? ''}
					className={`inputBox plr-3 todoDatePicker flex-4 zIndex-3 
						${customClassName ? customClassName : ''}`}
					timeCaption={timeCaption ?? 'Time'}
					dateFormat="hh:mm aa"
					timeFormat="hh:mm aa"
					showTimeSelect
					showTimeSelectOnly
					customInput={
						<CustomInput className="customDateInput" />
					}
					{...props}
				/>
			</div>
		</ModuleWrapper>
	);
};

export default CustomTimePicker;
