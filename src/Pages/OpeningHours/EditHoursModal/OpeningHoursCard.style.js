import styled from 'styled-components';

export const StylesWrapper = styled.div`
	font-family: 'nunitosans-medium' !important;
	font-size: 16px !important;
	line-height: 1.3 !important;
	color: #2e2e2e !important;
	max-height: 100%;
	overflow: auto;

	.opening-hours-row {
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 10px;
		width: 100%;
		padding-inline: 15px 20px !important;
	}
	.weekday {
		min-width: 90px !important;
		font-family: 'nunitosans-semi-bold';
		font-size: 16px !important;
		line-height: 27px;
	}

	.weekdaySecond {
		min-width: 97px !important;
		font-family: 'nunitosans-semi-bold';
		font-size: 16px !important;
		line-height: 27px;
	}

	.time-picker {
		font-family: 'nunitosans-regular';
		font-size: 13px;
		padding-bottom: 10px;
	}
	.time-text {
		font-size: 13px;
		color: #000000;
		font-family: 'nunitosans-regular';
	}
	.button {
		// min-width: 140px;
		// height: 2.8em;
		// flex: 1;
	}

	.switch-container {
		text-align: center;
	}

	.switchClass {
		.openSwitchText {
			font-family: nunitosans-medium;
			position: absolute;
			font-size: 10px;
		}
	}

	.removeIconWrapper {
		display: flex;
		justify-content: center;
		align-items: center;
		padding-top: 2px;
		margin-left: 13px;
		.removeIconImg {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 20px;
			height: 20px;
			cursor: pointer;
		}
	}

	.customeHrClass {
		border-top: 1px solid rgba(148, 150, 152, 0.5) !important;
		margin-bottom: 10px;
	}

	.customeButton {
		width: 105px !important;
		height: 35px !important;
		border: 0.6px solid #d5d5d5 !important;
		cursor: pointer;
	}
	.valueContainer {
		font-family: 'nunitosans-semi-bold';
		font-size: 14px !important;
		line-height: 21px !important;
		color: #202224;
	}
	.filledButtonNewTheme {
		width: 105px;
		height: 35px;
		border: 1px solid rgb(130, 128, 255);
		color: #8280ff;
		border-radius: 4.5px;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 4px;
		cursor: pointer;
		user-select: none;
		font-size: 13px;
		font-family: 'nunitosans-bold';
	}

	@media (max-width: 600px) {
		.weekday {
			min-width: 65px !important;
			font-family: 'nunitosans-semi-bold';
			font-size: 11px !important;
		}

		.weekdaySecond {
			min-width: 71px !important;
			font-family: 'nunitosans-semi-bold';
			font-size: 11px !important;
		}
		.filledButtonNewTheme {
			width: 71px;
			height: 23px;
			border: 1px solid rgb(130, 128, 255);
			color: #8280ff;
			border-radius: 4.5px;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 4px;
			cursor: pointer;
			user-select: none;
			font-size: 9px;
			font-family: 'nunitosans-bold';
		}
		.time-picker {
			font-family: 'nunitosans-regular';
			font-size: 8px;
		}
		.opening-hours-row {
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: 17px;
			width: 100%;
			padding-inline: 15px 20px !important;
		}
		.time-text {
			font-size: 9px;
			color: #000000;
			font-family: 'nunitosans-regular';
		}
		.customeButton {
			width: 71px !important;
			height: 23px !important;
			border: 0.6px solid #d5d5d5;
			cursor: pointer;
		}
		.valueContainer {
			font-family: 'nunitosans-semi-bold';
			font-size: 9px !important;
			line-height: 21px;
			color: #202224;
		}
		.removeIconWrapper {
			display: flex;
			justify-content: center;
			align-items: center;
			padding-top: 1px;
			margin-left: 5px;
			.removeIconImg {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 14px;
				height: 14px;
				cursor: pointer;
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.weekday {
			min-width: 65px !important;
			font-family: 'nunitosans-semi-bold';
			font-size: 12px !important;
		}

		.weekdaySecond {
			min-width: 68px !important;
			font-family: 'nunitosans-semi-bold';
			font-size: 12px !important;
		}
		.time-picker {
			font-family: 'nunitosans-regular';
			font-size: 8px;
		}
		.time-text {
			font-size: 10px;
			color: #000000;
			font-family: 'nunitosans-regular';
		}
		.customeButton {
			width: 79px !important;
			height: 26px !important;
			border: 0.6px solid #d5d5d5 !important;
			cursor: pointer !important;
		}
		.valueContainer {
			font-family: 'nunitosans-semi-bold';
			font-size: 10px !important;
			line-height: 21px;
			color: #202224;
		}
		.removeIconWrapper {
			display: flex;
			justify-content: center;
			align-items: center;
			padding-top: 4px;
			margin-left: 7px;
			.removeIconImg {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 15px;
				height: 15px;
				cursor: pointer;
			}
		}
		.filledButtonNewTheme {
			width: 79px;
			height: 26px;
			border: 1px solid rgb(130, 128, 255);
			color: #8280ff;
			border-radius: 4.5px;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 4px;
			cursor: pointer;
			user-select: none;
			font-size: 10px;
			font-family: 'nunitosans-bold';
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.weekday {
			min-width: 70px !important;
			font-family: 'nunitosans-semi-bold';
			font-size: 12px !important;
		}

		.weekdaySecond {
			min-width: 73px !important;
			font-family: 'nunitosans-semi-bold';
			font-size: 12px !important;
		}
		.time-picker {
			font-family: 'nunitosans-regular';
			font-size: 9px;
		}
		.time-text {
			font-size: 10px;
			color: #000000;
			font-family: 'nunitosans-regular';
		}
		.customeButton {
			width: 79px !important;
			height: 26px !important;
			border: 0.6px solid #d5d5d5 !important;
			cursor: pointer;
		}
		.valueContainer {
			font-family: 'nunitosans-semi-bold';
			font-size: 10px !important;
			line-height: 21px !important;
			color: #202224;
		}
		.removeIconWrapper {
			display: flex;
			justify-content: center;
			align-items: center;
			padding-top: 4px;
			margin-left: 4px;
			.removeIconImg {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 15px;
				height: 15px;
				cursor: pointer;
			}
		}
		.filledButtonNewTheme {
			width: 79px;
			height: 26px;
			border: 1px solid rgb(130, 128, 255);
			color: #8280ff;
			border-radius: 4.5px;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 4px;
			cursor: pointer;
			user-select: none;
			font-size: 10px;
			font-family: 'nunitosans-bold';
		}
	}
`;
