import styled from 'styled-components';

const NewSwitchWrapper = styled.div`
	display: inline-block;
	cursor: pointer;
	.custom-control.material-switch {
		--activeCircleColor: #ffffff;
		--inActiveCircleColor: #ffffff;
		--activeTrackColor: rgb(0, 204, 50);
		--inActiveTrackColor: #f95c69;
		padding-left: 0;
	}
	.custom-control.material-switch .material-switch-control-indicator {
		display: inline-block;
		position: relative;
		padding: 2px;
		width: 60px;
		height: 20px;
		background: var(--inActiveTrackColor);
		border-radius: 60px;
		transition: 0.3s;
	}
	.custom-control.material-switch
		.material-switch-control-input:checked
		~ .material-switch-control-indicator {
		background-color: var(--activeTrackColor);
	}
	.custom-control.material-switch .material-switch-control-indicator::after {
		content: '';
		display: block;
		position: absolute;
		height: 12px;
		width: 12px;
		border-radius: 50%;
		transition: 0.3s;
		top: 50%;
		left: 3px;
		transform: translateY(-50%);
		background: var(--inActiveCircleColor);
		box-shadow: 0 2px 10px #aaa;
	}
	.custom-control.material-switch
		.material-switch-control-input:checked
		~ .material-switch-control-indicator::after {
		background-color: var(--activeCircleColor);
		top: 50%;
		left: 96%;
		transform: translate(-100%, -50%);
	}
	.openSwitchText {
		font-family: 'nunitosans-medium';
		font-size: 9px;
	}
	.closeSwitchText {
		font-family: 'nunitosans-medium';
		position: absolute;
		right: 5.5px;
		font-size: 9px;
	}
	@media (max-width: 600px) {
		.custom-control.material-switch .material-switch-control-indicator {
			width: 60px;
			height: 20px;
		}
		.custom-control.material-switch
			.material-switch-control-indicator::after {
			height: 12px;
			width: 12px;
			left: 3px;
		}

		.openSwitchText {
			left: 8px !important;
		}

		.openSwitchText,
		.closeSwitchText {
			font-family: 'nunitosans-medium';
			position: absolute;
			font-size: 9px;
		}
	}
	@media only screen and (min-width: 961px) and (max-width: 1824px) and (max-height: 933px) {
		.custom-control.material-switch .material-switch-control-indicator {
			width: 60px;
			height: 20px;
		}
		.openSwitchText {
			left: 8px !important;
		}
		.openSwitchText,
		.closeSwitchText {
			font-family: 'nunitosans-medium';
			position: absolute;
			font-size: 9px;
		}
		.custom-control.material-switch
			.material-switch-control-indicator::after {
			height: 12px;
			width: 12px;
			left: 3px;
		}
	}
`;

export default NewSwitchWrapper;
