import styled from 'styled-components';

export const StylesWrapper = styled.div`
	font-family: 'nunitosans-medium' !important;
	font-size: 16px !important;
	line-height: 1.3 !important;
	color: #2e2e2e !important;
	max-height: 100%;
	overflow: auto;

	.modal-content {
		max-height: 80vh;
		overflow: auto;
	}

	.header {
		display: flex;
		justify-content: space-between;
		margin-bottom: 15px;
		padding-inline: 15px 20px !important;
		margin-top: 12px;
	}

	.headerText {
		display: flex;
		font-family: 'nunitosans-bold' !important;
		font-size: 16px !important;
		color: #202224 !important;
	}
	.leftContent {
		flex-grow: 1;
		text-align: left;
	}
	.venueHours {
		margin-right: 30px !important;
		flex-grow: inherit !important;
	}
	.weekDaytxt {
		width: 16% !important;
	}
	.openingCardBox {
		max-height: 950px !important;
		overflow-y: auto !important;
		scrollbarwidth: none !important;
		&::-webkit-scrollbar {
			width: 4px !important;
		}
		&::-webkit-scrollbar-thumb {
			background: rgba(1, 1, 1, 0.2) !important;
		}
	}
	@media (max-width: 600px) {
		.venueHours {
			margin-right: 20px !important;
			flex-grow: inherit !important;
		}
		.headerText {
			font-size: 11px !important;
		}
		.leftContent {
			flex-grow: 1;
			text-align: left;
		}
		.weekDaytxt {
			width: 16.4% !important;
		}
		.openingCardBox {
			max-height: 500px !important;
			overflow-y: auto !important;
			scrollbarwidth: none !important;
		}
	}

	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.headerText {
			font-size: 12px !important;
		}
		.leftContent {
			flex-grow: 1;
			text-align: left;
		}
		.weekDaytxt {
			width: 15% !important;
		}
		.venueHours {
			margin-right: 19px !important;
			flex-grow: inherit !important;
		}
		.openingCardBox {
			max-height: 556px !important;
			overflow-y: auto !important;
			scrollbarwidth: none !important;
		}
	}

	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.weekDaytxt {
			width: 15.5% !important;
		}
		.headerText {
			font-size: 12px !important;
		}
		.venueHours {
			margin-right: 22px !important;
			flex-grow: inherit !important;
		}
		.openingCardBox {
			max-height: 556px !important;
			overflow-y: auto !important;
			scrollbarwidth: none !important;
		}
	}
`;
