import { useEffect, useState } from 'react';
import moment from 'moment';
import { useSelector } from 'react-redux';

import { FilledButton } from '../../../Components/Layout/Buttons';
import { StylesWrapper } from './OpeningHoursCard.style';
import CustomTimePicker from './CustomTimePicker';
import { CircleCancelIcon } from '../../../Components/Icons/Cancel/CircleCancelIcon';
import NewFormSwitch2 from '../../../Components/NewForm/NewFormSwitch2';

const OpeningHoursCard = ({
	weekDay,
	isClosed,
	timeSlots,
	handleModalData,
	maxWeekDay,
    isVenueEdit,
	handleApplyToAll
}) => {
	const [validationMessage, setValidationMessage] = useState('');

	const convertTo24HourFormat = (time) => {
		if (/^\d{2}:\d{2}$/.test(time)) {
			return time;
		}
		const momentTime = moment(time, ['h:mm A', 'hh:mm A']);
		return momentTime.isValid() ? momentTime.format('HH:mm') : time;
	};

	const allThemeData = useSelector((state) => ({ ...state.themeChanger }));
	let today = new Date();
	let date =
		today.getFullYear() +
		'/' +
		parseInt(today.getMonth() + 1) +
		'/' +
		today.getDate();

	const [updateButton, setUpdateButton] = useState(false);
	const [data, setData] = useState({
		weekDay: weekDay,
		timeSlots: timeSlots.map((time) => ({
			openingHours: time.openingHours
				? new Date(
						date + ' ' + convertTo24HourFormat(time.openingHours)
				  )
				: null,
			closingHours: time.closingHours
				? new Date(
						date + ' ' + convertTo24HourFormat(time.closingHours)
				  )
				: null
		})),
		isClosed: isClosed
	});

	const handleStartDateChange = (index, date) => {
		setUpdateButton(true);
		setData((prevData) => {
			const updatedSlots = prevData.timeSlots.map((slot, i) =>
				i === index ? { ...slot, openingHours: date } : slot
			);

			const currentSlot = updatedSlots[index];
			const firstSlot = updatedSlots[0];

			if (currentSlot.closingHours && date >= currentSlot.closingHours) {
				updatedSlots[index].closingHours = null;
				// setValidationMessage("From time must be earlier than To time.");
			}

			if (
				index === 1 &&
				firstSlot.openingHours &&
				firstSlot.closingHours
			) {
				if (
					date <= firstSlot.closingHours &&
					date >= firstSlot.openingHours
				) {
					setValidationMessage(
						'From time for second slot should not overlap with the first slot.'
					);
					return prevData;
				}
			}

			if (isOverlapping(updatedSlots)) {
				setValidationMessage('Time slots are overlapping.');
				return prevData;
			}

			return { ...prevData, timeSlots: updatedSlots };
		});
	};

	const handleEndDateChange = (index, date) => {
		setUpdateButton(true);
		setData((prevData) => {
			const updatedSlots = prevData.timeSlots.map((slot, i) =>
				i === index ? { ...slot, closingHours: date } : slot
			);

			const currentSlot = updatedSlots[index];
			const firstSlot = updatedSlots[0];

			if (currentSlot.openingHours && date <= currentSlot.openingHours) {
				setValidationMessage('To time must be greater than From time.');
				return prevData;
			}

			if (currentSlot.openingHours && date === currentSlot.openingHours) {
				setValidationMessage(
					'From time and To time should not be the same.'
				);
				return prevData;
			}

			if (
				index === 1 &&
				firstSlot.openingHours &&
				firstSlot.closingHours
			) {
				if (
					date <= firstSlot.closingHours &&
					date >= firstSlot.openingHours
				) {
					setValidationMessage(
						'To time for second slot should not overlap with the first slot.'
					);
					return prevData;
				}
			}

			if (isOverlapping(updatedSlots)) {
				setValidationMessage('Time slots are overlapping.');
				return prevData;
			}

			return { ...prevData, timeSlots: updatedSlots };
		});
	};

	const handleSwitchChange = () => {
		setUpdateButton(true);
		setData((prevData) => {
			const updatedData = {
				...prevData,
				isClosed: !prevData.isClosed
			};

			handleModalData(updatedData);

			return updatedData;
		});
	};

	const handleAddTimeSlot = () => {
		if (data.timeSlots.length >= 2) {
			return;
		}

		setUpdateButton(true);
		setData((prevData) => ({
			...prevData,
			timeSlots: [
				...prevData.timeSlots,
				{
					openingHours: null,
					closingHours: null
				}
			]
		}));
	};

	const handleDeleteTimeSlot = () => {
		if (data.timeSlots.length > 1) {
			setUpdateButton(true);
			setData((prevData) => ({
				...prevData,
				timeSlots: prevData.timeSlots.slice(0, 1)
			}));
		}
	};

	const isOverlapping = (timeSlots) => {
		for (let i = 0; i < timeSlots.length - 1; i++) {
			for (let j = i + 1; j < timeSlots.length; j++) {
				const start1 = timeSlots[i].openingHours;
				const end1 = timeSlots[i].closingHours;
				const start2 = timeSlots[j].openingHours;
				const end2 = timeSlots[j].closingHours;

				if (start1 && end1 && start2 && end2) {
					if (
						(start1 < end2 && start2 < end1) ||
						(start2 < end1 && start1 < end2)
					) {
						return true;
					}
				}
			}
		}
		return false;
	};
	useEffect(() => {
		if (updateButton) {
			const updatedData = {
				weekDay: data?.weekDay,
				timeSlots: data?.timeSlots?.map((slot) => ({
					openingHours: slot?.openingHours
						? moment(slot.openingHours).format('HH:mm')
						: null,
					closingHours: slot?.closingHours
						? moment(slot.closingHours).format('HH:mm')
						: null
				})),
				isClosed: data?.isClosed
			};
			handleModalData(updatedData);
			setUpdateButton(false);
		}
	}, [updateButton]);

	return (
		<StylesWrapper {...allThemeData}>
			<div className={`opening-hours-row`}>
				<div className="weekday">
					{moment()
						.day(data.weekDay + 1)
						.format('dddd')}
				</div>
				<div className="d-flex flex-column gap-2">
					{data?.timeSlots[0] && (
						<div className="d-flex flex-column">
							<div className="d-flex gap-1 mr-16">
								<div className="time-picker">
									<span className="time-text">From</span>
									<CustomTimePicker
										date={data?.timeSlots[0]?.openingHours}
										handleDateChange={(date) =>
											handleStartDateChange(0, date)
										}
										customClassName="customeButton"
										 timeIntervals={isVenueEdit ? 30 : 15}
										placeholderText="Time"
										icon
									/>
								</div>
								<div className="time-picker">
									<span className="time-text">To</span>
									<CustomTimePicker
										date={data?.timeSlots[0]?.closingHours}
										handleDateChange={(date) =>
											handleEndDateChange(0, date)
										}
										customClassName="customeButton"
										 timeIntervals={isVenueEdit ? 30 : 15}
										placeholderText="Time"
										icon
									/>
								</div>
							</div>
						</div>
					)}
				</div>
				<div className="button">
					<FilledButton
						buttonText={'Add time slot'}
						color={'#8280FF'}
						background={'#ffffff'}
						className={'filledButtonNewTheme'}
						onClick={handleAddTimeSlot}
					/>
				</div>
				<div className="button w-15">
					{data.weekDay === 0 && (
						<FilledButton
							buttonText={'Apply to all'}
							background={'#e4e4fd'}
							color={'#8280FF'}
							className={'filledButtonNewTheme'}
							onClick={() => handleApplyToAll(data)}
						/>
					)}
				</div>
				<NewFormSwitch2
					checkedChildren={'OPEN'}
					unCheckedChildren={'CLOSED'}
					checked={data?.isClosed}
					onChange={handleSwitchChange}
				/>
			</div>
			<div className={`opening-hours-row`}>
				{data?.timeSlots[1] && (
					<div className="weekdaySecond">
						{weekDay !== undefined ? (
							<span style={{ visibility: 'hidden' }}>
								{moment()
									.day(weekDay + 1)
									.format('dddd')}
							</span>
						) : (
							'\u00A0'
						)}
					</div>
				)}
				<div className="d-flex flex-column gap-2">
					{data?.timeSlots[1] && (
						<div className="d-flex flex-column">
							<div className="d-flex gap-1 mr-16">
								<div className="time-picker">
									<span className="time-text">From</span>
									<CustomTimePicker
										date={data?.timeSlots[1]?.openingHours}
										handleDateChange={(date) =>
											handleStartDateChange(1, date)
										}
										customClassName="customeButton"
										timeIntervals={isVenueEdit ? 30 : 15}
										placeholderText="Time"
										icon
									/>
								</div>
								<div className="time-picker">
									<span className="time-text">To</span>
									<CustomTimePicker
										date={data?.timeSlots[1]?.closingHours}
										handleDateChange={(date) =>
											handleEndDateChange(1, date)
										}
										customClassName="customeButton"
									    timeIntervals={isVenueEdit ? 30 : 15}
										placeholderText="Time"
										icon
									/>
								</div>
								{/* <div className="circleCancelIcon">
									<CircleCancelIcon
										width={15}
										heigth={15}
										className="removeIconImg"
										onClick={handleDeleteTimeSlot}
									/>
								</div> */}
								<div
									className="removeIconWrapper"
									onClick={handleDeleteTimeSlot}
								>
									<div className="removeIconImg">
										<CircleCancelIcon
											width={'100%'}
											height={'100%'}
										/>
									</div>
								</div>
							</div>
							{validationMessage && (
								<div
									className="validation-text"
									style={{
										color: 'red',
										fontSize: '10px',
										marginTop: '5px',
										fontFamily: 'nunitosans-semi-bold'
									}}
								>
									{validationMessage}
								</div>
							)}
						</div>
					)}
				</div>
				<div className="button w-25"></div>
				<div className="button w-15"></div>
				<div className="switch-container"></div>
			</div>
			{weekDay !== maxWeekDay && <div className="customeHrClass" />}
		</StylesWrapper>
	);
};

export default OpeningHoursCard;
