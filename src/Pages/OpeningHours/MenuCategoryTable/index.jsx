import ReactTable from 'react-table-6';
import 'react-table-6/react-table.css';
import { StyleWrraper } from './index.style';

const MenuCategoryTable = ({ loading, tableData, tableColumns }) => {
	return (
		<StyleWrraper>
			<div className="borderBox">
				{loading ? (
					<></>
				) : (
					<div className="tableContainer">
						<ReactTable
							columns={tableColumns}
							data={tableData}
							showPagination={false}
							minRows={0}
							resizable={false}
							NoDataComponent={() => (
								<span className="noDataFoundContainer">
									No data found
								</span>
							)}
						/>
					</div>
				)}
			</div>
		</StyleWrraper>
	);
};

export default MenuCategoryTable;
