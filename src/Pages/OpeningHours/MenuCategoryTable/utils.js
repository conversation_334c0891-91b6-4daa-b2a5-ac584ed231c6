import moment from 'moment';
import { Fragment } from 'react';

export const formatMenuCategoryData = (data) => {
	let tableData = [];

	const weekDaysMap = {
		0: 'monday',
		1: 'tuesday',
		2: 'wednesday',
		3: 'thursday',
		4: 'friday',
		5: 'saturday',
		6: 'sunday'
	};

	if (data?.length > 0) {
		tableData = data?.map((item) => {
			let formattedRow = {
				rowData: item,
				weekDays: item?.weekDays?.map((day) => {
					return {
						weekDay: day?.weekDay,
						isClosed: day?.isClosed == '0' ? true : false,
						timeSlots: day?.timeSlots,
						rowData: day
					};
				}),
				subCategoryID: item?.subCategoryID,
				subCategoryName: item?.subCategoryName
			};

			item?.weekDays?.forEach((dayItem) => {
				const weekDay = weekDaysMap[dayItem?.weekDay];
				if (weekDay) {
					formattedRow[weekDay] =
						dayItem?.isClosed != '0'
							? 'Unavailable'
							: dayItem?.timeSlots.map((time) => ({
									openingHours: moment(
										time.openingHours,
										'HH:mm'
									).format('hh:mma'),
									closingHours: moment(
										time.closingHours,
										'HH:mm'
									).format('hh:mma')
							  }));
				}
			});

			return formattedRow;
		});
	}
	return tableData;
};

export const formatVenueOpeningHoursData = (data) => {
	let tableData = [];
	if (data?.length > 0) {
		tableData = data?.map((item) => {
			return {
				weekDay: item?.weekDay,
				isClosed: item?.isClosed == '0' ? true : false,
				timeSlots: item?.timeSlots.map((time) => ({
					openingHours: moment(time.openingHours, 'HH:mm').format(
						'hh:mma'
					),
					closingHours: moment(time.closingHours, 'HH:mm').format(
						'hh:mma'
					)
				})),
				rowData: item
			};
		});
	}
	return tableData;
};

export const getMenuCategoryTableColumns = ({ handleEditModal }) => [
	{
		Header: 'Name',
		accessor: 'subCategoryName',
		className: 'justify-content-start',
		minWidth: 100,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-start',
		Cell: ({ value }) => {
			return <span className="nameText">{value}</span>;
		}
	},
	{
		Header: 'Monday',
		accessor: 'monday',
		className: 'justify-content-center',
		minWidth: 124,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-center',
		style: { flex: 1 },
		Cell: ({ value }) => {
			if (value === 'Unavailable') {
				return 'Unavailable';
			} else {
				return value?.map((slot, index) => (
					<Fragment key={index}>
						{index != 0 && <br />}
						{slot.openingHours} - {slot.closingHours}
					</Fragment>
				));
			}
		}
	},
	{
		Header: 'Tuesday',
		accessor: 'tuesday',
		className: 'justify-content-center',
		minWidth: 124,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-center',
		style: { flex: 1 },
		Cell: ({ value }) => {
			if (value === 'Unavailable') {
				return 'Unavailable';
			} else {
				return value?.map((slot, index) => (
					<Fragment key={index}>
						{index != 0 && <br />}
						{slot.openingHours} - {slot.closingHours}
					</Fragment>
				));
			}
		}
	},
	{
		Header: 'Wednesday',
		accessor: 'wednesday',
		className: 'justify-content-center',
		minWidth: 124,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-center',
		style: { flex: 1 },
		Cell: ({ value }) => {
			if (value === 'Unavailable') {
				return 'Unavailable';
			} else {
				return value?.map((slot, index) => (
					<Fragment key={index}>
						{index != 0 && <br />}
						{slot.openingHours} - {slot.closingHours}
					</Fragment>
				));
			}
		}
	},
	{
		Header: 'Thursday',
		accessor: 'thursday',
		className: 'justify-content-center',
		minWidth: 124,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-center',
		style: { flex: 1 },
		Cell: ({ value }) => {
			if (value === 'Unavailable') {
				return 'Unavailable';
			} else {
				return value?.map((slot, index) => (
					<Fragment key={index}>
						{index != 0 && <br />}
						{slot.openingHours} - {slot.closingHours}
					</Fragment>
				));
			}
		}
	},

	{
		Header: 'Friday',
		accessor: 'friday',
		className: 'justify-content-center',
		minWidth: 124,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-center',
		style: { flex: 1 },
		Cell: ({ value }) => {
			if (value === 'Unavailable') {
				return 'Unavailable';
			} else {
				return value?.map((slot, index) => (
					<Fragment key={index}>
						{index != 0 && <br />}
						{slot.openingHours} - {slot.closingHours}
					</Fragment>
				));
			}
		}
	},
	{
		Header: 'Saturday',
		accessor: 'saturday',
		className: 'justify-content-center',
		minWidth: 124,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-center',
		style: { flex: 1 },
		Cell: ({ value }) => {
			if (value === 'Unavailable') {
				return 'Unavailable';
			} else {
				return value?.map((slot, index) => (
					<Fragment key={index}>
						{index != 0 && <br />}
						{slot.openingHours} - {slot.closingHours}
					</Fragment>
				));
			}
		}
	},

	{
		Header: 'Sunday',
		accessor: 'sunday',
		className: 'justify-content-center',
		minWidth: 124,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-center',
		style: { flex: 1 },
		Cell: ({ value }) => {
			if (value === 'Unavailable') {
				return 'Unavailable';
			} else {
				return value?.map((slot, index) => (
					<Fragment key={index}>
						{index != 0 && <br />}
						{slot.openingHours} - {slot.closingHours}
					</Fragment>
				));
			}
		}
	},
	{
		className: 'justify-content-start',
		minWidth: 40,
		filterable: false,
		sortable: false,
		headerClassName: 'justify-content-center',
		style: { flex: 1 },
		Cell: (row) => {
			return (
				<span
					className="editText"
					onClick={() => handleEditModal(row?.row)}
				>
					Edit
				</span>
			);
		}
	}
];
