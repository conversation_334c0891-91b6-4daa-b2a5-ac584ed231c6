import styled from 'styled-components';

export const StyleWrraper = styled.div`
	width: 100%;
	font-family: 'nunitosans-regular' !important;

	.borderBox {
		width: 100%;
		border: 1px solid #d5d5d5;
		border-radius: 4px;
		background: #fbfcff !important;
	}

	.tableContainer {
		width: 100%;
		overflow: auto;
		&::-webkit-scrollbar {
			width: 4px !important;
			height: 4px !important;
		}
		&::-webkit-scrollbar-thumb {
			border-radius: 4px !important;
			background: rgba(0, 0, 0, 0.2) !important;
		}
	}

	.ReactTable {
		border: none;
		border-radius: 0px 0px 4px 4px;
		font-family: 'nunitosans-regular' !important;
		height: 100%;
		width: 100%;
		min-width: 970px;
		overflow: hidden;
		position: relative;
		.noDataFoundContainer {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			font-family: 'nunitosans-regular' !important;
			font-size: 12px;
			color: #2e2e2e;
		}
		.rt-table {
			font-family: 'nunitosans-regular' !important;
			overflow: auto;
			::-webkit-scrollbar {
				width: 5px;
				height: 5px;
			}
		}
		.rt-thead.-header {
			background-color: #fff;
			box-shadow: none !important;
			position: sticky;
			top: 0;
			display: ${(props) => (props.hideHeader ? 'none' : '')};
		}
		.rt-thead {
			.rt-tr {
				.rt-th {
					min-width: 50px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					height: 46px;
					padding: 8px 8px !important;
					background-color: #f5f6fa !important;
					border: none;

					font-size: 14px !important;
					font-family: 'nunitosans-bold' !important;
					color: #202224 !important;
					display: flex;
					justify-content: center;
					align-items: center;
					div {
						font-size: 14px !important;
						font-family: 'nunitosans-bold' !important;
						color: #202224 !important;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}
			}
			.rt-tr > div:first-child {
				padding-left: 24px !important;
			}
		}

		.rt-tbody {
			overflow-y: initial;
			overflow-x: hidden;
			border-top: none;
			border-bottom: none;
			::-webkit-scrollbar {
				width: 5px;
				height: 5px;
			}
			::-webkit-scrollbar-track {
				border-radius: 50px;
				margin-bottom: 10px;
			}
			::-webkit-scrollbar-thumb {
				border-radius: 50px;
			}
			.rt-tr-group {
				border: none;
				flex: none;
				.rt-tr {
					border-bottom: 1px solid rgba(148, 150, 152, 0.5);
					.rt-td {
						min-width: 50px;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;

						height: 64px;
						padding: 6px 8px !important;
						border: none;
						border-radius: 0px;

						display: flex;
						justify-content: center;
						align-items: center;

						font-size: 16px !important;
						font-family: 'nunitosans-semi-bold' !important;
						color: #2e2e2e !important;
						.nameText {
							font-family: 'nunitosans-bold' !important;
							font-size: 16px !important;
							color: #2e2e2e !important;
                            white-space: normal;
                            word-wrap: break-word;
                            word-break: break-word;
						}
						.editText {
							font-family: 'nunitosans-bold' !important;
							font-size: 14px !important;
							color: #2e2e2e !important;
							text-decoration: underline;
							cursor: pointer !important;
						}
					}
				}
				.rt-tr > div:first-child {
					padding-left: 24px !important;
				}
				&:last-child {
					.rt-tr {
						border-bottom: 0 !important;
					}
				}
			}
		}
	}

	@media only screen and (max-width: 1299px) {
		.ReactTable {
			.rt-thead {
				.rt-tr {
					.rt-th {
						height: 35px !important;
						font-size: 11px !important;
						div {
							font-size: 11px !important;
						}
					}
				}
				.rt-tr > div:first-child {
					padding-left: 16px !important;
				}
			}
			.rt-tbody {
				.rt-tr-group {
					.rt-tr {
						.rt-td {
							height: 48px !important;
							font-size: 12px !important;
							.nameText {
								font-size: 12px !important;
							}
							.editText {
								font-size: 11px !important;
							}
						}
					}
					.rt-tr > div:first-child {
						padding-left: 16px !important;
					}
				}
			}
		}
	}

	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.ReactTable {
			.rt-thead {
				.rt-tr {
					.rt-th {
						height: 35px !important;
						font-size: 11px !important;
						div {
							font-size: 11px !important;
						}
					}
				}
				.rt-tr > div:first-child {
					padding-left: 18px !important;
				}
			}
			.rt-tbody {
				.rt-tr-group {
					.rt-tr {
						.rt-td {
							height: 48px !important;
							font-size: 12px !important;
							.nameText {
								font-size: 12px !important;
							}
							.editText {
								font-size: 11px !important;
							}
						}
					}
					.rt-tr > div:first-child {
						padding-left: 18px !important;
					}
				}
			}
		}
	}
`;
