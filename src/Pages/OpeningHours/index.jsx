import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import moment from 'moment';

import { VenuePanelRoutes } from '../../Utils/routes';
import Api from '../../Helper/Api';
import {
    formatMenuCategoryData,
    formatVenueOpeningHoursData,
    getMenuCategoryTableColumns
} from './MenuCategoryTable/utils';
import NewPageTitle from '../../Components/Common/NewPageTitle';
import NewPageWrapper from '../../Components/Common/NewPageWrapper';
import VenueOpeningHours from './VenueOpeningHours';
import MenuCategoryTable from './MenuCategoryTable';
import { OpeningHoursWrraper } from './index.style';
import EditHoursModal from './EditHoursModal';
import NewLoader from '../../Components/Common/NewLoader';
import NewFormCheckbox from '../../Components/NewForm/NewFormCheckbox';

const OpeningHours = () => {
    const state = useSelector((state) => ({ ...state }));
    const authData = state.auth;

    const [venueOpeningHoursLoading, setVenueOpeningHoursLoading] =
        useState(false);
    const [menucategoryData, setMenucategoryData] = useState([]);

    const [venueOpeningHours, setVenueOpeningHours] = useState([]);
    const [menucategory, setMenucategory] = useState([]);
    const [menucategoryHours, setMenucategoryHours] = useState([]);

    const [matchCategory, setMatchCategory] = useState('Yes');

    const [isVenueEdit, setIsVenueEdit] = useState(true);
    const [editModal, setEditModal] = useState(false);
    const [editModalLoading, setEditModalLoading] = useState(false);

    useEffect(() => {
        (async () => {
            await getVenueOpeningHoursList();
        })();
    }, [authData?.selectedVenue?.id]);

    const handleEditModal = (e) => {
        if (e?._original != null) {
            setIsVenueEdit(false);
            if (e?._original?.weekDays?.length > 0) {
                setMenucategoryHours(
                    e?._original?.rowData?.weekDays.map((day) => {
                        return {
                            weekDay: day?.weekDay,
                            isClosed: day?.isClosed == '0' ? true : false,
                            timeSlots: day?.timeSlots,
                            rowData: day
                        };
                    })
                );
            }
            if (e?._original?.subCategoryID) {
                setMenucategory({
                    subCategoryID: e?._original?.subCategoryID,
                    subCategoryName: e?._original?.subCategoryName
                });
            }
        } else {
            setIsVenueEdit(true);
        }
        setEditModal((prev) => !prev);
    };

    const getVenueOpeningHoursList = async () => {
        setVenueOpeningHoursLoading(true);
        try {
            const res = await Api(
                'POST',
                VenuePanelRoutes.getVenueOpeningHours,
                {
                    bar_id: authData?.selectedVenue?.id?.toString()
                }
            );
            if (res?.data?.status) {
                let venueOpeningHours = formatVenueOpeningHoursData(
                    res?.data?.data?.venueOpeningHours
                );
                setVenueOpeningHours(venueOpeningHours);
                let menuCategoryHours = formatMenuCategoryData(
                    res?.data?.data?.subCategoryOpeningHours
                );
                setMenucategoryData(menuCategoryHours);
                setMatchCategory(
                    res?.data?.data?.matchCategoryOpeningHours || 'No'
                );
            } else {
                toast.error(res?.data?.message);
            }
            setVenueOpeningHoursLoading(false);
        } catch (err) {
            setVenueOpeningHoursLoading(false);
            if (err?.response?.data?.message) {
                toast.error(err?.response?.data?.message);
            }
        }
    };

    const handleEditHoursAPI = async (payload, isMatchCategory) => {
        setEditModalLoading(true);
        if (isVenueEdit || isMatchCategory) {
            try {
                const res = await Api(
                    'POST',
                    VenuePanelRoutes.updateVenueOpeningHours,
                    isMatchCategory
                        ? payload
                        : {
                            ...payload,
                            matchCategoryOpeningHours: matchCategory
                        }
                );
                if (res?.data?.status) {
                    toast.success(res?.data?.message);
                    setEditModal(false);
                } else {
                    toast.error(res?.data?.message);
                }
                setEditModalLoading(false);
                getVenueOpeningHoursList();
            } catch (err) {
                setEditModalLoading(false);
                if (err?.response?.data?.message) {
                    toast.error(err?.response?.data?.message);
                }
            }
        } else {
            try {
                const res = await Api(
                    'POST',
                    VenuePanelRoutes.updateVenueCategoryOpeningHours,
                    { ...payload }
                );
                if (res?.data?.status) {
                    toast.success(res?.data?.message);
                    setEditModal(false);
                } else {
                    toast.error(res?.data?.message);
                }
                setEditModalLoading(false);
                getVenueOpeningHoursList();
            } catch (err) {
                setEditModalLoading(false);
                if (err?.response?.data?.message) {
                    toast.error(err?.response?.data?.message);
                }
            }
        }
    };

    const convertTo24HourFormat = (time) => {
        if (!time) return '';

        if (/^\d{2}:\d{2}$/.test(time)) {
            return time;
        }

        if (/^(0?[1-9]|1[0-2]):[0-5][0-9]\s?(AM|PM)$/i.test(time)) {
            return moment(time, ['h:mm A']).format('HH:mm');
        }

        const momentTime = moment(time);

        return momentTime.isValid() ? momentTime.format('HH:mm') : time;
    };

    const handleMatchCategoryChange = (value) => {
        setIsVenueEdit(true);
        if (value === 'Yes') {
            const payload = {
                bar_id: authData?.selectedVenue?.id.toString(),
                matchCategoryOpeningHours: 'Yes',
                venueOpeningHours: venueOpeningHours?.map((item, index) => ({
                    weekDay: index,
                    isClosed: item?.isClosed ? '0' : '1' || '0',
                    timeSlots: item?.timeSlots.map((time) => ({
                        openingHours: convertTo24HourFormat(time.openingHours),
                        closingHours: convertTo24HourFormat(time.closingHours)
                    }))
                }))
            };
            handleEditHoursAPI(payload, true);
        } else {
            const payload = {
                bar_id: authData?.selectedVenue?.id.toString(),
                matchCategoryOpeningHours: 'No',
                venueOpeningHours: venueOpeningHours?.map((item, index) => ({
                    weekDay: index,
                    isClosed: item?.isClosed ? '0' : '1' || '0',
                    timeSlots: item?.timeSlots.map((time) => ({
                        openingHours: convertTo24HourFormat(time.openingHours),
                        closingHours: convertTo24HourFormat(time.closingHours)
                    }))
                }))
            };
            handleEditHoursAPI(payload, true);
        }
    };

    return (
        <NewPageWrapper>
            <OpeningHoursWrraper>
                <div className="mainTitle">
                    <NewPageTitle>Opening Hours</NewPageTitle>
                </div>
                <p className="mainParagraph">
                    Set up your venue opening and menu category hours.
                </p>
                <div className="customeHrClass" />
                <NewLoader
                    loading={editModalLoading || venueOpeningHoursLoading}
                >
                    <div className="venueOpeningHoursContainer">
                        <VenueOpeningHours
                            venueOpeningHours={venueOpeningHours}
                            handleEditModal={handleEditModal}
                        />
                        <p className="mainParagraph">
                            Do the menu category hours match the venue opening
                            hours?
                        </p>
                        <div className="checkboxWrapper">
                            <NewFormCheckbox
                                wrapperClassName={'openingHoursCheckboxWrapper'}
                                name="matchCategoryOpeningHours"
                                value={'Yes'}
                                checked={matchCategory === 'Yes'}
                                onChange={() => {
                                    setMatchCategory('Yes');
                                    handleMatchCategoryChange('Yes');
                                }}
                            >
                                Yes
                            </NewFormCheckbox>
                            <NewFormCheckbox
                                wrapperClassName={'openingHoursCheckboxWrapper'}
                                name="matchCategoryOpeningHours"
                                value={'No'}
                                checked={matchCategory === 'No'}
                                onChange={() => {
                                    setMatchCategory('No');
                                    handleMatchCategoryChange('No');
                                }}
                            >
                                No
                            </NewFormCheckbox>
                        </div>
                        <p className="mainParagraph">
                            If 'No,' please provide specific menu category hours
                            below.
                        </p>
                    </div>
                </NewLoader>
                {matchCategory != 'No' ? (
                    <></>
                ) : (
                    <>
                        <div className="customeHrClass" />
                        <p className="subMainTitle">Menu Category Hours</p>
                        <p className="mainParagraph">
                            Customise the availability of your menu by assigning
                            specific opening hours to individual menu
                            categories.
                        </p>
                        <div className="menuCategoryTableWrapper">
                            <MenuCategoryTable
                                tableData={menucategoryData}
                                tableColumns={getMenuCategoryTableColumns({
                                    handleEditModal
                                })}
                                tableDataCount={3}
                                NoDataText={'No data found'}
                            />
                        </div>
                    </>
                )}
                {isVenueEdit && editModal ? (
                    <EditHoursModal
                        isOpen={editModal}
                        handleEditModal={handleEditModal}
                        closeModal={handleEditModal}
                        handleEditHoursAPI={handleEditHoursAPI}
                        editModalLoading={editModalLoading}
                        isVenueEdit={true}
                        openingHoursModalData={venueOpeningHours}
                        setModalData={setVenueOpeningHours}
                    />
                ) : (
                    <EditHoursModal
                        isOpen={editModal}
                        handleEditModal={handleEditModal}
                        closeModal={handleEditModal}
                        handleEditHoursAPI={handleEditHoursAPI}
                        editModalLoading={editModalLoading}
                        isVenueEdit={false}
                        openingHoursModalData={menucategoryHours}
                        setModalData={setMenucategoryHours}
                        menucategory={menucategory}
                    />
                )}
            </OpeningHoursWrraper>
        </NewPageWrapper>
    );
};

export default OpeningHours;
