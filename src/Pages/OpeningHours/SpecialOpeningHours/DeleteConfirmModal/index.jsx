import NewModal from '../../../Components/Common/NewModal';
import { StylesWrapper } from './index.styles';

const DeleteConfirmModal = ({
	isOpen,
	closeModal,
	handleDeletePickUpLocation,
	deleteLoading,
	data,
}) => {
	return (
		<NewModal
			isOpen={isOpen}
			toggle={closeModal}
			title={'Are you sure you want to delete the upcoming Special Hours?'}
			cancelButtonText="No"
			submitButtonText="Yes"
			handleSubmitButtonClick={handleDeletePickUpLocation}
			submitButtonLoading={deleteLoading}
		>
			<StylesWrapper>
				{data.event}
				{data.fromDate}-{data.toDate}
				{data.isVenueOpen}
			</StylesWrapper>
		</NewModal>
	);
};

export default DeleteConfirmModal;
