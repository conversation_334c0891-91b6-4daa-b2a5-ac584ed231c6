import styled from "styled-components";

export const SpecialOpeningHoursWrraper = styled.div`
	font-family: nunitosans-regular !important;

	.customeHrClass {
		border: 0.6px solid #949596;
		margin-block: 10px;
	}

	.categoryCotainer {
		width: 77% !important;
	}

	.circleCancelIcon {
		display: flex;
		width: 22%;
		align-items: center;
		justify-content: center;
	}

	.customContainer {
		height: 65px !important;
	}

	.titleWrap {
		display: flex;
		flex-direction: column;

		.subMainTitle {
			font-size: 24px;
			font-family: nunitosans-bold;
			color: #202224;
			width: 90% !important;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-bottom: 0px;
			width: fit-content;
		}

		.mainParagraph {
			font-size: 16px;
			font-weight: 400;
			font-family: nunitosans-regular;
			color: #2e2e2e;
			padding-bottom: 18px;
		}

		.listElement {
			font-size: 16px;
			font-weight: 400;
			font-family: nunitosans-regular;
			color: #2e2e2e;
		}
	}

	.input-group {
		.inputGroup,
		formGroupIcon {
			background-color: #fbfcff !important;
		}
		.inputBox {
			background-color: #fbfcff !important;
			font-family: nunitosans-regular !important;
			color: #a4a5a7 !important;
			font-size: 16px !important;
		}
	}

	.customeLabelClass {
		font-family: nunitosans-bold;
		color: #202224;
		font-size: 20px;
		padding-bottom: 0px;
		text-transform: none;
	}
	.customeInputClass {
		font-family: nunitosans-semi-bold !important;
		color: #2e2e2e !important;
		border: 0.6px solid #d5d5d5 !important;
		width: 90% !important;
	}

	.customeRateInputClass {
		font-family: nunitosans-semi-bold !important;
		color: #2e2e2e !important;
		border: 0.6px solid #d5d5d5 !important;
		width: 77% !important;
	}

	@media (max-width: 600px) {
		.titleWrap {
			.subMainTitle {
				font-size: 11px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 11px;
				color: #202224;
				font-family: nunitosans-regular;
			}

			.listElement {
				font-size: 11px;
				font-family: nunitosans-regular;
				color: #2e2e2e;
			}
		}

		.customeLabelClass {
			font-family: nunitosans-bold;
			font-size: 12px;
			font-weight: 400;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.titleWrap {
			.subMainTitle {
				font-size: 11px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 11px;
				font-family: nunitosans-regular;
			}

			.listElement {
				font-size: 11px;
				font-family: nunitosans-regular;
				color: #2e2e2e;
			}
		}

		.customeLabelClass {
			font-family: nunitosans-bold;
			font-size: 12px;
			font-weight: 400;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.titleWrap {
			.subMainTitle {
				font-size: 12px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 12px;
				font-family: nunitosans-regular;
			}

			.listElement {
				font-size: 12px;
				font-family: nunitosans-regular;
				color: #2e2e2e;
			}
		}

		.customeLabelClass {
			font-family: nunitosans-bold;
			font-size: 12px;
			font-weight: 400;
		}
	}
`;