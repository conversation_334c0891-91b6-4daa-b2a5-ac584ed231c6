import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { VenueApiRoutes } from '../../../Utils/routes';
import {
    SpecialOpeningHoursWrraper,
} from './index.style';
import Api from '../../../Helper/Api';
import { toast } from 'react-toastify';
import NewDesignCustomLabel from '../../../Components/Form/NewDesignCustomLabel';
import { Col, Row } from 'reactstrap';
import { FilledButton } from '../../../Components/Layout/Buttons';
import NewPageTitle from '../../../Components/Common/NewPageTitle';
import NewPageWrapper from '../../../Components/Common/NewPageWrapper';
import EmptyData from '../../../Components/Common/EmptyData';
import NewLoader from '../../../Components/Common/NewLoader';
import Switch from '../../../Components/Common/Switch';

const SpecialOpeningHours = () => {
    const state = useSelector((state) => ({ ...state }));
    const authData = state.auth;
    const [pickupLocationLoading, setPickUpLocationLoading] = useState(false);
    const [editPickupLocationLoading, setEditPickUpLocationLoading] = useState(false);
    const [pickupLocationList, setPickUpLocationList] = useState([]);
    const [deleteModal, setDeleteModal] = useState(false);
    const [deletePickUplocationId, setDeletePickUplocationId] = useState(null);
    const [deletePickUplocationLoading, setDeletePickUplocationLoading] = useState(null);
    const [createModal, setCreateModal] = useState(false);
    const [subCategoryData, setSubCategoryData] = useState([]);
    const [subCategoryOptionsLoading, setSubCategoryOptionsLoading] = useState(false);
    const [createLocationLoading, setCreateLocationLoading] = useState(false);
    const [pendingEdits, setPendingEdits] = useState({});

    useEffect(() => {
        (async () => {
            await getSubCategoryOptions();
            await getPickUpLocationList();
        })();
    }, [authData?.selectedVenue?.id]);

    const handleDeleteModal = () => {
        setDeleteModal((prev) => !prev);
    };

    const handleCreatePickUpModal = () => {
        setCreateModal((prev) => !prev);
    };

    const getPickUpLocationList = async () => {
        setPickUpLocationLoading(true);
        try {
            const res = await Api(
                'POST',
                VenueApiRoutes.getPickupLocationListv2,
                {
                    bar_id: authData?.selectedVenue?.id?.toString()
                }
            );

            if (res?.data?.status) {
                setPickUpLocationList(res?.data?.data);
            } else {
                toast.error(res?.data?.message);
            }
            setPickUpLocationLoading(false);
        } catch (err) {
            setPickUpLocationLoading(false);
            if (err?.response?.data?.message) {
                toast.error(err?.response?.data?.message);
            }
        }
    };

    const handleDeletePickUpLocation = async () => {
        setDeletePickUplocationLoading(true);
        try {
            const res = await Api(
                'DELETE',
                VenueApiRoutes.deletePickupLocationv2,
                {
                    bar_id: authData?.selectedVenue?.id?.toString(),
                    id: '' + deletePickUplocationId
                }
            );
            if (res?.data?.status) {
                toast.success(res?.data?.message);
                await getPickUpLocationList();
                setDeleteModal(false);
            } else {
                toast.error(res?.data?.message);
            }
            setDeletePickUplocationLoading(false);
        } catch (err) {
            setDeletePickUplocationLoading(false);
            if (err?.response?.data?.message) {
                toast.error(err?.response?.data?.message);
            }
        }
    };

    const handleCreatePickUpLocation = async (values) => {
        setCreateLocationLoading(true);
        try {
            const res = await Api(
                'POST',
                VenueApiRoutes.addPickupLocationv2,
                {
                    bar_id: authData?.selectedVenue?.id?.toString(),
                    address: values?.pickupLocationAddress,
                }
            );
            if (res?.data?.status) {
                toast.success(res?.data?.message);
                await getPickUpLocationList();
                setCreateModal(false);
            } else {
                toast.error(res?.data?.message);
            }
            setCreateLocationLoading(false);
        } catch (err) {
            setCreateLocationLoading(false);
            if (err?.response?.data?.message) {
                toast.error(err?.response?.data?.message);
            }
        }
    };

    const getSubCategoryOptions = async () => {
        setSubCategoryOptionsLoading(true);
        try {
            const res = await Api('POST', VenueApiRoutes.getMenuCategoryList, {
                bar_id: authData?.selectedVenue?.id
            });
            if (res?.data?.status) {
                let formattedData = res?.data?.data;
                formattedData = formattedData.map((item) => ({ label: item.name, value: item?.id }));
                setSubCategoryData(formattedData);
            } else {
                toast.error(res?.data?.message);
            }
            setSubCategoryOptionsLoading(false);
        } catch (err) {
            setSubCategoryOptionsLoading(false);
            if (err?.message) {
                toast.error(err?.message);
            }
        }
    };

    const handleEditPickupLocation = async (id, updatedData) => {
        const tempEdits = { ...pendingEdits[id] };
        setEditPickUpLocationLoading(true);
        try {
            const res = await Api('POST', VenueApiRoutes.editPickupLocationv2, {
                id: id.toString(),
                bar_id: authData?.selectedVenue?.id?.toString(),
                ...updatedData
            });

            if (res?.data?.status) {
                toast.success('Pickup location updated successfully');
                await getPickUpLocationList();
            } else {
                toast.error(res?.data?.message);
                setPendingEdits((prev) => ({
                    ...prev,
                    [id]: tempEdits
                }));
            }
            setEditPickUpLocationLoading(false);
        } catch (err) {
            setEditPickUpLocationLoading(false);
            setPendingEdits((prev) => ({
                ...prev,
                [id]: tempEdits
            }));
            toast.error(err?.response?.data?.message || 'Failed to update pickup location');
        }
    };

    return (
        <NewPageWrapper>
            <SpecialOpeningHoursWrraper>
                <div className="titleWrap">
                    <div className="subMainTitle">
                        <NewPageTitle className="subMainTitle">Special Opening Hours and Closures</NewPageTitle>
                    </div>
                    <p className="mainParagraph">
                        Set special hours or closures for holidays, events, or other unique occasions. These will temporarily override your regular menu hours
                    </p>
                    <div className="mainParagraph">
                        <FilledButton
                            buttonText={'Add new '}
                            background={'#e4e4fd'}
                            color={'#8280FF'}
                            style={{ width: '14em', heigth: '3.8em', border: '1px solid #8280FF' }}
                            onClick={() => setCreateModal(true)}
                        />
                    </div>
                </div>

                <div>
                    <Row>
                        <Col>
                            <NewDesignCustomLabel
                                id={'Details'}
                                label={'Details'}
                                className={'customeLabelClass'}
                            />
                        </Col>
                        <Col>
                            <NewDesignCustomLabel
                                id={'Dates'}
                                label={'Dates'}
                                className={'customeLabelClass'}
                            />
                        </Col>
                        <Col>
                            <NewDesignCustomLabel
                                id={'Special Hours'}
                                label={'Special Hours'}
                                className={'customeLabelClass'}
                            />
                        </Col>
                    </Row>
                </div>
                
                <hr className="customeHrClass" />

                <NewLoader loading={editPickupLocationLoading}>
                    {pickupLocationList.length > 0 ? (
                        pickupLocationList.map((pickupLocation) => (
                            <div key={pickupLocation.id} tabIndex={-1}>
                                {/* code */}
                            </div>
                        ))) : (
                        <div className="pa-t-100">
                            <EmptyData content="No special opening hours and closures set" />
                        </div>
                    )}
                </NewLoader>
            </SpecialOpeningHoursWrraper>
        </NewPageWrapper>
    );
};

export default SpecialOpeningHours;