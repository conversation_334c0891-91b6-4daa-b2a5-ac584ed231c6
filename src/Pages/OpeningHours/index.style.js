import styled from 'styled-components';

export const OpeningHoursWrraper = styled.div`
	font-family: nunitosans-regular !important;
	.mainTitle {
		padding-bottom: 7px;
	}
	.subMainTitle {
		font-size: 18px;
		font-family: nunitosans-bold;
		color: #2e2e2e;
	}
	.mainParagraph {
		font-size: 16px;
		font-family: nunitosans-regular;
		color: rgba(0, 0, 0, 1);
	}
	.customeHrClass {
		border-top: 1px solid rgba(148, 150, 152, 0.5) !important;
		margin-block: 22px;
	}
	.venueOpeningHoursContainer {
		width: 394px;
	}
	.checkboxWrapper {
		display: flex;
		gap: 10px;
		padding-block: 22px;
	}
	.openingHoursCheckboxWrapper {
		.newCustomCheckboxContainer {
			width: 112px !important;
			height: 37px !important;
			padding-inline: 9px !important;
		}
	}
	.menuCategoryTableWrapper {
		padding-top: 25px !important;
	}
	@media (max-width: 600px) {
		.mainTitle {
			padding-bottom: 5px;
		}
		.subMainTitle {
			font-size: 13px;
		}
		.mainParagraph {
			font-size: 12px;
		}
		.customeHrClass {
			margin-block: 15px;
		}
		.venueOpeningHoursContainer {
			width: 266px;
		}
		.checkboxWrapper {
			gap: 7px;
			padding-block: 15px;
		}
		.openingHoursCheckboxWrapper {
			.newCustomCheckboxContainer {
				width: 76px !important;
				height: 31px !important;
				padding-inline: 8px !important;
			}
		}
		.menuCategoryTableWrapper {
			padding-top: 17px !important;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.mainTitle {
			padding-bottom: 5px;
		}
		.subMainTitle {
			font-size: 14px;
		}
		.mainParagraph {
			font-size: 12px;
		}
		.customeHrClass {
			margin-block: 17px;
		}
		.venueOpeningHoursContainer {
			width: 296px;
		}
		.checkboxWrapper {
			gap: 8px;
			padding-block: 17px;
		}
		.openingHoursCheckboxWrapper {
			.newCustomCheckboxContainer {
				width: 84px !important;
				height: 34px !important;
				padding-inline: 9px !important;
			}
		}
		.menuCategoryTableWrapper {
			padding-top: 19px !important;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.mainTitle {
			padding-bottom: 5px;
		}
		.subMainTitle {
			font-size: 14px;
		}
		.mainParagraph {
			font-size: 12px;
		}
		.customeHrClass {
			margin-block: 17px;
		}
		.venueOpeningHoursContainer {
			width: 296px;
		}
		.checkboxWrapper {
			gap: 8px;
			padding-block: 17px;
		}
		.openingHoursCheckboxWrapper {
			.newCustomCheckboxContainer {
				width: 84px !important;
				height: 34px !important;
				padding-inline: 9px !important;
			}
		}
		.menuCategoryTableWrapper {
			padding-top: 19px !important;
		}
	}
`;
