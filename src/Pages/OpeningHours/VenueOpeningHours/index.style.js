import styled from 'styled-components';

export const VenueOpeningHoursWrraper = styled.div`
	font-family: nunitosans-regular !important;

	.subMainTitle {
		font-size: 18px;
		font-family: nunitosans-bold;
		color: #202224;
	}

	.editTitle {
		font-size: 18px;
		font-family: nunitosans-bold;
		color: #202224;
		text-decoration: underline;
		cursor: pointer;
	}

	.mainParagraph {
		font-size: 16px;
		font-family: nunitosans-regular;
		color: rgba(0, 0, 0, 1);
	}

	.venueHoursList {
		.venueHoursListItem {
			display: flex;
			justify-content: space-between;
			padding-top: 22px !important;
			.venueHoursListItemLabel {
				font-family: nunitosans-bold;
				color: #000000;
				font-size: 16px !important;
			}
			&:last-child {
				padding-bottom: 22px !important;
			}
		}
	}

	@media (max-width: 600px) {
		.subMainTitle {
			font-size: 12px;
		}

		.editTitle {
			font-size: 12px;
		}

		.mainParagraph {
			font-size: 11px;
		}

		.venueHoursList {
			.venueHoursListItem {
				padding-top: 15px !important;
				.venueHoursListItemLabel {
					font-size: 12px !important;
				}
				&:last-child {
					padding-bottom: 15px !important;
				}
			}
		}
	}

	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.subMainTitle {
			font-size: 13.5px;
		}

		.editTitle {
			font-size: 13.5px;
		}

		.mainParagraph {
			font-size: 12px;
		}

		.venueHoursList {
			.venueHoursListItem {
				padding-top: 17px !important;
				.venueHoursListItemLabel {
					font-size: 12px !important;
				}
				&:last-child {
					padding-bottom: 17px !important;
				}
			}
		}
	}

	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.subMainTitle {
			font-size: 13.5px;
		}

		.editTitle {
			font-size: 13.5px;
		}

		.mainParagraph {
			font-size: 12px;
		}

		.venueHoursList {
			.venueHoursListItem {
				padding-top: 17px !important;
				.venueHoursListItemLabel {
					font-size: 12px !important;
				}
				&:last-child {
					padding-bottom: 17px !important;
				}
			}
		}
	}
`;
