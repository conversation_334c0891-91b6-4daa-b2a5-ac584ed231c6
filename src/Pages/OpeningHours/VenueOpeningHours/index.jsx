import React from 'react';
import { VenueOpeningHoursWrraper } from './index.style';
import EmptyData from '../../../Components/Common/EmptyData';
import NewLoader from '../../../Components/Common/NewLoader';
import moment from 'moment';

const VenueOpeningHours = ({
	venueOpeningHours,
	handleEditModal,
	editVenuehoursLoading = false
}) => {
	return (
		<VenueOpeningHoursWrraper>
			<div className="d-flex justify-content-between align-items-center">
				<div className="subMainTitle">Venue Opening Hours</div>
				<span className="editTitle" onClick={handleEditModal}>
					Edit
				</span>
			</div>
			<p className="mainParagraph">
				Your venue will open and close based on the hours you set below.
			</p>
			<NewLoader loading={editVenuehoursLoading}>
				<div className="venueHoursList">
					{venueOpeningHours?.length ? (
						venueOpeningHours?.map((item, index) => (
							<div
								className={`venueHoursListItem`}
								key={item.weekDay}
								tabIndex={-1}
							>
								<div className="venueHoursListItemLabel">
									{moment()
										.day(item.weekDay + 1)
										.format('dddd')}
								</div>
								<div>
									{!item.isClosed ? (
										<p className="mainParagraph">
											Unavailable
										</p>
									) : (
										item.timeSlots.map((time, index) => (
											<p
												key={index}
												className="mainParagraph"
											>
												{time.openingHours} -{' '}
												{time.closingHours}
											</p>
										))
									)}
								</div>
							</div>
						))
					) : (
						<></>
					)}
				</div>
			</NewLoader>
		</VenueOpeningHoursWrraper>
	);
};

export default VenueOpeningHours;
