import { useSelector } from 'react-redux';
import ComingSoon from '../../Components/Common/ComingSoon';

import PageTitle from '../../Components/Common/PageTitle';
import PageWrapper from './index.style';

export const Rewards = () => {
	const allThemeData = useSelector((state) => ({ ...state.themeChanger }));

	return (
		<PageWrapper {...allThemeData}>
			<div className="page-header">
				<PageTitle className="pb-10" title="Rewards" />
			</div>
		</PageWrapper>
	);
};
