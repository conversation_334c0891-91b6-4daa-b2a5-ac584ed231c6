import { useDispatch, useSelector } from 'react-redux';
import 'react-tabs-scrollable/dist/rts.css';
import { useState } from 'react';

import { Col, Row } from 'reactstrap';
import { useNavigate } from 'react-router-dom';
import Scrollbars from 'react-custom-scrollbars';
import PageWrapper from './registerSubscription.style';
import SubscriptionCard from '../../Components/Authentication/SubscriptionCard';
import { VenueApiRoutes, VenuePanelRoutes } from '../../Utils/routes';
import VenueAccountVerificationModal from '../../Components/UserProfile/ConnectVenue/VenueAccountVerificationModal';
import Api from '../../Helper/Api';
import { toast } from 'react-toastify';
import authActions from '../../Redux/auth/actions';
import { somethingWentWrongError } from '../../Helper/somethingWentWrongError';

const currentSubscription = [
	{ title: '1 Connected Venue' },
	{ title: 'Unlimited Staff Profiles' },
	{ title: 'Basic Reports' },
	{ title: 'Live Order Data' },
	{ title: 'Remotely Manage/Update Venue App' }
];

const essentialSubscription = [
	{ title: '3 Connected Venue' },
	{ title: 'Unlimited Staff Profiles' },
	{ title: 'Venue Sales/Performance Analytics' },
	{ title: 'Live Order Data' },
	{ title: 'Remotely Manage/Update Venue App' }
];

const premiumSubscription = [
	{ title: 'Unlimited Connected Venues' },
	{ title: 'Unlimited Staff Profiles' },
	{ title: 'Venue Sales/Performance Analytics' },
	{ title: 'Live Order Data' },
	{ title: 'Customer Behavioural Analytics' },
	{ title: 'Remotely Manage/Update Venue App' }
];

export const RegisterSubscription = () => {
	const allThemeData = useSelector((state) => ({ ...state.themeChanger }));
	const authData = useSelector((state) => ({ ...state.auth }));
	const [isOpenAccountVerificationModal, setIsOpenAccountVerificationModal] =
		useState(false);
	const navigate = useNavigate();
	const dispatch = useDispatch();
	const handleCardButtonClick = async (id) => {
		try {
			const res = await Api('POST', VenueApiRoutes.subscription, {
				subscription_id: id
			});
			if (res?.data?.status) {
				dispatch(
					authActions.update_subscription({
						id: res?.data?.data?.subscription_id
					})
				);
				if (authData?.bars?.length === 0) {
					navigate(VenuePanelRoutes.connectVenue);
				} else {
					navigate(VenuePanelRoutes.dashboard);
				}
			} else {
				toast.error(res?.data?.message);
			}
		} catch (err) {
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	const handleAccountVerificationModal = () => {
		setIsOpenAccountVerificationModal(!isOpenAccountVerificationModal);
	};
	return (
		<Scrollbars autoHide>
			<PageWrapper {...allThemeData}>
				<h1 className="fs-32 bold-text text-center pa-b-32">
					Select Your Subscription
				</h1>
				<div className="pa-t-0 overflow-hidden">
					<Row className="g-2">
						<Col xl="4">
							<SubscriptionCard
								showTopButton={true}
								topButtonLabel="Most Popular"
								title={'Essential'}
								description="Everything you need to manage your MyTab account"
								amount={'0'}
								features={currentSubscription}
								buttonLabel={'Get Started'}
								handleButtonClick={() =>
									handleCardButtonClick('1')
								}
								activeButton={true}
							/>
						</Col>
						<Col xl="4">
							<SubscriptionCard
								title={'Classic'}
								description="Level up your business with access to Professional venue Sales/Performance Analytics and more venue accounts"
								amount={'49'}
								features={essentialSubscription}
								buttonLabel={'Coming Soon'}
							/>
						</Col>
						<Col xl="4">
							<SubscriptionCard
								title={'Premium'}
								description="Receive the best of MyTab with Premium reporting features including venue performance and customer behavioural analytics"
								amount={'79'}
								features={premiumSubscription}
								buttonLabel={'Coming Soon'}
							/>
						</Col>
					</Row>
				</div>
				<p className="fs-20 medium-text ptb-32 text-center">
					Prices in AUD & exclude GST.
				</p>
			</PageWrapper>
			<VenueAccountVerificationModal
				isOpen={isOpenAccountVerificationModal}
				handleModal={handleAccountVerificationModal}
				navigateTo={() => navigate(VenuePanelRoutes.dashboard)}
			/>
		</Scrollbars>
	);
};
