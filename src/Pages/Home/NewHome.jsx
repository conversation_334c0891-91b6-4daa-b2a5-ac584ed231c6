import React, { useEffect, useState } from 'react';
import MidCard from './NewHome/MidCard';
import ViewOne from '../../Assets/images/viewone.png';
import ViewTwo from '../../Assets/images/view2.png';
import DropdownIcon from '../../Assets/images/newDropdownIcon.svg';
import {
	DropdownItem,
	DropdownMenu,
	DropdownToggle,
	UncontrolledDropdown
} from 'reactstrap';
import LargeCard from './NewHome/LargeCard';
import LargeMidCard from './NewHome/LargeMidCard';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';
import authActions from '../../Redux/auth/actions';
import UserName from '../../Components/Layout/Header/UserName';
import { myTabReportsData, myTabSupportsData } from './utils';
import Api from '../../Helper/Api';
import { VenueApiRoutes, VenuePanelRoutes } from '../../Utils/routes';
import { toast } from 'react-toastify';
import StatusBar from './NewHome/StatusBar';
import IntroductionSection from './NewHome/IntroductionSection';

import NewHomeWrraper from './newHome.style';
import { useNavigate } from 'react-router-dom';
import NewPageWrapper from '../../Components/Common/NewPageWrapper';

const NewHome = () => {
	const navigate = useNavigate();
	const [statisticsLoading, setStatisticsLoading] = useState(true);
	const [statisticsData, setStatisticsData] = useState(null);

	const authData = useSelector((state) => state?.auth);
	const connectedVenueList = authData?.bars;
	const selectedVenue = authData?.selectedVenue;

	const dispatch = useDispatch();

	const getStatistics = async () => {
		try {
			setStatisticsLoading(true);
			const res = await Api('POST', VenueApiRoutes.getVenueStatistics, {
				bar_id: selectedVenue?.id
			});
			if (res?.data?.status) {		
				const statsData = res?.data?.data?.list[0];
				setStatisticsData(statsData);
				// Store the actual statistics data in Redux
				dispatch(authActions.set_statistics_data(res?.data?.data?.timezone));
				// Store timezone separately
				dispatch(authActions.set_timezone(res?.data?.data?.timezone));
			} else {
				toast.error(res?.data?.message);
			}
			setStatisticsLoading(false);
		} catch (err) {
			setStatisticsLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	const handleSelectVenue = (data) => {
		if (data) {
			dispatch(
				authActions.set_selected_venue({
					...data
				})
			);
		}
	};

	useEffect(() => {
		if (selectedVenue?.id) {
			(async () => {
				await getStatistics();
			})();
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [selectedVenue?.id]);

	useEffect(() => {
		if (selectedVenue == null) {
			const fetchVenueDetails = async () => {
				try {
					let res = await Api(
						'GET',
						VenueApiRoutes.getConnectedVenue
					);
					let connectedVenueData = res?.data?.data?.map(
						(item, index) => {
							return {
								avatar: item?.bar?.avatar,
								docketStatus: item?.bar?.docketStatus,
								id: item?.bar_id,
								managerName: item?.bar?.managerName,
								posStatus: item?.bar?.posStatus,
								restaurantName: item?.bar?.restaurantName,
								venueId: item?.bar?.venueId,
								docketCommission: item?.bar?.docketCommission,
								posFee: item?.bar?.posFee,
								passcodeStatus: item?.bar?.passcodeStatus,
								passcodeLength: item?.bar?.passcodeLength,
								countryCode: item?.bar?.countryCode,
								mobile: item?.bar?.mobile
							};
						}
					);
					if (res?.data?.data?.length === 0) {
						dispatch(authActions.update_venue_list([]));
					} else {
						dispatch(
							authActions.update_venue_list(connectedVenueData)
						);
						if (selectedVenue == null) {
							dispatch(
								authActions.set_selected_venue(
									connectedVenueData[0]
								)
							);
						}
					}
				} catch (error) {
					console.log(error);
				}
			};
			fetchVenueDetails();
		}
	}, []);

	return (
		<NewPageWrapper>
			<NewHomeWrraper>
				<div className="titleWrap">
					<div className="d-flex align-items-center gap-3 ">
						<UncontrolledDropdown>
							<DropdownToggle
								style={{
									paddingInline: 0,
									background: 'transparent',
									border: 'none',
									outline: 'none'
								}}
							>
								<div className="d-flex align-items-center gap-3">
									<h1 className="mainTitle">
										{selectedVenue?.restaurantName}
									</h1>
									<img
										caret
										src={DropdownIcon}
										className="dropdown-image"
										alt="dropdownIcon"
									/>
								</div>
							</DropdownToggle>
							<DropdownMenu className="custom-dropdown-menu-width">
								{connectedVenueList.map((value, index) => (
									<DropdownItem
										key={index}
										onClick={() => handleSelectVenue(value)}
									>
										{value.restaurantName}
									</DropdownItem>
								))}
							</DropdownMenu>
						</UncontrolledDropdown>
					</div>
					<p className="mainParagraph">
						Welcome <UserName transparent />, let’s get started
					</p>
				</div>
				{/*  */}

				<StatusBar
					statisticsData={statisticsData}
					statisticsLoading={statisticsLoading}
				/>
				{/*  */}
				<div className="margin-top-class">
					<p className="commonSectionHeading">
						Introduction to your MyTab Management Portal
					</p>
					<IntroductionSection />
				</div>
				<div className="margin-top-class ">
					<p className="commonSectionHeading">
						Simplifiy your service with MyTab integrations
					</p>
					<div className="row mt-1 gap-4 gap-md-4 gap-xl-0">
						<div className="col-xl-8 col-md-12 col-12">
							<LargeCard
								onClick={() =>
									navigate(VenuePanelRoutes.posIntegration)
								}
								largeMode
								buttonText={'Add integration'}
								background={'#e6e6ff'}
								color={'#8280FF'}
								buttonType={1}
								description={
									'MyTab’s leading integrations help you serve seamlessly  at the touch of a button.'
								}
								title={
									'Upgrade your MyTab Venue with an integration'
								}
								imageSrc={ViewOne}
							/>
						</div>
						<div className="col-xl-4 col-md-12 col-12">
							<LargeMidCard
								onClick={() =>
									window.open('https://n1g6fmcikpp.typeform.com/to/kXSk7Qf3', '_blank')
								}
								buttonText={'Refer now'}
								background={'#e6e6ff'}
								color={'#8280FF'}
								buttonType={1}
								description={
									'Love MyTab? Refer a friend to MyTab, and you’ll get a $250aud pre-paid Mastercard gift card when they become a venue.'
								}
								title={'Refer a friend & earn $250'}
							/>
						</div>
					</div>
				</div>
				<div className="margin-top-class ">
					<p className="commonSectionHeading">
						Discover more customers with MyTab
					</p>
					<div className="row mt-1 gap-4 gap-xl-0">
						<div className="col-xl-8 col-12">
							<LargeCard
								onClick={() =>
									navigate(VenuePanelRoutes.comingSoon)
								}
								largeMode
								buttonText={'Coming soon'}
								background={'#ccf0eb'}
								color={'#00B69B'}
								buttonType={1}
								description={
									'Use MyTab ads to increase sales, drive in-venue traffic, venue awareness and find thousands of new customers. '
								}
								imageSrc={ViewTwo}
								title={
									'Coming soon: Grow your customer base with MyTab Ads'
								}
								reverse
							/>
						</div>
						<div className="col-xl-4 col-12">
							<LargeMidCard
								onClick={() =>
									navigate(VenuePanelRoutes.segments)
								}
								buttonText={'View segments'}
								background={'#ccf0eb'}
								color={'#00B69B'}
								buttonType={1}
								description={
									'Explore your customers’ buying behaviours, preferences and patterns. Harness this data to create tailored discounts, targeted ads and personalised experiences that drive loyalty and maximise revenue.'
								}
								title={'Data that drives profit'}
							/>
						</div>
					</div>
				</div>
				<div className="margin-top-class ">
					<p className="commonSectionHeading">
						Make data informed decisions with MyTab Reports
					</p>
					<div className="row mt-1 gap-4 gap-md-0 mb-md-2 mt-md-2 gap-xl-0">
						{myTabReportsData.map((reportsData) => (
							<div className="col-xs-12 col-sm-12 col-md-6 col-lg-4 col-xl-4 mb-md-4 mb-xl-0 mb-0">
								<MidCard
									onClick={() => navigate(reportsData.link)}
									buttonText={reportsData.buttonTitle}
									background={'#dae6ff'}
									color={'#4880FF'}
									buttonType={1}
									description={reportsData.description}
									title={reportsData.title}
									key={reportsData.title}
								/>
							</div>
						))}
					</div>
				</div>
				<div className="margin-top-class ">
					<p className="commonSectionHeading">Support</p>
					<div className="row mt-1 gap-4 gap-md-0 gap-xl-0">
						{myTabSupportsData.map((supportData) => (
							<div className="col-xs-12 col-md-6 col-sm-12 col-md-4 col-lg-4 col-xl-4 mb-md-4 mb-lg-0 mb-xl-0 mb-0">
								<MidCard
									onClick={() => navigate(supportData.link)}
									background={'#f4d7fb'}
									color={'#C737EB'}
									buttonType={1}
									description={supportData.description}
									buttonText={supportData.buttonTitle}
									title={supportData.title}
									key={supportData.title}
								/>
							</div>
						))}
					</div>
				</div>
			</NewHomeWrraper>
		</NewPageWrapper>
	);
};

export default NewHome;
