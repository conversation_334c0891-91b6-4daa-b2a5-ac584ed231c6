import { format } from 'd3-format';
import convertNumToKM from '../../Helper/convertNumToKM';
import { VenuePanelRoutes } from '../../Utils/routes';

export const myTabReportsData = [
	{
		title: 'Item summary report',
		description: `Know what's selling and what's not, so you can make informed decisions anywhere, anytime.`,
		buttonTitle: 'View report',
		link: VenuePanelRoutes.summaryReport
	},
	{
		title: 'Coming soon: Venue analytics',
		description: `Analyse accurate data so you can make critical cost-saving decisions that reduce costs and maximise your margins.`,
		buttonTitle: 'Coming soon',
		link: VenuePanelRoutes.salesAnalytics
	},
	{
		title: 'Customer analytics',
		description: `Understand quality transactional sales and demographic data straight from real customers.`,
		buttonTitle: 'View customers',
		link: VenuePanelRoutes.customers
	}
];

export const myTabSupportsData = [
	{
		title: 'FAQ support forum',
		description: `Have a question? Read our FAQ’s for all our tips and tricks on how to manage MyTab. `,
		link: VenuePanelRoutes.support,
		buttonTitle: 'Visit FAQ forum'
	},
	{
		title: 'Coming soon: Live support',
		description: `Need a helping hand? Talk to one of our MyTab specialists today.`,
		link: VenuePanelRoutes.liveSupport,
		buttonTitle: 'Coming soon'
	},
	{
		title: 'Email us',
		description: `Have a question? Reach out to us and we will be happy to help.`,
		link: VenuePanelRoutes.emailUs,
		buttonTitle: 'Email us'
	}
];

export const formatOrderBreakdownData = (data) => {
	return data?.map((item, index) => {
		return {
			name: item?.name,
			value: Number(item?.value),
			fill: index === 0 ? '#F94D73' : '#FF9568'
		};
	});
};

export const formatServiceTypeRevenueData = (data) => {
	return data?.map((item, index) => {
		return {
			name: item?.name,
			value: Number(item?.value),
			fill: index === 0 ? '#F94D73' : '#FF9568'
		};
	});
};

export const formatGrossSalesPerHourData = (data) => {
	const chartConfig = [
		{
			name: 'Gross Sales',
			dataKey: 'grossSales',
			fill: '#FF5F5F'
		}
	];
	const chartData = data?.map((item) => {
		return {
			name: '' + format('02')(Number(item?.time)),
			grossSales: convertNumToKM(Number(item?.count))
		};
	});
	return {
		chartConfig: chartConfig,
		chartData: chartData
	};
};

export const formatCustomerCountByTimeData = (data) => {
	const chartConfig = [
		{
			name: 'Orders',
			dataKey: 'popularTimes',
			fill: '#FF5F5F'
		}
	];
	const chartData = data?.map((item) => {
		return {
			name: '' + format('02')(Number(item?.time)),
			popularTimes: Number(item?.count)
		};
	});
	return {
		chartConfig: chartConfig,
		chartData: chartData
	};
};

export const formatMostOrderedMenuItemsData = (data) => {
	const chartConfig = [
		{
			name: 'Ordered',
			dataKey: 'orderedMenu',
			fill: '#FF5F5F'
		}
	];
	const chartData = data?.map((item) => {
		return {
			name: item?.productName,
			orderedMenu: Number(item?.soldQunitity)
		};
	});
	return {
		chartConfig: chartConfig,
		chartData: chartData
	};
};
