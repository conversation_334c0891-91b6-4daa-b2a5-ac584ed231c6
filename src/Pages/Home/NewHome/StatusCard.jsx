import React from 'react';
import CardWrraper from './cards.style';

import upG from '../../../Assets/images/upG.svg';
import downG from '../../../Assets/images/downG.svg';

const StatusCard = ({ title, amount, amountUnit }) => {

	if(amount === null) {
		amount = 0;
	}
	
	return (
		<CardWrraper>
			<div className="StatusCard">
				<p className="smallTitle">{title}</p>
				<h6 className="amountText">
					{amountUnit}{amount}
				</h6>
			</div>
		</CardWrraper>
	);
};

export default StatusCard;
