import React from 'react';
import CardWrraper from './cards.style';
import completeG from '../../../Assets/images/completeG.svg';
import {
	FilledButton,
	OutlineButton
} from '../../../Components/Layout/Buttons';

const LargeCard = ({
	title,
	description,
	buttonType,
	color,
	background,
	setUpCompleted,
	buttonText,
	setUpCompletedButtonText,
	imageSrc,
	reverse,
	onClick
}) => {
	return (
		<CardWrraper>
			<div className="LargeCard">
				{reverse ? (
					<div className="reverse">
						<div className="d-flex flex-column justify-content-between w-50 h-100">
							<div>
								<div>
									<p className="mainTitle">{title}</p>
								</div>
								{setUpCompleted && (
									<div className="ext-end">
										{setUpCompleted && (
											<img
												src={completeG}
												alt="completeF"
											/>
										)}
									</div>
								)}
							</div>

							<p className="mainPragraph">{description}</p>
							{buttonType === 0 ? (
								<>
									{setUpCompleted ? (
										<OutlineButton
											onClick={onClick}
											buttonText={buttonText}
											style={{
												border: `1px solid ${color}`,
												borderRadius: '4.5px'
											}}
										/>
									) : (
										<OutlineButton
											onClick={onClick}
											buttonText={
												setUpCompletedButtonText
											}
											style={{
												border: `1px solid ${color}`,
												borderRadius: '4.5px'
											}}
										/>
									)}
								</>
							) : (
								<>
									{setUpCompleted ? (
										<FilledButton
											onClick={onClick}
											background={background}
											color={color}
											style={{
												border: `1px solid ${color}`,
												borderRadius: '4.5px'
											}}
											buttonText={
												setUpCompletedButtonText
											}
										/>
									) : (
										<FilledButton
											onClick={onClick}
											background={background}
											color={color}
											style={{
												border: `1px solid ${color}`,
												borderRadius: '4.5px'
											}}
											buttonText={buttonText}
										/>
									)}
								</>
							)}
						</div>
						<div className="sideImageWrraper justify-content-start">
							<img
								src={imageSrc}
								alt="img"
								className="sideImage"
								width={'100%'}
							/>
						</div>
					</div>
				) : (
					<div className="reverse">
						<div className="sideImageWrraper">
							<img
								src={imageSrc}
								alt="img"
								className="sideImage"
								width={'100%'}
							/>
						</div>
						<div className="d-flex flex-column justify-content-between w-50 h-100">
							<div className="">
								<div>
									<p className="mainTitle">{title}</p>
								</div>
								{setUpCompleted && (
									<div className="text-end">
										{setUpCompleted && (
											<img
												src={completeG}
												alt="completeF"
											/>
										)}
									</div>
								)}
							</div>

							<p className="mainPragraph">{description}</p>
							{buttonType === 0 ? (
								<>
									{setUpCompleted ? (
										<OutlineButton
											onClick={onClick}
											buttonText={buttonText}
											style={{
												border: `1px solid ${color}`,
												borderRadius: '4.5px'
											}}
										/>
									) : (
										<OutlineButton
											onClick={onClick}
											buttonText={
												setUpCompletedButtonText
											}
											style={{
												border: `1px solid ${color}`,
												borderRadius: '4.5px'
											}}
										/>
									)}
								</>
							) : (
								<>
									{setUpCompleted ? (
										<FilledButton
											onClick={onClick}
											background={background}
											color={color}
											style={{
												border: `1px solid ${color}`,
												borderRadius: '4.5px'
											}}
											buttonText={
												setUpCompletedButtonText
											}
										/>
									) : (
										<FilledButton
											onClick={onClick}
											background={background}
											color={color}
											style={{
												border: `1px solid ${color}`,
												borderRadius: '4.5px'
											}}
											buttonText={buttonText}
										/>
									)}
								</>
							)}
						</div>
					</div>
				)}
			</div>
		</CardWrraper>
	);
};

export default LargeCard;
