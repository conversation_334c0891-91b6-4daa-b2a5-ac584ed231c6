import React from 'react';
import StatusCard from './StatusCard';
import Skeleton from 'react-loading-skeleton';

const StatusBar = ({ statisticsData, statisticsLoading }) => {
	function calculateAndRound(statisticsData) {
		if (
			statisticsData?.amount == null ||
			statisticsData?.total_order == null ||
			statisticsData.amount === 0 ||
			statisticsData.total_order === 0
		) {
			return 0; // or whatever default value you want
		}

		const result = statisticsData.amount / statisticsData.total_order;
		return Number(result)?.toFixed(2);
		// return Math.round(result * 100) / 100;
	}

	const formatNumberWithCommas = (number) => {
		if (!number) return '0.00';
		return Number(number).toLocaleString('en-US', { minimumFractionDigits: 2 });
	};


	return (
		<>
			{statisticsLoading ? (
				<div className="row mt-3 pt-1 gap-4 gap-md-0 gap-xl-0">
					{[1, 2, 3, 4].map((_, index) => {
						return (
							<div
								className="col-xs-12 col-md-6 col-lg-4 col-xl-3 mb-md-3  mb-sm-0"
								key={_}
							>
								<Skeleton
									height="120px"
									width={'100%'}
									borderRadius={10}
								/>
							</div>
						);
					})}
				</div>
			) : (
				<div className="row mt-3 pt-1 gap-4 gap-md-0 gap-xl-0">
					<div className="statusCard-padding col-xs-12 col-md-6 col-lg-4 col-xl-3 mb-md-3 mb-sm-0">
						<StatusCard
							title={'Total Gross Sales'}
							amountUnit={'$'}
							amount={formatNumberWithCommas(
								statisticsData?.amount
							)}
						/>
					</div>
					<div className="statusCard-padding col-xs-12 col-md-6 col-lg-4 col-xl-3 mb-md-3 mb-sm-0">
						<StatusCard
							title={'Total Orders'}
							amountUnit={''}
							amount={statisticsData?.total_order}
						/>
					</div>
					<div className="statusCard-padding col-xs-12 col-md-6 col-lg-4 col-xl-3 mb-md-3 mb-sm-0">
						<StatusCard
							title={'Total Customers'}
							amountUnit={''}
							amount={statisticsData?.total_user}
						/>
					</div>
					<div className="statusCard-padding col-xs-12 col-md-6 col-lg-4 col-xl-3 mb-md-3 mb-sm-0">
						<StatusCard
							title={'Average Customer Spend'}
							amountUnit={'$'}
							amount={calculateAndRound(statisticsData)}
						/>
					</div>
				</div>
			)}
		</>
	);
};

export default StatusBar;
