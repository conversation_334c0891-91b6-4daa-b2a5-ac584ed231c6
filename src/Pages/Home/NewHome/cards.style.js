import styled from 'styled-components';

const cardHeadingText = `20px`;
const cardHeadingTextW = `700`;
const cardHeadingColor = `rgba(49, 49, 50, 0.92)`;

const cardParagrpahText = '16px';
const cardParagrpahTextW = '400';
const cardParagrpahColor = 'rgba(46, 46, 46, 1)';

const CardWrraper = styled.div`
	height: 100%;

	.reverse {
		display: flex;
		flex-direction: row-reverse;
		height: 100%;
		width: 100%;
		gap: 29px;
	}
	.StatusCard {
		height: 110px;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		padding: 15px 20px;
		width: 93%;
		background: #ffffff;
		border-radius: 3px;
		border: 0.6px solid rgba(213, 213, 213, 1);
		box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);

		.smallTitle {
			font-size: 16px;
			font-family: nunitosans-regular;
			color: #6c6d6f;
		}

		.amountText {
			font-size: 28px;
			font-family: nunitosans-bold;
			font-weight: 450px;
			color: #202224;
		}

		.growthText {
			span {
				font-size: 16px;
				font-family: nunitosans-semi-bold;
				margin-right: 2px;
			}

			font-size: 16px;
			font-family: nunitosans-semi-bold;
			color: #606060;
		}
	}

	.LargeCard {
		height: 260px;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		padding: 23px 28px;

		background: #ffffff;
		border-radius: 5px;
		border: 0.6px solid rgba(213, 213, 213, 1);

		.mainTitle {
			margin: 0px;
			font-size: 20px;
			font-family: nunitosans-bold;
			color: rgba(49, 49, 50, 0.92);
		}

		.mainPragraph {
			font-size: 16px;
			font-family: nunitosans-regular;
			color: rgba(46, 46, 46, 1);
		}

		.sideImageWrraper {
			height: inherit;
			display: flex;
			justify-content: end;
			align-items: center;
			width: 50%;
			.sideImage {
				width: 100%;
				height: 100%;
				/* width: 429px;
				height: 218px; */
			}
		}
	}

	.LargeMidCard {
		height: 260px;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		padding: 23px 28px;
		width: 100%;
		background: #ffffff;
		border-radius: 5px;
		border: 0.6px solid rgba(213, 213, 213, 1);

		.mainTitle {
			margin: 0px;
			font-size: 20px;
			font-family: nunitosans-bold;
			color: rgba(49, 49, 50, 0.92);
		}

		.mainPragraph {
			font-size: 16px;
			font-family: nunitosans-regular !important;
			color: rgba(46, 46, 46, 1);
		}
	}

	.MidCard {
		height: 207px;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		padding: 21px 28px;
		width: 97%;
		background: #ffffff;
		border-radius: 4px;
		border: 0.6px solid rgba(213, 213, 213, 1);

		.mainTitle {
			margin: 0px;
			font-size: 20px;
			font-family: nunitosans-bold;
			color: #313132eb !important;
			margin-bottom: 5px;
		}

		.mainPragraph {
			font-size: 16px;
			font-family: nunitosans-regular;
			color: rgba(46, 46, 46, 1);
			margin-bottom: 10px;
		}
	}

	@media (max-width: 600px) {
		.StatusCard {
			.upDownIcon {
				height: 12px;
				width: 12px;
			}
			.smallTitle {
				font-family: nunitosans-regular;
				font-size: 12px;
			}

			.amountText {
				font-size: 19px;
				font-family: nunitosans-bold;
				font-weight: 450px;
			}

			.growthText {
				span {
					font-size: 12px;
					font-family: nunitosans-semi-bold;
				}
				font-family: nunitosans-semi-bold;
				font-size: 12px;
			}
		}

		.LargeMidCard {
			.completeF {
				margin-top: -30px;
				margin-right: -10px;
				height: 20px;
				width: 20px;
			}
			.mainTitle {
				color: #414142;
				font-family: nunitosans-bold;
				font-size: 15px;
			}

			.mainPragraph {
				font-size: 12px;
				font-family: nunitosans-regular;
			}
		}
		.LargeCard {
			width: 100%;
			.completeF {
				margin-top: -30px;
				margin-right: -10px;
				height: 20px;
				width: 20px;
			}
			.mainTitle {
				color: #414142;
				font-family: nunitosans-bold;
				font-size: 15px;
			}

			.mainPragraph {
				font-size: 12px;
				font-family: nunitosans-regular;
			}
		}
		.MidCard {
			height: 167.6px;
			padding: 17px 21px;
			.completeF {
				margin-top: -30px;
				margin-right: -10px;
				height: 20px;
				width: 20px;
			}
			.mainTitle {
				font-family: nunitosans-bold !important;
				color: #313132eb !important;
				font-size: 15px;
			}

			.mainPragraph {
				font-size: 12px;
				font-family: nunitosans-regular;
			}
		}
	}

	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.StatusCard {
			height: 86px;
			padding: 12px 15px;
			justify-content: space-between;
			.upDownIcon {
				height: 17px;
				width: 17px;
			}
			.smallTitle {
				font-family: nunitosans-regular;
				font-size: 12px;
			}

			.amountText {
				font-size: 21px;
				font-family: nunitosans-bold;
				font-weight: 450px;
				color: #202224;
			}

			.growthText {
				span {
					font-size: 12px;
					font-family: nunitosans-semi-bold;
				}
				font-family: nunitosans-semi-bold;
				font-size: 12px;
			}
		}

		.statusCard-padding {
			padding-inline: 1.32vw;
		}

		.MidCard {
			height: 167.6px;
			padding: 17px 21px;
			.completeF {
				margin-top: -30px;
				margin-right: -10px;
				height: 20px;
				width: 20px;
			}

			.mainTitle {
				margin: 0px;
				font-size: 15px;
				color: #313132eb;
				font-family: nunitosans-bold !important;
			}
			.mainPragraph {
				font-size: 12px;
				font-family: nunitosans-regular;
			}
			.sideImageWrraper {
				height: inherit;
				.sideImage {
					width: 100%;
					height: 100%;
					max-height: 160px;
				}
			}
		}
		.LargeMidCard {
			height: 210.6px;
			width: 97%;
			padding: 18.6px 21px;
			.completeF {
				margin-top: -30px;
				margin-right: -10px;
				height: 20px;
				width: 20px;
			}

			.mainTitle {
				margin: 0px;
				font-size: 15px;
				color: #414142;
				font-family: nunitosans-bold;
			}
			.mainPragraph {
				font-size: 12px;
				font-family: nunitosans-regular;
			}
		}

		.LargeCard {
			height: 210.6px;
			width: 98%;

			padding: 18.6px 21px;
			.completeF {
				margin-top: -30px;
				margin-right: -10px;
				height: 20px;
				width: 20px;
			}

			.mainTitle {
				margin: 0px;
				font-size: 15px;
				color: #414142;
				font-family: nunitosans-bold;
			}
			.mainPragraph {
				font-size: 12px;
				font-family: nunitosans-regular;
			}
			.sideImageWrraper {
				height: inherit;
				.sideImage {
					/* width: 321.75px;
					height: 163.5px; */
					width: 100%;
					height: 100%;
				}
			}
		}
	}

	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.StatusCard {
			height: 86px;
			padding: 12px 15px;
			justify-content: space-between;
			.upDownIcon {
				height: 17px;
				width: 17px;
			}
			.smallTitle {
				font-family: nunitosans-regular;
				font-size: 12px;
			}

			.amountText {
				font-size: 21px;
				font-family: nunitosans-bold;
				font-weight: 450px;
				color: #202224;
			}

			.growthText {
				span {
					font-size: 12px;
					font-family: nunitosans-semi-bold;
				}
				font-family: nunitosans-semi-bold;
				font-size: 12px;
			}
		}

		.statusCard-padding {
			padding-inline: 1.32vw;
		}

		.MidCard {
			height: 167.6px;
			padding: 17px 21px;
			.completeF {
				margin-top: -30px;
				margin-right: -10px;
				height: 20px;
				width: 20px;
			}

			.mainTitle {
				margin: 0px;
				font-size: 15px;
				color: #313132eb;
				font-family: nunitosans-bold;
			}
			.mainPragraph {
				font-size: 12px;
				font-family: nunitosans-regular;
			}
			.sideImageWrraper {
				height: inherit;
				.sideImage {
					width: 100%;
					height: 100%;
					/* max-height: 160px; */
				}
			}
		}
		.LargeMidCard {
			height: 210.6px;
			width: 97%;
			padding: 18.6px 21px;
			.completeF {
				margin-top: -30px;
				margin-right: -10px;
				height: 20px;
				width: 20px;
			}

			.mainTitle {
				margin: 0px;
				font-size: 15px;
				color: #414142;
				font-family: nunitosans-bold;
			}
			.mainPragraph {
				font-size: 12px;
				font-family: nunitosans-regular;
			}
		}

		.LargeCard {
			height: 210.6px;
			width: 98%;

			padding: 18.6px 21px;
			.completeF {
				margin-top: -30px;
				margin-right: -10px;
				height: 20px;
				width: 20px;
			}

			.mainTitle {
				margin: 0px;
				font-size: 15px;
				color: #414142;
				font-family: nunitosans-bold;
			}
			.mainPragraph {
				font-size: 12px;
				font-family: nunitosans-regular;
			}
			.sideImageWrraper {
				height: inherit;
				.sideImage {
					width: 100%;
					height: 100%;
					/* width: 321.75px;
					height: 163.5px; */
				}
			}
		}
	}

	@media only screen and (min-width: 1824px) {
		.MidCard {
			height: 207px;
			padding: 21px 28px;
			.completeF {
				margin-top: -30px;
				margin-right: -10px;
				height: 29px;
				width: 29px;
			}
		}

		.LargeMidCard {
			height: 260px;
			width: 96.9%;
			padding: 23px 28px;
			.completeF {
				margin-top: -30px;
				margin-right: -10px;
				height: 29px;
				width: 29px;
			}
			.mainTitle {
				font-size: 20px !important;
			}
		}

		.LargeCard {
			height: 260px;
			width: 97.92%;
			padding: 23px 28px;
			.completeF {
				margin-top: -30px;
				margin-right: -10px;
				height: 29px;
				width: 29px;
			}

			.mainTitle {
				font-size: 20px;
			}

			.sideImageWrraper {
				height: inherit;
				.sideImage {
					width: 100%;
					height: 100%;
					/* width: 429px;
					height: 218px; */
				}
			}
		}
	}
`;

export default CardWrraper;
