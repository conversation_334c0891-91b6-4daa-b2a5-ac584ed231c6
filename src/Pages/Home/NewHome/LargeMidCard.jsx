import React from 'react';
import CardWrraper from './cards.style';
import {
	FilledButton,
	OutlineButton
} from '../../../Components/Layout/Buttons';
import completeG from '../../../Assets/images/completeG.svg';

const LargeMidCard = ({
	title,
	description,
	buttonType,
	color,
	background,
	setUpCompleted,
	buttonText,
	setUpCompletedButtonText = false,
	imageSrc,
	reverse,
	onClick
}) => {
	return (
		<CardWrraper>
			<div className="LargeMidCard">
				<div className="row align-items-center">
					<div className={setUpCompleted ? 'col-9' : 'col-12'}>
						<p className="mainTitle">{title}</p>
					</div>
					{setUpCompleted && (
						<div className="col-3 text-end">
							<img
								src={completeG}
								alt="completeF"
								className="completeF"
							/>
						</div>
					)}
				</div>

				<p className="mainPragraph">{description}</p>
				{buttonType === 0 ? (
					<>
						{setUpCompleted ? (
							<OutlineButton
								onClick={onClick}
								buttonText={setUpCompletedButtonText}
								style={{
									border: `1px solid ${color}`,
									borderRadius: '4.5px'
								}}
							/>
						) : (
							<OutlineButton
								buttonText={buttonText}
								onClick={onClick}
								style={{
									border: `1px solid ${color}`,
									borderRadius: '4.5px'
								}}
							/>
						)}
					</>
				) : (
					<>
						{setUpCompleted ? (
							<FilledButton
								onClick={onClick}
								background={background}
								color={color}
								style={{
									border: `1px solid ${color}`,
									borderRadius: '4.5px'
								}}
								buttonText={setUpCompletedButtonText}
							/>
						) : (
							<FilledButton
								onClick={onClick}
								background={background}
								color={color}
								style={{
									border: `1px solid ${color}`,
									borderRadius: '4.5px'
								}}
								buttonText={buttonText}
							/>
						)}
					</>
				)}
			</div>
		</CardWrraper>
	);
};

export default LargeMidCard;
