import React from 'react';
import MidCard from './MidCard';
import { useNavigate } from 'react-router-dom';
import { VenuePanelRoutes } from '../../../Utils/routes';
import { toast } from 'react-toastify';

const IntroductionSection = () => {
	const navigate = useNavigate();
	return (
		<div className="row Introduction-row mt-1 gap-4 gap-md-0 gap-xl-0">
			<div className="col-xs-12 card-height  col-md-6 col-sm-12  col-lg-4 col-xl-4 mb-xl-0 mb-lg-0 mb-md-3 mb-sm-0">
				<MidCard
					buttonText={'Start tutorial'}
					background={'#dae6ff'}
					color={'#4880FF'}
					buttonType={0}
					onClick={() => {
						toast.success('This feature is coming soon.');
					}}
					description={
						'Start the tutorial to learn how to get the most out of MyTab.'
					}
					title={'Coming soon: Get started tutorial'}
				/>
			</div>
			<div className="col-xs-12  col-md-6 col-sm-12  col-lg-4 col-xl-4 mb-xl-0 mb-lg-0 mb-md-3 mb-sm-0">
				<MidCard
					setUpCompleted
					buttonText={'Start tutorial'}
					setUpCompletedButtonText={'Review now'}
					background={'#dae6ff'}
					color={'#4880FF'}
					buttonType={0}
					onClick={() => {
						toast.success('Your Management Portal setup is complete.');
					}}
					description={
						'Review your set up checklist to make sure your venue account is ready to go.'
					}
					title={'Finish set up'}
				/>
			</div>
			<div className="col-xs-12  col-md-6 col-sm-12  col-lg-4 col-xl-4 mb-xl-0 mb-lg-0 mb-md-3 mb-sm-0">
				<MidCard
					onClick={() => navigate(VenuePanelRoutes.support)}
					buttonText={'Learn more'}
					background={'#dae6ff'}
					color={'#4880FF'}
					buttonType={0}
					description={
						'Have a question? Read our Get Started FAQ Support Forum for the best tips on setting up MyTab.'
					}
					title={'Support'}
				/>
			</div>
		</div>
	);
};

export default IntroductionSection;
