import styled from 'styled-components';

const NewHomeWrraper = styled.div`
	// padding: 1.7vw;

	.titleWrap {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;

		.mainTitle {
			font-size: 32px;
			font-family: nunitosans-bold;

			color: #202224;
		}

		.mainParagraph {
			font-size: 16px;
			font-weight: 400;
			font-family: nunitosans-regular;
			color: #202224;
		}
	}

	.custom-dropdown-menu-width {
		min-width: fit-content !important;
		width: fit-content !important;
		max-width: 332px !important;
	}

	.dropdown-image {
		height: 19px;
		width: 19px;
	}

	.dropdown-item,
	.dropdownToggle {
		font-size: 14px !important;
		white-space: normal;
	}
	.dropButton .dropdown-item.active,
	.dropdown-item:active,
	.dropdown-item:hover {
		color: var(--bs-dropdown-link-active-color);
		text-decoration: none;
		background-color: #fa7480;
	}
	.margin-top-class {
		margin-top: 2rem;
	}
	.commonSectionHeading {
		font-size: 16px;
		font-family: nunitosans-bold;
		color: #313132eb;
	}

	@media (max-width: 600px) {
		// padding: 5vw;

		.titleWrap {
			gap: 0.8rem;
			.mainTitle {
				font-size: 21px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 13px;
				color: #202224;
				font-family: nunitosans-regular;
			}
		}
		.margin-top-class {
			margin-top: 2rem;
		}
		.commonSectionHeading {
			margin-top: 2rem;
			color: #313132eb !important;
			font-family: nunitosans-bold;
			font-size: 14px;
		}
		.custom-dropdown-menu-width {
			min-width: fit-content !important;
			width: fit-content !important;
			max-width: 270px !important;
		}
	}

	@media only screen and (min-width: 600px) and (max-width: 1299px) {
		.titleWrap {
			gap: 0.8rem;
			.mainTitle {
				font-size: 24px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 13px;
				font-family: nunitosans-regular;
			}
		}
		.dropdown-image {
			height: 15px;
			width: 15px;
		}
		.margin-top-class {
			margin-top: 2rem;
		}
		.commonSectionHeading {
			font-size: 12px;
			color: #313132eb !important;
			font-family: nunitosans-bold;
		}
		.custom-dropdown-menu-width {
			min-width: fit-content !important;
			width: fit-content !important;
			max-width: 270px !important;
		}
	}
	@media only screen and (min-width: 1299px) and (max-width: 1824px) and (max-height: 900px) {
		.titleWrap {
			gap: 0.8rem;
			.mainTitle {
				font-size: 24px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 13px;
				font-family: nunitosans-regular;
			}
		}
		.dropdown-image {
			height: 15px;
			width: 15px;
		}
		.margin-top-class {
			margin-top: 2rem;
		}
		.commonSectionHeading {
			font-size: 12px;
			color: #313132eb !important;
			font-family: nunitosans-bold;
		}
		.custom-dropdown-menu-width {
			min-width: fit-content !important;
			width: fit-content !important;
			max-width: 270px !important;
		}
	}
`;

export default NewHomeWrraper;
