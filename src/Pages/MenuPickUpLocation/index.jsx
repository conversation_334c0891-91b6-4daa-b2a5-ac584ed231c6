import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { VenueApiRoutes } from '../../Utils/routes';
import { MenuPickUpLocationWrraper } from './index.style';
import Api from '../../Helper/Api';
import { toast } from 'react-toastify';
import NewDesignCustomLabel from '../../Components/Form/NewDesignCustomLabel';
import { Col, Row } from 'reactstrap';
import { FilledButton } from '../../Components/Layout/Buttons';
import NewPageTitle from '../../Components/Common/NewPageTitle';
import NewPageWrapper from '../../Components/Common/NewPageWrapper';
import DeleteConfirmModal from './DeleteConfirmModal';
import CreatePickUpLocationModal from './CreatePickUpLocationModal';
import EmptyData from '../../Components/Common/EmptyData';
import NewLoader from '../../Components/Common/NewLoader';
import PickUpLocation from './PickUpLocation';

const MenuPickUpLocation = () => {
	const state = useSelector((state) => ({ ...state }));
	const authData = state.auth;
	const [pickupLocationLoading, setPickUpLocationLoading] = useState(false);
	const [editPickupLocationLoading, setEditPickUpLocationLoading] =
		useState(false);
	const [pickupLocationList, setPickUpLocationList] = useState([]);
	const [deleteModal, setDeleteModal] = useState(false);
	const [deletePickUplocationId, setDeletePickUplocationId] = useState(null);
	const [deletePickUplocationLoading, setDeletePickUplocationLoading] =
		useState(null);
	const [createModal, setCreateModal] = useState(false);
	const [subCategoryData, setSubCategoryData] = useState([]);
	const [subCategoryOptionsLoading, setSubCategoryOptionsLoading] =
		useState(false);
	const [createLocationLoading, setCreateLocationLoading] = useState(false);
	const [pendingEdits, setPendingEdits] = useState({});

	useEffect(() => {
		(async () => {
			await getSubCategoryOptions();
			await getPickUpLocationList();
		})();
	}, [authData?.selectedVenue?.id]);

	const handleDeleteModal = () => {
		setDeleteModal((prev) => !prev);
	};

	const handleCreatePickUpModal = () => {
		setCreateModal((prev) => !prev);
	};

	const getPickUpLocationList = async () => {
		setPickUpLocationLoading(true);
		try {
			const res = await Api(
				'POST',
				VenueApiRoutes.getPickupLocationListv2,
				{
					bar_id: authData?.selectedVenue?.id?.toString()
				}
			);

			if (res?.data?.status) {
				setPickUpLocationList(res?.data?.data);
			} else {
				toast.error(res?.data?.message);
			}
			setPickUpLocationLoading(false);
		} catch (err) {
			setPickUpLocationLoading(false);
			if (err?.response?.data?.message) {
				toast.error(err?.response?.data?.message);
			}
		}
	};

	const handleDeletePickUpLocation = async () => {
		setDeletePickUplocationLoading(true);
		try {
			const res = await Api(
				'DELETE',
				VenueApiRoutes.deletePickupLocationv2,
				{
					bar_id: authData?.selectedVenue?.id?.toString(),
					id: '' + deletePickUplocationId
				}
			);
			if (res?.data?.status) {
				toast.success(res?.data?.message);
				await getPickUpLocationList();
				setDeleteModal(false);
			} else {
				toast.error(res?.data?.message);
			}
			setDeletePickUplocationLoading(false);
		} catch (err) {
			setDeletePickUplocationLoading(false);
			if (err?.response?.data?.message) {
				toast.error(err?.response?.data?.message);
			}
		}
	};

	const handleCreatePickUpLocation = async (values) => {
		setCreateLocationLoading(true);
		try {
			const res = await Api('POST', VenueApiRoutes.addPickupLocationv2, {
				bar_id: authData?.selectedVenue?.id?.toString(),
				address: values?.pickupLocationAddress
			});
			if (res?.data?.status) {
				toast.success(res?.data?.message);
				await getPickUpLocationList();
				setCreateModal(false);
			} else {
				toast.error(res?.data?.message);
			}
			setCreateLocationLoading(false);
		} catch (err) {
			setCreateLocationLoading(false);
			if (err?.response?.data?.message) {
				toast.error(err?.response?.data?.message);
			}
		}
	};

	const getSubCategoryOptions = async () => {
		setSubCategoryOptionsLoading(true);
		try {
			const res = await Api('POST', VenueApiRoutes.getMenuCategoryList, {
				bar_id: authData?.selectedVenue?.id
			});
			if (res?.data?.status) {
				let formattedData = res?.data?.data;
				formattedData = formattedData.map((item) => ({
					label: item.name,
					value: item?.id
				}));
				setSubCategoryData(formattedData);
			} else {
				// toast.error(res?.data?.message);
			}
			setSubCategoryOptionsLoading(false);
		} catch (err) {
			setSubCategoryOptionsLoading(false);
			if (err?.message) {
				// toast.error(err?.message);
			}
		}
	};

	const handleEditPickupLocation = async (id, updatedData) => {
		const tempEdits = { ...pendingEdits[id] };
		setEditPickUpLocationLoading(true);
		try {
			const res = await Api('POST', VenueApiRoutes.editPickupLocationv2, {
				id: id.toString(),
				bar_id: authData?.selectedVenue?.id?.toString(),
				...updatedData
			});

			if (res?.data?.status) {
				toast.success('Pickup location updated successfully');
				await getPickUpLocationList();
			} else {
				toast.error(res?.data?.message);
				setPendingEdits((prev) => ({
					...prev,
					[id]: tempEdits
				}));
			}
			setEditPickUpLocationLoading(false);
		} catch (err) {
			setEditPickUpLocationLoading(false);
			setPendingEdits((prev) => ({
				...prev,
				[id]: tempEdits
			}));
			toast.error(
				err?.response?.data?.message ||
					'Failed to update pickup location'
			);
		}
	};

	return (
		<NewPageWrapper>
			<MenuPickUpLocationWrraper>
				<div className="titleWrap">
					<div className="mainTitle">
						<NewPageTitle>Menu Pick Up Locations</NewPageTitle>
					</div>
					<p className="mainParagraph">
						Add your venue’s pick-up locations to inform customers
						where to collect their orders. Pick-up locations can be
						assigned to specific menu categories or applied globally
						across the entire menu. <br />
						Examples include:
					</p>
					<ul>
						<li className="listElement">Collect at counter</li>
						<li className="listElement">Collect at kiosk</li>
						<li className="listElement">Collect inside café</li>
					</ul>
					<div className="mainTitle">
						<FilledButton
							buttonText={'Create Pick Up Location'}
							background={'#e4e4fd'}
							color={'#8280FF'}
							style={{
								width: '160px',
								heigth: '3.8em',
								border: '1px solid #8280FF'
							}}
							onClick={() => setCreateModal(true)}
						/>
					</div>
				</div>

				<div>
					<Row>
						<Col>
							<NewDesignCustomLabel
								id={'locationName'}
								label={'Pick Up Location Name'}
								className={'customeLabelClass'}
							/>
						</Col>
						<Col>
							<NewDesignCustomLabel
								id={'menuCategories'}
								label={'Linked Menu Categories'}
								className={'customeLabelClass'}
							/>
						</Col>
					</Row>
				</div>
				<hr className="customeHrClass" />

				<NewLoader loading={editPickupLocationLoading}>
					{pickupLocationList.length > 0 ? (
						pickupLocationList.map((pickupLocation) => (
							<div key={pickupLocation.id} tabIndex={-1}>
								<PickUpLocation
									subCategoryData={subCategoryData}
									pickupLocation={pickupLocation}
									handleEditPickupLocation={
										handleEditPickupLocation
									}
									setDeleteModal={setDeleteModal}
									setDeletePickUplocationId={
										setDeletePickUplocationId
									}
									pendingEdits={pendingEdits}
									setPendingEdits={setPendingEdits}
								/>
								<hr className="customeHrClass" />
							</div>
						))
					) : (
						<div className="pa-t-100">
							<EmptyData content="No pick up locations found" />
						</div>
					)}
				</NewLoader>

				<DeleteConfirmModal
					isOpen={deleteModal}
					handleModal={handleDeleteModal}
					closeModal={handleDeleteModal}
					handleDeletePickUpLocation={handleDeletePickUpLocation}
					deleteLoading={deletePickUplocationLoading}
				/>
				<CreatePickUpLocationModal
					isOpen={createModal}
					handleModal={handleCreatePickUpModal}
					closeModal={handleCreatePickUpModal}
					handleCreatePickUpLocation={handleCreatePickUpLocation}
					createLocationLoading={createLocationLoading}
				/>
			</MenuPickUpLocationWrraper>
		</NewPageWrapper>
	);
};

export default MenuPickUpLocation;
