import { useEffect, useState } from 'react';
import NewModal from '../../../Components/Common/NewModal';
import NewDesignCustomLabel from '../../../Components/Form/NewDesignCustomLabel';
import NewDesignFormInput from '../../../Components/Form/NewDesignFormInput';
import { StylesWrapper } from './index.styles';

const CreatePickUpLocationModal = ({
	isOpen,
	closeModal,
	handleCreatePickUpLocation,
	createLocationLoading,
}) => {
	const [pickupLocationAddress, setPickupLocationAddress] = useState('');
	const [error, setError] = useState('');

	const handleChange = (e) => {
		setPickupLocationAddress(e.target.value);
		setError('');
	};

	const handleSubmit = () => {
		if (!pickupLocationAddress.trim()) {
			setError('Venue name is required.');
			return;
		}
		handleCreatePickUpLocation({ pickupLocationAddress });
	};

	useEffect(() => {
		if (isOpen) {
			setPickupLocationAddress('');
			setError('');
		}
	}, [isOpen]);

	return (
		<NewModal
			isOpen={isOpen}
			toggle={closeModal}
			title={'Create a pick up location'}
			cancelButtonText="Cancel"
			submitButtonText="Save"
			handleSubmitButtonClick={handleSubmit}
			submitButtonLoading={createLocationLoading}
		>
			<StylesWrapper>
				<div>
					<NewDesignCustomLabel
						id={'menuCategories'}
						label={'Pick up location title: (customers will see this)'}
						className='customeLabelClass'
					/>
					<NewDesignFormInput
						type="text"
						name="pickupLocation"
						placeholder="Example: Collect at kiosk"
						value={pickupLocationAddress}
						onChange={handleChange}
						error={!!error}
						errorMsg={error}
					/>
				</div>
			</StylesWrapper>
		</NewModal>
	);
};

export default CreatePickUpLocationModal;
