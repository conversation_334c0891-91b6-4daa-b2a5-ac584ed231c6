import { useEffect, useState } from "react";
import { MenuPickUpLocationWrraper, NewFormMultiSelectStylesWrapper } from "../index.style";
import { Col, Row } from "reactstrap";
import NewDesignFormInput from "../../../Components/Form/NewDesignFormInput";
import NewFormMultiSelect from "../../../Components/NewForm/NewFormMultiSelect";
import { CircleCancelIcon } from "../../../Components/Icons/Cancel/CircleCancelIcon";

const PickUpLocation = ({
    pickupLocation,
    handleEditPickupLocation,
    subCategoryData,
    setDeleteModal,
    setDeletePickUplocationId,
    pendingEdits,
    setPendingEdits,
}) => {
    const [linkedCategories, setLinkedCategories] = useState([]);

    useEffect(() => {
        const sub_category_ids = pickupLocation?.pickup_location_sub_categories.map((item) => ({ value: item.subCategoryID, label: item.name }));
        setLinkedCategories(sub_category_ids);
    }, [pickupLocation?.pickup_location_sub_categories]);

    const handleCategoryChange = (id, selectedCategories) => {
        setLinkedCategories(selectedCategories);
        const sub_category_ids = selectedCategories.map((item) => item.value);
        setPendingEdits((prev) => ({
            ...prev,
            [id]: { ...prev[id], sub_category_ids: sub_category_ids },
        }));
    };

    const handleAddressChange = (id, value) => {
        setPendingEdits((prev) => ({
            ...prev,
            [id]: { ...prev[id], address: value },
        }));
    };

    const handleFieldBlur = (id, fieldType) => {
        setPendingEdits((prev) => {
            const edits = prev[id];
            if (fieldType === "address" && edits?.address) {
                handleEditPickupLocation(id, { address: edits.address });
            } else if (fieldType === "sub_category_ids" && edits?.sub_category_ids) {
                handleEditPickupLocation(id, { sub_category_ids: edits.sub_category_ids });
            }

            return prev;
        });
    };

    const getCategoriesFieldPlaceholder = (pickupLocation) => {
        if (pickupLocation?.pickup_location_sub_categories) {
            return `${pickupLocation.pickup_location_sub_categories.length} Categories linked`;
        }
        return '0 Category linked';
    };

    return (
        <MenuPickUpLocationWrraper>
            <Row>
                <Col>
                    <NewDesignFormInput
                        value={pendingEdits[pickupLocation.id]?.address || pickupLocation.address}
                        className={'customeInputClass'}
                        onChange={(e) => handleAddressChange(pickupLocation.id, e.target.value)}
                        onBlur={(e) => handleFieldBlur(pickupLocation.id, 'address')}
                    />
                </Col>
                <Col>
                    <div className='d-flex'>
                        <div className=' categoryCotainer'>
                            <NewFormMultiSelectStylesWrapper>
                                <NewFormMultiSelect
                                    placeholder={getCategoriesFieldPlaceholder(pickupLocation)}
                                    options={subCategoryData}
                                    isSearchable
                                    wrapperClassName={'newFormMultiSelectWrapper'}
                                    isShowSelectedOptions={false}
                                    value={linkedCategories}
                                    onChange={(selected) => handleCategoryChange(pickupLocation.id, selected)}
                                    onMenuClose={() => handleFieldBlur(pickupLocation.id, 'sub_category_ids')}
                                />
                            </NewFormMultiSelectStylesWrapper>
                        </div>
                        {pickupLocation.isDefault != 1 && (
                            <div className="cancelIconWrapper">
                                <div className="cancelIcon">
                                    <CircleCancelIcon
                                        width="100%"
                                        height="100%"
                                        className="cursor-pointer zIndex-2"
                                        onClick={() => {
                                            setDeleteModal(() => true);
                                            setDeletePickUplocationId(() => pickupLocation?.id);
                                        }}
                                    />
                                </div>
                            </div>
                        )}
                    </div>
                </Col>
            </Row>
        </MenuPickUpLocationWrraper>
    )
}
export default PickUpLocation;