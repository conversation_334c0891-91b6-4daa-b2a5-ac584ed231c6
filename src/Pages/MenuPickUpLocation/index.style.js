import styled from 'styled-components';

export const MenuPickUpLocationWrraper = styled.div`
	font-family: nunitosans-regular !important;

	.customeHrClass {
		border: 0.6px solid #949596;
		margin-block: 10px;
	}

	ul {
		margin: 0px;
		padding-bottom: 18px;
	}

	.categoryCotainer {
		width: 77% !important;
	}

	.cancelIconWrapper {
		display: flex;
		width: 22%;
		align-items: center;
		justify-content: center;
		.cancelIcon {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 30px;
			height: 30px;
		}
	}

	.customContainer {
		height: 65px !important;
	}

	.titleWrap {
		display: flex;
		flex-direction: column;

		.mainTitle {
			display: flex;
			align-items: center;
			height: 38px !important;
			margin-bottom: 14px;
			width: fit-content;
		}

		.subMainTitle {
			font-size: 16px;
			font-family: nunitosans-bold;
			color: #2e2e2e;
		}

		.mainParagraph {
			font-size: 16px;
			font-family: nunitosans-regular;
			color: #2e2e2e;
			padding-bottom: 18px;
		}

		.listElement {
			font-size: 16px;
			font-family: nunitosans-regular;
			color: #2e2e2e;
		}
	}

	.input-group {
		.inputGroup,
		formGroupIcon {
			background-color: #fbfcff !important;
		}
		.inputBox {
			background-color: #fbfcff !important;
			font-family: nunitosans-regular !important;
			color: #a4a5a7 !important;
			font-size: 16px !important;
		}
	}

	.customeLabelClass {
		font-family: nunitosans-bold;
		color: #202224;
		font-size: 18px;
		padding-bottom: 0px;
		text-transform: none;
	}
	.customeInputClass {
		font-family: nunitosans-semi-bold !important;
		color: #2e2e2e !important;
		border: 0.6px solid #d5d5d5 !important;
		width: 90% !important;
	}

	.customeRateInputClass {
		font-family: nunitosans-semi-bold !important;
		color: #2e2e2e !important;
		border: 0.6px solid #d5d5d5 !important;
		width: 77% !important;
	}

	@media (max-width: 600px) {
		.titleWrap {
			.subMainTitle {
				font-size: 11px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 11px;
				color: #202224;
				font-family: nunitosans-regular;
			}

			.listElement {
				font-size: 11px;
				font-family: nunitosans-regular;
				color: #2e2e2e;
			}
		}

		.customeLabelClass {
			font-family: nunitosans-bold;
			font-size: 12.15px;
		}

		.cancelIconWrapper {
			.cancelIcon {
				width: 21px;
				height: 21px;
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.titleWrap {
			.subMainTitle {
				font-size: 11px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 11px;
				font-family: nunitosans-regular;
			}

			.listElement {
				font-size: 11px;
				font-family: nunitosans-regular;
				color: #2e2e2e;
			}
		}

		.customeLabelClass {
			font-family: nunitosans-bold;
			font-size: 13.5px;
		}

		.cancelIconWrapper {
			.cancelIcon {
				width: 23px;
				height: 23px;
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.titleWrap {
			.subMainTitle {
				font-size: 12px;
				font-family: nunitosans-bold;
			}

			.mainParagraph {
				font-size: 12px;
				font-family: nunitosans-regular;
			}

			.listElement {
				font-size: 12px;
				font-family: nunitosans-regular;
				color: #2e2e2e;
			}
		}

		.customeLabelClass {
			font-family: nunitosans-bold;
			font-size: 13.5px;
		}

		.cancelIconWrapper {
			.cancelIcon {
				width: 23px;
				height: 23px;
			}
		}
	}
`;

export const PageWrapper = styled.div`
	color: ${(props) => props.layoutTheme.textColor} !important;
`;

export const NewFormMultiSelectStylesWrapper = styled.div`
	.newFormMultiSelectWrapper {
		.customContainer {
			height: 65px !important;
		}
		.customControl {
			border: 0.6px solid rgb(213, 213, 213) !important;
			padding-inline: 16px !important;
		}
		.customPlaceholder {
			color: rgba(49, 49, 50, 0.92) !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 16px !important;
		}
		.customInput {
			input {
				color: rgba(49, 49, 50, 0.92) !important;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 16px !important;
			}
		}
		.customMenu {
			border: 0.6px solid rgb(213, 213, 213) !important;
		}
		.customOption {
			gap: 16px !important;
			padding-inline: 16px !important;
			color: rgba(49, 49, 50, 0.92) !important;
			border-bottom: 1px solid rgb(213, 213, 213) !important;
		}
		.customNoOptionsMessage {
			padding: 16px !important;
			color: rgba(49, 49, 50, 0.92) !important;
		}
		@media (max-width: 600px) {
			.customContainer {
				height: 47px !important;
			}
			.customControl {
				padding-inline: 16px !important;
			}
			.customPlaceholder {
				font-size: 10px !important;
			}
			.customInput {
				input {
					font-size: 10px !important;
				}
			}
			.customOption {
				gap: 10px !important;
				padding-inline: 16px !important;
			}
		}
		@media only screen and (min-width: 601px) and (max-width: 1299px) {
			.customContainer {
				height: 48.75px !important;
			}
			.customControl {
				padding-inline: 16px !important;
			}
			.customPlaceholder {
				font-size: 12px !important;
			}
			.customInput {
				input {
					font-size: 12px !important;
				}
			}
			.customOption {
				gap: 16px !important;
				padding-inline: 16px !important;
			}
		}
		@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
			.customContainer {
				height: 48.75px !important;
			}
			.customControl {
				padding-inline: 16px !important;
			}
			.customPlaceholder {
				font-size: 12px !important;
			}
			.customInput {
				input {
					font-size: 12px !important;
				}
			}
			.customOption {
				gap: 16px !important;
				padding-inline: 16px !important;
			}
		}
	}
`;
