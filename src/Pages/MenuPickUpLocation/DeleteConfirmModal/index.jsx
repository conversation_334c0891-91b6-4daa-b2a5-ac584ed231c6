import NewModal from '../../../Components/Common/NewModal';
import { StylesWrapper } from './index.styles';

const DeleteConfirmModal = ({
	isOpen,
	closeModal,
	handleDeletePickUpLocation,
	deleteLoading
}) => {
	return (
		<NewModal
			isOpen={isOpen}
			toggle={closeModal}
			title={'Delete Pick Up Location'}
			cancelButtonText="No"
			submitButtonText="Yes"
			handleSubmitButtonClick={handleDeletePickUpLocation}
			submitButtonLoading={deleteLoading}
		>
			<StylesWrapper>
				Are you sure you want to delete this pick up location?
			</StylesWrapper>
		</NewModal>
	);
};

export default DeleteConfirmModal;
