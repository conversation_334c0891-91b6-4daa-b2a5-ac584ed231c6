import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Modal } from 'reactstrap';
import { <PERSON><PERSON><PERSON> } from 'react-cropper';
import { NewCamera } from '../../../Components/Icons';
import { getFileExtension } from '../../../Helper/helper';
import { StyleWrapper, CropButtonStyleWrapper } from './index.style';
import 'cropperjs/dist/cropper.css';
import CustomButton from '../../../Components/Common/CustomButton';

const ImageUpload = ({
	name,
	label,
	value,
	onChange,
	error,
	errorMsg,
	...props
}) => {
	const [customFile, setCustomFile] = useState(null);
	const [tempCustomFile, setTempCustomFile] = useState(null);
	// file = {
	//   url: String or null,
	//   originalFile: File object or null
	// }
	const [isOpenCropModal, setIsOpenCropModal] = useState(false);
	const fileInput = useRef(null);
	const cropperRef = useRef(null);

	const handleUploadButtonClick = () => {
		fileInput?.current?.click();
	};

	const handleFileInputChange = (event) => {
		if (event?.target?.files?.length > 0) {
			setTempCustomFile({
				url: URL.createObjectURL(event.target.files[0]),
				originalFile: event.target.files[0]
			});
			setIsOpenCropModal(true);
		}
	};

	const handleCrop = useCallback(() => {
		const imageExt = getFileExtension(tempCustomFile?.originalFile?.name);
		cropperRef?.current?.cropper?.getCroppedCanvas()?.toBlob((blob) => {
			const imageFile = new File([blob], `${Date.now()}.${imageExt}`, {
				type: blob?.type
			});
			const blobUrl = URL.createObjectURL(imageFile);
			if (onChange) {
				onChange({
					url: blobUrl,
					originalFile: imageFile
				});
			} else {
				setCustomFile({
					url: blobUrl,
					originalFile: imageFile
				});
			}
			setIsOpenCropModal(false);
		}, customFile?.originalFile?.type);
	}, [tempCustomFile]);

	useEffect(() => {
		if (value) {
			setCustomFile(value);
		} else {
			setCustomFile(null);
		}
	}, [value]);
	return (
		<StyleWrapper>
			<input
				type="file"
				ref={fileInput}
				accept="image/*"
				hidden
				multiple={false}
				onChange={handleFileInputChange}
			/>
			<div className="uploadedImageWrapper">
				{customFile?.url ? (
					<img src={customFile?.url} alt="product-image" />
				) : (
					<div
						className="uploadButtonWrapper"
						onClick={handleUploadButtonClick}
					>
						<div className="uploadButton">
							<span className="label1">
								Click to select an image
							</span>
							<span className="label2">PNG,JPG up to 50MB</span>
						</div>
					</div>
				)}
				<div className="cameraButton" onClick={handleUploadButtonClick}>
					<div className="cameraIcon">
						<NewCamera
							width="100%"
							height="100%"
							fill={'#414141'}
						/>
					</div>
				</div>
			</div>
			<Modal
				isOpen={isOpenCropModal}
				toggle={() => {}} // Disable closing on outside click
				backdrop="static" // Prevent closing when clicking outside
				centered
				className="manageMenuImageCropModal"
				style={{
					width: '500px',
					minWidth: '500px',
					maxWidth: '500px'
				}}
			>
				<Cropper
					ref={cropperRef}
					src={tempCustomFile?.url}
					viewMode={2}
					aspectRatio={1 / 1}
					autoCropArea={1}
					rotatable={false}
					responsive={true}
					width={400}
					height={400}
				/>
				<CropButtonStyleWrapper>
					<CustomButton
						type={'button'}
						className="newThemeButtonFullWidth"
						onClick={handleCrop}
					>
						Crop Image
					</CustomButton>
				</CropButtonStyleWrapper>
			</Modal>
		</StyleWrapper>
	);
};

export default ImageUpload;
