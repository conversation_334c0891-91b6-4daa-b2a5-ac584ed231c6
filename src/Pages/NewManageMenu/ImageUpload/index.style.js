import styled from 'styled-components';

export const StyleWrapper = styled.div`
	.uploadedImageWrapper {
		background-color: #ffffff;
		position: relative;
		width: 225px;
		height: 225px;
		img {
			display: block;
			width: 100%;
			height: 100%;
			border-radius: 4px;
		}
		.uploadButtonWrapper {
			width: 100%;
			height: 100%;
			border-radius: 4px;
			border: 2px solid rgba(49, 49, 50, 0.35);
			padding: 3px;
			cursor: pointer;
			.uploadButton {
				width: 100%;
				height: 100%;
				border: 1px dashed rgba(49, 49, 50, 0.35);
				border-radius: 4px;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				gap: 6px;
				.label1 {
					color: rgba(1, 1, 1, 1);
					font-family: 'nunitosans-bold';
					font-size: 13px;
				}
				.label2 {
					color: rgba(1, 1, 1, 0.6);
					font-family: 'nunitosans-bold';
					font-size: 12px;
				}
			}
		}
		.cameraButton {
			position: absolute;
			top: 100%;
			left: 100%;
			transform: translate(-70%, -70%);
			background-color: #ececee;
			width: 56px;
			height: 56px;
			border-radius: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			.cameraIcon {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 19px;
				height: 15px;
			}
		}
	}
	@media (max-width: 600px) {
		.uploadedImageWrapper {
			width: 148px;
			height: 148px;
			.uploadButtonWrapper {
				.uploadButton {
					gap: 6px;
					.label1 {
						font-size: 10px;
					}
					.label2 {
						font-size: 9px;
					}
				}
			}
			.cameraButton {
				width: 36px;
				height: 36px;
				.cameraIcon {
					width: 12px;
					height: 9px;
				}
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.uploadedImageWrapper {
			width: 169px;
			height: 169px;
			.uploadButtonWrapper {
				.uploadButton {
					gap: 6px;
					.label1 {
						font-size: 10px;
					}
					.label2 {
						font-size: 9px;
					}
				}
			}
			.cameraButton {
				width: 42px;
				height: 42px;
				.cameraIcon {
					width: 14px;
					height: 11px;
				}
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.uploadedImageWrapper {
			width: 169px;
			height: 169px;
			.uploadButtonWrapper {
				.uploadButton {
					gap: 6px;
					.label1 {
						font-size: 10px;
					}
					.label2 {
						font-size: 9px;
					}
				}
			}
			.cameraButton {
				width: 42px;
				height: 42px;
				.cameraIcon {
					width: 14px;
					height: 11px;
				}
			}
		}
	}
`;

export const CropButtonStyleWrapper = styled.div`
	padding-top: 24px;
	.newThemeButtonFullWidth {
		background: linear-gradient(
			90deg,
			#ff6895aa,
			#ff7885aa,
			#ff9568ac,
			#ff9964ac
		) !important;
		width: 100%;
		height: 65px;
		font-family: 'nunitosans-bold';
		font-size: 16px;
		border: none;
		border-radius: 0;

		@media (max-width: 600px) {
			height: 43.8px;
			font-size: 12px;
		}

		@media only screen and (min-width: 600px) and (max-width: 960px) {
			height: 53.4px;
			font-size: 12.3px;
		}

		@media only screen and (min-width: 961px) and (max-width: 1824px) and (max-height: 933px) {
			height: 53.4px;
			font-size: 12.3px;
		}
	}
`;
