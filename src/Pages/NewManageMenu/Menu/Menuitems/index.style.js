import styled from 'styled-components';

const PageWrapper = styled.div`
	.productGridContainer {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 20px;
		padding-top: 22px;
	}
	@media only screen and (max-width: 559px) {
		.productGridContainer {
			grid-template-columns: repeat(1, 1fr);
			grid-gap: 15px;
			padding-top: 17px;
		}
	}
	@media only screen and (min-width: 560px) and (max-width: 600px) {
		.productGridContainer {
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 15px;
			padding-top: 17px;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 799px) {
		.productGridContainer {
			grid-template-columns: repeat(1, 1fr);
			grid-gap: 16px;
			padding-top: 18px;
		}
	}
	@media only screen and (min-width: 800px) and (max-width: 1199px) {
		.productGridContainer {
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 16px;
			padding-top: 18px;
		}
	}
	@media only screen and (min-width: 1200px) and (max-width: 1824px) {
		.productGridContainer {
			grid-template-columns: repeat(3, 1fr);
			grid-gap: 16px;
			padding-top: 18px;
		}
	}
`;

export default PageWrapper;
