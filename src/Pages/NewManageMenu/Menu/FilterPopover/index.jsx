import React from 'react';
import NewPopover from '../../../../Components/Common/NewPopover';
import { PopoverStyleWrraper } from './index.style';

const FilterPopover = ({ serviceType, setServiceType, children }) => {
	return (
		<NewPopover
			positions={['bottom', 'top', 'left', 'right']}
			align="end"
			onContentClick={(closePopover) => {
				closePopover();
			}}
			containerStyle={{ zIndex: 2 }}
			content={
				<PopoverStyleWrraper>
					<div
						className={`${
							serviceType === 'PICKUP' ? 'activeItem' : ''
						}`}
						onClick={() => {
							setServiceType('PICKUP');
						}}
					>
						Takeaway Menu
					</div>
					<div
						className={`${
							serviceType === 'TABLE' ? 'activeItem' : ''
						}`}
						onClick={() => {
							setServiceType('TABLE');
						}}
					>
						Table Service Menu
					</div>
					<div
						className={`${
							serviceType === 'BOTH' ? 'activeItem' : ''
						}`}
						onClick={() => {
							setServiceType('BOTH');
						}}
					>
						All Menus
					</div>
				</PopoverStyleWrraper>
			}
		>
			{children}
		</NewPopover>
	);
};

export default FilterPopover;
