import React from 'react';
import Skeleton from 'react-loading-skeleton';
import PageWrapper from './index.style';

const ProductItemSkeleton = () => {
	return (
		<PageWrapper className="pr-12">
			<div className="productGridContainer">
				{Array(9)
					.fill('1')
					.map((_, index) => {
						return (
							<div className="skeleton-wrapper" key={index}>
								<Skeleton
									height="100%"
									width={'100%'}
									borderRadius={6}
								/>
							</div>
						);
					})}
			</div>
		</PageWrapper>
	);
};

export default ProductItemSkeleton;
