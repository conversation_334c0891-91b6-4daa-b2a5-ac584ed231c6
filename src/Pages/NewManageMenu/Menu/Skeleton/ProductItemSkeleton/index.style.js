import styled from 'styled-components';

const PageWrapper = styled.div`
	.skeleton-wrapper {
		height: 162px !important;
	}
	@media (max-width: 600px) {
		.skeleton-wrapper {
			height: 110px !important;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.skeleton-wrapper {
			height: 122px !important;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.skeleton-wrapper {
			height: 122px !important;
		}
	}
	.productGridContainer {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 24px;
		padding-top: 32px;
	}
	@media only screen and (max-width: 559px) {
		.productGridContainer {
			grid-template-columns: repeat(1, 1fr);
			grid-gap: 14px;
			padding-top: 18px;
		}
	}
	@media only screen and (min-width: 560px) and (max-width: 600px) {
		.productGridContainer {
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 16px;
			padding-top: 18px;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 799px) {
		.productGridContainer {
			grid-template-columns: repeat(1, 1fr);
			grid-gap: 16px;
			padding-top: 24px;
		}
	}
	@media only screen and (min-width: 800px) and (max-width: 1199px) {
		.productGridContainer {
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 18px;
			padding-top: 24px;
		}
	}
	@media only screen and (min-width: 1200px) and (max-width: 1824px) {
		.productGridContainer {
			grid-template-columns: repeat(3, 1fr);
			grid-gap: 18px;
			padding-top: 24px;
		}
	}
`;

export default PageWrapper;
