import React, { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Tab, Tabs } from 'react-tabs-scrollable';
import { TabContent, TabPane } from 'reactstrap';

import { StylesWrapper, TabsWrapper } from './index.style';
import { FilledButton } from '../../../Components/Layout/Buttons';
import { VenueApiRoutes, VenuePanelRoutes } from '../../../Utils/routes';
import Api from '../../../Helper/Api';
import FilterPopover from './FilterPopover';
import { Filter } from '../../../Components/Icons';
import TabsSkeleton from './Skeleton/TabsSkeleton';
import ProductItemSkeleton from './Skeleton/ProductItemSkeleton';
import Menuitems from './Menuitems';
import NewLoader from '../../../Components/Common/NewLoader';
import GlobalPriceUpdateModal from './GlobalPriceUpdateModal';

const Menu = () => {
	const navigate = useNavigate();
	const state = useSelector((state) => ({ ...state }));
	const authData = state.auth;
	const location = useLocation();
	const [tabId, setTabId] = useState();
	const [tabMenuLoading, setTabMenuLoading] = useState(false);
	const [tabMenuData, setTabMenuData] = useState([]);
	const [categoryName, setCategoryName] = useState(
		location.state?.categoryName || ''
	);
	const [posStatus, setPosStatus] = useState(false);
	const [serviceType, setServiceType] = useState('BOTH');
	const [pageLoading, setPageLoading] = useState(false);
	const [updateSequenceLoading, setUpdateSequenceLoading] = useState(false);
	const [isOpenGlobalPriceModal, setIsOpenGlobalPriceModal] = useState(false);
	const categoryNameRef = useRef(categoryName);

	const getProductListData = async (isShowLoader = true) => {
		if (isShowLoader) {
			setTabMenuLoading(true);
		}
		try {
			const res = await Api('POST', VenueApiRoutes.getProductList, {
				bar_id: authData?.selectedVenue?.id,
				serviceType: serviceType
			});
			if (res?.data?.status) {
				setTabMenuData(res?.data?.data);

				if (categoryNameRef?.current && res?.data?.data.length > 0) {
					const index = res?.data?.data.findIndex(
						(item) =>
							item?.categoryName === categoryNameRef?.current
					);
					if (index !== -1) {
						setTabId(index);
					} else {
						setTabId(0);
					}
				} else {
					setTabId(0);
				}
				if (isShowLoader) {
					setTabMenuLoading(false);
				}
			} else {
				toast.error(res?.data?.message);
				if (isShowLoader) {
					setTabMenuLoading(false);
				}
			}
		} catch (err) {
			if (isShowLoader) {
				setTabMenuLoading(false);
			}
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};

	// define an onClick function to bind the value on tab click
	const onTabClick = (e, index) => {
		setCategoryName(e?.target?.textContent);
		setTabId(index);
	};

	useEffect(() => {
		categoryNameRef.current = categoryName;
	}, [categoryName]);

	useEffect(() => {
		if (authData?.selectedVenue?.posStatus === '1') {
			setPosStatus(true);
		} else {
			setPosStatus(false);
		}
	}, [authData?.selectedVenue?.posStatus]);

	useEffect(() => {
		if (authData?.selectedVenue?.id) {
			(async () => {
				await getProductListData();
			})();
		}
	}, [authData?.selectedVenue?.id, serviceType]);
	return (
		<StylesWrapper>
			<div className="description">
				A product is any item on your menu. Be sure to complete all
				fields when adding a product to attract customers and encourage
				them to add it to their cart. Listing all ingredients is crucial
				for customers with allergies. If you have POS integration
				enabled, please manage your menu through your Doshii portal{' '}
				<span
					className="linkText cursor-pointer"
					onClick={() =>
						window.open('https://connect.doshii.com/', '_blank')
					}
				>
					here.
				</span>
			</div>
			{!posStatus && (
				<div className="buttonWrapper">
					<FilledButton
						buttonText={'Create New Product'}
						background={'#ffffff'}
						color={'rgba(72, 128, 255, 1)'}
						style={{
							width: '160px',
							border: '1px solid rgba(72, 128, 255, 1)'
						}}
						onClick={() =>
							navigate(VenuePanelRoutes?.addManageMenu)
						}
					/>
					<FilledButton
						buttonText={'Global Price Update'}
						background={'#ffffff'}
						color={'rgba(72, 128, 255, 1)'}
						style={{
							width: '160px',
							border: '1px solid rgba(72, 128, 255, 1)'
						}}
						onClick={() => setIsOpenGlobalPriceModal(true)}
					/>
				</div>
			)}
			<NewLoader loading={pageLoading}>
				{tabMenuLoading ? (
					<>
						<TabsSkeleton />
						<ProductItemSkeleton />
					</>
				) : (
					<>
						{tabMenuData?.length > 0 ? (
							<>
								<TabsWrapper>
									<Tabs
										activeTab={tabId}
										onTabClick={onTabClick}
										leftBtnIcon={
											<i className="fa fa-angle-left"></i>
										}
										rightBtnIcon={
											<i className="fa fa-angle-right"></i>
										}
										hideNavBtnsOnMobile={false}
									>
										{tabMenuData?.length > 0 &&
											tabMenuData?.map((item, i) => (
												<Tab key={i}>
													{item?.categoryName}
												</Tab>
											))}
									</Tabs>
									<div className="tabBorderBottom" />
									<FilterPopover
										serviceType={serviceType}
										setServiceType={setServiceType}
									>
										<div className="filterIconWrapper">
											<div className="filterIcon">
												<Filter
													width={'100%'}
													height={'100%'}
													fill="#F94D73"
												/>
											</div>
										</div>
									</FilterPopover>
								</TabsWrapper>
								{/* <TabContent
									activeTab={'' + tabId}
									className="overflow-hidden"
								>
									{tabMenuData?.length > 0 &&
										tabMenuData?.map((item, i) => (
											<TabPane key={i} tabId={'' + i}>
												<Menuitems
													categoryData={
														tabMenuData[i]
													}
													getProductListData={
														getProductListData
													}
													setPageLoading={
														setPageLoading
													}
													isPopularTab={
														item?.categoryName ===
														'Popular'
													}
													setUpdateSequenceLoading={
														setUpdateSequenceLoading
													}
												/>
											</TabPane>
										))}
								</TabContent> */}
								<div className="customTabContent">
									{tabMenuData?.length > 0 &&
										tabMenuData?.map((item, i) => (
											<div
												key={i}
												className={`customTabPane ${i == tabId ? 'active' : ''
													}`}
											>
												<Menuitems
													categoryData={
														tabMenuData[i]
													}
													getProductListData={
														getProductListData
													}
													setPageLoading={
														setPageLoading
													}
													isPopularTab={
														item?.categoryName ===
														'Popular'
													}
													setUpdateSequenceLoading={
														setUpdateSequenceLoading
													}
												/>
											</div>
										))}
								</div>
							</>
						) : (
							<div className="noDataFoundWrapper">
								You have no menu products. To get started, click
								the create new product button.
							</div>
						)}
					</>
				)}
			</NewLoader>
			{isOpenGlobalPriceModal && (
				<GlobalPriceUpdateModal
					isOpen={isOpenGlobalPriceModal}
					closeModal={() => setIsOpenGlobalPriceModal(false)}
					barId={authData?.selectedVenue?.id}
					getProductListData={getProductListData}
				/>
			)}
		</StylesWrapper>
	);
};

export default Menu;
