import React, { useState } from 'react';
import { toast } from 'react-toastify';
import NewModal from '../../../../Components/Common/NewModal';
import { StylesWrapper } from './index.styles';
import { Backspace } from '../../../../Components/Icons';
import Api from '../../../../Helper/Api';
import { VenueApiRoutes } from '../../../../Utils/routes';

const GlobalPriceUpdateModal = ({
	isOpen,
	closeModal,
	barId,
	getProductListData
}) => {
	const [isPositive, setIsPositive] = useState(false);
	const [isPercentage, setIsPercentage] = useState(true);
	const [value, setValue] = useState('0');
	const [isLoading, setIsLoading] = useState(false);
	const VALUE_LIMIT = 12;

	const handleNumberClick = (item) => {
		if (value?.length < VALUE_LIMIT) {
			if (value === '0') {
				setValue(String(item));
				return;
			}
			if (item === '.' && value?.includes('.')) {
				return;
			}
			setValue((prev) => prev + '' + item);
		}
	};
	const handleClear = () => {
		let updatedValue = value?.slice(0, value?.length - 1);
		if (updatedValue) {
			setValue(updatedValue);
		} else {
			setValue('0');
		}
	};
	const handleOk = async () => {
		if (!Number(value)) {
			toast.error('Please enter valid number.');
			return;
		}
		try {
			setIsLoading(true);
			const res = await Api('POST', VenueApiRoutes?.globalPriceUpdate, {
				bar_id: barId,
				sign: isPositive ? '+' : '-',
				unit: isPercentage ? '%' : '$',
				amount: value
			});
			if (res?.data?.status) {
				await getProductListData(false);
				toast.success(res?.data?.message);
				closeModal();
			} else {
				toast.error(res?.data?.message);
			}
			setIsLoading(false);
		} catch (err) {
			setIsLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	return (
		<NewModal
			isOpen={isOpen}
			toggle={closeModal}
			title={'Change Prices'}
			showFooter={false}
			className={'globalPriceUpdateModal'}
			modalLoading={isLoading}
		>
			<StylesWrapper>
				<div className="displayContainer">
					<span className="displayText">
						{isPositive ? '+' : '-'}
					</span>
					{!isPercentage && <span className="displayText">$</span>}
					<span className="displayText">{value}</span>
					{isPercentage && <span className="displayText">%</span>}
				</div>
				<div className="tableContainer">
					<table>
						<tr>
							<td
								className={isPositive ? 'selectedSign' : ''}
								onClick={() => setIsPositive(true)}
							>
								+
							</td>
							<td
								className={!isPositive ? 'selectedSign' : ''}
								onClick={() => setIsPositive(false)}
							>
								-
							</td>
							<td
								className={!isPercentage ? 'selectedSign' : ''}
								onClick={() => setIsPercentage(false)}
							>
								$
							</td>
							<td
								className={isPercentage ? 'selectedSign' : ''}
								onClick={() => setIsPercentage(true)}
							>
								%
							</td>
						</tr>
						<tr>
							<td onClick={() => handleNumberClick('7')}>7</td>
							<td onClick={() => handleNumberClick('8')}>8</td>
							<td onClick={() => handleNumberClick('9')}>9</td>
							<td
								rowSpan={2}
								className="backspaceBtn"
								onClick={handleClear}
							>
								<div className="backspaceIconWrapper">
									<div className="backspaceIcon">
										<Backspace
											fill="#fff"
											width={'100%'}
											height={'100%'}
										/>
									</div>
								</div>
							</td>
						</tr>
						<tr>
							<td onClick={() => handleNumberClick('4')}>4</td>
							<td onClick={() => handleNumberClick('5')}>5</td>
							<td onClick={() => handleNumberClick('6')}>6</td>
						</tr>
						<tr>
							<td onClick={() => handleNumberClick('1')}>1</td>
							<td onClick={() => handleNumberClick('2')}>2</td>
							<td onClick={() => handleNumberClick('3')}>3</td>
							<td rowSpan={2} className="okBtnCell">
								<button
									className="okBtn"
									onClick={handleOk}
									disabled={isLoading}
								>
									OK
								</button>
							</td>
						</tr>
						<tr>
							<td
								colSpan={2}
								onClick={() => handleNumberClick('0')}
							>
								0
							</td>
							<td onClick={() => handleNumberClick('.')}>.</td>
						</tr>
					</table>
				</div>
			</StylesWrapper>
		</NewModal>
	);
};

export default GlobalPriceUpdateModal;
