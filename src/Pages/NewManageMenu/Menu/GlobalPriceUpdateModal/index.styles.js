import styled from 'styled-components';

export const StylesWrapper = styled.div`
	.displayContainer {
		height: 78px;
		padding: 16px;
		display: flex;
		justify-content: flex-end;
		align-items: flex-end;
		overflow-x: auto;
		.displayText {
			font-family: 'nunitosans-bold';
			font-size: 38px;
			line-height: 1;
			color: #202224;
		}
	}
	.tableContainer {
		display: flex;
		justify-content: center;
		table {
			width: 378px;
			height: 378px;
			border-collapse: collapse;
			border-bottom-left-radius: 9px;
			border-bottom-right-radius: 9px;
		}
		td {
			border: 1px solid #e6e6e6;
			width: calc(378px / 4);
			height: calc(378px / 4);
			text-align: center;
			vertical-align: middle;
			font-family: 'nunitosans-bold';
			font-size: 24px;
			line-height: 1;
			color: #202224;
			cursor: pointer;
			padding: 0 !important;
		}
		tr {
			&:first-child {
				background-color: #f8f8f8;
			}
			&:last-child {
				td {
					border-bottom: none;
					&:first-child {
						border-bottom-left-radius: 9px;
					}
				}
			}
		}
		tr {
			td {
				&:first-child {
					border-left: none;
				}
				&:last-child {
					border-right: none;
				}
			}
		}
		.backspaceBtn {
			background-color: #a3abb3;
			.backspaceIconWrapper {
				width: 100%;
				height: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
				.backspaceIcon {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 32px;
					height: 32px;
				}
			}
		}
		.okBtnCell {
			border-bottom: none;
			border-bottom-right-radius: 7px;
			.okBtn {
				width: 100%;
				height: 100%;
				border: none;
				background-color: #f95c69;
				color: white;
				border-bottom-right-radius: 7px;
			}
		}
		.selectedSign {
			color: #f95c69;
		}
	}
	@media (max-width: 600px) {
		.displayContainer {
			height: 53px;
			.displayText {
				font-size: 26px;
			}
		}
		.tableContainer {
			table {
				width: 255px;
				height: 255px;
			}
			td {
				width: calc(255px / 4);
				height: calc(255px / 4);
				font-size: 16px;
			}
			.backspaceBtn {
				.backspaceIconWrapper {
					.backspaceIcon {
						width: 22px;
						height: 22px;
					}
				}
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.displayContainer {
			height: 59px;
			.displayText {
				font-size: 29px;
			}
		}
		.tableContainer {
			table {
				width: 284px;
				height: 284px;
			}
			td {
				width: calc(284px / 4);
				height: calc(284px / 4);
				font-size: 18px;
			}
			.backspaceBtn {
				.backspaceIconWrapper {
					.backspaceIcon {
						width: 24px;
						height: 24px;
					}
				}
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.displayContainer {
			height: 59px;
			.displayText {
				font-size: 29px;
			}
		}
		.tableContainer {
			table {
				width: 284px;
				height: 284px;
			}
			td {
				width: calc(284px / 4);
				height: calc(284px / 4);
				font-size: 18px;
			}
			.backspaceBtn {
				.backspaceIconWrapper {
					.backspaceIcon {
						width: 24px;
						height: 24px;
					}
				}
			}
		}
	}
`;
