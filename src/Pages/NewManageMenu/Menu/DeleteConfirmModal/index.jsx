import NewModal from '../../../../Components/Common/NewModal';
import { StylesWrapper } from './index.styles';

const DeleteConfirmModal = ({
	isOpen,
	closeModal,
	handleDeleteProduct,
	deleteLoading
}) => {
	return (
		<NewModal
			isOpen={isOpen}
			toggle={closeModal}
			title={'Delete Product'}
			cancelButtonText="No"
			submitButtonText="Yes"
			handleSubmitButtonClick={handleDeleteProduct}
			submitButtonLoading={deleteLoading}
		>
			<StylesWrapper>
				Are you sure you want to delete this product?
			</StylesWrapper>
		</NewModal>
	);
};

export default DeleteConfirmModal;
