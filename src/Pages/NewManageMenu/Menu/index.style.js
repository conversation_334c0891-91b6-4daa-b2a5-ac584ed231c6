import styled from 'styled-components';

export const TabsWrapper = styled.div`
	position: relative;
	.rts___btn {
		border-radius: 0 !important;
		border: none !important;
		border-bottom: 2px solid rgba(237, 237, 237, 1) !important;
		font-family: 'nunitosans-medium' !important;
		font-size: 16px !important;
		line-height: 1 !important;
	}
	.rts___tabs___container {
		padding: 0 !important;
		.rts___nav___btn___container {
			.rts___nav___btn {
				padding: 0 !important;
				font-family: 'nunitosans-medium' !important;
				font-size: 20px !important;
				line-height: 1 !important;
				&:hover {
					background-color: transparent !important;
				}
				display: flex;
				align-items: flex-start;
			}
			.rts___left___nav___btn {
				padding-right: 12px !important;
			}
			.rts___right___nav___btn {
				padding-left: 12px !important;
				padding-right: 57px !important;
			}
		}
		.rts___tabs {
			padding: 0 !important;
			z-index: 2;
			.rts___tab {
				margin: 0 !important;
				padding: 0 !important;
				padding-bottom: 13px !important;
				padding-inline: 22px !important;
			}
			.rts___tab___selected {
				background-color: transparent !important;
				color: #202224 !important;
				box-shadow: none !important;
				border-bottom: 2px solid #f95c69 !important;
				outline: none !important;
				font-family: 'nunitosans-medium' !important;
			}
		}
	}
	.tabBorderBottom {
		border-bottom: 2px solid rgba(237, 237, 237, 1) !important;
		z-index: 1;
		position: absolute;
		bottom: 0;
		width: 100%;
	}
	.filterIconWrapper {
		position: absolute;
		right: 0;
		bottom: 7px;
		border: 1px solid #f94d73 !important;
		border-radius: 5px;
		width: 32px;
		height: 32px;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		.filterIcon {
			width: 16px;
			height: 16px;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	@media (max-width: 600px) {
		.rts___btn {
			font-size: 12px !important;
		}
		.rts___tabs___container {
			.rts___nav___btn___container {
				.rts___right___nav___btn {
					padding-right: 43px !important;
				}
			}
			.rts___tabs {
				.rts___tab {
					padding-inline: 14px !important;
				}
			}
		}
		.filterIconWrapper {
			width: 28px;
			height: 28px;
			.filterIcon {
				width: 14px;
				height: 14px;
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.rts___btn {
			font-size: 12px !important;
		}
		.rts___tabs___container {
			.rts___nav___btn___container {
				.rts___right___nav___btn {
					padding-right: 46px !important;
				}
			}
			.rts___tabs {
				.rts___tab {
					padding-inline: 17px !important;
				}
			}
		}
		.filterIconWrapper {
			width: 28px;
			height: 28px;
			.filterIcon {
				width: 14px;
				height: 14px;
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.rts___btn {
			font-size: 12px !important;
		}
		.rts___tabs___container {
			.rts___nav___btn___container {
				.rts___right___nav___btn {
					padding-right: 46px !important;
				}
			}
			.rts___tabs {
				.rts___tab {
					padding-inline: 17px !important;
				}
			}
		}
		.filterIconWrapper {
			width: 28px;
			height: 28px;
			.filterIcon {
				width: 14px;
				height: 14px;
			}
		}
	}
`;

export const StylesWrapper = styled.div`
	.description {
		color: #2e2e2e;
		font-family: 'nunitosans-regular';
		font-size: 16px;
		line-height: 22px;
		padding-bottom: 16px;
		.linkText {
			font-family: 'nunitosans-medium';
			font-size: 16px;
			line-height: 22px;
			color: #f95c69;
			text-decoration: underline;
			cursor: pointer;
		}
	}
	.buttonWrapper {
		display: flex;
		gap: 11px;
		padding-bottom: 20px;
	}
	.noDataFoundWrapper {
		font-family: 'nunitosans-regular';
		font-size: 14px;
		line-height: 19px;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 500px;
		color: rgba(32, 34, 36, 0.5);
		text-align: center;
	}
	.customTabContent {
		position: relative;
	}
	.customTabPane {
		display: none;
	}
	.customTabPane.active {
		display: block;
	}
	@media (max-width: 600px) {
		.description {
			font-size: 12px;
			line-height: 17px;
			padding-bottom: 11px;
			.linkText {
				font-size: 12px;
				line-height: 17px;
			}
		}
		.buttonWrapper {
			gap: 7px;
			padding-bottom: 15px;
		}
		.noDataFoundWrapper {
			font-size: 11px;
			line-height: 14px;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.description {
			font-size: 12px;
			line-height: 17px;
			padding-bottom: 12px;
			.linkText {
				font-size: 12px;
				line-height: 17px;
			}
		}
		.buttonWrapper {
			gap: 8px;
			padding-bottom: 16px;
		}
		.noDataFoundWrapper {
			font-size: 12px;
			line-height: 15px;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.description {
			font-size: 12px;
			line-height: 17px;
			padding-bottom: 12px;
			.linkText {
				font-size: 12px;
				line-height: 17px;
			}
		}
		.buttonWrapper {
			gap: 8px;
			padding-bottom: 16px;
		}
		.noDataFoundWrapper {
			font-size: 12px;
			line-height: 15px;
		}
	}
`;
