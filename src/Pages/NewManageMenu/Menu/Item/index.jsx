import { forwardRef, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { useSelector, useDispatch } from 'react-redux';

import { TrashBinIcon, VerticalSixDotIcon } from '../../../../Components/Icons';
import { VenueApiRoutes, VenuePanelRoutes } from '../../../../Utils/routes';
import Switch from '../../../../Components/Common/Switch';
import Api from '../../../../Helper/Api';
import ItemCard from './index.style';
import DeleteConfirmModal from '../DeleteConfirmModal';

const Item = forwardRef(
	(
		{
			id,
			withOpacity,
			isDragging,
			style,
			attributes,
			listeners,
			data,
			getProductListData,
			isEditFromVenueSetup = false,
			forVenuSetup,
			setPageLoading,
			isPopularTab,
			...props
		},
		ref
	) => {
		const authData = useSelector((state) => ({ ...state.auth }));
		const dispatch = useDispatch();
		const navigate = useNavigate();
		const [switchLoading, setSwitchLoading] = useState(false);
		const [deleteProductLoading, setDeleteProductLoading] = useState(false);
		const [switchStatus, setSwitchStatus] = useState(false);
		const [itemData, setItemData] = useState(null);
		const [deleteModal, setDeleteModal] = useState(false);
		const [avatar, setAvatar] = useState(null);
		const handleDeleteModal = () => {
			setDeleteModal((prev) => !prev);
		};
		const handleSwitchChange = async () => {
			setPageLoading(true);
			setSwitchLoading(true);
			try {
				setSwitchStatus(itemData?.status === 'Active' ? false : true);
				const res = await Api(
					'PUT',
					VenueApiRoutes.changeProductStatus,
					{
						status:
							itemData?.status === 'Active'
								? 'Inactive'
								: 'Active',
						id: itemData?.id,
						bar_id: authData?.selectedVenue?.id
					}
				);
				if (res?.data?.status) {
					if (getProductListData) {
						await getProductListData(false);
					}
				} else {
					setSwitchStatus(
						itemData?.status === 'Active' ? true : false
					);
					toast.error(res?.data?.message);
				}
				setSwitchLoading(false);
				setPageLoading(false);
			} catch (err) {
				setSwitchStatus(itemData?.status === 'Active' ? true : false);
				setSwitchLoading(false);
				setPageLoading(false);
				if (err?.message) {
					toast.error(err?.message);
				}
			}
		};
		const handleDeleteProduct = async () => {
			setDeleteProductLoading(true);
			setPageLoading(true);
			try {
				const res = await Api('DELETE', VenueApiRoutes.deleteProduct, {
					sub_category_id: itemData?.subCategoryID,
					id: itemData?.id,
					bar_id: authData?.selectedVenue?.id
				});
				if (res?.data?.status) {
					setDeleteModal(false);
					if (getProductListData) {
						await getProductListData(false);
					}
					toast.success(res?.data?.message);
				} else {
					toast.error(res?.data?.message);
				}
				setDeleteProductLoading(false);
				setPageLoading(false);
			} catch (err) {
				setDeleteProductLoading(false);
				setPageLoading(false);
				if (err?.message) {
					toast.error(err?.message);
				}
			}
		};
		useEffect(() => {
			if (data?.avatar) {
				setAvatar(data?.avatar);
			} else {
				setAvatar(authData?.selectedVenue?.avatar);
			}
			setItemData(data);
			setSwitchStatus(data?.status === 'Active' ? true : false);
		}, [data]);

		return (
			<ItemCard
				ref={ref}
				style={{ ...style }}
				onClick={() => {
					isEditFromVenueSetup === true
						? forVenuSetup()
						: navigate(
								VenuePanelRoutes?.editManageMenu +
									`/${itemData?.id}`
						  );
				}}
				withOpacity={withOpacity}
				isDragging={isDragging}
				isPopularTab={isPopularTab}
				{...props}
			>
				{!isPopularTab && (
					<div className="dragIconWrapper">
						<div
							className="dragIcon"
							{...attributes}
							{...listeners}
							style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
						>
							<VerticalSixDotIcon
								height={'100%'}
								width={'100%'}
							/>
						</div>
					</div>
				)}
				<div className="itemWrapper">
					<div className="itemImageWrapper">
						<img
							src={
								avatar
									? avatar
									: 'https://via.placeholder.com/100x100?text=IMAGE'
							}
							alt="item-img"
						/>
					</div>
					<div className="itemBody">
						<div>
							<div className="d-flex justify-content-between align-items-center gap-3">
								<p className="itemTitle">{itemData?.name}</p>
								{!isPopularTab && (
									<span className="trashIconWrapper">
										<div className="trashIcon">
											<TrashBinIcon
												height={'100%'}
												width={'100%'}
												fill={'rgb(32, 34, 36)'}
												className="cursor-pointer zIndex-2"
												onClick={(event) => {
													event.stopPropagation();
													setDeleteModal(true);
												}}
											/>
										</div>
									</span>
								)}
							</div>
							<div className="itemDescription">
								{itemData?.description}
							</div>
						</div>
						<div className="d-flex align-items-center justify-content-between gap-1">
							<p className="itemPrice">${itemData?.price}</p>
							{!isPopularTab && (
								<Switch
									checked={switchStatus}
									onChange={() => {
										handleSwitchChange();
									}}
									onClick={(event) => event.stopPropagation()}
									disabled={switchLoading}
								/>
							)}
						</div>
					</div>
				</div>
				<DeleteConfirmModal
					isOpen={deleteModal}
					closeModal={handleDeleteModal}
					handleDeleteProduct={handleDeleteProduct}
					deleteLoading={deleteProductLoading}
				/>
			</ItemCard>
		);
	}
);

export default Item;
