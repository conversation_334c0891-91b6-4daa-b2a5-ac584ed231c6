import styled from 'styled-components';

const ItemCard = styled.div`
	border: 1px solid #dddddd;
	border-radius: 6px;
	background-color: #ffffff;
	opacity: ${(props) => (props.withOpacity ? '0.3' : '1')};
	transform-origin: '50% 50%';
	transform: ${(props) => (props.isDragging ? 'scale(1.05)' : 'scale(1)')};
	position: relative;
	cursor: pointer;
	display: flex;
	padding: 24px;
	padding-left: ${(props) =>
		props?.isPopularTab ? '24px !important' : '0 !important'};
	.dragIconWrapper {
		display: flex;
		justify-content: center;
		align-items: center;
		.dragIcon {
			width: 32px;
			height: 32px;
		}
	}
	.itemWrapper {
		flex: 1;
		display: flex;
		gap: 32px;
		.itemImageWrapper {
			img {
				display: block;
				width: 116px;
				height: 112px;
				border-radius: 6px;
				object-fit: cover;
			}
		}
		.itemBody {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			.itemTitle {
				text-overflow: ellipsis;
				overflow: hidden;
				display: -webkit-box !important;
				-webkit-line-clamp: 1;
				-webkit-box-orient: vertical;
				white-space: normal;
				color: rgba(32, 34, 36, 1);
				font-family: 'nunitosans-semi-bold';
				font-size: 16px;
				line-height: 22px;
			}
			.itemDescription {
				text-overflow: ellipsis;
				overflow: hidden;
				display: -webkit-box !important;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				white-space: normal;
				color: rgb(46, 46, 46);
				font-family: 'nunitosans-regular';
				font-size: 14px;
				line-height: 19px;
				padding-top: 6px;
			}
			.itemPrice {
				color: rgba(32, 34, 36, 1);
				font-family: 'nunitosans-semi-bold';
				font-size: 16px;
				line-height: 22px;
			}
			.trashIconWrapper {
				display: flex;
				justify-content: center;
				align-items: center;
				.trashIcon {
					width: 18px;
					height: 18px;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
		}
	}
	@media (max-width: 600px) {
		padding: 16px;
		padding-left: ${(props) =>
			props?.isPopularTab ? '16px !important' : '0 !important'};
		.dragIconWrapper {
			.dragIcon {
				width: 22px;
				height: 22px;
			}
		}
		.itemWrapper {
			gap: 16px;
			.itemImageWrapper {
				img {
					width: 82px;
					height: 80px;
				}
			}
			.itemBody {
				.itemTitle {
					font-size: 12px;
					line-height: 17px;
				}
				.itemDescription {
					font-size: 11px;
					line-height: 14px;
					padding-top: 2px;
				}
				.itemPrice {
					font-size: 12px;
					line-height: 17px;
				}
				.trashIconWrapper {
					.trashIcon {
						width: 16px;
						height: 16px;
					}
				}
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		padding: 18px;
		padding-left: ${(props) =>
			props?.isPopularTab ? '18px !important' : '0 !important'};
		.dragIconWrapper {
			.dragIcon {
				width: 24px;
				height: 24px;
			}
		}
		.itemWrapper {
			gap: 18px;
			.itemImageWrapper {
				img {
					width: 89px;
					height: 86px;
				}
			}
			.itemBody {
				.itemTitle {
					font-size: 12px;
					line-height: 17px;
				}
				.itemDescription {
					font-size: 11px;
					line-height: 14px;
					padding-top: 4px;
				}
				.itemPrice {
					font-size: 12px;
					line-height: 17px;
				}
				.trashIconWrapper {
					.trashIcon {
						width: 16px;
						height: 16px;
					}
				}
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		padding: 18px;
		padding-left: ${(props) =>
			props?.isPopularTab ? '18px !important' : '0 !important'};
		.dragIconWrapper {
			.dragIcon {
				width: 24px;
				height: 24px;
			}
		}
		.itemWrapper {
			gap: 18px;
			.itemImageWrapper {
				img {
					width: 89px;
					height: 86px;
				}
			}
			.itemBody {
				.itemTitle {
					font-size: 12px;
					line-height: 17px;
				}
				.itemDescription {
					font-size: 11px;
					line-height: 14px;
					padding-top: 4px;
				}
				.itemPrice {
					font-size: 12px;
					line-height: 17px;
				}
				.trashIconWrapper {
					.trashIcon {
						width: 16px;
						height: 16px;
					}
				}
			}
		}
	}
`;

export default ItemCard;
