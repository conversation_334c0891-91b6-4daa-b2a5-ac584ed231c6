import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { StylesWrapper } from './index.styles';
import UpsellItem from './UpsellItem';
import LinkCategoryModal from './LinkCategoryModal';
import Api from '../../../Helper/Api';
import { VenueApiRoutes } from '../../../Utils/routes';
import CategoryListSkeleton from './CategoryListSkeleton';
import UnlinkConfirmModal from './UnlinkConfirmModal';

const UpSells = ({ barId }) => {
	const [categoryListLoading, setCategoryListLoading] = useState(false);
	const [categoryList, setCategoryList] = useState([]);
	const [linkCategoryLoading, setLinkCategoryLoading] = useState(false);
	const [isOpenLinkCategoryModal, setIsOpenLinkCategoryModal] =
		useState(false);
	const [linkCategoryModalData, setLinkCategoryModalData] = useState(null);
	const [unlinkCategoryLoading, setUnlinkCategoryLoading] = useState(false);
	const [isOpenUnlinkConfirmModal, setIsOpenUnlinkConfirmModal] =
		useState(false);
	const [unlinkCategoryId, setUnlinkCategoryId] = useState(null);

	const handleCloseLinkCategoryModal = async () => {
		setIsOpenLinkCategoryModal(false);
		setLinkCategoryModalData(null);
	};
	const getCategoryList = async (isShowLoader = true) => {
		if (isShowLoader) {
			setCategoryListLoading(true);
		}
		try {
			const res = await Api(
				'POST',
				VenueApiRoutes?.getUpsellCategoryList,
				{
					bar_id: barId
				}
			);
			if (res?.data?.status) {
				let formatedCategoryList = res?.data?.data?.map((item) => ({
					categoryId: item?.id,
					categoryName: item?.name,
					linkedCategoryId:
						item?.childSubCategoryLink?.childSubCategoryID,
					linkedCategoryName:
						item?.childSubCategoryLink?.subCategory?.name,
					isCategoryLinked: item?.childSubCategoryLink ? true : false
				}));
				setCategoryList(formatedCategoryList);
			} else {
				toast.error(res?.data?.message);
			}
			if (isShowLoader) {
				setCategoryListLoading(false);
			}
		} catch (err) {
			if (isShowLoader) {
				setCategoryListLoading(false);
			}
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	const handleLinkCategory = async (
		type,
		parentCategoryId,
		childCategoryId
	) => {
		if (!childCategoryId) {
			toast.error('Please select one category');
			return;
		}
		setLinkCategoryLoading(true);
		try {
			const res = await Api(
				type === 'EDIT' ? 'PUT' : 'POST',
				VenueApiRoutes?.upsellCategory,
				{
					bar_id: barId,
					parent_sub_category_id: parentCategoryId,
					child_sub_category_id: childCategoryId
				}
			);
			if (res?.data?.status) {
				await getCategoryList(false);
				handleCloseLinkCategoryModal();
				toast.success(res?.data?.message);
			} else {
				toast.error(res?.data?.message);
			}
			setLinkCategoryLoading(false);
		} catch (err) {
			setLinkCategoryLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	const handleUnlinkCategory = async () => {
		setUnlinkCategoryLoading(true);
		try {
			const res = await Api('DELETE', VenueApiRoutes?.upsellCategory, {
				bar_id: barId,
				parent_sub_category_id: unlinkCategoryId
			});
			if (res?.data?.status) {
				await getCategoryList(false);
				setUnlinkCategoryId(null);
				setIsOpenUnlinkConfirmModal(false);
				toast.success(res?.data?.message);
			} else {
				toast.error(res?.data?.message);
			}
			setUnlinkCategoryLoading(false);
		} catch (err) {
			setUnlinkCategoryLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	useEffect(() => {
		getCategoryList();
	}, []);
	return (
		<StylesWrapper>
			<div className="description">
				{`Maximise sales and enhance customer satisfaction with MyTab’s
				menu up sell feature. As customers browse, our system
				automatically suggests 3 relevant complementary items from a
				linked upsell category, creating a personalised experience that
				drives additional sales. For example, when a customer selects an
				item from the Coffee category, and you’ve linked Treats as the
				upsell category, 3 products from Treats (eg. pastries, cakes, or
				cookies) will be suggested when a customer has opened a product
				under the Coffee category. This intuitive feature helps
				customers discover new items, personalises the ordering process
				and reduces food waste by promoting complementary products that
				might otherwise have been missed.`}
			</div>
			<div className="upSellItemCardWrapper">
				{categoryListLoading ? (
					<CategoryListSkeleton />
				) : (
					<>
						{categoryList?.length > 0 ? (
							categoryList?.map((item) => (
								<UpsellItem
									key={item?.categoryId}
									categoryId={String(item?.categoryId)}
									categoryName={item?.categoryName}
									linkedCategoryName={
										item?.linkedCategoryName
									}
									isCategoryLinked={item?.isCategoryLinked}
									onPlusButtonClick={() => {
										setLinkCategoryModalData(item);
										setIsOpenLinkCategoryModal(true);
									}}
									onChangeCategoryClick={() => {
										setLinkCategoryModalData(item);
										setIsOpenLinkCategoryModal(true);
									}}
									onRemoveButtonClick={() => {
										setUnlinkCategoryId(item?.categoryId);
										setIsOpenUnlinkConfirmModal(true);
									}}
								/>
							))
						) : (
							<div className="noDataFoundWrapper">
								You have no upsell categories. To get started,
								add products to your menu.
							</div>
						)}
					</>
				)}
			</div>
			{isOpenLinkCategoryModal && (
				<LinkCategoryModal
					isOpen={isOpenLinkCategoryModal}
					closeModal={handleCloseLinkCategoryModal}
					modalData={linkCategoryModalData}
					categoryList={categoryList}
					handleLinkCategory={handleLinkCategory}
					linkCategoryLoading={linkCategoryLoading}
				/>
			)}
			{isOpenUnlinkConfirmModal && (
				<UnlinkConfirmModal
					isOpen={isOpenUnlinkConfirmModal}
					closeModal={() => {
						setUnlinkCategoryId(null);
						setIsOpenUnlinkConfirmModal(false);
					}}
					handleUnlinkCategory={handleUnlinkCategory}
					unlinkCategoryLoading={unlinkCategoryLoading}
				/>
			)}
		</StylesWrapper>
	);
};

export default UpSells;
