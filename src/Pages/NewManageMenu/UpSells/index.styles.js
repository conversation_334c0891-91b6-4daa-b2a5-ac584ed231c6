import styled from 'styled-components';

export const StylesWrapper = styled.div`
	.description {
		color: #2e2e2e;
		font-family: 'nunitosans-regular';
		font-size: 16px;
		line-height: 22px;
	}
	.noDataFoundWrapper {
		font-family: 'nunitosans-regular';
		font-size: 14px;
		line-height: 19px;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 500px;
		color: rgba(32, 34, 36, 0.5);
		text-align: center;
	}
	.upSellItemCardWrapper {
		padding-top: 16px;
	}
	@media (max-width: 600px) {
		.description {
			font-size: 12px;
			line-height: 17px;
		}
		.noDataFoundWrapper {
			font-size: 11px;
			line-height: 14px;
		}
		.upSellItemCardWrapper {
			padding-top: 11px;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.description {
			font-size: 12px;
			line-height: 17px;
		}
		.noDataFoundWrapper {
			font-size: 12px;
			line-height: 15px;
		}
		.upSellItemCardWrapper {
			padding-top: 12px;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.description {
			font-size: 12px;
			line-height: 17px;
		}
		.noDataFoundWrapper {
			font-size: 12px;
			line-height: 15px;
		}
		.upSellItemCardWrapper {
			padding-top: 12px;
		}
	}
`;
