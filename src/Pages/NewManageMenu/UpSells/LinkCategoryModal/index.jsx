import React, { useEffect, useState } from 'react';
import NewModal from '../../../../Components/Common/NewModal';
import NewFormCheckbox from '../../../../Components/NewForm/NewFormCheckbox';
import { StylesWrapper } from './index.styles';

const LinkCategoryModal = ({
	isOpen,
	closeModal,
	modalData,
	categoryList,
	handleLinkCategory,
	linkCategoryLoading
}) => {
	const [selectedCategoryId, setSelectedCategoryId] = useState(null);
	const handleCheckboxChange = (categoryId) => {
		if (selectedCategoryId == categoryId) {
			setSelectedCategoryId(null);
		} else {
			setSelectedCategoryId(categoryId);
		}
	};
	useEffect(() => {
		if (modalData?.linkedCategoryId) {
			setSelectedCategoryId(modalData?.linkedCategoryId);
		}
	}, [modalData]);
	return (
		<NewModal
			isOpen={isOpen}
			toggle={closeModal}
			title={'Link Up Sell Category'}
			submitButtonText="Link category"
			handleSubmitButtonClick={() =>
				handleLinkCategory(
					modalData?.isCategoryLinked ? 'EDIT' : 'ADD',
					modalData?.categoryId,
					selectedCategoryId
				)
			}
			submitButtonLoading={linkCategoryLoading}
		>
			<StylesWrapper>
				{/* <div key={'all-day-menu'} className="categoryItem">
					<NewFormCheckbox />
					<span className="categoryName">All Day Menu</span>
				</div> */}
				{categoryList?.length > 0 &&
					categoryList?.map((item) =>
						modalData?.categoryId != item?.categoryId ? (
							<div
								key={item?.categoryId}
								className="categoryItem"
							>
								<NewFormCheckbox
									checked={
										item?.categoryId == selectedCategoryId
									}
									onChange={() =>
										handleCheckboxChange(item?.categoryId)
									}
								/>
								<span className="categoryName">
									{item?.categoryName}
								</span>
							</div>
						) : (
							<></>
						)
					)}
			</StylesWrapper>
		</NewModal>
	);
};

export default LinkCategoryModal;
