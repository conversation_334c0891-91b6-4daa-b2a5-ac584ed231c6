import styled from 'styled-components';

export const StylesWrapper = styled.div`
	.categoryItem {
		display: flex;
		align-items: center;
		gap: 16px;
		border-bottom: 1px solid rgba(0, 0, 0, 0.17) !important;
		padding-inline: 16px;
		padding-block: 12px;
		.categoryName {
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 16px !important;
			line-height: 27px !important;
			color: rgba(46, 46, 46, 1) !important;
		}
	}
	.categoryItem:last-child {
		border-bottom: none !important;
	}
	@media (max-width: 600px) {
		.categoryItem {
			gap: 11px;
			padding-block: 8px;
			.categoryName {
				font-size: 11px !important;
				line-height: 18px !important;
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.categoryItem {
			gap: 12px;
			padding-block: 9px;
			.categoryName {
				font-size: 12px !important;
				line-height: 20px !important;
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.categoryItem {
			gap: 12px;
			padding-block: 9px;
			.categoryName {
				font-size: 12px !important;
				line-height: 20px !important;
			}
		}
	}
`;
