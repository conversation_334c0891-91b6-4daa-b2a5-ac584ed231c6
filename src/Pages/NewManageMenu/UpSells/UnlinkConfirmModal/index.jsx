import React from 'react';
import NewModal from '../../../../Components/Common/NewModal';
import { StylesWrapper } from './index.styles';

const UnlinkConfirmModal = ({
	isOpen,
	closeModal,
	handleUnlinkCategory,
	unlinkCategoryLoading
}) => {
	return (
		<NewModal
			isOpen={isOpen}
			toggle={closeModal}
			title={'Unlink Up Sell Category'}
			submitButtonText="Unlink category"
			handleSubmitButtonClick={handleUnlinkCategory}
			submitButtonLoading={unlinkCategoryLoading}
		>
			<StylesWrapper>
				Are you sure you want to unlink this Up Sell category?
			</StylesWrapper>
		</NewModal>
	);
};

export default UnlinkConfirmModal;
