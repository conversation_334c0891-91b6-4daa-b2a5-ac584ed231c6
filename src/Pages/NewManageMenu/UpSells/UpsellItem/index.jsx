import React, { useEffect, useState } from 'react';
import {
	Accordion,
	AccordionBody,
	AccordionHeader,
	AccordionItem
} from 'reactstrap';
import { CircleArrowDown, LArrowDownRight } from '../../../../Components/Icons';
import { CircleCancelIcon } from '../../../../Components/Icons/Cancel/CircleCancelIcon';
import { CirclePlusIcon } from '../../../../Components/Icons/Plus/CirclePlusIcon';
import { StylesWrapper } from './index.style';

const UpsellItem = ({
	categoryName,
	categoryId,
	linkedCategoryName,
	isCategoryLinked,
	onPlusButtonClick,
	onChangeCategoryClick,
	onRemoveButtonClick
}) => {
	const [open, setOpen] = useState();
	const toggle = (id) => {
		if (isCategoryLinked) {
			if (open) {
				setOpen();
			} else {
				setOpen(id);
			}
		} else {
			setOpen();
		}
	};
	useEffect(() => {
		if (!isCategoryLinked) {
			setOpen();
		}
	}, [isCategoryLinked]);
	return (
		<StylesWrapper isCategoryLinked={isCategoryLinked}>
			<Accordion open={open} toggle={toggle} className="customAccordion">
				<AccordionItem>
					<AccordionHeader targetId={categoryId}>
						<div className="accordionHeaderContentWrapper">
							<span className="categoryName">{categoryName}</span>
							{isCategoryLinked ? (
								<>
									<span className="categoryLinked">
										Up Sell Category Linked
									</span>
									<div className="dropdownIconWrapper">
										<div className="dropdownIcon">
											<CircleArrowDown
												width="100%"
												height="100%"
											/>
										</div>
									</div>
								</>
							) : (
								<>
									<span className="categoryNotLinked">
										No Up Sell Category Linked
									</span>
									<div className="plusIconWrapper">
										<div className="plusIcon">
											<CirclePlusIcon
												width="100%"
												height="100%"
												onClick={onPlusButtonClick}
											/>
										</div>
									</div>
								</>
							)}
						</div>
					</AccordionHeader>
					<AccordionBody accordionId={categoryId}>
						<div className="accordionBodyContentWrapper">
							<div className="categoryNameWrapper">
								<div className="arrowIconWrapper">
									<div className="arrowIcon">
										<LArrowDownRight
											width="100%"
											height="100%"
											fill="rgba(0, 0, 0, 0.24)"
										/>
									</div>
								</div>
								<span className="categoryName">
									{linkedCategoryName}
								</span>
							</div>
							<span
								className="categoryChange"
								onClick={onChangeCategoryClick}
							>
								Change Up Sell Category
							</span>
							<div className="cancelIconWrapper">
								<div className="cancelIcon">
									<CircleCancelIcon
										width="100%"
										height="100%"
										onClick={onRemoveButtonClick}
									/>
								</div>
							</div>
						</div>
					</AccordionBody>
				</AccordionItem>
			</Accordion>
		</StylesWrapper>
	);
};

export default UpsellItem;
