import styled from 'styled-components';

export const StylesWrapper = styled.div`
	padding-bottom: 16px !important;
	.customAccordion {
		.accordion-item {
			border: 1px solid rgba(213, 213, 213, 1) !important;
			border-radius: 4px !important;
			.accordion-header {
				.accordion-button {
					background-color: #ffffff !important;
					border-radius: 3px !important;
					padding: 0 !important;
					padding-left: 44px !important;
					padding-right: 23px !important;
					height: 71px !important;
					cursor: ${(props) =>
						props?.isCategoryLinked
							? 'pointer'
							: 'default'} !important;
				}
				.accordion-button::after {
					display: none !important;
				}
				.accordion-button:focus {
					box-shadow: none !important;
				}
				.accordion-button:not(.collapsed) {
					box-shadow: none !important;
				}
			}
			.accordion-collapse {
				border-bottom-right-radius: 4px !important;
				border-bottom-left-radius: 4px !important;
				border-top: 1px solid rgba(213, 213, 213, 1) !important;
				.accordion-body {
					padding: 0 !important;
					padding-left: 44px !important;
					padding-right: 23px !important;
					height: 71px !important;
					display: flex !important;
					align-items: center !important;
				}
			}
		}
		.accordionHeaderContentWrapper {
			flex: 1 !important;
			display: flex;
			align-items: center;
			gap: 34px;
			.categoryName {
				flex: 1;
				color: rgba(46, 46, 46, 1) !important;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 16px !important;
				line-height: 27px !important;
			}
			.categoryLinked {
				color: rgba(46, 46, 46, 0.73) !important;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 16px !important;
				line-height: 27px !important;
			}
			.categoryNotLinked {
				color: rgba(249, 92, 105, 0.85) !important;
				font-family: 'nunitosans-semi-bold' !important;
				font-size: 16px !important;
				line-height: 27px !important;
			}
			.dropdownIconWrapper {
				display: flex;
				justify-content: center;
				align-items: center;
				.dropdownIcon {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 28px;
					height: 28px;
				}
			}
			.plusIconWrapper {
				display: flex;
				justify-content: center;
				align-items: center;
				.plusIcon {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 28px;
					height: 28px;
					cursor: pointer;
				}
			}
		}
		.accordionBodyContentWrapper {
			flex: 1 !important;
			display: flex;
			align-items: center;
			gap: 34px;
			.categoryNameWrapper {
				flex: 1;
				display: flex;
				align-items: center;
				gap: 8px;
				.arrowIconWrapper {
					display: flex;
					justify-content: center;
					align-items: center;
					.arrowIcon {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 19px;
						height: 25px;
					}
				}
				.categoryName {
					color: rgba(46, 46, 46, 1) !important;
					font-family: 'nunitosans-semi-bold' !important;
					font-size: 16px !important;
					line-height: 27px !important;
				}
			}
			.cancelIconWrapper {
				display: flex;
				justify-content: center;
				align-items: center;
				.cancelIcon {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 28px;
					height: 28px;
					cursor: pointer;
				}
			}
			.categoryChange {
				color: rgba(32, 34, 36, 1);
				font-family: 'nunitosans-bold' !important;
				font-size: 16px !important;
				line-height: 22px !important;
				text-decoration: underline !important;
				cursor: pointer;
			}
			.removeIconImg {
				display: block;
				width: 28px;
				height: 28px;
				cursor: pointer;
			}
		}
	}
	@media (max-width: 600px) {
		padding-bottom: 11px !important;
		.customAccordion {
			.accordion-item {
				.accordion-header {
					.accordion-button {
						padding-left: 30px !important;
						padding-right: 15px !important;
						height: 48px !important;
					}
				}
				.accordion-collapse {
					.accordion-body {
						padding-left: 30px !important;
						padding-right: 15px !important;
						height: 48px !important;
					}
				}
			}
			.accordionHeaderContentWrapper {
				gap: 23px;
				.categoryName {
					font-size: 11px !important;
					line-height: 18px !important;
				}
				.categoryLinked {
					font-size: 11px !important;
					line-height: 18px !important;
				}
				.categoryNotLinked {
					font-size: 11px !important;
					line-height: 18px !important;
				}
				.dropdownIconWrapper {
					.dropdownIcon {
						width: 19px;
						height: 19px;
					}
				}
				.plusIconWrapper {
					.plusIcon {
						width: 19px;
						height: 19px;
					}
				}
			}
			.accordionBodyContentWrapper {
				gap: 23px;
				.categoryNameWrapper {
					.arrowIconWrapper {
						.arrowIcon {
							width: 14px;
							height: 19px;
						}
					}
					.categoryName {
						font-size: 11px !important;
						line-height: 18px !important;
					}
				}
				.cancelIconWrapper {
					.cancelIcon {
						width: 19px;
						height: 19px;
					}
				}
				.categoryChange {
					font-size: 11px !important;
					line-height: 18px !important;
				}
				.removeIconImg {
					width: 19px;
					height: 19px;
				}
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		padding-bottom: 12px !important;
		.customAccordion {
			.accordion-item {
				.accordion-header {
					.accordion-button {
						padding-left: 33px !important;
						padding-right: 17px !important;
						height: 53px !important;
					}
				}
				.accordion-collapse {
					.accordion-body {
						padding-left: 33px !important;
						padding-right: 17px !important;
						height: 53px !important;
					}
				}
			}
			.accordionHeaderContentWrapper {
				gap: 26px;
				.categoryName {
					font-size: 12px !important;
					line-height: 20px !important;
				}
				.categoryLinked {
					font-size: 12px !important;
					line-height: 20px !important;
				}
				.categoryNotLinked {
					font-size: 12px !important;
					line-height: 20px !important;
				}
				.dropdownIconWrapper {
					.dropdownIcon {
						width: 21px;
						height: 21px;
					}
				}
				.plusIconWrapper {
					.plusIcon {
						width: 21px;
						height: 21px;
					}
				}
			}
			.accordionBodyContentWrapper {
				gap: 26px;
				.categoryNameWrapper {
					.arrowIconWrapper {
						.arrowIcon {
							width: 14px;
							height: 19px;
						}
					}
					.categoryName {
						font-size: 12px !important;
						line-height: 20px !important;
					}
				}
				.cancelIconWrapper {
					.cancelIcon {
						width: 21px;
						height: 21px;
					}
				}
				.categoryChange {
					font-size: 12px !important;
					line-height: 20px !important;
				}
				.removeIconImg {
					width: 21px;
					height: 21px;
				}
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		padding-bottom: 12px !important;
		.customAccordion {
			.accordion-item {
				.accordion-header {
					.accordion-button {
						padding-left: 33px !important;
						padding-right: 17px !important;
						height: 53px !important;
					}
				}
				.accordion-collapse {
					.accordion-body {
						padding-left: 33px !important;
						padding-right: 17px !important;
						height: 53px !important;
					}
				}
			}
			.accordionHeaderContentWrapper {
				gap: 26px;
				.categoryName {
					font-size: 12px !important;
					line-height: 20px !important;
				}
				.categoryLinked {
					font-size: 12px !important;
					line-height: 20px !important;
				}
				.categoryNotLinked {
					font-size: 12px !important;
					line-height: 20px !important;
				}
				.dropdownIconWrapper {
					.dropdownIcon {
						width: 21px;
						height: 21px;
					}
				}
				.plusIconWrapper {
					.plusIcon {
						width: 21px;
						height: 21px;
					}
				}
			}
			.accordionBodyContentWrapper {
				gap: 26px;
				.categoryNameWrapper {
					.arrowIconWrapper {
						.arrowIcon {
							width: 14px;
							height: 19px;
						}
					}
					.categoryName {
						font-size: 12px !important;
						line-height: 20px !important;
					}
				}
				.cancelIconWrapper {
					.cancelIcon {
						width: 21px;
						height: 21px;
					}
				}
				.categoryChange {
					font-size: 12px !important;
					line-height: 20px !important;
				}
				.removeIconImg {
					width: 21px;
					height: 21px;
				}
			}
		}
	}
`;
