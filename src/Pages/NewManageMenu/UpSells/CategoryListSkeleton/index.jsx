import React from 'react';
import Skeleton from 'react-loading-skeleton';
import StylesWrapper from './index.style';

const CategoryListSkeleton = () => {
	return (
		<StylesWrapper>
			{Array(10)
				.fill('')
				.map((_, index) => {
					return (
						<div className="skeletonWrapper" key={index}>
							<Skeleton
								height="100%"
								width={'100%'}
								borderRadius={4}
							/>
						</div>
					);
				})}
		</StylesWrapper>
	);
};

export default CategoryListSkeleton;
