import styled from 'styled-components';

const StylesWrapper = styled.div`
	.skeletonWrapper {
		height: 71px !important;
		margin-bottom: 16px;
	}
	@media (max-width: 600px) {
		.skeletonWrapper {
			height: 48px !important;
			margin-bottom: 11px;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.skeletonWrapper {
			height: 53px !important;
			margin-bottom: 12px;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.skeletonWrapper {
			height: 53px !important;
			margin-bottom: 12px;
		}
	}
`;

export default StylesWrapper;
