export const content = [
	{
		title: 'Add Products:',
		description:
			'<PERSON>gin adding your products on the Menu screen, make sure you fill in all required details such as the product name, description, base price, and how the product is served.'
	},
	{
		title: 'Rearrange Products:',
		description:
			'You can change the order of your products by dragging and dropping them on the Menu screen, this sequence will reflect on the customer menu.'
	},
	{
		title: 'Rearrange Categories:',
		description:
			'To rearrange how your categories appear on your customer menu, simply go to the Categories screen and use the drag-and-drop feature to reorder them.'
	},
	{
		title: 'Upsells:',
		description:
			'Maximise sales and enhance customer satisfaction with MyTab’s menu up sell feature. Simply use the Upsell screen to link one category to another, and MyTab will automatically suggest relevant items to customers browsing that category.'
	}
];

export const tabData = [
	{
		label: 'Products',
		key: 'Products'
	},
	{
		label: 'Option Sets (Modifiers)',
		key: 'Option Sets (Modifiers)'
	},
	{
		label: 'Categories',
		key: 'Categories'
	},
	{
		label: 'Up Sells',
		key: 'Up Sells'
	}
];

export const tabButtonData = [
	{
		label: 'Menu',
		key: 'menu'
	},
	{
		label: 'Categories',
		key: 'categories'
	},
	{
		label: 'Up Sells',
		key: 'up-sells'
	}
];
