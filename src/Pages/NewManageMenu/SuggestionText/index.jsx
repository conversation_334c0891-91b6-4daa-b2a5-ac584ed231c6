import React from 'react';
import { StylesWrapper } from './index.style';
import { content } from '../utils';

const SuggestionText = () => {
	return (
		<StylesWrapper>
			<span className="regularText">
				Welcome to your MyTab Manage Menu page. Here’s how to get
				started:
			</span>
			{content?.length > 0 &&
				content?.map((item, index) => (
					<div className="contentWrapper" key={index}>
						{/* <span className="boldText">{index + 1}.</span> */}
						<div className="regularText">
							<span className="boldText">{item?.title} </span>
							{item?.description}
						</div>
					</div>
				))}
		</StylesWrapper>
	);
};

export default SuggestionText;
