import styled from 'styled-components';

export const StylesWrapper = styled.div`
	padding-top: 14px;
	line-height: 1 !important;
	.regularText {
		color: #2e2e2e;
		font-family: 'nunitosans-regular';
		font-size: 16px;
		line-height: 22px !important;
	}
	.boldText {
		color: #2e2e2e;
		font-family: 'nunitosans-bold';
		font-size: 16px;
		line-height: 22px !important;
	}
	.contentWrapper {
		display: flex;
		gap: 4px;
	}
	@media (max-width: 600px) {
		.regularText {
			font-size: 12px;
			line-height: 17px !important;
		}
		.boldText {
			font-size: 12px;
			line-height: 17px !important;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.regularText {
			font-size: 12px;
			line-height: 17px !important;
		}
		.boldText {
			font-size: 12px;
			line-height: 17px !important;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.regularText {
			font-size: 12px;
			line-height: 17px !important;
		}
		.boldText {
			font-size: 12px;
			line-height: 17px !important;
		}
	}
`;
