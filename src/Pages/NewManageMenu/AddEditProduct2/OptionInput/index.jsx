import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Col, Row } from 'reactstrap';
import FormInput from '../../../../Components/Form/FormInput';
import { CircleMinusIcon, HamburgerIcon } from '../../../../Components/Icons';
import { useEffect, useState } from 'react';
import FormInputGroup from '../../../../Components/Form/FormInputGroup';
import { InputGroupStylesWrapper, InputStylesWrapper } from '../index.style';

const OptionInput = (props) => {
	const {
		isDragging,
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition
	} = useSortable({ id: props?.id });
	const style = {
		transform: CSS.Transform.toString(transform),
		transition: transition || undefined
	};
	const [itemName, setItemName] = useState('');
	const [price, setPrice] = useState(null);
	useEffect(() => {
		if (props?.values?.[props.name]?.[props.index]) {
			setItemName(props.values[props.name][props.index]?.itemName);
			setPrice(props.values[props.name][props.index]?.price);
		}
	}, [
		props.values,
		props?.values?.[props.name]?.length,
		props.values[props.name][props.index]?.itemName,
		props.values[props.name][props.index]?.price
	]);
	return (
		<div className="extraFormRow" ref={setNodeRef} style={style}>
			<div className="flex-1 formFieldWrapper">
				<InputStylesWrapper>
					<FormInput
						type="text"
						name={`${props.name}.${props.index}.itemName`}
						label="Extra Item"
						placeholder="Enter Extra Item"
						value={props.values[props.name][props.index]?.itemName}
						onChange={props.handleChange}
						error={
							props.touched?.[props.name]?.[props.index]
								?.itemName &&
							!!props.errors?.[props.name]?.[props.index]
								?.itemName
						}
						errorMsg={
							props.errors?.[props.name]?.[props.index]?.itemName
						}
						disabled={props.posStatus}
						formGroupClassName={'productFormInput'}
					/>
				</InputStylesWrapper>
			</div>
			<div className="flex-1 formFieldWrapper">
				<InputGroupStylesWrapper>
					<FormInputGroup
						type="number"
						name={`${props.name}.${props.index}.price`}
						label="Price"
						placeholder="Enter Price"
						value={props.values[props.name][props.index]?.price}
						onChange={props.handleChange}
						error={
							props.touched?.[props.name]?.[props.index]?.price &&
							!!props.errors?.[props.name]?.[props.index]?.price
						}
						errorMsg={
							props.errors?.[props.name]?.[props.index]?.price
						}
						icon={<span className="fs-14">$</span>}
						iconPlacement="start"
						className={'pl-0'}
						iconBackgroundClass={'inputGroupBackground'}
						onBlur={() => {
							props.setFieldValue(
								`${props.name}.${props.index}.price`,
								Number(
									props.values[props.name][props.index]?.price
								).toFixed(2)
							);
						}}
						onWheel={(e) => e.target.blur()}
						disabled={props.posStatus}
						formGroupClassName={'productFormInput'}
					/>
				</InputGroupStylesWrapper>
			</div>
			<div className="minusIconWrapper">
				<div className="minusIcon">
					<CircleMinusIcon
						height={'100%'}
						width={'100%'}
						fill={'#F94D73'}
						className="cursor-pointer"
						onClick={() => {
							props.handleDeletedAdditionalExtras(
								props.values[props.name][props.index]?.itemId
							);
							let items = props.values[props.name];
							items.splice(props.index, 1);
							props.setFieldValue(props.name, [...items]);
						}}
					/>
				</div>
			</div>
			<HamburgerIcon
				height={18}
				width={18}
				fill={'#BABABA'}
				className="cursor-pointer outline-none"
				{...attributes}
				{...listeners}
				style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
			/>
		</div>
	);
};

export default OptionInput;
