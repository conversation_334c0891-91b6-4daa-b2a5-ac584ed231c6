import { useDispatch, useSelector } from 'react-redux';
import authActions from '../../../../Redux/auth/actions';
import { ThreeDotIcon2 } from '../../../../Components/Icons';
import NewPopover from '../../../../Components/Common/NewPopover';
import { PopoverStyleWrraper } from '../popoverContent.style';

const AdditionalExtrasPopover = ({
	additionalExtras,
	setFieldValue,
	name,
	values,
	posStatus
}) => {
	const authData = useSelector((state) => ({ ...state.auth }));
	const dispatch = useDispatch();
	return (
		<NewPopover
			positions={['bottom', 'top', 'left', 'right']}
			align="end"
			onContentClick={(closePopover) => {
				closePopover();
			}}
			containerStyle={{ zIndex: 2 }}
			content={
				<PopoverStyleWrraper>
					<div
						onClick={() => {
							// Deep copy additionalExtras before dispatch
							const copiedExtras = JSON.parse(JSON.stringify(additionalExtras)).map(eachValue => {
								if (eachValue?.itemId) {
									delete eachValue.itemId;
								}
								return eachValue;
							});

							dispatch(authActions.copy_paste_additional_extras(copiedExtras));
						}}
					>
						Copy
					</div>
					{!posStatus && (
						<div
							onClick={() => {
								if (authData?.copiedAdditionalExtras !== null) {
									// Deep copy the pasted values before setting them
									const updatedExtras = [
										...JSON.parse(JSON.stringify(values.additionalExtras)), 
										...JSON.parse(JSON.stringify(authData?.copiedAdditionalExtras))
									];

									setFieldValue(name, updatedExtras);
								}
							}}
						>
							Paste Additional Extras
						</div>
					)}
					<div
						onClick={() => {
							if (authData?.copiedRequiredOptions !== null) {
								// Deep copy before setting field values
								const updatedRequiredExtras = [
									...JSON.parse(JSON.stringify(values.additionalExtras)),
									...JSON.parse(JSON.stringify(authData?.copiedRequiredOptions))
								];

								setFieldValue(name, updatedRequiredExtras);
							}
						}}
					>
						Paste Required Extra Items
					</div>
				</PopoverStyleWrraper>
			}
		>
			<div className="threeDotIconWrapper">
				<div className="threeDotIcon">
					<ThreeDotIcon2
						width="100%"
						height="100%"
						fill="rgb(32, 34, 36)"
						className="cursor-pointer"
					/>
				</div>
			</div>
		</NewPopover>
	);
};

export default AdditionalExtrasPopover;
