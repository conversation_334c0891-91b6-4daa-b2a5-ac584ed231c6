import { Button } from 'reactstrap';
import { useMediaQuery } from 'react-responsive';
import { useState, useEffect } from 'react';
import { PlusIcon } from '../../../../Components/Icons';
import RequiredOptionsItem from '../RequiredOptionsItem';
import RequiredOptionsSectionPopover from '../RequiredOptionsSectionPopover';

const RequiredOptions = ({
	name,
	values,
	handleChange,
	setFieldValue,
	touched,
	errors,
	handleDeletedRequiredOptions,
	handleDeletedRequiredOptionsItem,
	posStatus
}) => {
	const isXL = useMediaQuery({ maxWidth: 1199 });
	const [requiredOptions, setRequiredOptions] = useState(null);
	const handleAddSection = () => {
		if (posStatus) {
			setFieldValue(name, [
				...values?.requiredOptions,
				{
					serviceType: null,
					data: []
				}
			]);
		} else {
			setFieldValue(name, [
				...values?.requiredOptions,
				{
					serviceType: null,
					data: [
						{
							itemName: '',
							price: '0.00'
						}
					]
				}
			]);
		}
	};
	useEffect(() => {
		if (values?.requiredOptions?.length > 0) {
			setRequiredOptions(values?.requiredOptions);
		}
	}, [values, values?.requiredOptions, values?.requiredOptions?.length]);
	return (
		<>
			<div className="additionalItemsHeader formFieldWrapper">
				<div className="flex-1">
					<p className="pa-b-6 newCommonLabel">Required Options</p>
					<p className={`hintText h-px-36`}>
						(Customers must select one of the options under each
						“Please select...” subheading)
					</p>
				</div>
				<Button
					className="themeButton plusBtn"
					onClick={() => handleAddSection()}
				>
					<div className="plusIcon">
						<PlusIcon
							height={'100%'}
							width={'100%'}
							stroke={'#FFF'}
						/>
					</div>
				</Button>
				{!posStatus && (
					<RequiredOptionsSectionPopover
						name={name}
						setFieldValue={setFieldValue}
						values={values}
					/>
				)}
			</div>
			{values?.requiredOptions?.length > 0 &&
				values?.requiredOptions?.map((item, index) => {
					return (
						<RequiredOptionsItem
							key={index}
							index={index}
							name={name}
							values={values}
							setFieldValue={setFieldValue}
							handleChange={handleChange}
							touched={touched}
							errors={errors}
							handleDeletedRequiredOptions={
								handleDeletedRequiredOptions
							}
							handleDeletedRequiredOptionsItem={
								handleDeletedRequiredOptionsItem
							}
							posStatus={posStatus}
						/>
					);
				})}
		</>
	);
};

export default RequiredOptions;
