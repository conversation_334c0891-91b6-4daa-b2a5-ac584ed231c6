import * as Yup from 'yup';

const SUPPORTED_FORMATS = ['image/jpg', 'image/jpeg', 'image/png'];
const FILE_SIZE = 20 * 1024 * 1024;

export const validationSchema = Yup.object().shape({
	image: Yup.mixed()
		.nullable()
		.test(
			'FILE_FORMAT',
			'Image must be in jpg, jpeg or png format.',
			(value) => {
				if (typeof value === 'string') {
					return true;
				}
				return (
					!value || (value && SUPPORTED_FORMATS.includes(value.type))
				);
			}
		)
		.test('FILE_SIZE', 'Image size must be less than 20Mb.', (value) => {
			if (typeof value === 'string') {
				return true;
			}
			return !value || (value && value.size <= FILE_SIZE);
		}),
	category: Yup.string().required('Please select category.'),
	itemName: Yup.string().required('Please enter product name.'),
	basePrice: Yup.number()
		.typeError('Base price must be in number.')
		.positive('Base price must be more than 0.')
		.required('Please enter product base price.'),
	productTax: Yup.string().required('Please select product tax.'),
	description: Yup.string().required('Please enter your description.'),
	serviceType: Yup.array().min(1, 'Please select service type.'),
	stockQuantity: Yup.number()
		.integer('Stock Quantity can not be decimal value.')
		.typeError('Stock Quantity must be in number.')
		.min(0, 'Stock Quantity must be greater than or equal to 0.'),
	requiredOptions: Yup.array().of(
		Yup.object().shape({
			data: Yup.array().of(
				Yup.object().shape({
					itemName: Yup.string().required(
						'Please enter extra item name.'
					),
					price: Yup.number()
						.typeError('Price must be in number.')
						.min(0, 'Price must be more than or equal to 0.')
						.required('Please enter price.')
				})
			)
		})
	),
	additionalExtras: Yup.array().of(
		Yup.object().shape({
			itemName: Yup.string().required('Please enter extra item name.'),
			price: Yup.number()
				.typeError('Price must be in number.')
				.min(0, 'Price must be more than or equal to 0.')
				.required('Please enter price.')
		})
	)
});
