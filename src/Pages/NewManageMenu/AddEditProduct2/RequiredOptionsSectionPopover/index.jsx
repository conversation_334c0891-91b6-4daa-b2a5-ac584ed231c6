import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import authActions from '../../../../Redux/auth/actions';
import NewPopover from '../../../../Components/Common/NewPopover';
import { PopoverStyleWrraper } from '../popoverContent.style';
import { ThreeDotIcon2 } from '../../../../Components/Icons';

const RequiredOptionsSectionPopover = ({ name, setFieldValue, values }) => {
	const authData = useSelector((state) => ({ ...state.auth }));
	const dispatch = useDispatch();

	const [localValues, setLocalValues] = useState(
		JSON.parse(JSON.stringify(values[name])) // Deep copy values[name] when initializing
	);

	useEffect(() => {
		setLocalValues(JSON.parse(JSON.stringify(values[name]))); // Deep copy values[name] in useEffect
	}, [values, name]);

	return (
		<NewPopover
			positions={['bottom', 'top', 'left', 'right']}
			align="end"
			onContentClick={(closePopover) => {
				closePopover();
			}}
			containerStyle={{ zIndex: 2 }}
			content={
				<PopoverStyleWrraper>
					<div
						onClick={() => {
							dispatch(
								authActions.copy_paste_required_options_section(
									JSON.parse(JSON.stringify(localValues)) // Deep copy before dispatch
								)
							);
						}}
					>
						Copy
					</div>
					<div
						onClick={() => {
							if (
								authData?.copiedRequiredOptionsSection !== null
							) {
								const updatedValues = [
									...localValues.map((item) =>
										JSON.parse(JSON.stringify(item))
									), // Deep copy local values
									...authData?.copiedRequiredOptionsSection.map(
										(item) =>
											JSON.parse(JSON.stringify(item))
									) // Deep copy pasted values
								];

								setFieldValue(name, updatedValues); // Update form value
								setLocalValues(updatedValues); // Update local state
							}
						}}
					>
						Paste
					</div>
				</PopoverStyleWrraper>
			}
		>
			<div className="threeDotIconWrapper">
				<div className="threeDotIcon">
					<ThreeDotIcon2
						width="100%"
						height="100%"
						fill="rgb(32, 34, 36)"
						className="cursor-pointer"
					/>
				</div>
			</div>
		</NewPopover>
	);
};

export default RequiredOptionsSectionPopover;
