import { Button } from 'reactstrap';
import NewPopover from '../../../../Components/Common/NewPopover';
import { PopoverStyleWrraper } from '../popoverContent.style';

const ServiceTypePopover = ({
	setServiceType,
	handleServiceType,
	serviceType
}) => {
	return (
		<NewPopover
			positions={['bottom', 'top', 'left', 'right']}
			align="center"
			onContentClick={(closePopover) => {
				closePopover();
			}}
			containerStyle={{ zIndex: 2 }}
			content={
				<PopoverStyleWrraper>
					<div
						onClick={() => {
							setServiceType('Takeaway');
							handleServiceType('PICKUP');
						}}
					>
						Takeaway
					</div>
					<div
						onClick={() => {
							setServiceType('Table Service');
							handleServiceType('TABLE');
						}}
					>
						Table Service
					</div>
					<div
						onClick={() => {
							setServiceType('Both');
							handleServiceType('BOTH');
						}}
					>
						Both
					</div>
				</PopoverStyleWrraper>
			}
		>
			<Button type="button" className="serviceTypeButton">
				<span className="pleaseSelectButtonText">
					{serviceType ? serviceType : 'Service Type'}
				</span>
			</Button>
		</NewPopover>
	);
};

export default ServiceTypePopover;
