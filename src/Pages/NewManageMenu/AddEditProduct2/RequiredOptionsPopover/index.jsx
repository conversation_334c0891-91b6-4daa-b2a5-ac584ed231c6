import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { ThreeDotIcon, ThreeDotIcon2 } from '../../../../Components/Icons';
import authActions from '../../../../Redux/auth/actions';
import NewPopover from '../../../../Components/Common/NewPopover';
import { PopoverStyleWrraper } from '../popoverContent.style';

const RequiredOptionsPopover = ({ name, setFieldValue, values, index }) => {
	const authData = useSelector((state) => ({ ...state.auth }));
	const dispatch = useDispatch();

	const [localRequiredOptions, setLocalRequiredOptions] = useState(
		values?.[name]
	);

	useEffect(() => {
		setLocalRequiredOptions(values?.[name]);
	}, [values, name]);

	return (
		<NewPopover
			positions={['bottom', 'top', 'left', 'right']}
			align="end"
			onContentClick={(closePopover) => {
				closePopover();
			}}
			containerStyle={{ zIndex: 2 }}
			content={
				<PopoverStyleWrraper>
					<div
						onClick={() => {
							dispatch(
								authActions.copy_paste_required_options(
									JSON.parse(
										JSON.stringify(
											localRequiredOptions?.[index]?.data
										)
									) // Deep copy before dispatching
								)
							);
						}}
					>
						Copy
					</div>
					<div
						onClick={() => {
							if (authData?.copiedMenuItemOptions !== null) {
								const updatedOptions = JSON.parse(
									JSON.stringify(localRequiredOptions)
								); // Deep copy local values

								updatedOptions[index].data = [
									...updatedOptions[index].data,
									...authData?.copiedMenuItemOptions.map(
										(option) => ({ ...option })
									) // Deep copy copied options
								];

								setFieldValue(name, updatedOptions); // Update form value
								setLocalRequiredOptions(updatedOptions); // Update local state
							}
						}}
					>
						Paste
					</div>
				</PopoverStyleWrraper>
			}
		>
			<div className="threeDotIconWrapper">
				<div className="threeDotIcon">
					<ThreeDotIcon2
						width="100%"
						height="100%"
						fill="#ffffff"
						className="cursor-pointer"
					/>
				</div>
			</div>
		</NewPopover>
	);
};

export default RequiredOptionsPopover;
