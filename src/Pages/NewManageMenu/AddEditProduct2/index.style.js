import styled from 'styled-components';

export const StylesWrapper = styled.div`
	.newCommonLabel {
		color: rgba(32, 34, 36, 1) !important;
		font-family: 'nunitosans-bold' !important;
		font-size: 16px !important;
		line-height: 1 !important;
	}
	.newFormCheckbox {
		.form-label {
			color: rgba(32, 34, 36, 1) !important;
			font-family: 'nunitosans-regular' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
	}
	.hintText {
		color: #2e2e2e !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 16px !important;
		line-height: 22px !important;
	}
	.noDataText {
		font-family: 'nunitosans-regular';
		font-size: 14px;
		line-height: 19px;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 60px;
		color: rgba(32, 34, 36, 0.5);
		text-align: center;
	}
	.errorMessageText {
		color: rgb(255, 95, 95) !important;
		font-family: 'nunitosans-semi-bold' !important;
		font-size: 12px !important;
		line-height: 1 !important;
	}
	.formGridContainer {
		display: grid;
		grid-template-columns: 1fr 1fr;
		grid-gap: 0 48px;
	}
	.formFieldWrapper {
		padding-bottom: 20px;
	}
	.serviceTypeContainer {
		display: flex;
		gap: 18px;
	}
	.checkBoxCursor {
		cursor: pointer;
	}
	.dietaryRequirementsContainer {
		width: 700px;
		display: grid;
		grid-template-columns: auto auto auto;
		gap: 18px;
		@media only screen and (max-width: 1824px) {
			width: 630px;
			grid-template-columns: auto auto auto;
			gap: 14px;
		}
		@media only screen and (max-width: 820px) {
			width: 100%;
			grid-template-columns: auto auto auto;
			gap: 14px;
		}
		@media only screen and (max-width: 660px) {
			width: 100%;
			grid-template-columns: auto auto;
			gap: 14px;
		}
		@media only screen and (max-width: 460px) {
			width: 100%;
			grid-template-columns: auto;
			gap: 14px;
		}
	}
	.horizontalLine {
		border-top: 1px solid #dddddd;
		margin-block: 20px;
	}
	.additionalItemsWrapper {
		display: flex;
		gap: 48px;
		@media only screen and (max-width: 1210px) {
			gap: 24px;
			flex-direction: column;
		}
	}
	.additionalItemsHeader {
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 25px;
	}
	.plusBtn {
		width: 40px !important;
		height: 40px !important;
		padding: 0 !important;
		display: flex !important;
		justify-content: center !important;
		align-items: center !important;
		.plusIcon {
			display: flex !important;
			justify-content: center !important;
			align-items: center !important;
			width: 24px !important;
			height: 24px !important;
		}
	}
	.minusIconWrapper {
		display: flex;
		justify-content: center;
		align-items: center;
		.minusIcon {
			width: 18px !important;
			height: 18px !important;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	.threeDotIconWrapper {
		display: flex;
		justify-content: center;
		align-items: center;
		.threeDotIcon {
			width: 6px !important;
			height: 20px !important;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	.pleaseSelectCard {
		width: 100%;
		background: #fc6461;
		border-radius: 8px;
		height: 52px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-left: 16px;
		padding-right: 19px;
		.pleaseSelectCardTitle {
			font-family: 'nunitosans-bold' !important;
			font-size: 16px !important;
			line-height: 1 !important;
			color: #ffffff;
		}
		.actionsWrapper {
			display: flex;
			align-items: center;
			gap: 16px;
		}
		.serviceTypeButton {
			background: #fd7371 !important;
			border: 0px;
			max-width: fit-content !important;
			width: fit-content !important;
			min-width: 163px !important;
			height: 36px !important;
			border-radius: 6px !important;
			margin-top: 0 !important;
			padding-inline: 8px !important;
			display: flex;
			justify-content: center;
			align-items: center;
			.pleaseSelectButtonText {
				font-family: 'nunitosans-bold' !important;
				font-size: 16px !important;
				line-height: 1 !important;
			}
		}
		.addOptionsButton {
			background: #fd7371 !important;
			border: 0px;
			max-width: fit-content !important;
			width: fit-content !important;
			min-width: 163px !important;
			height: 36px !important;
			border-radius: 6px !important;
			margin-top: 0 !important;
			padding-inline: 8px !important;
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 8px;
			font-family: 'nunitosans-bold' !important;
			font-size: 16px !important;
			line-height: 1 !important;
			.addOptionsButtonIcon {
				width: 24px;
				height: 24px;
			}
		}
	}
	.addAdditionalCard {
		width: 100%;
		border-radius: 8px;
		border: 1px solid #fc6461;
		height: 52px !important;
		display: flex;
		align-items: center;
		padding-inline: 16px;
		font-family: 'nunitosans-bold' !important;
		font-size: 16px !important;
		line-height: 1 !important;
		color: #fc6461;
	}
	.extraFormRow {
		display: flex;
		align-items: center;
		gap: 24px;
	}
	.saveCancelBtnWrapper {
		display: flex;
		align-items: end;
		justify-content: flex-end;
		gap: 15px;
		padding-top: 24px;
	}
	@media (max-width: 600px) {
		.newCommonLabel {
			font-size: 12px !important;
		}
		.newFormCheckbox {
			.form-label {
				font-size: 12px !important;
			}
		}
		.hintText {
			font-size: 12px !important;
			line-height: 17px !important;
		}
		.noDataText {
			font-size: 11px;
			line-height: 14px;
		}
		.errorMessageText {
			font-size: 10px !important;
		}
		.formGridContainer {
			grid-template-columns: 1fr;
			grid-gap: 0;
		}
		.formFieldWrapper {
			padding-bottom: 14px;
		}
		.horizontalLine {
			margin-block: 14px;
		}
		.plusBtn {
			width: 30px !important;
			height: 30px !important;
			.plusIcon {
				width: 18px !important;
				height: 18px !important;
			}
		}
		.minusIconWrapper {
			.minusIcon {
				width: 14px !important;
				height: 14px !important;
			}
		}
		.threeDotIconWrapper {
			.threeDotIcon {
				width: 4px !important;
				height: 14px !important;
			}
		}
		.pleaseSelectCard {
			height: 39px;
			padding-left: 12px;
			padding-right: 16px;
			.pleaseSelectCardTitle {
				font-size: 12px !important;
			}
			.actionsWrapper {
				gap: 12px;
			}
			.serviceTypeButton {
				min-width: 120px !important;
				height: 27px !important;
				.pleaseSelectButtonText {
					font-size: 12px !important;
				}
			}
			.addOptionsButton {
				min-width: 120px !important;
				height: 27px !important;
				gap: 6px;
				font-size: 12px !important;
				.addOptionsButtonIcon {
					width: 18px;
					height: 18px;
				}
			}
		}
		.addAdditionalCard {
			height: 39px !important;
			padding-inline: 12px;
			font-size: 12px !important;
		}
		.extraFormRow {
			gap: 15px;
		}
		.saveCancelBtnWrapper {
			gap: 12px;
			padding-top: 18px;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.newCommonLabel {
			font-size: 12px !important;
		}
		.newFormCheckbox {
			.form-label {
				font-size: 12px !important;
			}
		}
		.hintText {
			font-size: 12px !important;
			line-height: 17px !important;
		}
		.noDataText {
			font-size: 12px;
			line-height: 15px;
		}
		.errorMessageText {
			font-size: 10px !important;
		}
		.formGridContainer {
			grid-template-columns: 1fr 1fr;
			grid-gap: 0 15px;
		}
		.formFieldWrapper {
			padding-bottom: 15px;
		}
		.horizontalLine {
			margin-block: 15px;
		}
		.plusBtn {
			width: 30px !important;
			height: 30px !important;
			.plusIcon {
				width: 18px !important;
				height: 18px !important;
			}
		}
		.minusIconWrapper {
			.minusIcon {
				width: 14px !important;
				height: 14px !important;
			}
		}
		.threeDotIconWrapper {
			.threeDotIcon {
				width: 4px !important;
				height: 14px !important;
			}
		}
		.pleaseSelectCard {
			height: 39px;
			padding-left: 12px;
			padding-right: 16px;
			.pleaseSelectCardTitle {
				font-size: 12px !important;
			}
			.actionsWrapper {
				gap: 12px;
			}
			.serviceTypeButton {
				min-width: 120px !important;
				height: 27px !important;
				.pleaseSelectButtonText {
					font-size: 12px !important;
				}
			}
			.addOptionsButton {
				min-width: 120px !important;
				height: 27px !important;
				gap: 6px;
				font-size: 12px !important;
				.addOptionsButtonIcon {
					width: 18px;
					height: 18px;
				}
			}
		}
		.addAdditionalCard {
			height: 39px !important;
			padding-inline: 12px;
			font-size: 12px !important;
		}
		.extraFormRow {
			gap: 15px;
		}
		.saveCancelBtnWrapper {
			gap: 12px;
			padding-top: 18px;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.newCommonLabel {
			font-size: 12px !important;
		}
		.newFormCheckbox {
			.form-label {
				font-size: 12px !important;
			}
		}
		.hintText {
			font-size: 12px !important;
			line-height: 17px !important;
		}
		.noDataText {
			font-size: 12px;
			line-height: 15px;
		}
		.errorMessageText {
			font-size: 10px !important;
		}
		.formGridContainer {
			grid-template-columns: 1fr 1fr;
			grid-gap: 0 36px;
		}
		.formFieldWrapper {
			padding-bottom: 15px;
		}
		.horizontalLine {
			margin-block: 15px;
		}
		.plusBtn {
			width: 30px !important;
			height: 30px !important;
			.plusIcon {
				width: 18px !important;
				height: 18px !important;
			}
		}
		.minusIconWrapper {
			.minusIcon {
				width: 14px !important;
				height: 14px !important;
			}
		}
		.threeDotIconWrapper {
			.threeDotIcon {
				width: 4px !important;
				height: 14px !important;
			}
		}
		.pleaseSelectCard {
			height: 39px;
			padding-left: 12px;
			padding-right: 16px;
			.pleaseSelectCardTitle {
				font-size: 12px !important;
			}
			.actionsWrapper {
				gap: 12px;
			}
			.serviceTypeButton {
				min-width: 120px !important;
				height: 27px !important;
				.pleaseSelectButtonText {
					font-size: 12px !important;
				}
			}
			.addOptionsButton {
				min-width: 120px !important;
				height: 27px !important;
				gap: 6px;
				font-size: 12px !important;
				.addOptionsButtonIcon {
					width: 18px;
					height: 18px;
				}
			}
		}
		.addAdditionalCard {
			height: 39px !important;
			padding-inline: 12px;
			font-size: 12px !important;
		}
		.extraFormRow {
			gap: 18px;
		}
		.saveCancelBtnWrapper {
			gap: 12px;
			padding-top: 18px;
		}
	}
`;

export const ImageUploadStylesWrapper = styled.div`
	.newImageUploadWrapper {
		.imageContainer {
			width: 100px !important;
			height: 100px !important;
			.icon {
				width: 32px !important;
				height: 32px !important;
				.cameraIcon {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 18px !important;
					height: 18px !important;
				}
			}
		}
		.headingTextColor {
			color: rgb(255, 95, 95) !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 12px !important;
			line-height: 1 !important;
		}
	}
	@media (max-width: 600px) {
		.newImageUploadWrapper {
			.imageContainer {
				width: 75px !important;
				height: 75px !important;
				.icon {
					width: 24px !important;
					height: 24px !important;
					.cameraIcon {
						width: 14px !important;
						height: 14px !important;
					}
				}
			}
			.headingTextColor {
				font-size: 10px !important;
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.newImageUploadWrapper {
			.imageContainer {
				width: 75px !important;
				height: 75px !important;
				.icon {
					width: 24px !important;
					height: 24px !important;
					.cameraIcon {
						width: 14px !important;
						height: 14px !important;
					}
				}
			}
			.headingTextColor {
				font-size: 10px !important;
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.newImageUploadWrapper {
			.imageContainer {
				width: 75px !important;
				height: 75px !important;
				.icon {
					width: 24px !important;
					height: 24px !important;
					.cameraIcon {
						width: 14px !important;
						height: 14px !important;
					}
				}
			}
			.headingTextColor {
				font-size: 10px !important;
			}
		}
	}
`;

export const InputStylesWrapper = styled.div`
	width: 100%;
	.productFormInput {
		line-height: 0 !important;
		.form-label {
			color: rgba(32, 34, 36, 1) !important;
			font-family: 'nunitosans-bold' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
		.inputBox {
			background-color: #fff !important;
			color: rgba(32, 34, 36, 1) !important;
			font-family: 'nunitosans-regular' !important;
			font-size: 16px !important;
			line-height: 1 !important;
			&::placeholder {
				color: #979797 !important;
				font-family: 'nunitosans-regular' !important;
				font-size: 16px !important;
				line-height: 1 !important;
			}
		}
		textarea {
			padding: 0.75rem !important;
		}
		.headingTextColor {
			color: rgb(255, 95, 95) !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 12px !important;
			line-height: 1 !important;
		}
	}
	@media (max-width: 600px) {
		.productFormInput {
			.form-label {
				font-size: 12px !important;
			}
			.inputBox {
				font-size: 12px !important;
				&::placeholder {
					font-size: 12px !important;
				}
			}
			.headingTextColor {
				font-size: 10px !important;
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.productFormInput {
			.form-label {
				font-size: 12px !important;
			}
			.inputBox {
				font-size: 12px !important;
				&::placeholder {
					font-size: 12px !important;
				}
			}
			.headingTextColor {
				font-size: 10px !important;
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.productFormInput {
			.form-label {
				font-size: 12px !important;
			}
			.inputBox {
				font-size: 12px !important;
				&::placeholder {
					font-size: 12px !important;
				}
			}
			.headingTextColor {
				font-size: 10px !important;
			}
		}
	}
`;

export const InputGroupStylesWrapper = styled.div`
	width: 100%;
	.productFormInput {
		line-height: 0 !important;
		.form-label {
			color: rgba(32, 34, 36, 1) !important;
			font-family: 'nunitosans-bold' !important;
			font-size: 16px !important;
			line-height: 1 !important;
		}
		.input-group {
			.inputGroup {
				background-color: #fff !important;
				span {
					color: rgba(32, 34, 36, 1) !important;
					font-family: 'nunitosans-regular' !important;
					font-size: 16px !important;
					line-height: 1 !important;
				}
			}
			.inputBox {
				background-color: #fff !important;
				color: rgba(32, 34, 36, 1) !important;
				font-family: 'nunitosans-regular' !important;
				font-size: 16px !important;
				line-height: 1 !important;
				&::placeholder {
					color: #979797 !important;
					font-family: 'nunitosans-regular' !important;
					font-size: 16px !important;
					line-height: 1 !important;
				}
			}
		}
		.headingTextColor {
			color: rgb(255, 95, 95) !important;
			font-family: 'nunitosans-semi-bold' !important;
			font-size: 12px !important;
			line-height: 1 !important;
		}
	}
	@media (max-width: 600px) {
		.productFormInput {
			.form-label {
				font-size: 12px !important;
			}
			.input-group {
				.inputGroup {
					span {
						font-size: 12px !important;
					}
				}
				.inputBox {
					font-size: 12px !important;
					&::placeholder {
						font-size: 12px !important;
					}
				}
			}
			.headingTextColor {
				font-size: 10px !important;
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.productFormInput {
			.form-label {
				font-size: 12px !important;
			}
			.input-group {
				.inputGroup {
					span {
						font-size: 12px !important;
					}
				}
				.inputBox {
					font-size: 12px !important;
					&::placeholder {
						font-size: 12px !important;
					}
				}
			}
			.headingTextColor {
				font-size: 10px !important;
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.productFormInput {
			.form-label {
				font-size: 12px !important;
			}
			.input-group {
				.inputGroup {
					span {
						font-size: 12px !important;
					}
				}
				.inputBox {
					font-size: 12px !important;
					&::placeholder {
						font-size: 12px !important;
					}
				}
			}
			.headingTextColor {
				font-size: 10px !important;
			}
		}
	}
`;
