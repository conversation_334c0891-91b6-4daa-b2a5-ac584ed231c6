import { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import FormInput from '../../../Components/Form/FormInput';
import { CameraIcon } from '../../../Components/Icons';
import FormCheck<PERSON>ox from '../../../Components/Form/FormCheckBox';
import ImageUpload from '../../../Components/Form/ImageUpload';
import { VenueApiRoutes, VenuePanelRoutes } from '../../../Utils/routes';
import AdditionalExtras from './AdditionalExtras';
import RequiredOptions from './RequiredOptions';
import Api from '../../../Helper/Api';
import {
	createFormData,
	formatFoodOptionListData,
	formatSegmentTagsOption,
	formatSingleProductDetails,
	formatSubCategoryOption,
	taxList
} from './utils';
import MenuItemFormSkeleton from './Skeleton/MenuItemFormSkeleton';
import FormInputGroup from '../../../Components/Form/FormInputGroup';
import Switch from '../../../Components/Common/Switch';
import authActions from '../../../Redux/auth/actions';
import NewPageWrapper from '../../../Components/Common/NewPageWrapper';
import {
	StylesWrapper,
	InputGroupStylesWrapper,
	InputStylesWrapper,
	ImageUploadStylesWrapper
} from './index.style';
import NewPageTitle from '../../../Components/Common/NewPageTitle';
import { FilledButton } from '../../../Components/Layout/Buttons';
import FormSelect2 from '../../../Components/Form/FormSelect2';
import FormMultiSelect2 from '../../../Components/Form/FormMultiSelect2';
import { validationSchema } from './validationSchema';

const AddEditProduct2 = () => {
	const state = useSelector((state) => ({ ...state }));
	const authData = state?.auth;
	const navigate = useNavigate();
	const { pathname } = useLocation();
	const params = useParams();
	const dispatch = useDispatch();
	const [subCategoryOptionsLoading, setSubCategoryOptionsLoading] =
		useState(false);
	const [segmentTagsLoading, setSegmentTagsLoading] = useState(false);
	const [submitFormLoading, setSubmitFormLoading] = useState(false);
	const [getProductLoading, setGetProductLoading] = useState(false);
	const [subCategoryData, setSubCategoryData] = useState([]);
	const [categoryName, setCategoryName] = useState();
	const [segmentTagsData, setSegmentTagsData] = useState([]);
	const [dietaryRequirements, setDietaryRequirements] = useState([]);
	const [
		initialSelectedDietaryRequirements,
		setInitialSelectedDietaryRequirements
	] = useState([]);
	const [initialFormData, setInitialFormData] = useState(null);
	const [deletedRequiredOptions, setDeletedRequiredOptions] = useState([]);
	const [deletedRequiredOptionsItems, setDeletedRequiredOptionsItems] =
		useState([]);
	const [deletedAdditionalExtras, setDeletedAdditionalExtras] = useState([]);
	const [requiredOptionPopover, setRequiredOptionPopover] = useState(false);
	const [additionalExtrasPopover, setAdditionalExtrasPopover] =
		useState(false);
	const [isError, setIsError] = useState(false);
	const [posStatus, setPosStatus] = useState(false);
	const isEdit = pathname.includes('/edit');
	const requiredOptionPopoverToggle = () => {
		setRequiredOptionPopover(!requiredOptionPopover);
	};
	const additionalExtrasPopoverToggle = () => {
		setAdditionalExtrasPopover(!additionalExtrasPopover);
	};
	const getSegmentTagsFieldPlaceholder = () => {
		let selectedSegmentCount = values?.segmentTags?.length;
		if (selectedSegmentCount) {
			return `${selectedSegmentCount} Segment Tags Selected`;
		}
		return 'Select Segment Tags';
	};
	const handleDeletedRequiredOptions = (data) => {
		let deletedItems = [];
		if (data?.data?.length > 0) {
			deletedItems = data?.data?.map((item) => {
				if (item?.itemId) {
					return item?.itemId;
				}
				return;
			});
		}
		setDeletedRequiredOptions((prev) => [...prev, data?.id]);
		setDeletedRequiredOptionsItems((prev) => [...prev, ...deletedItems]);
	};
	const handleDeletedRequiredOptionsItem = (itemId) => {
		if (itemId) {
			setDeletedRequiredOptionsItems((prev) => [...prev, itemId]);
		}
	};
	const handleDeletedAdditionalExtras = (itemId) => {
		if (itemId) {
			setDeletedAdditionalExtras((prev) => [...prev, itemId]);
		}
	};
	const handleStockLevelChange = () => {
		setFieldValue('stockLevel', !values?.stockLevel);
	};
	const handleStockLimitChange = () => {
		setFieldValue('stockLimit', !values?.stockLimit);
	};
	const getDietaryRequirements = async () => {
		try {
			const res = await Api('POST', VenueApiRoutes.getFoodOptionList);
			if (res?.data?.status) {
				let formatedData = formatFoodOptionListData(res?.data?.data);
				setDietaryRequirements(formatedData);
			} else {
				toast.error(res?.data?.message);
			}
		} catch (err) {
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	const getSubCategoryOptions = async () => {
		try {
			setSubCategoryOptionsLoading(true);
			const res = await Api('POST', VenueApiRoutes.getSubCategoryList, {
				bar_id: authData?.selectedVenue?.id
			});
			if (res?.data?.status) {
				let formatedData = formatSubCategoryOption(res?.data?.data);
				setSubCategoryData(
					formatedData?.sort((item1, item2) =>
						item1?.label?.localeCompare(item2?.label)
					)
				);
			} else {
				toast.error(res?.data?.message);
			}
			setSubCategoryOptionsLoading(false);
		} catch (err) {
			setSubCategoryOptionsLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	const getSegmentTags = async () => {
        try {
            setSegmentTagsLoading(true);
            const res = await Api('POST', VenueApiRoutes?.getSegmentTagsList, {
                bar_id: authData?.selectedVenue?.id
            });
            if (res?.data?.status) {
                let formatedData = formatSegmentTagsOption(res?.data?.data);
                setSegmentTagsData(formatedData); // removed sorting here
            } else {
                toast.error(res?.data?.message);
            }
            setSegmentTagsLoading(false);
        } catch (err) {
            setSegmentTagsLoading(false);
            if (err?.message) {
                toast.error(err?.message);
            }
        }
    };

	const getProductDetails = async () => {
		try {
			// setGetProductLoading(true);
			const res = await Api('POST', VenueApiRoutes.getSingleProduct, {
				id: params?.id,
				bar_id: authData?.selectedVenue?.id
			});
			if (res?.data?.status) {
				let formatedData = formatSingleProductDetails(res?.data?.data);
				setValues(formatedData);
				setInitialSelectedDietaryRequirements(
					formatedData?.dietaryRequirements
				);
				setInitialFormData(formatedData);
				setCategoryName(res?.data?.data?.sub_category?.name);
			} else {
				toast.error(res?.data?.message);
			}
			// setGetProductLoading(false);
		} catch (err) {
			// setGetProductLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	const submitFormHandler = async (values) => {
		let formData = createFormData({
			values: values,
			barId: authData?.selectedVenue?.id,
			productId: params?.id,
			initialSelectedDietaryRequirements:
				initialSelectedDietaryRequirements,
			deletedRequiredOptions: deletedRequiredOptions,
			deletedRequiredOptionsItems: deletedRequiredOptionsItems,
			deletedAdditionalExtras: deletedAdditionalExtras,
			isEdit: isEdit,
			initialFormData: initialFormData
		});
		try {
			setSubmitFormLoading(true);
			const res = await Api(
				'POST',
				!isEdit
					? VenueApiRoutes.addProduct
					: VenueApiRoutes.editProduct,
				formData
			);
			if (res?.data?.status) {
				toast.success(res?.data?.message);
				navigate(VenuePanelRoutes?.manageMenu, {
					state: { categoryName }
				});
			} else {
				toast.error(res?.data?.message);
			}
			setSubmitFormLoading(false);
		} catch (err) {
			setSubmitFormLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	const {
		values,
		setFieldValue,
		touched,
		errors,
		handleSubmit,
		handleChange,
		setValues,
		isValid,
		isValidating,
		validateForm,
		isSubmitting
	} = useFormik({
		initialValues: {
			image: null,
			mainCategoryId: '',
			category: '',
			itemName: '',
			basePrice: '',
			productTax: 'gst',
			pickupLocationId: '',
			pickupLocationAddress: '',
			segmentTags: [],
			description: '',
			serviceType: [], //["PICKUP","TABLE"]
			dietaryRequirements: [],
			stockLevel: false,
			stockLimit: false,
			stockQuantity: 0,
			stockRemaining: 0,
			requiredOptions: [], //{ serviceType: null || "PICKUP" || "TABLE" || "BOTH", data: [] }
			additionalExtras: []
		},
		validationSchema: validationSchema,
		onSubmit: submitFormHandler
	});
	useEffect(() => {
		(async () => {
			if (isEdit) {
				setGetProductLoading(true);
			}
			await getSubCategoryOptions();
			await getSegmentTags();
			await getDietaryRequirements();
			if (isEdit) {
				await getProductDetails();
				setGetProductLoading(false);
			}
		})();
	}, [authData?.selectedVenue?.id]);
	const handleSave = async () => {
		let res = await validateForm(values);
		if (Object.keys(res).length === 0) {
			setIsError(false);
		} else {
			setIsError(true);
		}
		handleSubmit();
	};
	useEffect(() => {
		if (authData?.selectedVenue?.posStatus === '1') {
			if (!isEdit) {
				navigate(VenuePanelRoutes?.manageMenu);
			} else {
				setPosStatus(true);
			}
		} else {
			setPosStatus(false);
		}
	}, [authData?.selectedVenue?.posStatus]);
	useEffect(() => {
		return () => {
			if (authData?.selectedVenue?.posStatus === '1') {
				dispatch(authActions.copy_paste_required_options(null));
			}
		};
	}, []);
	return (
		<NewPageWrapper>
			<StylesWrapper>
				<NewPageTitle>
					{!pathname.includes('/edit')
						? 'Add Menu Item'
						: 'Edit Menu Item'}
				</NewPageTitle>
				{getProductLoading ? (
					<MenuItemFormSkeleton />
				) : (
					<form className="overflow-hidden pa-t-14">
						<div className="formFieldWrapper">
							<ImageUploadStylesWrapper>
								<ImageUpload
									name={'image'}
									defaultImage={
										authData?.selectedVenue?.avatar
									}
									icon={
										<div className="cameraIcon">
											<CameraIcon
												height={'100%'}
												weight={'100%'}
											/>
										</div>
									}
									value={values.image}
									setFieldValue={setFieldValue}
									error={touched.image && !!errors.image}
									errorMsg={errors.image}
									className={'newImageUploadWrapper'}
								/>
							</ImageUploadStylesWrapper>
						</div>
						<div className="formGridContainer">
							<div className="formFieldWrapper">
								<FormSelect2
									name="category"
									label="Category"
									placeholder="Select Category"
									options={subCategoryData}
									value={subCategoryData?.find(
										(item) => item?.value == values.category
									)}
									onChange={(item) => {
										setFieldValue('category', item?.value);
										setFieldValue(
											'mainCategoryId',
											item?.categoryId
										);
										setCategoryName(item?.label);
										setFieldValue(
											'pickupLocationId',
											item?.pickupLocationId
										);
										setFieldValue(
											'pickupLocationAddress',
											item?.pickupLocationAddress
										);
									}}
									error={
										touched.category && !!errors.category
									}
									errorMsg={errors.category}
									isLoading={subCategoryOptionsLoading}
									isSearchable={true}
									disabled={posStatus}
								/>
							</div>
							<div className="formFieldWrapper">
								<InputStylesWrapper>
									<FormInput
										type="text"
										name="itemName"
										label="Product Name"
										placeholder="Enter Product Name"
										value={values.itemName}
										onChange={handleChange}
										error={
											touched.itemName &&
											!!errors.itemName
										}
										errorMsg={errors.itemName}
										disabled={posStatus}
										formGroupClassName={'productFormInput'}
									/>
								</InputStylesWrapper>
							</div>
							<div className="formFieldWrapper">
								<InputGroupStylesWrapper>
									<FormInputGroup
										type="number"
										name="basePrice"
										label="Product Base Price"
										placeholder="Enter Product Base Price"
										value={values?.basePrice}
										onChange={handleChange}
										error={
											touched.basePrice &&
											!!errors.basePrice
										}
										errorMsg={errors.basePrice}
										icon={<span className="fs-14">$</span>}
										iconPlacement="start"
										className={'pl-0'}
										iconBackgroundClass={
											'inputGroupBackground'
										}
										onBlur={() => {
											values?.basePrice &&
												setFieldValue(
													'basePrice',
													Number(
														values?.basePrice
													).toFixed(2)
												);
										}}
										onWheel={(e) => e.target.blur()}
										disabled={posStatus}
										formGroupClassName={'productFormInput'}
									/>
								</InputGroupStylesWrapper>
							</div>
							<div className="formFieldWrapper">
								<FormSelect2
									name="productTax"
									label="Product Tax"
									placeholder="Select Product Tax"
									options={taxList}
									value={taxList?.find(
										(item) =>
											item?.value === values?.productTax
									)}
									onChange={(item) => {
										setFieldValue(
											'productTax',
											item?.value
										);
									}}
									error={
										touched.productTax &&
										!!errors.productTax
									}
									errorMsg={errors.productTax}
								/>
							</div>
							<div className="formFieldWrapper">
								<InputStylesWrapper>
									<FormInput
										type="text"
										name="pickupLocationAddress"
										label="Pickup Location"
										placeholder="Pickup Location"
										value={values.pickupLocationAddress}
										disabled={true}
										formGroupClassName={'productFormInput'}
									/>
								</InputStylesWrapper>
							</div>
							<div className="formFieldWrapper">
								<FormMultiSelect2
									name="segmentTags"
									label="Segment Tags"
									placeholder={getSegmentTagsFieldPlaceholder()}
									options={segmentTagsData}
									value={values?.segmentTags}
									onChange={(items) => {
										setFieldValue('segmentTags', items);
									}}
									isLoading={segmentTagsLoading}
									isSearchable={true}
								/>
							</div>
						</div>
						<div className="formFieldWrapper">
							<InputStylesWrapper>
								<FormInput
									type="textarea"
									name="description"
									label="Description"
									rows={4}
									placeholder="Enter Description"
									value={values.description}
									onChange={handleChange}
									error={
										touched.description &&
										!!errors.description
									}
									errorMsg={errors.description}
									disabled={posStatus}
									formGroupClassName={'productFormInput'}
								/>
							</InputStylesWrapper>
						</div>
						<div className="formFieldWrapper">
							<div className="newCommonLabel">Service Type</div>
							<div className="serviceTypeContainer pa-t-12">
								<div>
									<FormCheckBox
										label={'Takeaway'}
										name="serviceType"
										value={'PICKUP'}
										checked={values?.serviceType?.includes(
											'PICKUP'
										)}
										onChange={handleChange}
										formGroupClassName={'newFormCheckbox'}
									/>
								</div>
								<div>
									<FormCheckBox
										label={'Table Service'}
										name="serviceType"
										value={'TABLE'}
										checked={values?.serviceType?.includes(
											'TABLE'
										)}
										onChange={handleChange}
										formGroupClassName={'newFormCheckbox'}
									/>
								</div>
							</div>
							{touched.serviceType && !!errors.serviceType && (
								<p className="pa-t-6 errorMessageText">
									{errors?.serviceType}
								</p>
							)}
						</div>
						<>
							<div className="newCommonLabel">
								Dietary Requirements
							</div>
							<div className="pa-t-12">
								{dietaryRequirements?.length > 0 ? (
									<div className="dietaryRequirementsContainer">
										{dietaryRequirements?.map(
											(item, index) => {
												return (
													<div
														className="dietaryRequirementItem"
														key={index}
													>
														<FormCheckBox
															label={item?.label}
															name="dietaryRequirements"
															value={item?.value}
															checked={values?.dietaryRequirements?.includes(
																item?.value?.toString()
															)}
															onChange={
																handleChange
															}
															formGroupClassName={
																'newFormCheckbox'
															}
														/>
													</div>
												);
											}
										)}
									</div>
								) : (
									<div className="noDataText">
										No Dietary Requirements Options
									</div>
								)}
							</div>
						</>
						<div className="horizontalLine" />
						{/* Stock Option */}
						<div className="">
							<div className="formFieldWrapper">
								<div
									className="d-flex align-items-center"
									style={{ lineHeight: 1 }}
								>
									<span className="pr-18 newCommonLabel">
										Stock Level
									</span>
									<Switch
										name="stockLevel"
										checked={values.stockLevel}
										onChange={() =>
											handleStockLevelChange()
										}
									/>
								</div>
								<p className={`pa-t-6 hintText`}>
									When enabled, this optional feature will
									track the quantity of this product and
									automatically mark the product as
									unavailable on the customer app when the
									stock limit is reached.
								</p>
							</div>
							{values?.stockLevel && (
								<>
									{/* <span className="newCommonLabel">
										Total Stock Quantity
									</span>
									<p className={'pa-t-3 pa-b-6 hintText'}>
										Enter your available stock level, each
										time a customer purchases this menu
										item, the stock count will be reduced.
										This item will be marked as unavailable
										when the number reaches 0.
									</p> */}
									<div className="formGridContainer">
										<div className="formFieldWrapper">
											<InputStylesWrapper>
												<FormInput
													label="Enter stock quantity"
													type="number"
													name="stockQuantity"
													placeholder="Enter stock quantity"
													value={values.stockQuantity}
													onChange={(e) => {
														setFieldValue(
															'stockQuantity',
															e?.target?.value
														);
														setFieldValue(
															'stockRemaining',
															e?.target?.value
														);
													}}
													onWheel={(e) =>
														e.target.blur()
													}
													error={
														touched.stockQuantity &&
														!!errors.stockQuantity
													}
													errorMsg={
														errors.stockQuantity
													}
													formGroupClassName={
														'productFormInput'
													}
												/>
											</InputStylesWrapper>
										</div>
										<div className="formFieldWrapper">
											<InputStylesWrapper>
												<FormInput
													label="Remaining quantity"
													type="number"
													name="stockRemaining"
													placeholder="Remaining quantity"
													value={
														values.stockRemaining
													}
													onChange={handleChange}
													disabled
													formGroupClassName={
														'productFormInput'
													}
												/>
												{values.stockRemaining == 0 && (
													<p className="pa-t-6 errorMessageText">
														*currently marked as
														unavailable on customer
														app
													</p>
												)}
											</InputStylesWrapper>
										</div>
									</div>
									<div className="formFieldWrapper">
										<div
											className="d-flex align-items-center"
											style={{ lineHeight: 1 }}
										>
											<span className="newCommonLabel pr-18">
												Daily Stock Refresh
											</span>
											<Switch
												name="stockLimit"
												checked={values?.stockLimit}
												onChange={() =>
													handleStockLimitChange()
												}
											/>
										</div>
										<p className={'pa-t-6 hintText'}>
											When enabled, this optional feature
											will automatically refresh to the
											original stock level entered at the
											beginning of each day. This prevents
											you from having to manually enter
											the stock level daily.
										</p>
									</div>
								</>
							)}
						</div>
						<div className="additionalItemsWrapper">
							<div className="flex-1">
								<RequiredOptions
									name="requiredOptions"
									values={values}
									handleChange={handleChange}
									setFieldValue={setFieldValue}
									touched={touched}
									errors={errors}
									requiredOptionPopover={
										requiredOptionPopover
									}
									requiredOptionPopoverToggle={
										requiredOptionPopoverToggle
									}
									handleDeletedRequiredOptions={
										handleDeletedRequiredOptions
									}
									handleDeletedRequiredOptionsItem={
										handleDeletedRequiredOptionsItem
									}
									posStatus={posStatus}
								/>
							</div>
							<div className="flex-1">
								<AdditionalExtras
									name="additionalExtras"
									values={values}
									handleChange={handleChange}
									setFieldValue={setFieldValue}
									touched={touched}
									errors={errors}
									additionalExtrasPopover={
										additionalExtrasPopover
									}
									additionalExtrasPopoverToggle={
										additionalExtrasPopoverToggle
									}
									handleDeletedAdditionalExtras={
										handleDeletedAdditionalExtras
									}
									posStatus={posStatus}
								/>
							</div>
						</div>
						<div className="saveCancelBtnWrapper">
							<FilledButton
								buttonText={'Cancel'}
								background={'#ffffff'}
								color={'rgba(107, 194, 66, 1)'}
								style={{
									width: '160px',
									border: '1px solid rgba(107, 194, 66, 1)'
								}}
								onClick={() => {
									navigate(VenuePanelRoutes?.manageMenu, {
										state: {
											categoryName:
												initialFormData?.categoryName
										}
									});
								}}
							/>
							<FilledButton
								buttonText={'Save Product'}
								background={'rgba(107, 194, 66, 0.2)'}
								color={'rgba(107, 194, 66, 1)'}
								style={{
									width: '160px',
									border: '1px solid rgba(107, 194, 66, 1)'
								}}
								onClick={handleSave}
								loading={submitFormLoading}
							/>
						</div>
						<p
							className={`pa-t-6 ${
								isError ? 'visible' : 'invisible'
							} errorMessageText`}
						>
							Please fill in all required fields.
						</p>
					</form>
				)}
			</StylesWrapper>
		</NewPageWrapper>
	);
};

export default AddEditProduct2;
