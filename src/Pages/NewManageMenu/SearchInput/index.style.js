import styled from 'styled-components';

export const StylesWrapper = styled.div`
	.newCustomInputContainer {
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 18px;
		border: 2px solid rgba(213, 213, 213, 1);
		border-radius: 2px;
		height: 46px;
		padding-inline: 18px;
		background-color: rgba(245, 246, 250, 1);
		border-radius: 4px;
	}
	.newCustomInput {
		width: 100% !important;
		height: 100% !important;
		outline: none !important;
		border: none !important;
		color: #979797 !important;
		font-family: 'nunitosans-regular' !important;
		font-size: 14px !important;
		line-height: 1 !important;
		background-color: transparent !important;
		&::placeholder {
			color: #979797 !important;
			font-family: 'nunitosans-regular' !important;
			font-size: 14px !important;
			line-height: 1 !important;
		}
	}
	.prefixContainer {
		align-self: stretch;
		display: flex;
		align-items: center;
		.searchIcon {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 19px;
			height: 17px;
		}
	}
	@media (max-width: 600px) {
		.newCustomInputContainer {
			gap: 12px;
			height: 35px !important;
			padding-inline: 12px;
		}
		.newCustomInput {
			font-size: 11px !important;
			&::placeholder {
				font-size: 11px !important;
			}
		}
		.prefixContainer {
			.searchIcon {
				width: 15px;
				height: 14px;
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.newCustomInputContainer {
			gap: 14px;
			height: 38px !important;
			padding-inline: 14px;
		}
		.newCustomInput {
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
		}
		.prefixContainer {
			.searchIcon {
				width: 15px;
				height: 14px;
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.newCustomInputContainer {
			gap: 14px;
			height: 38px !important;
			padding-inline: 14px;
		}
		.newCustomInput {
			font-size: 12px !important;
			&::placeholder {
				font-size: 12px !important;
			}
		}
		.prefixContainer {
			.searchIcon {
				width: 15px;
				height: 14px;
			}
		}
	}
`;
