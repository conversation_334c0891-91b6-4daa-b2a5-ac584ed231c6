import { StylesWrapper } from './index.style';
import { SearchIcon } from '../../../Components/Icons/SearchIcon/SearchIcon';

const SearchInput = ({
	id,
	name,
	value,
	onChange,
	onBlur,
	placeholder,
	disabled = false,
	...props
}) => {
	return (
		<StylesWrapper>
			<div className="newCustomInputContainer">
				<div className="prefixContainer">
					<div className="searchIcon">
						<SearchIcon
							stroke="#000000"
							width="100%"
							height="100%"
						/>
					</div>
				</div>
				<input
					className="newCustomInput"
					type={'text'}
					id={id}
					name={name}
					value={value}
					onChange={onChange}
					onBlur={onBlur}
					placeholder={placeholder}
					disabled={disabled}
					{...props}
				/>
			</div>
		</StylesWrapper>
	);
};

export default SearchInput;
