import styled from 'styled-components';

export const StylesWrapper = styled.div`
	/* .searchExportWrapper {
		display: flex;
		align-items: center;
		gap: 19px;
		padding-top: 12px;
		padding-bottom: 24px;
	} */
	.horizontalLine {
		border-bottom: 1px solid rgba(32, 34, 36, 0.47);
		margin-top: 20px;
		margin-bottom: 16px;
	}
	@media (max-width: 600px) {
		/* .searchExportWrapper {
			gap: 12px;
			padding-top: 12px;
			padding-bottom: 18px;
		} */
		.horizontalLine {
			margin-top: 16px;
			margin-bottom: 12px;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		/* .searchExportWrapper {
			gap: 12px;
			padding-top: 12px;
			padding-bottom: 18px;
		} */
		.horizontalLine {
			margin-top: 16px;
			margin-bottom: 12px;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		/* .searchExportWrapper {
			gap: 15px;
			padding-top: 12px;
			padding-bottom: 18px;
		} */
		.horizontalLine {
			margin-top: 16px;
			margin-bottom: 12px;
		}
	}
`;
