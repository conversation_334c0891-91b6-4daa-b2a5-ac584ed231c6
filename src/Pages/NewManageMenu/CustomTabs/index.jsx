import React, { useState } from 'react';
import { Tab, Tabs } from 'react-tabs-scrollable';
import { StylesWrapper } from './index.style';
import { tabData } from '../utils';

const CustomTabs = () => {
	const [activeTab, setActiveTab] = useState(1);

	const onTabClick = (e, index) => {
		setActiveTab(index);
	};
	return (
		<StylesWrapper>
			<Tabs
				activeTab={activeTab}
				onTabClick={onTabClick}
				leftBtnIcon={<i className="fa fa-angle-left"></i>}
				rightBtnIcon={<i className="fa fa-angle-right"></i>}
				hideNavBtnsOnMobile={false}
			>
				{tabData?.length > 0 &&
					tabData?.map((item) => (
						<Tab key={item?.key}>{item?.label}</Tab>
					))}
			</Tabs>
		</StylesWrapper>
	);
};

export default CustomTabs;
