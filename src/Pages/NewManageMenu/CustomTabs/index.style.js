import styled from 'styled-components';

export const StylesWrapper = styled.div`
	.rts___btn {
		border-radius: 0 !important;
		border: none !important;
		border-bottom: 4px solid transparent !important;
		font-family: 'nunitosans-medium' !important;
		font-size: 20px !important;
		line-height: 1 !important;
	}
	.rts___tabs___container {
		padding: 0 !important;
		.rts___nav___btn___container {
			.rts___nav___btn {
				padding: 0 !important;
				font-family: 'nunitosans-medium' !important;
				font-size: 20px !important;
				line-height: 1 !important;
				&:hover {
					background-color: transparent !important;
				}
			}
			.rts___left___nav___btn {
				padding-right: 12px !important;
			}
			.rts___right___nav___btn {
				padding-left: 12px !important;
			}
		}
		.rts___tabs {
			padding: 0 !important;
			gap: 45px !important;
			.rts___tab {
				margin: 0 !important;
				padding: 0 !important;
				padding-bottom: 6px !important;
			}
			.rts___tab___selected {
				background-color: transparent !important;
				color: #202224 !important;
				box-shadow: none !important;
				border-bottom: 4px solid #f95c69 !important;
			}
		}
	}
	@media (max-width: 600px) {
		.rts___btn {
			font-size: 16px !important;
		}
		.rts___tabs___container {
			.rts___nav___btn___container {
				.rts___nav___btn {
					font-size: 16px !important;
				}
			}
			.rts___tabs {
				gap: 27px !important;
			}
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.rts___btn {
			font-size: 16px !important;
		}
		.rts___tabs___container {
			.rts___nav___btn___container {
				.rts___nav___btn {
					font-size: 16px !important;
				}
			}
			.rts___tabs {
				gap: 34px !important;
			}
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.rts___btn {
			font-size: 16px !important;
		}
		.rts___tabs___container {
			.rts___nav___btn___container {
				.rts___nav___btn {
					font-size: 20px !important;
				}
			}
			.rts___tabs {
				gap: 34px !important;
			}
		}
	}
`;
