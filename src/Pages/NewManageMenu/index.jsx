import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';

import { StylesWrapper } from './index.style';
import NewPageWrapper from '../../Components/Common/NewPageWrapper';
import SuggestionText from './SuggestionText';
import { FilledButton } from '../../Components/Layout/Buttons';
import SearchInput from './SearchInput';
import CustomTabs from './CustomTabs';
import TabButtons from './TabButtons';
import Menu from './Menu';
import Categories from './Categories';
import UpSells from './UpSells';
import NewPageTitle from '../../Components/Common/NewPageTitle';

const ManageMenu = () => {
	const navigate = useNavigate();
	const dispatch = useDispatch();
	const location = useLocation();
	const authData = useSelector((state) => ({ ...state.auth }));
	const [pageLoading, setPageLoading] = useState(false);
	const [selectedTabButtonKey, setSelectedTabButtonKey] = useState('menu');

	return (
		<NewPageWrapper>
			<StylesWrapper>
				<NewPageTitle>Manage Menu</NewPageTitle>
				<SuggestionText />
				{/* <div className="searchExportWrapper">
					<div className="flex-1">
						<SearchInput placeholder={'Search your menu'} />
					</div>
					<FilledButton
						buttonText={'Export Menu (CSV)'}
						background={'rgba(72, 128, 255, 0.2)'}
						color={'#4880FF'}
						style={{ width: '160px' }}
					/>
				</div> 
				<CustomTabs /> */}
				<TabButtons
					selectedTabButtonKey={selectedTabButtonKey}
					setSelectedTabButtonKey={setSelectedTabButtonKey}
				/>
				<div className="horizontalLine" />
				{selectedTabButtonKey === 'menu' && <Menu />}
				{selectedTabButtonKey === 'categories' && (
					<Categories barId={authData?.selectedVenue?.id} />
				)}
				{selectedTabButtonKey === 'up-sells' && (
					<UpSells barId={authData?.selectedVenue?.id} />
				)}
			</StylesWrapper>
		</NewPageWrapper>
	);
};

export default ManageMenu;
