import React, { useState } from 'react';
import NewPageWrapper from '../../../Components/Common/NewPageWrapper';
import NewFormMultiSelect from '../../../Components/NewForm/NewFormMultiSelect';
import NewFormLabel from '../../../Components/NewForm/NewFormLabel';
import NewFormErrorMessage from '../../../Components/NewForm/NewFormErrorMessage';
import { StylesWrapper, SubmitButtonStylesWrapper } from './index.style';
import NewFormInput from '../../../Components/NewForm/NewFormInput';
import NewFormTextarea from '../../../Components/NewForm/NewFormTextarea';
import NewFormSelect from '../../../Components/NewForm/NewFormSelect';
import ImageUpload from '../ImageUpload';
import { FilledButton } from '../../../Components/Layout/Buttons';
import NewFormCheckbox from '../../../Components/NewForm/NewFormCheckbox';

const AddEditProduct = () => {
	return (
		<>
			<NewPageWrapper>
				<StylesWrapper>
					<form>
						<h1 className="titleText">Product Details</h1>
						<div className="newFormContainer">
							<div className="flex-1">
								<div className="newFormGroup">
									<NewFormLabel
										className={'pa-b-10'}
										label={'Category'}
										tooltip={
											'Select the appropriate category for the product, this will determine where it will appear on the menu (e.g. Coffee, Mains, Dessert).'
										}
									/>
									<NewFormMultiSelect
										placeholder="Link product to a category"
										options={Array(15)
											.fill('')
											.map((item, i) => ({
												label: `Option ${i + 1}`,
												value: i + 1
											}))}
										isSearchable
									/>
									<NewFormErrorMessage
										className={'pa-t-6'}
										message={''}
									/>
								</div>
								<div className="newFormGroup">
									<NewFormLabel
										className={'pa-b-10'}
										label={'Product Name'}
										tooltip={
											'Enter the name of the product as it will appear on the menu, making it clear and appealing to customers.'
										}
									/>
									<NewFormInput placeholder="Enter product name " />
									<NewFormErrorMessage
										className={'pa-t-6'}
										message={''}
									/>
								</div>
								<div className="newFormGroup">
									<NewFormLabel
										className={'pa-b-10'}
										label={'Product Description'}
										tooltip={
											'Write a tempting description to encourage customers to order, and include a detailed list of ingredients to highlight allergens.'
										}
									/>
									<NewFormTextarea placeholder="Provide a brief description of the product and list ingredient information for allergies." />
									<NewFormErrorMessage
										className={'pa-t-6'}
										message={''}
									/>
								</div>
								<div className="newFormGroup">
									<NewFormLabel
										className={'pa-b-10'}
										label={'Segment Tags'}
										tooltip={
											'Select segments that apply for this product to ensure you can capture the best data on your segments page.'
										}
									/>
									<NewFormMultiSelect
										placeholder="Select segment tags for this product"
										options={Array(15)
											.fill('')
											.map((item, i) => ({
												label: `Option ${i + 1}`,
												value: i + 1
											}))}
										isSearchable
									/>
									<NewFormErrorMessage
										className={'pa-t-6'}
										message={''}
									/>
								</div>
								<h1 className="titleText">Product Prices</h1>
								<div className="newFormGroup">
									<NewFormLabel
										className={'pa-b-10'}
										label={'Product Base Price'}
										tooltip={
											'Set the base price for the product before any option sets are added. Option Sets will adjust the final price based on customer selections.'
										}
									/>
									<NewFormInput
										placeholder="0.00"
										prefix={'$'}
									/>
									<NewFormErrorMessage
										className={'pa-t-6'}
										message={''}
									/>
								</div>
								<div className="newFormGroup">
									<NewFormLabel
										className={'pa-b-10'}
										label={'Product Tax'}
										tooltip={
											'Choose the applicable tax rate for this product.'
										}
									/>
									<NewFormSelect
										placeholder="GST Tax"
										options={Array(15)
											.fill('')
											.map((item, i) => ({
												label: `Option ${i + 1}`,
												value: i + 1
											}))}
										isSearchable
									/>
									<NewFormErrorMessage
										className={'pa-t-6'}
										message={''}
									/>
								</div>
								<h1 className="titleText">
									Product Service Details
								</h1>
								<div className="newFormGroup">
									<NewFormLabel
										className={'pa-b-10'}
										label={'Service Type'}
										tooltip={
											'Select how the product is served to the customer.'
										}
									/>
									<NewFormMultiSelect
										placeholder="Select how you would like to serve this product"
										options={Array(15)
											.fill('')
											.map((item, i) => ({
												label: `Option ${i + 1}`,
												value: i + 1
											}))}
										isSearchable
									/>
									<NewFormErrorMessage
										className={'pa-t-6'}
										message={''}
									/>
								</div>
								<h1 className="titleText">
									Product Option Sets
								</h1>
								<div className="newFormGroup">
									<NewFormLabel
										className={'pa-b-10'}
										label={'Pick Up (Collect at Counter)'}
										tooltip={
											'Choose all Option Sets (modifiers) that apply to the Pick Up service for this product.'
										}
									/>
									<NewFormMultiSelect
										placeholder="Link available option sets to the pickup service type for this product"
										options={Array(15)
											.fill('')
											.map((item, i) => ({
												label: `Option ${i + 1}`,
												value: i + 1
											}))}
										isSearchable
									/>
									<NewFormErrorMessage
										className={'pa-t-6'}
										message={''}
									/>
								</div>
								<div className="newFormGroup">
									<NewFormLabel
										className={'pa-b-10'}
										label={'Table Service'}
										tooltip={
											'Choose all Option Sets (modifiers) that apply to the Table Service for this product.'
										}
									/>
									<NewFormMultiSelect
										placeholder="Link available option sets to the table service service type for this product"
										options={Array(15)
											.fill('')
											.map((item, i) => ({
												label: `Option ${i + 1}`,
												value: i + 1
											}))}
										isSearchable
									/>
									<NewFormErrorMessage
										className={'pa-t-6'}
										message={''}
									/>
								</div>
								<div className="newFormGroup">
									<NewFormLabel
										className={'pa-b-10'}
										label={'Both'}
										tooltip={
											'Choose all Option Sets (modifiers) that apply to both Pick Up and Table Service for this product.'
										}
									/>
									<NewFormMultiSelect
										placeholder="Link available option sets to both service types for this product"
										options={Array(15)
											.fill('')
											.map((item, i) => ({
												label: `Option ${i + 1}`,
												value: i + 1
											}))}
										isSearchable
									/>
									<NewFormErrorMessage
										className={'pa-t-6'}
										message={''}
									/>
								</div>
								<h1 className="titleText">
									Product Stock Details
								</h1>
								<div className="newFormGroup">
									<NewFormLabel
										className={'pa-b-10'}
										label={'Stock Level'}
										tooltip={
											'Set the current stock level for the product to help manage inventory and prevent overselling.'
										}
									/>
									<NewFormCheckbox>
										Enable stock level feature
									</NewFormCheckbox>
									<NewFormErrorMessage
										className={'pa-t-6'}
										message={''}
									/>
									<div
										className="pa-t-20 d-flex"
										style={{ gap: '9px' }}
									>
										<div className="flex-1">
											<NewFormInput placeholder="Enter total stock level amount" />
										</div>
										<div className="flex-1">
											<NewFormInput placeholder="Stock level remaining" />
										</div>
									</div>
								</div>
								<div className="newFormGroup">
									<NewFormLabel
										className={'pa-b-10'}
										label={'Stock Level Daily Refresh'}
										tooltip={`Enable this to automatically reset this product's stock level each day, so you don’t have to update it manually.`}
									/>
									<NewFormCheckbox>
										Enable daily stock refresh
									</NewFormCheckbox>
									<NewFormErrorMessage
										className={'pa-t-6'}
										message={''}
									/>
								</div>
								<h1 className="titleText">
									Product Dietary Labels
								</h1>
								<div className="newFormGroup">
									<NewFormLabel
										className={'pa-b-10'}
										label={'Dietary Labels'}
										tooltip={
											'Select any relevant dietary labels (e.g. gluten free, vegan) that apply to the product.'
										}
									/>
									<NewFormMultiSelect
										placeholder="Select product dietary information"
										options={Array(15)
											.fill('')
											.map((item, i) => ({
												label: `Option ${i + 1}`,
												value: i + 1
											}))}
										isSearchable
									/>
									<NewFormErrorMessage
										className={'pa-t-6'}
										message={''}
									/>
								</div>
							</div>
							<div className="newFormGroup">
								<NewFormLabel
									className={'pa-b-10'}
									label={'Product Image:'}
								/>
								<ImageUpload />
							</div>
						</div>
					</form>
				</StylesWrapper>
			</NewPageWrapper>
			<SubmitButtonStylesWrapper>
				<FilledButton
					buttonText={'Cancel'}
					background={'#ffffff'}
					color={'rgba(107, 194, 66, 1)'}
					style={{
						width: '160px',
						border: '1px solid rgba(107, 194, 66, 1)'
					}}
				/>
				<FilledButton
					buttonText={'Save Product'}
					background={'rgba(107, 194, 66, 0.2)'}
					color={'rgba(107, 194, 66, 1)'}
					style={{ width: '160px' }}
				/>
			</SubmitButtonStylesWrapper>
		</>
	);
};

export default AddEditProduct;
