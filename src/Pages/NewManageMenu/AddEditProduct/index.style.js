import styled from 'styled-components';

export const StylesWrapper = styled.div`
	.titleText {
		color: #202224;
		font-family: 'nunitosans-bold';
		font-size: 32px;
		padding-bottom: 36px;
	}
	.newFormContainer {
		width: 1228px;
		display: flex;
		gap: 85px;
	}
	.newFormGroup {
		padding-bottom: 32px;
	}
	@media (max-width: 600px) {
		.titleText {
			font-size: 24px;
			padding-bottom: 22px;
		}
		.newFormGroup {
			padding-bottom: 19px;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.titleText {
			font-size: 24px;
			padding-bottom: 27px;
		}
		.newFormContainer {
			width: 100%;
			gap: 48px;
			padding-right: 18px;
		}
		.newFormGroup {
			padding-bottom: 24px;
		}
	}
	@media (max-width: 899px) {
		.newFormContainer {
			width: 100%;
			flex-direction: column-reverse;
			gap: 12px;
			padding-right: 0;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) {
		.titleText {
			font-size: 24px;
			padding-bottom: 27px;
		}
		.newFormContainer {
			width: 921px;
			gap: 64px;
		}
		.newFormGroup {
			padding-bottom: 24px;
		}
	}
`;

export const SubmitButtonStylesWrapper = styled.div`
	background: rgba(213, 213, 213, 1);
	height: 91px;
	display: flex;
	justify-content: flex-end;
	align-items: center;
	gap: 15px;
	padding-inline: 24px;
	position: sticky;
	bottom: 0;
	/* z-index: 1; */
	@media (max-width: 600px) {
		height: 68px;
		gap: 12px;
		padding-inline: 18px;
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		height: 68px;
		gap: 12px;
		padding-inline: 18px;
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) {
		height: 68px;
		gap: 12px;
		padding-inline: 18px;
	}
`;
