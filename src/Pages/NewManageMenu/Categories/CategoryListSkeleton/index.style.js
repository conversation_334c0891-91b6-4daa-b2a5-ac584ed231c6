import styled from 'styled-components';

const StylesWrapper = styled.div`
	.skeletonWrapper {
		height: 75px !important;
		margin-bottom: 6px;
	}
	@media (max-width: 600px) {
		.skeletonWrapper {
			height: 50px !important;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		.skeletonWrapper {
			height: 56px !important;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		.skeletonWrapper {
			height: 56px !important;
		}
	}
`;

export default StylesWrapper;
