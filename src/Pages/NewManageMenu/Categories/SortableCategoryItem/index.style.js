import styled from 'styled-components';

export const StylesWrapper = styled.div`
	background: rgba(255, 255, 255, 1);
	border: 1px solid rgba(213, 213, 213, 1);
	border-radius: 4px;
	height: 75px;
	display: flex;
	align-items: center;
	gap: 20px;
	padding-left: 13px;
	padding-right: 24px;
	margin-bottom: 6px;
	.dragIconWrapper {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 6px;
		border-radius: 4px;
		cursor: move;
		&:hover {
			background-color: rgba(1, 1, 1, 0.05);
			transition: all 0.3s;
		}
		.dragIcon {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 10px;
			height: 16px;
		}
	}
	.categoryName {
		flex: 1;
		font-family: 'nunitosans-semi-bold';
		font-size: 16px;
		line-height: 27px;
		color: rgba(46, 46, 46, 1);
	}
	.productCount {
		font-family: 'nunitosans-semi-bold';
		font-size: 16px;
		line-height: 27px;
		color: rgba(46, 46, 46, 0.73);
	}
	@media (max-width: 600px) {
		height: 50px;
		gap: 13px;
		padding-left: 9px;
		padding-right: 16px;
		.categoryName {
			font-size: 11px;
			line-height: 18px;
		}
		.productCount {
			font-size: 11px;
			line-height: 18px;
		}
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		height: 56px;
		gap: 15px;
		padding-left: 10px;
		padding-right: 18px;
		.categoryName {
			font-size: 12px;
			line-height: 20px;
		}
		.productCount {
			font-size: 12px;
			line-height: 20px;
		}
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		height: 56px;
		gap: 15px;
		padding-left: 10px;
		padding-right: 18px;
		.categoryName {
			font-size: 12px;
			line-height: 20px;
		}
		.productCount {
			font-size: 12px;
			line-height: 20px;
		}
	}
`;
