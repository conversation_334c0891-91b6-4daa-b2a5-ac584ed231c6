import React, { createContext, useContext, useMemo } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import { VerticalSixDotIcon2 } from '../../../../Components/Icons';
import { StylesWrapper } from './index.style';

const SortableItemContext = createContext({
	attributes: {},
	listeners: undefined,
	ref() {}
});

const DragHandle = () => {
	const { attributes, listeners, ref } = useContext(SortableItemContext);
	return (
		<div
			{...attributes}
			{...listeners}
			ref={ref}
			className="dragIconWrapper"
		>
			<div className="dragIcon">
				<VerticalSixDotIcon2 height={'100%'} width={'100%'} />
			</div>
		</div>
	);
};

const SortableCategoryItem = ({ categoryId, categoryName, productCount }) => {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		setActivatorNodeRef,
		isDragging
	} = useSortable({ id: categoryId });

	const context = useMemo(
		() => ({
			attributes,
			listeners,
			ref: setActivatorNodeRef
		}),
		[attributes, listeners, setActivatorNodeRef]
	);

	const style = {
		opacity: isDragging ? 0.4 : undefined,
		transform: CSS.Translate.toString(transform),
		transition
	};
	return (
		<SortableItemContext.Provider value={context}>
			<StylesWrapper ref={setNodeRef} style={style}>
				<DragHandle />
				<div className="categoryName">{categoryName}</div>
				<div className="productCount">{productCount + ' products'}</div>
			</StylesWrapper>
		</SortableItemContext.Provider>
	);
};

export default SortableCategoryItem;
