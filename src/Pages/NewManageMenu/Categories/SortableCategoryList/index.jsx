import React, { useMemo, useState } from 'react';
import {
	defaultDropAnimationSideEffects,
	DndContext,
	DragOverlay,
	KeyboardSensor,
	PointerSensor,
	useSensor,
	useSensors
} from '@dnd-kit/core';
import {
	arrayMove,
	SortableContext,
	sortableKeyboardCoordinates
} from '@dnd-kit/sortable';
import { restrictToParentElement } from '@dnd-kit/modifiers';
import SortableCategoryItem from '../SortableCategoryItem';

const dropAnimationConfig = {
	sideEffects: defaultDropAnimationSideEffects({
		styles: {
			active: {
				opacity: '0.2'
			}
		}
	})
};

const SortableCategoryList = ({
	categoryList,
	handleUpdateCategorySequence
}) => {
	const sensors = useSensors(
		useSensor(PointerSensor, { activationConstraint: { distance: 5 } }),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates
		})
	);
	const [active, setActive] = useState(null);
	const activeItem = useMemo(
		() => categoryList.find((item) => item.categoryId == active?.id),
		[active, categoryList]
	);
	const handleDragEnd = (event) => {
		const { active, over } = event;
		if (active?.id !== over?.id) {
			const oldIndex = categoryList?.findIndex(
				(item) => item?.categoryId == active.id
			);
			const newIndex = categoryList?.findIndex(
				(item) => item?.categoryId == over.id
			);
			const updatedCategoryList = arrayMove(
				categoryList,
				oldIndex,
				newIndex
			);
			handleUpdateCategorySequence(updatedCategoryList);
		}
		setActive(null);
	};
	return (
		<DndContext
			sensors={sensors}
			onDragStart={({ active }) => {
				setActive(active);
			}}
			onDragEnd={handleDragEnd}
			onDragCancel={() => {
				setActive(null);
			}}
			modifiers={[restrictToParentElement]}
		>
			<SortableContext items={categoryList}>
				{categoryList?.length > 0 &&
					categoryList?.map((item) => {
						return (
							<SortableCategoryItem
								key={item?.categoryName}
								categoryId={item?.categoryId}
								categoryName={item?.categoryName}
								productCount={item?.productCount}
							/>
						);
					})}
			</SortableContext>
			<DragOverlay dropAnimation={dropAnimationConfig}>
				{activeItem ? (
					<SortableCategoryItem
						categoryId={activeItem?.categoryId}
						categoryName={activeItem?.categoryName}
						productCount={activeItem?.productCount}
					/>
				) : null}
			</DragOverlay>
		</DndContext>
	);
};
export default SortableCategoryList;
