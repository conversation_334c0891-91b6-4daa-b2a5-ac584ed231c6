import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import SortableCategoryList from './SortableCategoryList';
import { StylesWrapper } from './index.style';
import { VenueApiRoutes } from '../../../Utils/routes';
import Api from '../../../Helper/Api';
import CategoryListSkeleton from './CategoryListSkeleton';
import NewLoader from '../../../Components/Common/NewLoader';

const Categories = ({ barId }) => {
	const [categoryList, setCategoryList] = useState([]);
	const [categoryListLoading, setCategoryListLoading] = useState(false);
	const [updateSequenceLoading, setUpdateSequenceLoading] = useState(false);

	const handleUpdateCategorySequence = async (updatedCategoryList) => {
		const currentCategoryList = [...categoryList];
		setCategoryList(updatedCategoryList);
		setUpdateSequenceLoading(true);
		const payload = {
			bar_id: barId,
			sub_category_ids: [
				0,
				...updatedCategoryList?.map((item) => item?.categoryId)
			]
		};
		try {
			const res = await Api(
				'POST',
				VenueApiRoutes?.updateSubcategorySequence,
				payload
			);
			if (res?.data?.status) {
				toast.success(res?.data?.message);
			} else {
				setCategoryList(currentCategoryList);
				toast.error(res?.data?.message);
			}
			setUpdateSequenceLoading(false);
		} catch (err) {
			setCategoryList(currentCategoryList);
			setUpdateSequenceLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	const getCategoryList = async () => {
		setCategoryListLoading(true);
		try {
			const res = await Api('POST', VenueApiRoutes?.getMenuCategoryList, {
				bar_id: barId
			});
			if (res?.data?.status) {
				let formatedCategoryList = res?.data?.data?.map((item) => ({
					categoryId: item?.id,
					categoryName: item?.name,
					productCount: item?.productCount
				}));
				setCategoryList(formatedCategoryList);
			} else {
				toast.error(res?.data?.message);
			}
			setCategoryListLoading(false);
		} catch (err) {
			setCategoryListLoading(false);
			if (err?.message) {
				toast.error(err?.message);
			}
		}
	};
	useEffect(() => {
		getCategoryList();
	}, []);
	return (
		<StylesWrapper>
			<div className="description">
				Categories allow you to group products under specific headings
				on your menu, making it easier for customers to navigate. The
				order of the categories below determines how they appear on the
				menu. To rearrange them, simply use the dotted icon on the left
				side of each box and drag-and-drop the categories into your
				preferred sequence.
			</div>
			<NewLoader loading={updateSequenceLoading}>
				<div className="categoryItemCardWrapper">
					{categoryListLoading ? (
						<CategoryListSkeleton />
					) : (
						<>
							{categoryList?.length > 0 ? (
								<SortableCategoryList
									categoryList={categoryList}
									setCategoryList={setCategoryList}
									handleUpdateCategorySequence={
										handleUpdateCategorySequence
									}
								/>
							) : (
								<div className="noDataFoundWrapper">
									You have no menu categories. To get started,
									add products to your menu.
								</div>
							)}
						</>
					)}
				</div>
			</NewLoader>
		</StylesWrapper>
	);
};

export default Categories;
