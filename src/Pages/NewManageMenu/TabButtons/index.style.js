import styled from 'styled-components';

export const StylesWrapper = styled.div`
	display: flex;
	flex-wrap: wrap;
	gap: 11px;
	padding-top: 16px;
	@media (max-width: 600px) {
		padding-top: 11px;
		gap: 7px;
	}
	@media only screen and (min-width: 601px) and (max-width: 1299px) {
		padding-top: 12px;
		gap: 8px;
	}
	@media only screen and (min-width: 1300px) and (max-width: 1824px) and (max-height: 900px) {
		padding-top: 12px;
		gap: 8px;
	}
`;
