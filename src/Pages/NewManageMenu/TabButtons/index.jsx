import React from 'react';
import { StylesWrapper } from './index.style';
import { FilledButton } from '../../../Components/Layout/Buttons';
import { tabButtonData } from '../utils';

const TabButtons = ({ selectedTabButtonKey, setSelectedTabButtonKey }) => {
	return (
		<StylesWrapper>
			{tabButtonData?.length > 0 &&
				tabButtonData?.map((item) => (
					<FilledButton
						key={item?.key}
						buttonText={item?.label}
						background={
							item?.key == selectedTabButtonKey
								? 'rgba(130, 128, 255, 0.2)'
								: '#ffffff'
						}
						color={'rgba(130, 128, 255, 1)'}
						style={{
							width: '160px',
							border: '1px solid rgba(130, 128, 255, 1)'
						}}
						onClick={() => setSelectedTabButtonKey(item?.key)}
					/>
				))}
		</StylesWrapper>
	);
};

export default TabButtons;
