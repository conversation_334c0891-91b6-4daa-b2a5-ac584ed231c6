export const getFileExtension = (fileName) => {
	// Extract the part of the fileName after the last dot (.)
	if (fileName) {
		const dotIndex = fileName?.lastIndexOf('.');
		if (dotIndex !== -1) {
			return fileName?.slice(dotIndex + 1)?.toLowerCase();
		} else {
			return null; // No file extension found
		}
	}
	return null;
};

// Function to generate random string
export const generateRandomString = (length = 8) => {
	var result = '';
	var characters =
		'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
	var charactersLength = characters.length;
	for (var i = 0; i < length; i++) {
		result += characters.charAt(
			Math.floor(Math.random() * charactersLength)
		);
	}
	return result;
};

export const getUniqueFileName = (originalFilename) => {
	const filename =
		generateRandomString(6) +
		'_' +
		Date.now() +
		'.' +
		getFileExtension(originalFilename);
	return filename;
};
