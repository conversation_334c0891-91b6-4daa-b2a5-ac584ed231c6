{"name": "myab-new-cms", "version": "0.1.0", "private": true, "dependencies": {"@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@fortawesome/fontawesome-svg-core": "^6.2.1", "@fortawesome/free-regular-svg-icons": "^6.2.1", "@fortawesome/free-solid-svg-icons": "^6.2.1", "@fortawesome/react-fontawesome": "^0.2.0", "@fullcalendar/core": "^6.1.4", "@fullcalendar/daygrid": "^6.1.4", "@fullcalendar/interaction": "^6.1.4", "@fullcalendar/react": "^6.1.4", "@reduxjs/toolkit": "^1.9.0", "@stripe/react-stripe-js": "^2.1.0", "@stripe/stripe-js": "^1.53.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@typeform/embed-react": "^4.2.0", "axios": "^1.1.3", "bootstrap": "^5.2.2", "classnames": "^2.3.2", "d3-array": "^3.2.2", "d3-format": "^3.1.0", "d3-scale": "^4.0.2", "date-fns": "^2.29.3", "firebase": "^10.4.0", "formik": "^2.2.9", "google-maps-react": "^2.0.6", "js-file-download": "^0.4.12", "moment": "^2.29.4", "moment-timezone": "^0.5.45", "multiselect-react-dropdown": "^2.0.25", "node-sass": "^8.0.0", "prettier": "^2.8.4", "radium": "^0.26.2", "react": "^18.2.0", "react-avatar": "^5.0.3", "react-beautiful-dnd": "^13.1.1", "react-cookie": "^4.1.1", "react-cropper": "^2.3.3", "react-custom-scrollbars": "^4.2.1", "react-date-range": "^1.4.0", "react-datepicker": "^4.8.0", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-globe.gl": "^2.22.8", "react-infinite-scroll-component": "^6.1.0", "react-intl-tel-input": "^8.2.0", "react-lazy-load-image-component": "^1.6.3", "react-loader-spinner": "^5.3.4", "react-loading-skeleton": "^3.3.1", "react-masonry-css": "^1.0.16", "react-otp-input": "^2.4.0", "react-paginate": "^8.1.4", "react-qr-code": "^2.0.9", "react-query": "^3.39.3", "react-range": "^1.10.0", "react-redux": "^8.0.5", "react-responsive": "^9.0.2", "react-router-dom": "^6.4.3", "react-scripts": "5.0.1", "react-select": "^5.7.0", "react-table-6": "^6.11.0", "react-tabs-scrollable": "^1.0.10", "react-tiny-popover": "^8.1.4", "react-toastify": "^9.1.1", "react-wordcloud": "^1.2.7", "reactstrap": "^9.1.5", "recharts": "^2.2.0", "redux": "^4.2.0", "redux-persist": "^6.0.0", "styled-components": "^5.3.6", "styled-theme": "^0.3.3", "three": "^0.147.0", "web-vitals": "^2.1.4", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "format": "prettier --write \"./**/*.{ts,tsx,js,jsx,json}\" --config ./.prettierrc"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}